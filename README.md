### app_service
### 创新业务服务

#### 目录结构
```
.
├── README.md
├── apps                                         // 业务模块 抽谷机/百人团
│   ├── group_buying								// 百人团
│   │   ├── ...
│   ├── creation_event								 // 造物活动
│   │   ├── constant                             // 定义该业务模块下所需常量
│   │   ├── define                               // 定义req、resp、层与层之间转发的结构体模型
│   │   │   ├── validator					 // 定义模块下使用的校验方法，具体参考后续参数校验部分
│   │   │   ├── retcode                    // 定义该业务下的错误
│   │   ├── repository                           // 定义数据库操作方法
│   │   ├── model                                // 定义数据表结构
│   │   ├── service                              // 定义service层方法
│   │   ├── task                                 // 定义异步任务和定时任务
│   │   │   ├── msg							// 消息作业（异步任务
│   │   │   ├── rpc							// rpc作业（定时任务）
│   │   ├── facade								// 对其他模块暴露的方法
│   │   ├── api                                 // 定义api层方法
│   │   ├── router								// 定义url路由
├── third_party                                             // 第三方服务
│   ├── watbg                                         // 具体的第三方
├── cmd                                                // 应用服务启动入口
│   ├── http_server                              // http服务入口
│   │   ├── main.go
│   ├── task_server                              // task服务入口
│   │   ├── main.go
│   ├── grpc_server                              // grpc
│   │   ├── main.go
├── conf                                         // 配置文件
│   └── http_server
│   │   │   ├── config-dev.yaml				// local环境配置文件
│   │   │   ├── config-sit.yaml				// sit环境配置文件
│   │   │   ├── config-sim.yaml				// sim环境配置文件
│   │   │   ├── config-prod.yaml				// prod环境配置文件
│   │   ├── config.yaml                            // 共有配置
├── deploy                                       // 部署文件
├── go.mod                                       // 项目依赖
├── gen                                          // 生成工具
├── pkg                                          // pkg
│   └── middlewares
│   │   ├── panic_handler           // panic统一处理
│   │   ├── response_handler        // panic统一处理
│   │   ├── timeout_handler         // panic统一处理
│   └── util                              // 公共util工具
│   └── search                            // 
├── log                                          // 项目日志文件
├── test                                         // 测试，包括一下线下执行的文件
│   └── command                            // 命令执行


```