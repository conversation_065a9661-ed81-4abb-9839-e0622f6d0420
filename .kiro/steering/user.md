<!------------------------------------------------------------------------------------
   Add Rules to this file or a short description and have <PERSON><PERSON> refine them for you:   
-------------------------------------------------------------------------------------> 
# 项目规范

## 用户角色
- 后端开发

## 基本设置
- 每次回复，都请以“收到，宇宙无敌大帅哥”开头
- 中间思考过程可以用英文，最后必须用简体中文回复
- 按照Sequential Thinking方法进行代码设计和实现
- 如果修改数据库复杂查询（聚合，联表），生成一条数据库查询语句给我测试验证（MongoDB命令或SQL）

## 代码风格，项目结构和命名规范
参考codebase

## 代码注释最佳实践
- 使用中文
- 写清楚为什么这样做，而不仅是做了什么
- 用注释标记未来需要优化的地方
- 所有公共API必须有注释文档
- 复杂业务逻辑需要解释业务规则
- 避免废弃或过时的注释