# 需求文档

## 介绍

卡牌订单推送通知系统是一个专门用于处理卡牌交易订单推送通知的功能模块。该系统通过消息接口驱动的方式实现推送通知，确保订单状态变化时能够及时、准确地通知相关用户。系统采用异步处理机制，不影响订单创建的响应时间，同时保证推送通知与订单消息的数据一致性。

## 卡牌2.0新增需求

### 需求 8

**用户故事：** 作为一个用户，我想要在聊天中创建交易订单，以便与商家进行具体的卡牌交易

#### 验收标准

1. WHEN 用户在聊天页面点击"创建交易订单"按钮 THEN 系统 SHALL 显示订单创建页面
2. WHEN 用户上传卡片图片 THEN 系统 SHALL 要求每个卡片坑位必须上传正反面两张图片
3. WHEN 任一卡片坑位只上传1张图片 THEN 系统 SHALL 阻止提交并显示toast"宝贝图片正反面没添加全哦"
<!-- 4. WHEN 用户删除卡片图片 THEN 系统 SHALL 删除图片对象存储资源 -->
5. WHEN 用户未填写出售价格 THEN 系统 SHALL 阻止提交并显示toast"出售价格还没填写哦"
6. WHEN 用户输入每组卡片数量和总价格 THEN 系统 SHALL 显示卡牌总数，订单金额
7. WHEN 图片和价格校验通过且用户确认订单信息 THEN 系统 SHALL 创建订单记录并通过发送消息接口将订单消息发送到聊天
8. WHEN 订单创建成功 THEN 系统 SHALL 更新订单状态为"待支付"
9. WHEN 订单消息发送 THEN 系统 SHALL 在聊天中显示订单卡片信息

### 需求 9

**用户故事：** 作为一个买家，我想要查看和管理我的订单状态，以便跟踪交易进度

#### 验收标准

1. WHEN 买家查看订单详情 THEN 系统 SHALL 显示卖家昵称，订单状态、卡片信息、订单金额，实付款和收货地址
2. WHEN 订单状态为"待支付" THEN 系统 SHALL 显示支付按钮和24小时倒计时（小时:分钟格式）
3. WHEN 买家点击"去支付" THEN 系统 SHALL 校验用户实名认证状态
5. WHEN 实名认证通过 THEN 系统 SHALL 校验用户是否已添加收货地址
6. IF 用户未添加收货地址 THEN 系统 SHALL 提示"请先添加收货地址"
7. WHEN 所有校验通过且买家完成支付 THEN 系统 SHALL 更新订单状态为"待发货"
8. WHEN 订单创建24小时后仍未支付 THEN 系统 SHALL 自动取消订单
9. WHEN 卖家发货 THEN 系统 SHALL 更新订单状态为"待收货"
10. WHEN 买家确认收货 THEN 系统 SHALL 更新订单状态为"交易完成"

### 需求 11

**用户故事：** 作为一个用户，我想要查看我的买卖订单列表，以便管理我的所有交易

#### 验收标准

1. WHEN 用户进入"我买的"页面 THEN 系统 SHALL 显示用户作为买家的所有订单
2. WHEN 用户进入"我卖的"页面 THEN 系统 SHALL 显示用户作为卖家的所有订单
3. WHEN 用户选择订单状态筛选 THEN 系统 SHALL 按状态过滤显示订单列表
4. WHEN 用户点击订单项 THEN 系统 SHALL 显示订单详情页面
5. WHEN 显示订单列表 THEN 系统 SHALL 包含订单状态、商品信息、价格和时间
<!-- 6. WHEN 订单状态发生变化 THEN 系统 SHALL 实时更新订单列表显示 -->

### 需求 12

**用户故事：** 作为一个卖家，我想要管理我收到的订单，以便及时处理买家的购买请求

#### 验收标准

1. WHEN 卖家收到新订单 THEN 系统 SHALL 在聊天中显示订单消息
2. WHEN 卖家查看订单详情 THEN 系统 SHALL 显示买家信息、收货地址和订单状态
3. WHEN 订单状态为"待发货" THEN 卖家 SHALL 可以点击"发货"按钮
4. IF 订单被取消 THEN 系统 SHALL 更新订单状态为"交易关闭"

### 需求 13

**用户故事：** 作为一个用户，我想要能够取消或删除订单，以便管理不需要的交易

#### 验收标准

1. WHEN 订单状态为"待支付" THEN 买家 SHALL 可以取消订单
2. WHEN 买家取消待支付订单 THEN 系统 SHALL 更新订单状态为"已取消"并记录取消原因为"买家取消"
3. WHEN 卖家取消订单 THEN 系统 SHALL 更新订单状态为"已取消"并记录取消原因为"卖家取消"
4. WHEN 订单创建24小时后仍未支付 THEN 系统 SHALL 自动取消订单并记录取消原因为"超时取消"
5. WHEN 订单状态为"已取消"或"交易完成" THEN 用户 SHALL 可以删除订单记录
6. WHEN 用户删除订单 THEN 系统 SHALL 将订单标记为已删除但保留数据用于统计
7. WHEN 订单状态为"已支付"、"待发货"或"待收货" THEN 系统 SHALL 禁止用户取消订单
8. WHEN 订单被取消或删除 THEN 系统 SHALL 在订单列表中相应更新显示状态
9. WHEN 卖家或买家删除订单 THEN 系统 SHALL 仅对当前用户隐藏，不影响对方查看


### 需求 14

**用户故事：** 作为一个用户，我想要及时收到订单状态变化的推送通知，以便第一时间了解交易进展

#### 验收标准

1. WHEN 系统集成极光推送服务 THEN 系统 SHALL 支持向用户设备发送推送通知
2. WHEN 卖家创建订单并通过发送消息接口发送给买家 THEN 系统 SHALL 向买家推送标题"你有新的订单「待付款」"，内容"来自 [卖家昵称]"的通知
3. WHEN 买家完成支付 THEN 系统 SHALL 向卖家推送标题"你有订单「待发货」"，内容"来自 [买家昵称]"的通知
4. WHEN 卖家确认发货 THEN 系统 SHALL 向买家推送标题"你有订单「待收货」"，内容"来自 [卖家昵称]"的通知
5. WHEN 买家确认收货 THEN 系统 SHALL 向卖家推送标题"交易完成"，内容"来自 [买家昵称]"的通知
6. WHEN 用户点击订单相关推送通知 THEN 系统 SHALL 跳转到对应的订单详情页面
7. WHEN 用户收到新消息 THEN 系统 SHALL 推送标题"你有新的消息"，内容显示消息内容预览的通知