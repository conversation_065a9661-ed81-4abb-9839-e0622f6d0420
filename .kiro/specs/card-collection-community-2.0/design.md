# 卡牌2.0新增设计

### 概述

卡牌2.0在原有帖子发布和IM聊天基础上，新增了完整的交易订单系统，包括订单创建、支付流程、状态管理、地址管理和推送通知等功能。系统支持多组卡片交易，提供完整的买卖双方交易体验。

### 新增业务流程UML

#### 1. 交易订单创建流程时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant API as API服务
    participant DB as MySQL数据库
    participant COS as 文件存储
    participant Push as 极光推送

    Note over U,COS: 第一步：上传图片获取URL
    U->>API: POST /web/v1/upload/images (上传卡片图片)
    API->>API: 校验图片格式和大小
    API->>COS: 上传图片到对象存储
    COS-->>API: 返回图片URL
    API-->>U: 返回图片URL
    
    Note over U,Push: 第二步：使用URL创建订单
    U->>API: POST /web/v1/card_community/orders/create (包含图片URL)
    API->>API: 校验卡片图片（正反面URL）
    API->>API: 校验价格信息
    API->>API: 校验卡片组数 = 数组长度
    API->>API: 校验总数量 = Quantity之和
    
    alt 图片不完整
        API-->>U: 返回错误"宝贝图片正反面没添加全哦"
    end
    
    alt 价格未填写
        API-->>U: 返回错误"出售价格还没填写哦"
    end
    
    alt 参数校验失败
        API-->>U: 返回参数错误信息
    end
    
    API->>DB: 创建订单记录（状态：待支付，包含卡片信息和第一张卡牌图）
    DB-->>API: 返回订单ID
    
    API->>DB: 发送订单消息到聊天
    DB-->>API: 消息保存成功
    
    Note over API: 异步发送推送通知事件（解耦）
    API->>API: 发送订单创建事件到消息队列
    
    API-->>U: 返回订单创建成功
    
    Note over API: 后台异步处理推送通知
    API->>API: 消费订单创建事件
    API->>Push: 调用极光推送服务
    Push-->>Push: 推送标题"你有新的订单「待付款」"，内容"来自 [卖家昵称]"给买家
```


#### 2. 订单支付流程时序图

```mermaid
sequenceDiagram
    participant B as 买家
    participant API as API服务
    participant DB as MySQL数据库
    participant Auth as 实名认证服务
    participant Addr as 地址服务
    participant Pay as 支付服务
    participant Push as 极光推送
    participant S as 卖家

    B->>API: POST /web/v1/card_community/orders/pay
    API->>Auth: 校验实名认证状态
    
    alt 未实名认证
        Auth-->>API: 返回未认证
        API-->>B: 弹出实名认证弹框
    end
    
    API->>API: 校验前端传递的收货地址
    
    alt 无收货地址
        API-->>B: 提示"请先添加收货地址"
    end
    
    API->>Pay: 调用支付接口
    Pay-->>API: 支付成功
    
    API->>DB: 更新订单状态为"待发货"
    DB-->>API: 状态更新成功
    
    Note over API: 异步发送推送通知事件（解耦）
    API->>API: 发送订单支付事件到消息队列
    
    API-->>B: 支付成功
    
    Note over API: 后台异步处理推送通知
    API->>API: 消费订单支付事件
    API->>Push: 调用极光推送服务
    Push-->>S: 推送标题"你有订单「待发货」"，内容"来自 [买家昵称]"给卖家
```

#### 3. 订单状态流转图

```mermaid
stateDiagram-v2
    [*] --> 待支付 : 创建订单
    待支付 --> 已取消 : 买家取消/卖家取消
    待支付 --> 已取消 : 24小时超时
    待支付 --> 待发货 : 买家支付
    待发货 --> 待收货 : 卖家发货
    待收货 --> 交易完成 : 买家确认收货
    已取消 --> [*] : 删除订单
    交易完成 --> [*] : 删除订单
```

#### 4. 推送通知实现机制时序图

```mermaid
sequenceDiagram
    participant S as 卖家
    participant API as API服务
    participant DB as 数据库
    participant Push as 推送服务
    participant B as 买家

    Note over S,B: 卖家创建订单并发送给买家
    S->>API: 用户点击创建订单
    API->>DB: 创建订单记录
    DB-->>API: 返回订单ID
    
    Note over API: 通过发送消息接口发送订单通知
    API->>API: 构造订单消息内容
    API->>DB: 保存订单消息到messages表
    DB-->>API: 消息保存成功
    
    API->>DB: 更新conversations表最后消息信息
    DB-->>API: 会话更新成功
    
    API-->>S: 返回订单创建成功
    
    Note over API,Push: 异步发送推送通知（不影响订单创建）
    par 异步推送处理
        API->>Push: 异步发送推送通知
        Note over Push: 标题："你有新的订单「待付款」"<br/>内容："来自 [卖家昵称]"
        Push-->>B: 推送通知到买家设备
    end
    
    Note over B: 买家收到推送通知
    B->>API: 点击推送通知
    API->>API: 跳转到订单详情页面
    API-->>B: 显示订单详情
```

#### 5. 推送通知流程图

```mermaid
flowchart TD
    A[订单状态变化] --> B{判断推送类型}
    
    B -->|创建订单| C[推送给买家: 你有新的订单「待付款」<br/>内容: 来自 [卖家昵称]]
    B -->|支付完成| D[推送给卖家: 你有订单「待发货」<br/>内容: 来自 [买家昵称]]
    B -->|卖家发货| E[推送给买家: 你有订单「待收货」<br/>内容: 来自 [卖家昵称]]
    B -->|确认收货| F[推送给卖家: 交易完成<br/>内容: 来自 [买家昵称]]
    
    C --> H[点击跳转订单详情]
    D --> H
    E --> H
    F --> H
    
    H --> I[显示订单详情页面]
```

### 新增数据模型

#### 1. 订单表 (card_orders)

```sql
CREATE TABLE `card_orders` (
    `id` VARCHAR(64) NOT NULL COMMENT '订单ID（同时作为订单号显示给用户）',
    `conversation_group_id` VARCHAR(64) NOT NULL COMMENT '会话组ID',
    `buyer_id` VARCHAR(64) NOT NULL COMMENT '买家ID',
    `seller_id` VARCHAR(64) NOT NULL COMMENT '卖家ID',
    `card_items` JSON NOT NULL COMMENT '卡片信息，类型:[]CardItem',
    `first_card_image_url` VARCHAR(500) NOT NULL COMMENT '第一张卡牌图片URL（用于订单展示）',
    `card_groups_count` INT NOT NULL DEFAULT 0 COMMENT '卡片组数',
    `total_quantity` INT NOT NULL DEFAULT 0 COMMENT '卡片总数量',
    `total_amount` BIGINT NOT NULL COMMENT '订单总金额(分)',
    `pay_amount` BIGINT NOT NULL COMMENT '支付金额(分)',
    `payment_method` TINYINT NOT NULL DEFAULT 0 COMMENT '支付方式（如：0=卡牌钱包）',
    `transaction_no` VARCHAR(64) COMMENT '支付流水单号',
    `status` TINYINT NOT NULL DEFAULT 0 COMMENT '订单状态：0=待支付 10=待发货 20=待收货 30=交易完成 40=已取消',
    `cancel_type` TINYINT DEFAULT NULL COMMENT '取消类型：1=买家取消 2=卖家取消 3=超时取消',
    `shipping_address` JSON COMMENT '收货地址信息，类型:Address',
    `payment_at` DATETIME(3) DEFAULT NULL COMMENT '支付时间',
    `delivered_at` DATETIME(3) DEFAULT NULL COMMENT '发货时间',
    `received_at` DATETIME(3) DEFAULT NULL COMMENT '收货时间',
    `expired_at` DATETIME(3) NOT NULL COMMENT '订单过期时间',
    `finished_at` DATETIME(3) DEFAULT NULL COMMENT '订单完成时间',
    `cancel_at` DATETIME(3) DEFAULT NULL COMMENT '订单取消时间',
    `buyer_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '买家是否删除：0=否 1=是',
    `seller_deleted` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '卖家是否删除：0=否 1=是',
    `created_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    `updated_at` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    PRIMARY KEY (`id`),
    INDEX `idx_conversation_group_id` (`conversation_group_id`),
    INDEX `idx_buyer_id` (`buyer_id`),
    INDEX `idx_seller_id` (`seller_id`),
    INDEX `idx_status` (`status`),
    INDEX `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='卡牌交易订单表';
```

#### 2. 扩展消息表结构

```sql
-- 在原有messages表基础上新增订单相关字段
ALTER TABLE `messages` 
ADD COLUMN `order_id` VARCHAR(64) DEFAULT NULL COMMENT '关联的订单ID（订单消息类型时使用）' AFTER `post_snapshot`,
ADD COLUMN `order_snapshot` JSON DEFAULT NULL COMMENT '订单快照信息，类型:OrderSnapshot' AFTER `order_id`,
ADD INDEX `idx_order_id` (`order_id`);

-- 更新消息类型注释
ALTER TABLE `messages` 
MODIFY COLUMN `message_type` TINYINT NOT NULL COMMENT '消息类型：1=文本 2=图片 3=帖子快照 4=订单消息';

-- 更新会话表最后消息类型注释
ALTER TABLE `conversations` 
MODIFY COLUMN `last_message_type` TINYINT DEFAULT NULL COMMENT '最后消息类型：1=文本 2=图片 3=帖子快照 4=订单消息';
```

#### 3. 枚举定义

```go
// OrderStatus 订单状态
type OrderStatus int
const (
    OrderStatusUnPaid      OrderStatus = 0  // 待支付
    OrderStatusUnDelivered OrderStatus = 10 // 待发货
    OrderStatusUnReceive   OrderStatus = 20 // 待收货
    OrderStatusCompleted   OrderStatus = 30 // 交易完成
    OrderStatusCanceled    OrderStatus = 40 // 已取消
)

// CancelType 取消类型
type CancelType int
const (
    CancelTypeBuyer   CancelType = 1 // 买家取消
    CancelTypeSeller  CancelType = 2 // 卖家取消
    CancelTypeTimeout CancelType = 3 // 超时取消
)

// MessageType 消息类型扩展
type MessageType int
const (
    MessageTypeText  MessageType = 1 // 文本
    MessageTypeImage MessageType = 2 // 图片
    MessageTypePost  MessageType = 3 // 帖子快照
    MessageTypeOrder MessageType = 4 // 订单消息
)

// OrderPushType 订单推送类型
type OrderPushType int
const (
    PushTypeOrderUnPaid         OrderPushType = 1 // 待支付
    PushTypeOrderUnDelivered    OrderPushType = 2 // 待发货
    PushTypeOrderUnReceive      OrderPushType = 3 // 待收货
    PushTypeOrderCompleted      OrderPushType = 4 // 交易完成
)

// UserType 用户类型（用于订单统计）
type UserType int
const (
    UserTypeBuyer  UserType = 1 // 买家
    UserTypeSeller UserType = 2 // 卖家
)
```

### 组件接口和API设计

#### 1. Web端订单相关API

```go
// POST /web/v1/card_community/orders/create - 创建订单（使用图片URL）
// GET /web/v1/card_community/orders/detail - 获取订单详情
// GET /web/v1/card_community/orders/my - 我买的订单列表
// GET /web/v1/card_community/orders/stats - 订单状态统计
// POST /web/v1/card_community/orders/pay - 支付订单
// POST /web/v1/card_community/orders/cancel - 取消订单
// POST /web/v1/card_community/orders/delete - 删除订单
// POST /web/v1/card_community/orders/ship - 卖家发货
// POST /web/v1/card_community/orders/confirm - 买家确认收货
```

#### 2. Admin端订单管理API

```go
// GET /v1/card_community/orders/list - 获取求购订单列表（支持筛选和分页）
// GET /v1/card_community/orders/detail - 获取求购订单详情  
// GET /v1/card_community/orders/export - 导出求购订单数据（返回下载链接）
```

#### 3. Web端订单管理组件 (OrderManager)

```go
type OrderManager interface {
    // CreateOrder 创建交易订单
    CreateOrder(ctx context.Context, req *CreateOrderRequest) (*Order, error)
    
    // GetOrderDetail 获取订单详情
    GetOrderDetail(ctx context.Context, orderID string) (*OrderDetail, error)
    
    // GetOrderList 获取订单列表（我买的/我卖的）
    GetOrderList(ctx context.Context, req *GetOrderListRequest) (*OrderListResponse, error)
    
    // PayOrder 支付订单
    PayOrder(ctx context.Context, req *PayOrderRequest) (*PayOrderResponse, error)
    
    // CancelOrder 取消订单
    CancelOrder(ctx context.Context, orderID string, cancelType CancelType) error
    
    // DeleteOrder 删除订单
    DeleteOrder(ctx context.Context, orderID, userID string) error
    
    // UpdateOrderStatus 更新订单状态
    UpdateOrderStatus(ctx context.Context, orderID string, status OrderStatus) error
    
    // ShipOrder 卖家发货
    ShipOrder(ctx context.Context, orderID string) error
    
    // ConfirmReceived 买家确认收货
    ConfirmReceived(ctx context.Context, orderID string) error
    
    // GetOrderStatusStats 获取订单各状态数量统计
    GetOrderStatusStats(ctx context.Context, userID string, userType UserType) (*OrderStatusStats, error)
}

// CreateOrderRequest 创建订单请求
type CreateOrderRequest struct {
    ConversationID    string        `json:"conversation_id" binding:"required"` // 会话ID
    BuyerID           string        `json:"buyer_id" binding:"required"`        // 买家ID
    // SellerID          string        `json:"seller_id" binding:"required"`       // 卖家ID
    CardItems         []CardItem    `json:"card_items" binding:"required,min=1"` // 卡片信息
    FirstCardImageURL string        `json:"first_card_image_url" binding:"required"` // 第一张卡牌图片URL
    CardGroupsCount   int           `json:"card_groups_count" binding:"required,min=1"` // 卡片组数
    TotalQuantity     int           `json:"total_quantity" binding:"required,min=1"`    // 卡片总数量
    TotalAmount       int64         `json:"total_amount" binding:"required,min=1"`      // 总金额(分)
    // Remark            string        `json:"remark"`                             // 备注
}

// CardItem 卡片项目
type CardItem struct {
    FrontImageURL string `json:"front_image_url" binding:"required"`    // 正面图片URL
    // FrontImageID  string `json:"front_image_id" binding:"required"`     // 正面图片资源ID
    BackImageURL  string `json:"back_image_url" binding:"required"`     // 反面图片URL
    // BackImageID   string `json:"back_image_id" binding:"required"`      // 反面图片资源ID
    Quantity      int    `json:"quantity" binding:"required,min=1"`     // 数量
}

// OrderStatusStats 订单状态统计
type OrderStatusStats struct {
    UnPaidCount         int64 `json:"unpaid_count"`          // 待支付订单数
    UnDeliveredCount    int64 `json:"undelivered_count"`     // 待发货订单数
    UnReceiveCount      int64 `json:"unreceive_count"`       // 待收货订单数
}

```

```go
// PayOrderRequest 支付订单请求
type PayOrderRequest struct {
    OrderID         string  `json:"order_id" binding:"required"`         // 订单ID
    ShippingAddress Address `json:"shipping_address" binding:"required"` // 收货地址（前端传递）
}
```

#### 4. Admin端订单管理组件 (AdminOrderManager)

```go
// Admin端订单管理服务接口（在service层实现）
type AdminOrderService interface {
    // GetOrderListForAdmin 获取求购订单列表（支持筛选和分页）
    GetOrderListForAdmin(req *GetOrderAdminListReq) (*GetOrderAdminListResp, error)
    
    // GetOrderDetailForAdmin 获取求购订单详情
    GetOrderDetailForAdmin(req *GetOrderAdminDetailReq) (*GetOrderAdminDetailResp, error)
    
    // ExportOrdersForAdmin 导出求购订单数据
    ExportOrdersForAdmin(req *ExportOrderAdminReq) (*ExportOrderAdminResp, error)
}

// GetOrderAdminListReq 管理端订单列表请求
type GetOrderAdminListReq struct {
    pagination.Pagination
    // 筛选条件
    Status         *enums.OrderStatus `form:"status" json:"status"`                   // 订单状态筛选
    StartTime      time.Time          `form:"start_time" json:"start_time"`           // 开始时间
    EndTime        time.Time          `form:"end_time" json:"end_time"`               // 结束时间
    OrderID        string             `form:"order_id" json:"order_id"`               // 订单号
    ConversationGroupID string        `form:"conversation_group_id" json:"conversation_group_id"` // 会话组ID
    BuyerID        string             `form:"buyer_id" json:"buyer_id"`               // 买家ID
    BuyerPhone     string             `form:"buyer_phone" json:"buyer_phone"`         // 买家手机号
    SellerID       string             `form:"seller_id" json:"seller_id"`             // 卖家ID
    SellerPhone    string             `form:"seller_phone" json:"seller_phone"`       // 卖家手机号
}

// GetOrderAdminListResp 管理端订单列表响应
type GetOrderAdminListResp struct {
    List  []*GetOrderAdminListData `json:"list"`  // 订单列表
    Total int64                     `json:"total"` // 总数量
}

// GetOrderAdminListData 管理端订单列表数据
type GetOrderAdminListData struct {
    OrderID             string            `json:"order_id"`               // 订单ID
    ConversationGroupID string            `json:"conversation_group_id"`  // 会话组ID
    FirstCardImageURL   string            `json:"first_card_image_url"`   // 第一张卡牌图片URL
    CardGroupsCount     int               `json:"card_groups_count"`      // 卡片组数
    TotalQuantity       int               `json:"total_quantity"`         // 卡片总数量
    TotalAmount         int64             `json:"total_amount"`           // 订单金额
    SellerUserInfo      UserInfo          `json:"seller_user_info"`       // 卖家用户信息
    BuyerUserInfo       UserInfo          `json:"buyer_user_info"`        // 买家用户信息
    Status              enums.OrderStatus `json:"status"`                 // 订单状态
    PaymentAt           *time.Time        `json:"payment_at"`             // 支付时间
    CreatedAt           time.Time         `json:"created_at"`             // 创建时间
    DeliveredAt         *time.Time        `json:"delivered_at"`           // 发货时间
    ReceivedAt          *time.Time        `json:"received_at"`            // 终态时间
    ShippingAddress     *Address          `json:"shipping_address"`       // 收货地址信息（脱敏）
}

// GetOrderAdminDetailReq 管理端订单详情请求
type GetOrderAdminDetailReq struct {
    OrderID string `form:"order_id" json:"order_id" binding:"required"` // 订单ID
}

// GetOrderAdminDetailResp 管理端订单详情响应
type GetOrderAdminDetailResp struct {
    // 基本订单信息
    OrderID             string            `json:"order_id"`               // 订单编号
    TotalAmount         int64             `json:"total_amount"`           // 订单金额
    PayAmount           int64             `json:"pay_amount"`             // 付款金额
    CardGroupsCount     int               `json:"card_groups_count"`      // 交易数量（组数）
    TotalQuantity       int               `json:"total_quantity"`         // 卡片总数量
    ConversationGroupID string            `json:"conversation_group_id"`  // 会话组ID
    Status              enums.OrderStatus `json:"status"`                 // 订单状态
    PaymentMethod       enums.PaymentMethod            `json:"payment_method"`         // 支付渠道
    TransactionNo       string            `json:"transaction_no"`         // 流水单号
    
    // 用户信息
    SellerUserInfo UserInfo `json:"seller_user_info"` // 卖家用户信息
    BuyerUserInfo  UserInfo `json:"buyer_user_info"`  // 买家用户信息
    
    // 时间信息
    CreatedAt   time.Time  `json:"created_at"`   // 创建时间
    PaymentAt   *time.Time `json:"payment_at"`   // 支付时间
    DeliveredAt *time.Time `json:"delivered_at"` // 发货时间
    ReceivedAt  *time.Time `json:"received_at"`  // 终态时间（收货时间）
    
    // 收货信息
    ShippingAddress *Address `json:"shipping_address"` // 收货地址信息
    
    // 卡片信息
    CardItems []CardItem `json:"card_items"` // 卡片详情
}

// 卡片信息使用现有的CardItem结构

// ExportOrderAdminReq 管理端订单导出请求
type ExportOrderAdminReq struct {
    // 筛选条件（与列表查询相同）
    Status         *enums.OrderStatus `form:"status" json:"status"`
    StartTime      time.Time          `form:"start_time" json:"start_time"`
    EndTime        time.Time          `form:"end_time" json:"end_time"`
    OrderID        string             `form:"order_id" json:"order_id"`
    ConversationGroupID string        `form:"conversation_group_id" json:"conversation_group_id"`
    BuyerID        string             `form:"buyer_id" json:"buyer_id"`
    BuyerPhone     string             `form:"buyer_phone" json:"buyer_phone"`
    SellerID       string             `form:"seller_id" json:"seller_id"`
    SellerPhone    string             `form:"seller_phone" json:"seller_phone"`
}

// ExportOrderAdminResp 管理端订单导出响应
type ExportOrderAdminResp struct {
    DownloadURL string `json:"download_url"` // 文件下载链接
    FileName    string `json:"file_name"`    // 文件名
}

```

#### 5. 极光推送通知组件 (PushManager)

```go
type PushManager interface {
    // PushOrderNotification 推送订单相关通知
    PushOrderNotification(ctx context.Context, req *PushOrderRequest) error
    
    // PushMessageNotification 推送消息通知
    PushMessageNotification(ctx context.Context, req *PushMessageRequest) error
}

// PushOrderRequest 订单推送请求
type PushOrderRequest struct {
    UserID      string            `json:"user_id"`      // 接收用户ID
    OrderID     string            `json:"order_id"`     // 订单ID
    PushType    OrderPushType     `json:"push_type"`    // 推送类型
    FromUser    string            `json:"from_user"`    // 来源用户昵称
    OrderStatus OrderStatus       `json:"order_status"` // 订单状态
}

// PushMessageRequest 消息推送请求
type PushMessageRequest struct {
    UserID      string      `json:"user_id"`      // 接收用户ID
    MessageID   string      `json:"message_id"`   // 消息ID
    MessageType MessageType `json:"message_type"` // 消息类型
    Content     string      `json:"content"`      // 消息内容
    FromUser    string      `json:"from_user"`    // 来源用户昵称
}

```

#### 6. 发送消息组件 (MessageSender)

```go

// CreateOrderMessageRequest 创建订单消息请求
type CreateOrderMessageRequest struct {
    // 新增字段
    OrderID        string      `json:"order_id" binding:"required"`        // 订单ID
    OrderSnapshot  *OrderSnapshot `json:"order_snapshot" binding:"required"` // 订单快照信息
}

// OrderSnapshot 订单快照信息
type OrderSnapshot struct {
    OrderID         string    `json:"order_id"`         // 订单ID
    Status          string    `json:"status"`           // 订单状态
    TotalAmount     int64     `json:"total_amount"`     // 订单总金额（分）
    FirstCardImage  string    `json:"first_card_image"` // 第一张卡牌图片URL
    CardGroupsCount int       `json:"card_groups_count"` // 卡片组数
    TotalQuantity   int       `json:"total_quantity"`   // 卡片总数量
    // CreatedAt       time.Time `json:"created_at"`       // 创建时间
}
```






### 技术实现要点

#### 1. 订单号策略说明

订单号直接使用订单ID，无需单独的order_no字段。订单ID既作为数据库主键，也作为用户可见的订单号。

#### 2. 收货地址JSON结构定义

```go
// Address 收货地址结构
type Address struct {
    Name        string `json:"name"`         // 收货人姓名
    MobilePhone string `json:"mobile_phone"` // 收货人电话
    Code        string `json:"code"`         // 区域代码
    Area        string `json:"area"`         // 区域
    Place       string `json:"place"`        // 详细地址
}
```

#### 3. 订单详情API响应结构

```go
// OrderDetailResponse 订单详情响应
type OrderDetailResponse struct {
    OrderID string `json:"order_id"` // 订单ID（同时作为订单号）
    //ConversationID    string            `json:"conversation_id"`      // 会话ID
    BuyerID        string     `json:"buyer_id"`        // 买家ID
    BuyerNickname  string     `json:"buyer_nickname"`  // 买家昵称（关联查询用户表）
    SellerID       string     `json:"seller_id"`       // 卖家ID
    SellerNickname string     `json:"seller_nickname"` // 卖家昵称（关联查询用户表）
    CardItems      []CardItem `json:"card_items"`      // 卡片信息
    //FirstCardImageURL string            `json:"first_card_image_url"` // 第一张卡牌图片URL
    CardGroupsCount int    `json:"card_groups_count"` // 卡片组数
    TotalQuantity   int    `json:"total_quantity"`    // 卡片总数量
    TotalAmount     int64  `json:"total_amount"`      // 订单总金额(分)
    PayAmount       int64  `json:"pay_amount"`        // 支付金额(分)
    PaymentMethod   enums.PaymentMethod `json:"payment_method"`    // 支付方式
    //TransactionNo   string            `json:"transaction_no"`    // 支付流水单号
    Status          enums.OrderStatus `json:"status"`           // 订单状态
    CancelType      *enums.CancelType `json:"cancel_type"`      // 取消类型
    ShippingAddress *Address          `json:"shipping_address"` // 收货地址
    PaymentAt       *time.Time        `json:"payment_at"`       // 支付时间
    DeliveredAt     *time.Time        `json:"delivered_at"`     // 发货时间
    ReceivedAt      *time.Time        `json:"received_at"`      // 收货时间(成交时间）
    ExpiredAt       time.Time         `json:"expired_at"`       // 订单过期时间
    //FinishedAt      *time.Time        `json:"finished_at"`      // 订单完成时间
    //CancelAt  *time.Time `json:"cancel_at"`  // 订单取消时间
    CreatedAt time.Time `json:"created_at"` // 创建时间
    //UpdatedAt time.Time `json:"updated_at"` // 更新时间
}
```

#### 4. 订单超时处理

```go
// 使用定时任务处理订单超时
func HandleExpiredOrders() {
    // 查询超时待支付的订单
    expiredOrders := orderRepo.FindExpiredOrders()
    
    for _, order := range expiredOrders {
        // 再次查询判断状态是否可取消
    
        // 更新订单状态为已取消，并记录终态时间
        orderRepo.UpdateStatusWithFinishedTime(order.ID, OrderStatusCanceled, CancelTypeTimeout, time.Now())
        
        // 记录超时取消日志
        logger.Info("Order timeout cancelled", 
            "order_id", order.ID,
            "buyer_id", order.BuyerID)
    }
}
```

#### 5. 订单创建参数校验流程

```go
func ValidateCreateOrderRequest(req *CreateOrderRequest) error {
    // 1. 校验卡片组数 = 卡片数组长度
    if req.CardGroupsCount != len(req.CardItems) {
        return ErrCardGroupsCountMismatch
    }
    
    // 2. 校验总数量 = 所有卡片Quantity之和
    totalQuantity := 0
    for _, item := range req.CardItems {
        totalQuantity += item.Quantity
    }
    if req.TotalQuantity != totalQuantity {
        return ErrTotalQuantityMismatch
    }
    
    // 3. 校验第一张卡牌图片URL是否与第一组卡片的正面图片一致
    if len(req.CardItems) > 0 && req.FirstCardImageURL != req.CardItems[0].FrontImageURL {
        return ErrFirstCardImageMismatch
    }
    
    return nil
}
```

#### 6. 订单创建业务规则校验

```go
// 校验卖家待支付订单数量限制
func CheckSellerUnpaidOrderLimit(ctx context.Context, sellerID string) error {
    // 查询卖家当前待支付订单数量
    query := repo.GetQuery()
    cardOrderSchema := query.CardOrder
    cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(ctx))
    
    queryWrapper := search.NewQueryBuilder().
        Eq(cardOrderSchema.SellerID, sellerID).
        Eq(cardOrderSchema.Status, enums.OrderStatusUnPaid.Int32()).
        Eq(cardOrderSchema.SellerDeleted, false).
        Build()
    
    count, err := cardOrderRepo.Count(queryWrapper)
    if err != nil {
        return err
    }
    
    // 如果待支付订单数量 >= 5，则不能创建新订单
    if count >= 5 {
        return CC500408Err // "你有5笔订单买家还未支付，不能再创建了哦"
    }
    
    return nil
}

```

#### 6. 支付前校验流程

```go
func ValidateBeforePay(ctx context.Context, userID string, address *Address) error {
    // 1. 校验实名认证状态
    if !authService.IsRealNameVerified(userID) {
        return ErrRealNameNotVerified // 弹出实名认证弹框
    }
    
    // 2. 校验前端传递的收货地址
    if address == nil || address.Name == "" || address.MobilePhone == "" || address.Place == "" {
        return ErrNoShippingAddress // 提示"请先添加收货地址"
    }
    
    return nil
}
```



### 性能优化

#### 1. 订单列表查询优化

```sql
-- 买家订单列表查询（支持分页和状态筛选）
SELECT * FROM card_orders 
WHERE buyer_id = ? 
  AND buyer_deleted = 0 
  AND (? = 0 OR status = ?) -- 状态筛选
ORDER BY id DESC 
LIMIT ? OFFSET ?;

-- 卖家订单列表查询
SELECT * FROM card_orders 
WHERE seller_id = ? 
  AND seller_deleted = 0 
  AND (? = 0 OR status = ?) 
ORDER BY id DESC 
LIMIT ? OFFSET ?;

-- Admin端订单列表查询（支持多条件筛选）
SELECT 
    co.*,
    u1.nickname as seller_nickname,
    u2.nickname as buyer_nickname
FROM card_orders co
LEFT JOIN users u1 ON co.seller_id = u1.id
LEFT JOIN users u2 ON co.buyer_id = u2.id
WHERE 1=1
  AND (? = '' OR co.id = ?)                    -- 订单号筛选
  AND (? = '' OR co.conversation_group_id = ?) -- 会话组ID筛选
  AND (? = '' OR co.buyer_id = ?)              -- 买家ID筛选
  AND (? = '' OR co.seller_id = ?)             -- 卖家ID筛选
  AND (? IS NULL OR co.status = ?)             -- 状态筛选
  AND (? IS NULL OR co.created_at >= ?)        -- 开始时间筛选
  AND (? IS NULL OR co.created_at <= ?)        -- 结束时间筛选
  AND (? = '' OR u2.mobile_phone = ?)          -- 买家手机号筛选
  AND (? = '' OR u1.mobile_phone = ?)          -- 卖家手机号筛选
ORDER BY co.created_at DESC
LIMIT ? OFFSET ?;

-- 买家订单状态统计查询（只统计待支付、待发货、待收货）
SELECT 
    status,
    COUNT(*) as count
FROM card_orders 
WHERE buyer_id = ? 
  AND buyer_deleted = 0 
  AND status IN (0, 10, 20) -- 0=待支付 10=待发货 20=待收货
GROUP BY status;

-- 卖家订单状态统计查询（只统计待支付、待发货、待收货）
SELECT 
    status,
    COUNT(*) as count
FROM card_orders 
WHERE seller_id = ? 
  AND seller_deleted = 0 
  AND status IN (0, 10, 20) -- 0=待支付 10=待发货 20=待收货
GROUP BY status;
```

#### 2. 缓存策略

```go
// 订单详情缓存（5分钟）
func GetOrderDetail(orderID string) (*Order, error) {
    cacheKey := fmt.Sprintf("order:detail:%s", orderID)
    
    // 先从缓存获取
    if cached := redis.Get(cacheKey); cached != nil {
        return cached, nil
    }
    
    // 从数据库查询
    order := orderRepo.GetByID(orderID)
    
    // 写入缓存
    redis.Set(cacheKey, order, 5*time.Minute)
    
    return order, nil
}

// 订单状态统计缓存（1分钟）
func GetOrderStatusStats(userID string, userType UserType) (*OrderStatusStats, error) {
    cacheKey := fmt.Sprintf("order:stats:%s:%d", userID, userType)
    
    // 先从缓存获取
    if cached := redis.Get(cacheKey); cached != nil {
        return cached, nil
    }
    
    // 从数据库查询统计
    stats := orderRepo.GetStatusStats(userID, userType)
    
    // 写入缓存（1分钟）
    redis.Set(cacheKey, stats, 1*time.Minute)
    
    return stats, nil
}
```

### 监控和运维

#### 1. Admin端数据脱敏处理

```go
// 收货人姓名脱敏：只显示姓氏
func MaskReceiverName(name string) string {
    if len(name) == 0 {
        return ""
    }
    runes := []rune(name)
    if len(runes) == 1 {
        return string(runes[0]) + "*"
    }
    return string(runes[0]) + "*"
}

// 手机号脱敏：133****1234
func MaskPhoneNumber(phone string) string {
    if len(phone) != 11 {
        return phone
    }
    return phone[:3] + "****" + phone[7:]
}

// 收货地址脱敏：返回脱敏后的Address结构
func MaskAddress(address *Address) *Address {
    if address == nil {
        return nil
    }
    return &Address{
        Name:        MaskReceiverName(address.Name),
        MobilePhone: MaskPhoneNumber(address.MobilePhone),
        Code:        address.Code,
        Area:        address.Area,
```

#### 2. Admin端导出功能实现

```go
// 导出文件命名规则：yyyy/m/d至yyyy/m/d卡牌求购订单
func GenerateExportFileName(startDate, endDate *time.Time) string {
    if startDate == nil || endDate == nil {
        now := time.Now()
        return fmt.Sprintf("%s卡牌求购订单.xlsx", now.Format("2006/1/2"))
    }
    
    start := startDate.Format("2006/1/2")
    end := endDate.Format("2006/1/2")
    return fmt.Sprintf("%s至%s卡牌求购订单.xlsx", start, end)
}

// 导出Excel表头定义
var ExportHeaders = []string{
    "创建时间", "订单号", "会话ID", "交易数量", "订单金额",
    "卖家昵称", "卖家ID", "买家昵称", "买家ID", "订单状态",
    "付款时间", "发货时间", "终态时间", "收货人", "收货电话", "收货地址",
}
```

#### 3. 异常处理

```go
// 订单参数校验错误处理
func HandleValidationError(err error) (string, int) {
    switch err {
    case ErrCardImageIncomplete:
        return "宝贝图片正反面没添加全哦", 400
    case ErrPriceNotSet:
        return "出售价格还没填写哦", 400
    case ErrRealNameNotVerified:
        return "请先完成实名认证", 400
    case ErrNoShippingAddress:
        return "请先添加收货地址", 400
    default:
        return "参数错误", 400
    }
}

// 推送通知异常处理
func HandlePushFailure(ctx context.Context, req *PushOrderRequest, err error) {
    // 记录推送失败日志
    logger.Error("Push notification failed",
        "user_id", req.UserID,
        "order_id", req.OrderID,
        "push_type", req.PushType,
        "error", err)
    
    // 推送失败重试机制
    retryCount := 3
    for i := 0; i < retryCount; i++ {
        time.Sleep(time.Duration(i+1) * time.Second)
        if err := pushManager.PushOrderNotification(ctx, req); err == nil {
            logger.Info("Push notification retry success",
                "user_id", req.UserID,
                "order_id", req.OrderID,
                "retry_count", i+1)
            return
        }
    }
    
    // 重试失败，记录到失败队列
    // failureQueue.Add(req)
}
```