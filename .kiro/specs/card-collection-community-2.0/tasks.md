# 卡牌2.0新增功能实现计划

- [x] 1. 数据库集成配置
- [x] 1.1 创建MySQL表结构初始化
  - 在gen/SQL/目录创建card_orders_migration.sql
  - 定义card_orders表结构
  - 扩展messages表结构，新增order_id和order_snapshot字段
  - 扩展conversations表，更新last_message_type注释
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 1.2 创建GORM代码生成配置
  - 在gen/目录扩展card_community相关代码生成配置
  - 配置card_orders表的dal/model和dal/query自动生成
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 1.3 运行代码生成
  - 执行`go run ./gen/gen.go`生成订单数据模型和查询代码
  - 生成dal/model/card_order.go和dal/query相关代码
  - 生成repo目录下的订单仓储代码
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 2. 定义数据模型和枚举类型
- [x] 2.1 创建订单相关枚举定义文件
  - 实现define/enums/order.go定义订单状态枚举
  - 实现define/enums/order.go定义取消类型枚举
  - 实现define/enums/order.go定义订单推送类型枚举
  - 实现define/enums/order.go定义用户类型枚举（买家/卖家）
  - 实现define/enums/order.go定义支付方式枚举
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 2.2 扩展消息类型枚举
  - 更新define/enums/message.go，新增订单消息类型
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 2.3 补充数据模型结构体
  - 实现dal/model/card_order.go，补充业务方法
  - 为订单模型添加状态判断方法（如IsUnPaid()、CanCancel()等）
  - 添加JSON处理方法（卡片信息、收货地址）
  - 添加用户角色判断和权限验证方法
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 3. 扩展错误码定义
  - 更新define/err_code.go，新增订单相关错误码
  - 包含订单、支付、推送通知相关错误码
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4. 创建请求响应结构定义
- [x] 4.1 定义订单相关请求响应结构
  - 实现define/order_web.go定义订单相关请求响应结构
  - 包含CreateOrderRequest、PayOrderRequest、OrderDetailResponse等
  - 定义CardItem、Address、OrderStatusStats等辅助结构
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4.2 扩展消息相关结构
  - 更新define/message_web.go，新增订单消息相关结构
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4.3 定义Admin端订单相关结构
  - 实现define/order_admin.go定义管理端订单相关结构
  - 包含GetOrderAdminListReq、GetOrderAdminDetailReq、ExportOrderAdminReq等
  - 定义管理端订单列表和详情响应结构
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 4.4 定义推送通知相关结构
  - 更新define/common.go定义推送通知请求结构
  - 包含PushOrderRequest、PushMessageRequest等
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 5. 实现服务层业务逻辑
- [x] 5.1 实现Web端订单管理服务
  - 实现service/order_web.go完整的订单业务逻辑
  - 包含创建订单、支付、发货、确认收货等核心方法
  - 实现订单状态统计、列表查询等辅助方法
  - 集成实名认证校验、收货地址验证等业务规则
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 5.3 实现Admin端订单管理服务
  - 实现service/order_admin.go管理端订单业务逻辑
  - 实现订单列表查询（支持多条件筛选和分页）
  - 实现订单详情查询（包含完整的用户和地址信息）
  - 实现订单数据导出功能（参考项目仓库）
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 5.4 实现推送通知服务
  - 实现service/push_web.go推送通知业务逻辑
  - 包含订单推送通知、消息推送通知方法
  - 实现推送内容构造和发送逻辑（当前为模拟实现）
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 5.5 扩展服务层统一入口
  - 更新service/service.go，提供统一的服务入口
  - 提供统一的依赖注入和初始化
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 6. 实现API层代码
- [x] 6.1 实现Web端订单API接口
  - 实现api/web/order.go提供完整的订单API接口
  - GET /web/v1/card_community/orders/detail - 获取订单详情
  - GET /web/v1/card_community/orders/list - 统一订单列表接口（支持买家/卖家视角）
  - GET /web/v1/card_community/orders/stats - 统一订单状态统计接口
  - POST /web/v1/card_community/orders/create - 创建订单
  - POST /web/v1/card_community/orders/pay - 支付订单
  - POST /web/v1/card_community/orders/cancel - 取消订单
  - POST /web/v1/card_community/orders/delete - 删除订单
  - POST /web/v1/card_community/orders/ship - 卖家发货
  - POST /web/v1/card_community/orders/confirm - 买家确认收货
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 6.2 实现Admin端订单API接口
  - 实现api/admin/order.go提供管理端订单API接口
  - GET /admin/v1/orders/list - 获取订单列表（管理端）
  - GET /admin/v1/orders/detail - 获取订单详情（管理端）
  - GET /admin/v1/orders/export - 导出订单数据（管理端）
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 7. 生成Swagger文档
  - 为所有新增API接口添加Swagger注释
  - 运行swag init生成Swagger文档
  - 确保文档正确描述订单相关接口参数和响应
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 8. 配置路由注册
- [x] 8.1 创建Web端订单路由配置
  - 实现router/web/order.go配置Web端订单相关路由
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 8.2 创建Admin端订单路由配置
  - 实现router/admin/order.go配置Admin端订单相关路由
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 8.3 更新主路由注册文件
  - 更新router/router.go，注册新增的订单路由（Web端和Admin端）
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 9. 实现核心业务逻辑层
- [x] 9.1 实现订单业务逻辑支持
  - 实现service/logic/order.go中的实名认证检查
  - 实现service/logic/conversation.go中的会话查找和订单限制检查
  - 实现订单创建、支付、状态流转的核心业务逻辑
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

- [x] 9.2 扩展消息管理业务逻辑
  - service/logic/message.go已支持订单消息类型处理
  - 支持消息类型验证和内容生成
  - 支持会话更新和消息统计
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [x] 10. 实现数据访问层
- [x] 10.1 实现订单数据访问接口
  - 实现repo/card_order.gen.go提供订单CRUD操作
  - 支持订单列表查询，支持买家/卖家视角和状态筛选
  - 支持订单状态统计查询和超时订单查询
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [X] 11. 集成到主应用
- [X] 11.1 注册模块路由到主应用
  - 修改主路由文件，添加订单相关路由组
  - 配置订单API路径前缀
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_


- [ ] 12. 完善推送通知集成
- [x] 12.1 集成极光推送服务
  - 阅读参考PushMarketChangesMessage函数
  - 替换service/push_web.go中的模拟实现
  - 集成真实的极光推送SDK
  - 实现推送失败重试机制
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 13. 实现订单超时处理
- [ ] 13.1 实现定时任务处理超时订单
  - 实现定时任务处理超时未支付订单
  - 实现订单自动取消和状态更新
  - 集成定时任务调度器
  - _需求: 1.3, 1.4, 1.5, 1.6_

- [ ] 14. 实现缓存和性能优化
- [ ] 14.1 实现订单详情缓存
  - 实现订单详情5分钟缓存策略
  - 实现缓存键命名规范和过期策略
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 14.2 实现订单状态统计缓存
  - 实现订单状态统计1分钟缓存策略
  - 实现缓存更新和失效机制
  - _需求: 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_

- [ ] 15. 创建基础测试用例
- [ ] 15.1 创建订单业务逻辑单元测试
  - 为订单核心业务逻辑创建单元测试文件
  - 测试订单创建、支付、状态流转等核心功能
  - 测试参数校验和错误处理
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6_

- [ ] 15.2 创建订单API集成测试
  - 创建订单API接口的集成测试
  - 测试订单相关API接口的完整流程
  - 测试订单消息发送和推送通知
  - _需求: 1.1, 1.2, 1.3, 1.4, 1.5, 1.6, 2.1, 2.2, 2.3, 2.4, 2.5, 2.6_