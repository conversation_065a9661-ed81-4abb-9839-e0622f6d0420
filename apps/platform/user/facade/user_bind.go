package facade

import (
	enum "app_service/apps/platform/user/define/enums"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"context"
	"errors"
	"gorm.io/gorm"
)

func GetUserBindCount(ctx context.Context, userId string) (int32, error) {
	userBindSchema := repo.GetQuery().UserBind
	qwForUb := search.NewQueryBuilder().
		Eq(userBindSchema.UserID, userId).
		Eq(userBindSchema.Type, enum.Bind.Val()).Build()
	count, err := repo.NewUserBindRepo(userBindSchema.WithContext(ctx)).Count(qwForUb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	return int32(count), nil
}
