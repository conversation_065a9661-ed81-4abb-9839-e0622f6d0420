package facade

import (
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/repo"
	"context"
	"errors"
	"gorm.io/gorm"
)

func AddUserPurchaseLog(ctx context.Context, record *model.UserPurchaseLog) error {
	purchaseLogSchema := repo.GetQuery().UserPurchaseLog
	err := repo.NewUserPurchaseLogRepo(purchaseLogSchema.WithContext(ctx)).Save(record)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return common_define.CommonErr.Err(err)
	}
	return nil
}
