package service

import (
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/define"
	enum "app_service/apps/platform/user/define/enums"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"gorm.io/gorm"
)

func (s *Service) Bind(req *define.BindReq) (*define.BindResp, error) {
	if len(req.InviteCode) != 6 {
		return nil, response.ParamErr.SetMsg("invite code fail")
	}

	//受邀人
	targetUserId := s.GetUserId()
	if len(targetUserId) <= 0 {
		return nil, define.NotForNewUserErr
	}

	//查询邀请人
	newUserSchema := repo.GetQuery().NewUser
	queryWrapper := search.NewQueryBuilder().Eq(newUserSchema.InviteCode, req.InviteCode).Build()
	newUserTab, err := repo.NewNewUserRepo(newUserSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, common_define.CommonErr.Err(err)
	}
	if newUserTab == nil {
		return nil, define.NotForNewUserErr
	}

	log.Ctx(s.ctx).Infof("userId:%s, targetUserId:%s", newUserTab.UserID, targetUserId)

	//查询绑定关系
	userBindSchema := repo.GetQuery().UserBind
	qwForUb := search.NewQueryBuilder().
		Eq(userBindSchema.TargetUserID, targetUserId).
		Eq(userBindSchema.Type, enum.Bind.Val()).Build()
	userBindTab, err := repo.NewUserBindRepo(userBindSchema.WithContext(s.ctx)).SelectOne(qwForUb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	//判断是否能绑定
	userFromCtx := s.GetUserFromCtx()
	if userBindTab == nil {
		//一个小时内，才能绑定
		if util.Now().Unix()-userFromCtx.RegTime > int64(3600) {
			log.Ctx(s.ctx).Errorf("userId:%s, targetUserId:%s, over 1 hour bind fail", newUserTab.UserID, targetUserId)
			return nil, define.OldUserBindFailErr
		}
		now := util.Now()
		bindUserTab := &model.UserBind{
			UserID:       newUserTab.UserID,
			TargetUserID: targetUserId,
			Type:         enum.Bind.Val(),
			BindTime:     &now,
		}

		err := repo.NewUserBindRepo(userBindSchema.WithContext(s.ctx)).Save(bindUserTab)
		if err != nil {
			return nil, common_define.CommonErr.Err(err)
		}

		//发送Kafka
		_ = kafka_util.SendMsg(s.ctx, "user_bind", bindUserTab)
	}

	return nil, nil
}
