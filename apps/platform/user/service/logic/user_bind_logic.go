package logic

import (
	enum "app_service/apps/platform/user/define/enums"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"app_service/third_party/pat"
	"context"
	"errors"
	"gorm.io/gorm"
)

func GetRealUserBindCount(ctx context.Context, userId string) (int32, error) {
	//查询绑定关系
	userBindSchema := repo.GetQuery().UserBind
	qwForUb := search.NewQueryBuilder().
		Eq(userBindSchema.UserID, userId).
		Eq(userBindSchema.Type, enum.Bind.Val()).Build()
	userBindTabList, err := repo.NewUserBindRepo(userBindSchema.WithContext(ctx)).SelectList(qwForUb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return 0, err
	}
	if len(userBindTabList) == 0 {
		return 0, nil
	}

	//查询实名信息
	userIdList := make([]string, 0)
	for _, userBind := range userBindTabList {
		userIdList = append(userIdList, userBind.TargetUserID)
	}
	realInfoList, err := pat.GetRealInfo(ctx, userIdList)
	if err != nil {
		return 0, err
	}
	realCount := 0
	for _, realInfo := range realInfoList {
		if len(realInfo.RealName) > 0 {
			realCount = realCount + 1
		}
	}

	return int32(realCount), nil
}
