package service

import (
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/define"
	"app_service/apps/platform/user/repo"
	"app_service/apps/platform/user/service/logic"
	"app_service/pkg/search"
	"errors"
	"gorm.io/gorm"

	// 商家权限检查相关导入
	cc_logic "app_service/apps/business/card_community/service/logic"
)

func (s *Service) GetUserInfo(req *define.GetUserInfoReq) (*define.GetUserInfoResp, error) {
	//查询用户邀请码
	userId := s.GetUserId()
	if len(userId) <= 0 {
		return nil, define.NotForNewUserErr
	}

	newUserSchema := repo.GetQuery().NewUser
	queryWrapper := search.NewQueryBuilder().Eq(newUserSchema.UserID, userId).Build()
	newUserTab, err := repo.NewNewUserRepo(newUserSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, common_define.CommonErr.Err(err)
	}

	//新增
	if newUserTab == nil {
		userFromCtx := s.GetUserFromCtx()
		newUserTab = &model.NewUser{
			UserID:     userId,
			InviteCode: userFromCtx.InviteId,
		}
		err = repo.NewNewUserRepo(newUserSchema.WithContext(s.ctx)).Save(newUserTab)
		if err != nil {
			return nil, common_define.CommonErr.Err(err)
		}
	}

	//查询用户邀请了几个人
	count, _ := logic.GetRealUserBindCount(s.ctx, userId)

	// 检查用户是否为商家
	isMerchant := cc_logic.CheckUserMerchantStatus(s.ctx, userId)

	return &define.GetUserInfoResp{
		UserId:      userId,
		InviteCode:  newUserTab.InviteCode,
		InviteCount: count,
		IsMerchant:  isMerchant,
	}, nil
}
