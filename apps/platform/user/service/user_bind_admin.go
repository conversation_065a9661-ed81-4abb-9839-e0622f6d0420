package service

import (
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/define"
	enum "app_service/apps/platform/user/define/enums"
	user_facade "app_service/apps/platform/user/facade"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"gorm.io/gorm"
)

func (s *Service) ManualBind(req *define.BindAdminReq) (*define.BindAdminResp, error) {
	log.Ctx(s.ctx).Infof("userId:%s, targetUserId:%s", req.UserId, req.TargetUserId)

	user, err := user_facade.GetNewUser(s.ctx, req.UserId)
	if err != nil {
		return nil, err
	}

	targetUser, err := user_facade.GetNewUser(s.ctx, req.TargetUserId)
	if err != nil {
		return nil, err
	}

	//查询绑定关系
	userBindSchema := repo.GetQuery().UserBind
	qwForUb := search.NewQueryBuilder().
		Eq(userBindSchema.TargetUserID, targetUser.UserID).
		Eq(userBindSchema.Type, enum.Bind.Val()).Build()
	userBindTab, err := repo.NewUserBindRepo(userBindSchema.WithContext(s.ctx)).SelectOne(qwForUb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}

	if userBindTab != nil {
		return nil, common_define.CommonErr.SetMsg("用户已经被绑定")
	}

	now := util.Now()
	bindUserTab := &model.UserBind{
		UserID:       user.UserID,
		TargetUserID: targetUser.UserID,
		Type:         enum.Bind.Val(),
		BindTime:     &now,
	}

	err = repo.NewUserBindRepo(userBindSchema.WithContext(s.ctx)).Save(bindUserTab)
	if err != nil {
		return nil, common_define.CommonErr.Err(err)
	}

	//发送Kafka
	_ = kafka_util.SendMsg(s.ctx, "user_bind", bindUserTab)

	return nil, nil
}
