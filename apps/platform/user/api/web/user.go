package web

import (
	"app_service/apps/platform/user/define"
	"app_service/apps/platform/user/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetUserInfo
// @Summary 获取用户信息
// @Description 获取用户信息
// @Tags 用户端
// @x-apifox-folder "用户端/用户信息"
// @Param data body define.GetUserInfoReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetUserInfoResp}
// @Router  /web/v1/user/user/get_user_info [get]
// @Security Bearer
func GetUserInfo(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetUserInfoReq{}, s.GetUserInfo)
}
