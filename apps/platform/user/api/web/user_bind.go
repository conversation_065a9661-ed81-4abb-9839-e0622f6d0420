package web

import (
	"app_service/apps/platform/user/define"
	"app_service/apps/platform/user/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// Bind
// @Summary 绑定邀请码
// @Description 绑定邀请码
// @Tags 用户端
// @Param data body define.BindReq true "请求参数"
// @Success 200 {object} response.Data{data=define.BindResp}
// @Router  /web/v1/user/user_bind/bind [post]
// @Security Bearer
func Bind(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.BindReq{}, s.Bind)
}
