package admin

import (
	"app_service/apps/platform/user/define"
	"app_service/apps/platform/user/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// ManualBind
// @Summary 人工绑定邀请
// @Description 人工绑定邀请
// @Tags 管理端
// @Param data body define.BindReq true "请求参数"
// @Success 200 {object} response.Data{data=define.BindResp}
// @Router  /admin/v1/user/user_bind/manual_bind [post]
// @Security Bearer
func ManualBind(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.BindAdminReq{}, s.ManualBind)
}
