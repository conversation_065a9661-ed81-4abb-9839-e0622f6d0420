package repo

import (
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/dal/query"
	"app_service/pkg/search"
	"github.com/pkg/errors"
	"gorm.io/gorm"
)

type UserLoginLogRepository struct {
	do query.IUserLoginLogDo
}

func NewUserLoginLogRepo(do query.IUserLoginLogDo) *UserLoginLogRepository {
	return &UserLoginLogRepository{
		do: do,
	}
}

func (r *UserLoginLogRepository) SelectOne(queryWrapper *search.QueryWrapper) (*model.UserLoginLog, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	if len(records) == 0 {
		return nil, gorm.ErrRecordNotFound
	}
	if len(records) > 1 {
		return nil, errors.New("more than one item found")
	}
	return records[0], nil
}

func (r *UserLoginLogRepository) SelectList(queryWrapper *search.QueryWrapper) ([]*model.UserLoginLog, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}

	records, err := r.do.Find()
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (r *UserLoginLogRepository) SelectPage(queryWrapper *search.QueryWrapper, pageIndex int, pageSize int) ([]*model.UserLoginLog, int64, error) {
	if queryWrapper != nil {
		if len(queryWrapper.ScopeOpts) != 0 {
			r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
		}
		if len(queryWrapper.SelectFields) != 0 {
			r.do = r.do.Select(queryWrapper.SelectFields...)
		}
		if len(queryWrapper.OrderBy) != 0 {
			r.do = r.do.Order(queryWrapper.OrderBy...)
		}
	}
	records, count, err := r.do.FindByPage(search.Paginate(pageSize, pageIndex))
	if err != nil {
		return nil, 0, err
	}
	return records, count, nil
}

func (r *UserLoginLogRepository) Count(queryWrapper *search.QueryWrapper) (int64, error) {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	count, err := r.do.Count()
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (r *UserLoginLogRepository) Save(model *model.UserLoginLog) error {
	err := r.do.Create(model)
	if err != nil {
		return err
	}
	return nil
}

func (r *UserLoginLogRepository) BatchSave(models []*model.UserLoginLog, batchSize int) error {
	err := r.do.CreateInBatches(models, batchSize)
	if err != nil {
		return err
	}
	return nil
}

func (r *UserLoginLogRepository) UpdateById(model *model.UserLoginLog) error {
	result, err := r.do.Updates(model)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *UserLoginLogRepository) Update(ms *model.UserLoginLog, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(
			search.MakeOpt(queryWrapper.ScopeOpts...),
		)
	}
	result, err := r.do.Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *UserLoginLogRepository) UpdateField(params interface{}, queryWrapper *search.QueryWrapper) error {
	if queryWrapper != nil {
		r.do = r.do.Scopes(search.MakeOpt(queryWrapper.ScopeOpts...))
	}
	result, err := r.do.Updates(params)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *UserLoginLogRepository) RemoveByIds(ms ...*model.UserLoginLog) error {
	result, err := r.do.Delete(ms...)
	if err != nil {
		return err
	} else if result.RowsAffected != int64(len(ms)) {
		return UpdateFail
	}
	return nil
}
