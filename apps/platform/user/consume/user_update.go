package consume

import (
	asset_facade "app_service/apps/platform/asset/facade"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/repo"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"time"
)

type UserUpdateConsumer struct {
	middlewares.BaseConsumer
}

func NewUserUpdateConsumer() *UserUpdateConsumer {
	return &UserUpdateConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.UserSyncs, constant.CommonGroup),
	}
}

func (o *UserUpdateConsumer) GetTopic() string {
	return constant.UserSyncs
}

func (o *UserUpdateConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *UserUpdateConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserUpdateConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[user_syncs]kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &userInfo{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		//查询用户信息
		userSchema := repo.GetQuery().NewUser
		queryWrapper := search.NewQueryBuilder().Eq(userSchema.UserID, data.ID).Build()
		userTab, err := repo.NewNewUserRepo(userSchema.WithContext(ctx)).SelectOne(queryWrapper)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return err
		}
		if userTab == nil {
			userTab = &model.NewUser{
				UserID:      data.ID,
				MobilePhone: data.MobilePhone,
				Nickname:    data.Nickname,
				Avatar:      data.Avatar,
				RealName:    data.RealName,
				IDCard:      data.IDCard,
				InviteCode:  data.RelatedIDs.InviteID,
				CreatedAt:   data.CreatedAt,
				UpdatedAt:   data.UpdatedAt,
			}
			err := repo.NewNewUserRepo(userSchema.WithContext(ctx)).Save(userTab)
			if err != nil {
				return err
			}

			//查询UserWallet
			_, err = asset_facade.GetUserWallet(ctx, data.ID)
			if err != nil {
				return err
			}
		} else {
			userTab.Nickname = data.Nickname
			userTab.Avatar = data.Avatar
			userTab.RealName = data.RealName
			userTab.IDCard = data.IDCard
			userTab.MobilePhone = data.MobilePhone
			userTab.InviteCode = data.RelatedIDs.InviteID
			userTab.CreatedAt = data.CreatedAt
			err := repo.NewNewUserRepo(userSchema.WithContext(ctx)).UpdateById(userTab)
			if err != nil {
				return err
			}
		}

		return nil
	}

	return middlewares.SafeHandler(handler)
}

type userInfo struct {
	ID         string `json:"_id"`
	Status     int    `json:"status"`
	Nickname   string `json:"nickname"`
	Avatar     string `json:"avatar"`
	RegIP      string `json:"reg_ip"`
	RelatedIDs struct {
		InviteID       string `json:"invite_id"`
		ParentInviteID string `json:"parent_invite_id"`
		WalletID       string `json:"wallet_id"`
	} `json:"related_ids"`
	RealName    string    `json:"real_name"`
	IDCard      string    `json:"id_card"`
	MobilePhone string    `json:"mobile_phone"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Birthday    string    `json:"birthday"`
}
