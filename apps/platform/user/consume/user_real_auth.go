package consume

import (
	"app_service/apps/business/invite_reward/service/logic"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/repo"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
)

type UserRealAuthConsumer struct {
	middlewares.BaseConsumer
}

func NewUserRealAuthConsumer() *UserRealAuthConsumer {
	return &UserRealAuthConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.UserRealAuth, constant.CommonGroup),
	}
}

func (o *UserRealAuthConsumer) GetTopic() string {
	return constant.UserRealAuth
}

func (o *UserRealAuthConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *UserRealAuthConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserRealAuthConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[user_real_auth][app_service_user]kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &userInfo{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		if data == nil {
			return common_define.CommonWarnErr.Err(nil)
		}
		if len(data.RealName) > 0 {
			update := map[string]interface{}{
				"real_name":  data.RealName,
				"id_card":    data.IDCard,
				"updated_at": util.Now(),
			}
			userSchema := repo.GetQuery().NewUser
			queryWrapper := search.NewQueryBuilder().Eq(userSchema.UserID, data.ID).Build()
			err := repo.NewNewUserRepo(userSchema.WithContext(ctx)).UpdateField(update, queryWrapper)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}

			//发送实名奖励
			err = logic.AddRealAuthReward(ctx, data.ID)
			if err != nil {
				log.Ctx(ctx).Errorf("AddRealAuthReward err:%v", err)
				return err
			}
		}

		return nil
	}

	return middlewares.SafeHandler(handler)
}
