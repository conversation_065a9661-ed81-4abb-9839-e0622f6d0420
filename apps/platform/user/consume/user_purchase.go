package consume

import (
	"app_service/apps/business/invite_reward/service/logic"
	asset_facade "app_service/apps/platform/asset/facade"
	common_constant "app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	enum "app_service/apps/platform/user/define/enums"
	"app_service/apps/platform/user/facade"
	"app_service/apps/platform/user/repo"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/gorm"
	"math"
	"strings"
	"time"
)

type UserPurchaseConsumer struct {
	middlewares.BaseConsumer
}

func NewUserPurchaseConsumer() *UserPurchaseConsumer {
	return &UserPurchaseConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(common_constant.UserPurchase, common_constant.CommonGroup),
	}
}

func (o *UserPurchaseConsumer) GetTopic() string {
	return common_constant.UserPurchase
}

func (o *UserPurchaseConsumer) GetGroup() string {
	return common_constant.CommonGroup
}

func (o *UserPurchaseConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserUpdateConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[user_purchase]kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		userPurchaseLog := &model.UserPurchaseLog{}
		if strings.Contains(string(event.Message().Body), "issue_item_id") {
			//一手
			data := &userPurchase4Issue{}
			err := util.JsonStr2Struct(string(event.Message().Body), data)
			if err != nil {
				log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
				return common_define.CommonWarnErr.Err(err)
			}
			userPurchaseLog = &model.UserPurchaseLog{
				UserID:        data.UserId,
				Type:          enum.ISSUE.Val(),
				RelateOrderID: data.ID,
				OrderAmount:   data.PayAmount,
				PurchaseTime:  data.PayTime,
				Extra:         event.Message().Body,
			}
			err = facade.AddUserPurchaseLog(ctx, userPurchaseLog)
			if err != nil {
				log.Ctx(ctx).Errorf("保持消费记录 err:%v", err)
				return err
			}

		} else {
			//二手
			data := &userPurchase4Sale{}
			err := util.JsonStr2Struct(string(event.Message().Body), data)
			if err != nil {
				log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
				return common_define.CommonWarnErr.Err(err)
			}

			userPurchaseLog = &model.UserPurchaseLog{
				UserID:        data.BuyUserId,
				Type:          enum.SALE.Val(),
				RelateOrderID: data.UniId,
				OrderAmount:   int32(data.OrderAmount),
				PurchaseTime:  util.Now(),
				Extra:         event.Message().Body,
			}
			err = facade.AddUserPurchaseLog(ctx, userPurchaseLog)
			if err != nil {
				log.Ctx(ctx).Errorf("保持消费记录 err:%v", err)
				return err
			}
		}

		//发送奖励
		if userPurchaseLog.OrderAmount > 0 {
			inviteConfig, err := logic.GetInviteConfig(ctx)
			if err != nil {
				log.Ctx(ctx).Error("消费提测下发失败 err:%v", err)
				return err
			}
			purchaseRewardEnable := inviteConfig.PurchaseRewardEnable
			if purchaseRewardEnable == common_constant.Yes {
				//查询上级
				userBindSchema := repo.GetQuery().UserBind
				qwForBind := search.NewQueryBuilder().
					Eq(userBindSchema.TargetUserID, userPurchaseLog.UserID).
					Eq(userBindSchema.Type, enum.Bind.Val()).Build()
				userBindTab, err := repo.NewUserBindRepo(userBindSchema.WithContext(ctx)).SelectOne(qwForBind)
				if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
					return err
				}
				if userBindTab == nil {
					return nil
				}

				//查询用户
				targetUser, _ := facade.GetNewUser(ctx, userBindTab.TargetUserID)

				purchaseRewardAmount := inviteConfig.PurchaseRewardAmount
				rewardAmount := float64(userPurchaseLog.OrderAmount) / 100 * float64(purchaseRewardAmount) / 100
				bonusAmount := int32(math.Round(rewardAmount))
				if bonusAmount < 1 {
					bonusAmount = 1
				}

				err = asset_facade.AddUserBonus(ctx, userBindTab.UserID,
					userPurchaseLog.RelateOrderID,
					targetUser.Nickname,
					targetUser.Avatar, "消费提成", bonusAmount)
				if err != nil {
					log.Ctx(ctx).Error("发放奖励失败 err:%v", err)
					return err
				}
			}
		}

		return nil
	}

	return middlewares.SafeHandler(handler)
}

type nodeMsg struct {
	Event string `json:"event"`
	Data  any    `json:"data"`
}

type userPurchase4Issue struct {
	ID          string    `json:"_id"`
	UserId      string    `json:"user_id"`
	IssueItemId string    `json:"issue_item_id"`
	ItemId      string    `json:"item_id"`
	PayAmount   int32     `json:"pay_amount"`
	PayTime     time.Time `json:"pay_time"`
}

type userPurchase4Sale struct {
	SaleBatchId string      `json:"sale_batch_id"`
	BuyUserId   string      `json:"buy_user_id"`
	OrderAmount float32     `json:"order_amount"`
	SuccessList interface{} `json:"success_list"`
	UniId       string      `json:"uni_id"`
}
