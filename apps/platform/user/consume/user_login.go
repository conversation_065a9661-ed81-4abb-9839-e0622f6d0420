package consume

import (
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/user/dal/model"
	"app_service/apps/platform/user/repo"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"gorm.io/datatypes"
	"time"
)

type UserLoginConsumer struct {
	middlewares.BaseConsumer
}

func NewUserLoginConsumer() *UserLoginConsumer {
	return &UserLoginConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.UserLogin, constant.CommonGroup),
	}
}

func (o *UserLoginConsumer) GetTopic() string {
	return constant.UserLogin
}

func (o *UserLoginConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *UserLoginConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserLoginConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[user_syncs]kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &loginRecord{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		//写入MYSQL
		address := datatypes.JSON(data.Address)
		loginLogSchema := repo.GetQuery().UserLoginLog
		m := &model.UserLoginLog{
			ID:        data.Id,
			UserID:    data.UserID,
			Type:      data.Type,
			Version:   data.Version,
			Address:   &address,
			LoginTime: data.LoginTime,
			LoginIP:   data.LoginIP,
		}
		err = repo.NewUserLoginLogRepo(loginLogSchema.WithContext(ctx)).Save(m)
		if err != nil {
			return err
		}

		return nil
	}

	return middlewares.SafeHandler(handler)
}

type loginRecord struct {
	Id        string    `json:"_id"`
	Type      string    `json:"type"`
	LoginTime time.Time `json:"login_time"`
	LoginIP   string    `json:"login_ip"`
	Version   string    `json:"version"`
	UserID    string    `json:"user_id"`
	Address   string    `json:"address"`
}

type Address struct {
	Region Region `json:"region"`
}

type Region struct {
	Country  string `json:"country"`
	Province string `json:"province"`
	City     string `json:"city"`
	ISP      string `json:"isp"`
}
