// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/platform/user/dal/model"
)

func newUserPurchaseLog(db *gorm.DB, opts ...gen.DOOption) userPurchaseLog {
	_userPurchaseLog := userPurchaseLog{}

	_userPurchaseLog.userPurchaseLogDo.UseDB(db, opts...)
	_userPurchaseLog.userPurchaseLogDo.UseModel(&model.UserPurchaseLog{})

	tableName := _userPurchaseLog.userPurchaseLogDo.TableName()
	_userPurchaseLog.ALL = field.NewAsterisk(tableName)
	_userPurchaseLog.UserPurchaseID = field.NewInt64(tableName, "user_purchase_id")
	_userPurchaseLog.UserID = field.NewString(tableName, "user_id")
	_userPurchaseLog.Type = field.NewInt32(tableName, "type")
	_userPurchaseLog.RelateOrderID = field.NewString(tableName, "relate_order_id")
	_userPurchaseLog.OrderAmount = field.NewInt32(tableName, "order_amount")
	_userPurchaseLog.PurchaseTime = field.NewTime(tableName, "purchase_time")
	_userPurchaseLog.Extra = field.NewField(tableName, "extra")
	_userPurchaseLog.CreatedAt = field.NewTime(tableName, "created_at")
	_userPurchaseLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userPurchaseLog.IsDel = field.NewField(tableName, "is_del")

	_userPurchaseLog.fillFieldMap()

	return _userPurchaseLog
}

// userPurchaseLog 用户消费记录表
type userPurchaseLog struct {
	userPurchaseLogDo

	ALL            field.Asterisk
	UserPurchaseID field.Int64  // Id
	UserID         field.String // 用户id
	Type           field.Int32  // 类型 1-一手消费 2-二手消费
	RelateOrderID  field.String // 关联订单Id
	OrderAmount    field.Int32  // 订单金额
	PurchaseTime   field.Time   // 消费时间
	Extra          field.Field
	CreatedAt      field.Time  // 创建时间
	UpdatedAt      field.Time  // 更新时间
	IsDel          field.Field // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (u userPurchaseLog) Table(newTableName string) *userPurchaseLog {
	u.userPurchaseLogDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userPurchaseLog) As(alias string) *userPurchaseLog {
	u.userPurchaseLogDo.DO = *(u.userPurchaseLogDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userPurchaseLog) updateTableName(table string) *userPurchaseLog {
	u.ALL = field.NewAsterisk(table)
	u.UserPurchaseID = field.NewInt64(table, "user_purchase_id")
	u.UserID = field.NewString(table, "user_id")
	u.Type = field.NewInt32(table, "type")
	u.RelateOrderID = field.NewString(table, "relate_order_id")
	u.OrderAmount = field.NewInt32(table, "order_amount")
	u.PurchaseTime = field.NewTime(table, "purchase_time")
	u.Extra = field.NewField(table, "extra")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.IsDel = field.NewField(table, "is_del")

	u.fillFieldMap()

	return u
}

func (u *userPurchaseLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userPurchaseLog) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 10)
	u.fieldMap["user_purchase_id"] = u.UserPurchaseID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["type"] = u.Type
	u.fieldMap["relate_order_id"] = u.RelateOrderID
	u.fieldMap["order_amount"] = u.OrderAmount
	u.fieldMap["purchase_time"] = u.PurchaseTime
	u.fieldMap["extra"] = u.Extra
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_del"] = u.IsDel
}

func (u userPurchaseLog) clone(db *gorm.DB) userPurchaseLog {
	u.userPurchaseLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userPurchaseLog) replaceDB(db *gorm.DB) userPurchaseLog {
	u.userPurchaseLogDo.ReplaceDB(db)
	return u
}

type userPurchaseLogDo struct{ gen.DO }

type IUserPurchaseLogDo interface {
	gen.SubQuery
	Debug() IUserPurchaseLogDo
	WithContext(ctx context.Context) IUserPurchaseLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserPurchaseLogDo
	WriteDB() IUserPurchaseLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserPurchaseLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserPurchaseLogDo
	Not(conds ...gen.Condition) IUserPurchaseLogDo
	Or(conds ...gen.Condition) IUserPurchaseLogDo
	Select(conds ...field.Expr) IUserPurchaseLogDo
	Where(conds ...gen.Condition) IUserPurchaseLogDo
	Order(conds ...field.Expr) IUserPurchaseLogDo
	Distinct(cols ...field.Expr) IUserPurchaseLogDo
	Omit(cols ...field.Expr) IUserPurchaseLogDo
	Join(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo
	Group(cols ...field.Expr) IUserPurchaseLogDo
	Having(conds ...gen.Condition) IUserPurchaseLogDo
	Limit(limit int) IUserPurchaseLogDo
	Offset(offset int) IUserPurchaseLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserPurchaseLogDo
	Unscoped() IUserPurchaseLogDo
	Create(values ...*model.UserPurchaseLog) error
	CreateInBatches(values []*model.UserPurchaseLog, batchSize int) error
	Save(values ...*model.UserPurchaseLog) error
	First() (*model.UserPurchaseLog, error)
	Take() (*model.UserPurchaseLog, error)
	Last() (*model.UserPurchaseLog, error)
	Find() ([]*model.UserPurchaseLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserPurchaseLog, err error)
	FindInBatches(result *[]*model.UserPurchaseLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserPurchaseLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserPurchaseLogDo
	Assign(attrs ...field.AssignExpr) IUserPurchaseLogDo
	Joins(fields ...field.RelationField) IUserPurchaseLogDo
	Preload(fields ...field.RelationField) IUserPurchaseLogDo
	FirstOrInit() (*model.UserPurchaseLog, error)
	FirstOrCreate() (*model.UserPurchaseLog, error)
	FindByPage(offset int, limit int) (result []*model.UserPurchaseLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserPurchaseLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userPurchaseLogDo) Debug() IUserPurchaseLogDo {
	return u.withDO(u.DO.Debug())
}

func (u userPurchaseLogDo) WithContext(ctx context.Context) IUserPurchaseLogDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userPurchaseLogDo) ReadDB() IUserPurchaseLogDo {
	return u.Clauses(dbresolver.Read)
}

func (u userPurchaseLogDo) WriteDB() IUserPurchaseLogDo {
	return u.Clauses(dbresolver.Write)
}

func (u userPurchaseLogDo) Session(config *gorm.Session) IUserPurchaseLogDo {
	return u.withDO(u.DO.Session(config))
}

func (u userPurchaseLogDo) Clauses(conds ...clause.Expression) IUserPurchaseLogDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userPurchaseLogDo) Returning(value interface{}, columns ...string) IUserPurchaseLogDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userPurchaseLogDo) Not(conds ...gen.Condition) IUserPurchaseLogDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userPurchaseLogDo) Or(conds ...gen.Condition) IUserPurchaseLogDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userPurchaseLogDo) Select(conds ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userPurchaseLogDo) Where(conds ...gen.Condition) IUserPurchaseLogDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userPurchaseLogDo) Order(conds ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userPurchaseLogDo) Distinct(cols ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userPurchaseLogDo) Omit(cols ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userPurchaseLogDo) Join(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userPurchaseLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userPurchaseLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userPurchaseLogDo) Group(cols ...field.Expr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userPurchaseLogDo) Having(conds ...gen.Condition) IUserPurchaseLogDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userPurchaseLogDo) Limit(limit int) IUserPurchaseLogDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userPurchaseLogDo) Offset(offset int) IUserPurchaseLogDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userPurchaseLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserPurchaseLogDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userPurchaseLogDo) Unscoped() IUserPurchaseLogDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userPurchaseLogDo) Create(values ...*model.UserPurchaseLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userPurchaseLogDo) CreateInBatches(values []*model.UserPurchaseLog, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userPurchaseLogDo) Save(values ...*model.UserPurchaseLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userPurchaseLogDo) First() (*model.UserPurchaseLog, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPurchaseLog), nil
	}
}

func (u userPurchaseLogDo) Take() (*model.UserPurchaseLog, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPurchaseLog), nil
	}
}

func (u userPurchaseLogDo) Last() (*model.UserPurchaseLog, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPurchaseLog), nil
	}
}

func (u userPurchaseLogDo) Find() ([]*model.UserPurchaseLog, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserPurchaseLog), err
}

func (u userPurchaseLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserPurchaseLog, err error) {
	buf := make([]*model.UserPurchaseLog, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userPurchaseLogDo) FindInBatches(result *[]*model.UserPurchaseLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userPurchaseLogDo) Attrs(attrs ...field.AssignExpr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userPurchaseLogDo) Assign(attrs ...field.AssignExpr) IUserPurchaseLogDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userPurchaseLogDo) Joins(fields ...field.RelationField) IUserPurchaseLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userPurchaseLogDo) Preload(fields ...field.RelationField) IUserPurchaseLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userPurchaseLogDo) FirstOrInit() (*model.UserPurchaseLog, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPurchaseLog), nil
	}
}

func (u userPurchaseLogDo) FirstOrCreate() (*model.UserPurchaseLog, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserPurchaseLog), nil
	}
}

func (u userPurchaseLogDo) FindByPage(offset int, limit int) (result []*model.UserPurchaseLog, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userPurchaseLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userPurchaseLogDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userPurchaseLogDo) Delete(models ...*model.UserPurchaseLog) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userPurchaseLogDo) withDO(do gen.Dao) *userPurchaseLogDo {
	u.DO = *do.(*gen.DO)
	return u
}
