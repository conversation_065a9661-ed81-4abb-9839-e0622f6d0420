// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/platform/user/dal/model"
)

func newUserBind(db *gorm.DB, opts ...gen.DOOption) userBind {
	_userBind := userBind{}

	_userBind.userBindDo.UseDB(db, opts...)
	_userBind.userBindDo.UseModel(&model.UserBind{})

	tableName := _userBind.userBindDo.TableName()
	_userBind.ALL = field.NewAsterisk(tableName)
	_userBind.UserBindID = field.NewInt64(tableName, "user_bind_id")
	_userBind.UserID = field.NewString(tableName, "user_id")
	_userBind.TargetUserID = field.NewString(tableName, "target_user_id")
	_userBind.BindTime = field.NewTime(tableName, "bind_time")
	_userBind.Type = field.NewInt32(tableName, "type")
	_userBind.CreatedBy = field.NewString(tableName, "created_by")
	_userBind.CreatedAt = field.NewTime(tableName, "created_at")
	_userBind.UpdatedBy = field.NewString(tableName, "updated_by")
	_userBind.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userBind.IsDel = field.NewField(tableName, "is_del")

	_userBind.fillFieldMap()

	return _userBind
}

// userBind 用户Bind
type userBind struct {
	userBindDo

	ALL          field.Asterisk
	UserBindID   field.Int64  // Id
	UserID       field.String // 用户id
	TargetUserID field.String // 被邀请用户id
	BindTime     field.Time   // 绑定时间
	Type         field.Int32  // 类型 1-邀请绑定
	CreatedBy    field.String // 创建人
	CreatedAt    field.Time   // 创建时间
	UpdatedBy    field.String // 更新人
	UpdatedAt    field.Time   // 更新时间
	IsDel        field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (u userBind) Table(newTableName string) *userBind {
	u.userBindDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userBind) As(alias string) *userBind {
	u.userBindDo.DO = *(u.userBindDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userBind) updateTableName(table string) *userBind {
	u.ALL = field.NewAsterisk(table)
	u.UserBindID = field.NewInt64(table, "user_bind_id")
	u.UserID = field.NewString(table, "user_id")
	u.TargetUserID = field.NewString(table, "target_user_id")
	u.BindTime = field.NewTime(table, "bind_time")
	u.Type = field.NewInt32(table, "type")
	u.CreatedBy = field.NewString(table, "created_by")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedBy = field.NewString(table, "updated_by")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.IsDel = field.NewField(table, "is_del")

	u.fillFieldMap()

	return u
}

func (u *userBind) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userBind) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 10)
	u.fieldMap["user_bind_id"] = u.UserBindID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["target_user_id"] = u.TargetUserID
	u.fieldMap["bind_time"] = u.BindTime
	u.fieldMap["type"] = u.Type
	u.fieldMap["created_by"] = u.CreatedBy
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_by"] = u.UpdatedBy
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_del"] = u.IsDel
}

func (u userBind) clone(db *gorm.DB) userBind {
	u.userBindDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userBind) replaceDB(db *gorm.DB) userBind {
	u.userBindDo.ReplaceDB(db)
	return u
}

type userBindDo struct{ gen.DO }

type IUserBindDo interface {
	gen.SubQuery
	Debug() IUserBindDo
	WithContext(ctx context.Context) IUserBindDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserBindDo
	WriteDB() IUserBindDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserBindDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserBindDo
	Not(conds ...gen.Condition) IUserBindDo
	Or(conds ...gen.Condition) IUserBindDo
	Select(conds ...field.Expr) IUserBindDo
	Where(conds ...gen.Condition) IUserBindDo
	Order(conds ...field.Expr) IUserBindDo
	Distinct(cols ...field.Expr) IUserBindDo
	Omit(cols ...field.Expr) IUserBindDo
	Join(table schema.Tabler, on ...field.Expr) IUserBindDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserBindDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserBindDo
	Group(cols ...field.Expr) IUserBindDo
	Having(conds ...gen.Condition) IUserBindDo
	Limit(limit int) IUserBindDo
	Offset(offset int) IUserBindDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBindDo
	Unscoped() IUserBindDo
	Create(values ...*model.UserBind) error
	CreateInBatches(values []*model.UserBind, batchSize int) error
	Save(values ...*model.UserBind) error
	First() (*model.UserBind, error)
	Take() (*model.UserBind, error)
	Last() (*model.UserBind, error)
	Find() ([]*model.UserBind, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBind, err error)
	FindInBatches(result *[]*model.UserBind, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserBind) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserBindDo
	Assign(attrs ...field.AssignExpr) IUserBindDo
	Joins(fields ...field.RelationField) IUserBindDo
	Preload(fields ...field.RelationField) IUserBindDo
	FirstOrInit() (*model.UserBind, error)
	FirstOrCreate() (*model.UserBind, error)
	FindByPage(offset int, limit int) (result []*model.UserBind, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserBindDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userBindDo) Debug() IUserBindDo {
	return u.withDO(u.DO.Debug())
}

func (u userBindDo) WithContext(ctx context.Context) IUserBindDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userBindDo) ReadDB() IUserBindDo {
	return u.Clauses(dbresolver.Read)
}

func (u userBindDo) WriteDB() IUserBindDo {
	return u.Clauses(dbresolver.Write)
}

func (u userBindDo) Session(config *gorm.Session) IUserBindDo {
	return u.withDO(u.DO.Session(config))
}

func (u userBindDo) Clauses(conds ...clause.Expression) IUserBindDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userBindDo) Returning(value interface{}, columns ...string) IUserBindDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userBindDo) Not(conds ...gen.Condition) IUserBindDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userBindDo) Or(conds ...gen.Condition) IUserBindDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userBindDo) Select(conds ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userBindDo) Where(conds ...gen.Condition) IUserBindDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userBindDo) Order(conds ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userBindDo) Distinct(cols ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userBindDo) Omit(cols ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userBindDo) Join(table schema.Tabler, on ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userBindDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userBindDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userBindDo) Group(cols ...field.Expr) IUserBindDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userBindDo) Having(conds ...gen.Condition) IUserBindDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userBindDo) Limit(limit int) IUserBindDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userBindDo) Offset(offset int) IUserBindDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userBindDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBindDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userBindDo) Unscoped() IUserBindDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userBindDo) Create(values ...*model.UserBind) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userBindDo) CreateInBatches(values []*model.UserBind, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userBindDo) Save(values ...*model.UserBind) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userBindDo) First() (*model.UserBind, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBind), nil
	}
}

func (u userBindDo) Take() (*model.UserBind, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBind), nil
	}
}

func (u userBindDo) Last() (*model.UserBind, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBind), nil
	}
}

func (u userBindDo) Find() ([]*model.UserBind, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserBind), err
}

func (u userBindDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBind, err error) {
	buf := make([]*model.UserBind, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userBindDo) FindInBatches(result *[]*model.UserBind, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userBindDo) Attrs(attrs ...field.AssignExpr) IUserBindDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userBindDo) Assign(attrs ...field.AssignExpr) IUserBindDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userBindDo) Joins(fields ...field.RelationField) IUserBindDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userBindDo) Preload(fields ...field.RelationField) IUserBindDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userBindDo) FirstOrInit() (*model.UserBind, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBind), nil
	}
}

func (u userBindDo) FirstOrCreate() (*model.UserBind, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBind), nil
	}
}

func (u userBindDo) FindByPage(offset int, limit int) (result []*model.UserBind, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userBindDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userBindDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userBindDo) Delete(models ...*model.UserBind) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userBindDo) withDO(do gen.Dao) *userBindDo {
	u.DO = *do.(*gen.DO)
	return u
}
