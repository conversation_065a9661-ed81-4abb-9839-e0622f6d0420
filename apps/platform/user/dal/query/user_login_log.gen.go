// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/platform/user/dal/model"
)

func newUserLoginLog(db *gorm.DB, opts ...gen.DOOption) userLoginLog {
	_userLoginLog := userLoginLog{}

	_userLoginLog.userLoginLogDo.UseDB(db, opts...)
	_userLoginLog.userLoginLogDo.UseModel(&model.UserLoginLog{})

	tableName := _userLoginLog.userLoginLogDo.TableName()
	_userLoginLog.ALL = field.NewAsterisk(tableName)
	_userLoginLog.ID = field.NewString(tableName, "id")
	_userLoginLog.UserID = field.NewString(tableName, "user_id")
	_userLoginLog.Type = field.NewString(tableName, "type")
	_userLoginLog.Version = field.NewString(tableName, "version")
	_userLoginLog.Address = field.NewField(tableName, "address")
	_userLoginLog.LoginTime = field.NewTime(tableName, "login_time")
	_userLoginLog.LoginIP = field.NewString(tableName, "login_ip")
	_userLoginLog.CreatedAt = field.NewTime(tableName, "created_at")
	_userLoginLog.UpdatedAt = field.NewTime(tableName, "updated_at")

	_userLoginLog.fillFieldMap()

	return _userLoginLog
}

// userLoginLog 用户登录日志表
type userLoginLog struct {
	userLoginLogDo

	ALL       field.Asterisk
	ID        field.String // Id
	UserID    field.String // 用户ID
	Type      field.String // 登录类型
	Version   field.String // 版本号
	Address   field.Field  // 地址信息(JSON格式)
	LoginTime field.Time   // 登录时间
	LoginIP   field.String // 登录IP
	CreatedAt field.Time   // 创建时间
	UpdatedAt field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (u userLoginLog) Table(newTableName string) *userLoginLog {
	u.userLoginLogDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userLoginLog) As(alias string) *userLoginLog {
	u.userLoginLogDo.DO = *(u.userLoginLogDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userLoginLog) updateTableName(table string) *userLoginLog {
	u.ALL = field.NewAsterisk(table)
	u.ID = field.NewString(table, "id")
	u.UserID = field.NewString(table, "user_id")
	u.Type = field.NewString(table, "type")
	u.Version = field.NewString(table, "version")
	u.Address = field.NewField(table, "address")
	u.LoginTime = field.NewTime(table, "login_time")
	u.LoginIP = field.NewString(table, "login_ip")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedAt = field.NewTime(table, "updated_at")

	u.fillFieldMap()

	return u
}

func (u *userLoginLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userLoginLog) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 9)
	u.fieldMap["id"] = u.ID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["type"] = u.Type
	u.fieldMap["version"] = u.Version
	u.fieldMap["address"] = u.Address
	u.fieldMap["login_time"] = u.LoginTime
	u.fieldMap["login_ip"] = u.LoginIP
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_at"] = u.UpdatedAt
}

func (u userLoginLog) clone(db *gorm.DB) userLoginLog {
	u.userLoginLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userLoginLog) replaceDB(db *gorm.DB) userLoginLog {
	u.userLoginLogDo.ReplaceDB(db)
	return u
}

type userLoginLogDo struct{ gen.DO }

type IUserLoginLogDo interface {
	gen.SubQuery
	Debug() IUserLoginLogDo
	WithContext(ctx context.Context) IUserLoginLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserLoginLogDo
	WriteDB() IUserLoginLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserLoginLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserLoginLogDo
	Not(conds ...gen.Condition) IUserLoginLogDo
	Or(conds ...gen.Condition) IUserLoginLogDo
	Select(conds ...field.Expr) IUserLoginLogDo
	Where(conds ...gen.Condition) IUserLoginLogDo
	Order(conds ...field.Expr) IUserLoginLogDo
	Distinct(cols ...field.Expr) IUserLoginLogDo
	Omit(cols ...field.Expr) IUserLoginLogDo
	Join(table schema.Tabler, on ...field.Expr) IUserLoginLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserLoginLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserLoginLogDo
	Group(cols ...field.Expr) IUserLoginLogDo
	Having(conds ...gen.Condition) IUserLoginLogDo
	Limit(limit int) IUserLoginLogDo
	Offset(offset int) IUserLoginLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserLoginLogDo
	Unscoped() IUserLoginLogDo
	Create(values ...*model.UserLoginLog) error
	CreateInBatches(values []*model.UserLoginLog, batchSize int) error
	Save(values ...*model.UserLoginLog) error
	First() (*model.UserLoginLog, error)
	Take() (*model.UserLoginLog, error)
	Last() (*model.UserLoginLog, error)
	Find() ([]*model.UserLoginLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserLoginLog, err error)
	FindInBatches(result *[]*model.UserLoginLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserLoginLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserLoginLogDo
	Assign(attrs ...field.AssignExpr) IUserLoginLogDo
	Joins(fields ...field.RelationField) IUserLoginLogDo
	Preload(fields ...field.RelationField) IUserLoginLogDo
	FirstOrInit() (*model.UserLoginLog, error)
	FirstOrCreate() (*model.UserLoginLog, error)
	FindByPage(offset int, limit int) (result []*model.UserLoginLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserLoginLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userLoginLogDo) Debug() IUserLoginLogDo {
	return u.withDO(u.DO.Debug())
}

func (u userLoginLogDo) WithContext(ctx context.Context) IUserLoginLogDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userLoginLogDo) ReadDB() IUserLoginLogDo {
	return u.Clauses(dbresolver.Read)
}

func (u userLoginLogDo) WriteDB() IUserLoginLogDo {
	return u.Clauses(dbresolver.Write)
}

func (u userLoginLogDo) Session(config *gorm.Session) IUserLoginLogDo {
	return u.withDO(u.DO.Session(config))
}

func (u userLoginLogDo) Clauses(conds ...clause.Expression) IUserLoginLogDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userLoginLogDo) Returning(value interface{}, columns ...string) IUserLoginLogDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userLoginLogDo) Not(conds ...gen.Condition) IUserLoginLogDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userLoginLogDo) Or(conds ...gen.Condition) IUserLoginLogDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userLoginLogDo) Select(conds ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userLoginLogDo) Where(conds ...gen.Condition) IUserLoginLogDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userLoginLogDo) Order(conds ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userLoginLogDo) Distinct(cols ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userLoginLogDo) Omit(cols ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userLoginLogDo) Join(table schema.Tabler, on ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userLoginLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userLoginLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userLoginLogDo) Group(cols ...field.Expr) IUserLoginLogDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userLoginLogDo) Having(conds ...gen.Condition) IUserLoginLogDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userLoginLogDo) Limit(limit int) IUserLoginLogDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userLoginLogDo) Offset(offset int) IUserLoginLogDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userLoginLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserLoginLogDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userLoginLogDo) Unscoped() IUserLoginLogDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userLoginLogDo) Create(values ...*model.UserLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userLoginLogDo) CreateInBatches(values []*model.UserLoginLog, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userLoginLogDo) Save(values ...*model.UserLoginLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userLoginLogDo) First() (*model.UserLoginLog, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserLoginLog), nil
	}
}

func (u userLoginLogDo) Take() (*model.UserLoginLog, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserLoginLog), nil
	}
}

func (u userLoginLogDo) Last() (*model.UserLoginLog, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserLoginLog), nil
	}
}

func (u userLoginLogDo) Find() ([]*model.UserLoginLog, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserLoginLog), err
}

func (u userLoginLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserLoginLog, err error) {
	buf := make([]*model.UserLoginLog, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userLoginLogDo) FindInBatches(result *[]*model.UserLoginLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userLoginLogDo) Attrs(attrs ...field.AssignExpr) IUserLoginLogDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userLoginLogDo) Assign(attrs ...field.AssignExpr) IUserLoginLogDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userLoginLogDo) Joins(fields ...field.RelationField) IUserLoginLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userLoginLogDo) Preload(fields ...field.RelationField) IUserLoginLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userLoginLogDo) FirstOrInit() (*model.UserLoginLog, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserLoginLog), nil
	}
}

func (u userLoginLogDo) FirstOrCreate() (*model.UserLoginLog, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserLoginLog), nil
	}
}

func (u userLoginLogDo) FindByPage(offset int, limit int) (result []*model.UserLoginLog, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userLoginLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userLoginLogDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userLoginLogDo) Delete(models ...*model.UserLoginLog) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userLoginLogDo) withDO(do gen.Dao) *userLoginLogDo {
	u.DO = *do.(*gen.DO)
	return u
}
