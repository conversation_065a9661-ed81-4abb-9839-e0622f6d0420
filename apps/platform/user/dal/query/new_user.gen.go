// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/platform/user/dal/model"
)

func newNewUser(db *gorm.DB, opts ...gen.DOOption) newUser {
	_newUser := newUser{}

	_newUser.newUserDo.UseDB(db, opts...)
	_newUser.newUserDo.UseModel(&model.NewUser{})

	tableName := _newUser.newUserDo.TableName()
	_newUser.ALL = field.NewAsterisk(tableName)
	_newUser.ID = field.NewInt64(tableName, "id")
	_newUser.UserID = field.NewString(tableName, "user_id")
	_newUser.MobilePhone = field.NewString(tableName, "mobile_phone")
	_newUser.Nickname = field.NewString(tableName, "nickname")
	_newUser.IDCard = field.NewString(tableName, "id_card")
	_newUser.Avatar = field.NewString(tableName, "avatar")
	_newUser.RealName = field.NewString(tableName, "real_name")
	_newUser.InviteCode = field.NewString(tableName, "invite_code")
	_newUser.CreatedAt = field.NewTime(tableName, "created_at")
	_newUser.UpdatedAt = field.NewTime(tableName, "updated_at")
	_newUser.IsDel = field.NewField(tableName, "is_del")

	_newUser.fillFieldMap()

	return _newUser
}

// newUser 用户表（新）
type newUser struct {
	newUserDo

	ALL         field.Asterisk
	ID          field.Int64  // Id
	UserID      field.String // 用户id
	MobilePhone field.String // 手机号号码
	Nickname    field.String // 昵称
	IDCard      field.String // 身份证号码
	Avatar      field.String // 用户头像
	RealName    field.String // 真实姓名
	InviteCode  field.String // 邀请码
	CreatedAt   field.Time   // 创建时间
	UpdatedAt   field.Time   // 更新时间
	IsDel       field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (n newUser) Table(newTableName string) *newUser {
	n.newUserDo.UseTable(newTableName)
	return n.updateTableName(newTableName)
}

func (n newUser) As(alias string) *newUser {
	n.newUserDo.DO = *(n.newUserDo.As(alias).(*gen.DO))
	return n.updateTableName(alias)
}

func (n *newUser) updateTableName(table string) *newUser {
	n.ALL = field.NewAsterisk(table)
	n.ID = field.NewInt64(table, "id")
	n.UserID = field.NewString(table, "user_id")
	n.MobilePhone = field.NewString(table, "mobile_phone")
	n.Nickname = field.NewString(table, "nickname")
	n.IDCard = field.NewString(table, "id_card")
	n.Avatar = field.NewString(table, "avatar")
	n.RealName = field.NewString(table, "real_name")
	n.InviteCode = field.NewString(table, "invite_code")
	n.CreatedAt = field.NewTime(table, "created_at")
	n.UpdatedAt = field.NewTime(table, "updated_at")
	n.IsDel = field.NewField(table, "is_del")

	n.fillFieldMap()

	return n
}

func (n *newUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := n.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (n *newUser) fillFieldMap() {
	n.fieldMap = make(map[string]field.Expr, 11)
	n.fieldMap["id"] = n.ID
	n.fieldMap["user_id"] = n.UserID
	n.fieldMap["mobile_phone"] = n.MobilePhone
	n.fieldMap["nickname"] = n.Nickname
	n.fieldMap["id_card"] = n.IDCard
	n.fieldMap["avatar"] = n.Avatar
	n.fieldMap["real_name"] = n.RealName
	n.fieldMap["invite_code"] = n.InviteCode
	n.fieldMap["created_at"] = n.CreatedAt
	n.fieldMap["updated_at"] = n.UpdatedAt
	n.fieldMap["is_del"] = n.IsDel
}

func (n newUser) clone(db *gorm.DB) newUser {
	n.newUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return n
}

func (n newUser) replaceDB(db *gorm.DB) newUser {
	n.newUserDo.ReplaceDB(db)
	return n
}

type newUserDo struct{ gen.DO }

type INewUserDo interface {
	gen.SubQuery
	Debug() INewUserDo
	WithContext(ctx context.Context) INewUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() INewUserDo
	WriteDB() INewUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) INewUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) INewUserDo
	Not(conds ...gen.Condition) INewUserDo
	Or(conds ...gen.Condition) INewUserDo
	Select(conds ...field.Expr) INewUserDo
	Where(conds ...gen.Condition) INewUserDo
	Order(conds ...field.Expr) INewUserDo
	Distinct(cols ...field.Expr) INewUserDo
	Omit(cols ...field.Expr) INewUserDo
	Join(table schema.Tabler, on ...field.Expr) INewUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) INewUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) INewUserDo
	Group(cols ...field.Expr) INewUserDo
	Having(conds ...gen.Condition) INewUserDo
	Limit(limit int) INewUserDo
	Offset(offset int) INewUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) INewUserDo
	Unscoped() INewUserDo
	Create(values ...*model.NewUser) error
	CreateInBatches(values []*model.NewUser, batchSize int) error
	Save(values ...*model.NewUser) error
	First() (*model.NewUser, error)
	Take() (*model.NewUser, error)
	Last() (*model.NewUser, error)
	Find() ([]*model.NewUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NewUser, err error)
	FindInBatches(result *[]*model.NewUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.NewUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) INewUserDo
	Assign(attrs ...field.AssignExpr) INewUserDo
	Joins(fields ...field.RelationField) INewUserDo
	Preload(fields ...field.RelationField) INewUserDo
	FirstOrInit() (*model.NewUser, error)
	FirstOrCreate() (*model.NewUser, error)
	FindByPage(offset int, limit int) (result []*model.NewUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) INewUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (n newUserDo) Debug() INewUserDo {
	return n.withDO(n.DO.Debug())
}

func (n newUserDo) WithContext(ctx context.Context) INewUserDo {
	return n.withDO(n.DO.WithContext(ctx))
}

func (n newUserDo) ReadDB() INewUserDo {
	return n.Clauses(dbresolver.Read)
}

func (n newUserDo) WriteDB() INewUserDo {
	return n.Clauses(dbresolver.Write)
}

func (n newUserDo) Session(config *gorm.Session) INewUserDo {
	return n.withDO(n.DO.Session(config))
}

func (n newUserDo) Clauses(conds ...clause.Expression) INewUserDo {
	return n.withDO(n.DO.Clauses(conds...))
}

func (n newUserDo) Returning(value interface{}, columns ...string) INewUserDo {
	return n.withDO(n.DO.Returning(value, columns...))
}

func (n newUserDo) Not(conds ...gen.Condition) INewUserDo {
	return n.withDO(n.DO.Not(conds...))
}

func (n newUserDo) Or(conds ...gen.Condition) INewUserDo {
	return n.withDO(n.DO.Or(conds...))
}

func (n newUserDo) Select(conds ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Select(conds...))
}

func (n newUserDo) Where(conds ...gen.Condition) INewUserDo {
	return n.withDO(n.DO.Where(conds...))
}

func (n newUserDo) Order(conds ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Order(conds...))
}

func (n newUserDo) Distinct(cols ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Distinct(cols...))
}

func (n newUserDo) Omit(cols ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Omit(cols...))
}

func (n newUserDo) Join(table schema.Tabler, on ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Join(table, on...))
}

func (n newUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) INewUserDo {
	return n.withDO(n.DO.LeftJoin(table, on...))
}

func (n newUserDo) RightJoin(table schema.Tabler, on ...field.Expr) INewUserDo {
	return n.withDO(n.DO.RightJoin(table, on...))
}

func (n newUserDo) Group(cols ...field.Expr) INewUserDo {
	return n.withDO(n.DO.Group(cols...))
}

func (n newUserDo) Having(conds ...gen.Condition) INewUserDo {
	return n.withDO(n.DO.Having(conds...))
}

func (n newUserDo) Limit(limit int) INewUserDo {
	return n.withDO(n.DO.Limit(limit))
}

func (n newUserDo) Offset(offset int) INewUserDo {
	return n.withDO(n.DO.Offset(offset))
}

func (n newUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) INewUserDo {
	return n.withDO(n.DO.Scopes(funcs...))
}

func (n newUserDo) Unscoped() INewUserDo {
	return n.withDO(n.DO.Unscoped())
}

func (n newUserDo) Create(values ...*model.NewUser) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Create(values)
}

func (n newUserDo) CreateInBatches(values []*model.NewUser, batchSize int) error {
	return n.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (n newUserDo) Save(values ...*model.NewUser) error {
	if len(values) == 0 {
		return nil
	}
	return n.DO.Save(values)
}

func (n newUserDo) First() (*model.NewUser, error) {
	if result, err := n.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.NewUser), nil
	}
}

func (n newUserDo) Take() (*model.NewUser, error) {
	if result, err := n.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.NewUser), nil
	}
}

func (n newUserDo) Last() (*model.NewUser, error) {
	if result, err := n.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.NewUser), nil
	}
}

func (n newUserDo) Find() ([]*model.NewUser, error) {
	result, err := n.DO.Find()
	return result.([]*model.NewUser), err
}

func (n newUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.NewUser, err error) {
	buf := make([]*model.NewUser, 0, batchSize)
	err = n.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (n newUserDo) FindInBatches(result *[]*model.NewUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return n.DO.FindInBatches(result, batchSize, fc)
}

func (n newUserDo) Attrs(attrs ...field.AssignExpr) INewUserDo {
	return n.withDO(n.DO.Attrs(attrs...))
}

func (n newUserDo) Assign(attrs ...field.AssignExpr) INewUserDo {
	return n.withDO(n.DO.Assign(attrs...))
}

func (n newUserDo) Joins(fields ...field.RelationField) INewUserDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Joins(_f))
	}
	return &n
}

func (n newUserDo) Preload(fields ...field.RelationField) INewUserDo {
	for _, _f := range fields {
		n = *n.withDO(n.DO.Preload(_f))
	}
	return &n
}

func (n newUserDo) FirstOrInit() (*model.NewUser, error) {
	if result, err := n.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.NewUser), nil
	}
}

func (n newUserDo) FirstOrCreate() (*model.NewUser, error) {
	if result, err := n.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.NewUser), nil
	}
}

func (n newUserDo) FindByPage(offset int, limit int) (result []*model.NewUser, count int64, err error) {
	result, err = n.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = n.Offset(-1).Limit(-1).Count()
	return
}

func (n newUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = n.Count()
	if err != nil {
		return
	}

	err = n.Offset(offset).Limit(limit).Scan(result)
	return
}

func (n newUserDo) Scan(result interface{}) (err error) {
	return n.DO.Scan(result)
}

func (n newUserDo) Delete(models ...*model.NewUser) (result gen.ResultInfo, err error) {
	return n.DO.Delete(models)
}

func (n *newUserDo) withDO(do gen.Dao) *newUserDo {
	n.DO = *do.(*gen.DO)
	return n
}
