// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q               = new(Query)
	NewUser         *newUser
	UserBind        *userBind
	UserLoginLog    *userLoginLog
	UserPurchaseLog *userPurchaseLog
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	NewUser = &Q.NewUser
	UserBind = &Q.UserBind
	UserLoginLog = &Q.UserLoginLog
	UserPurchaseLog = &Q.UserPurchaseLog
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:              db,
		NewUser:         newNewUser(db, opts...),
		UserBind:        newUserBind(db, opts...),
		UserLoginLog:    newUserLoginLog(db, opts...),
		UserPurchaseLog: newUserPurchaseLog(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	NewUser         newUser
	UserBind        userBind
	UserLoginLog    userLoginLog
	UserPurchaseLog userPurchaseLog
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		NewUser:         q.NewUser.clone(db),
		UserBind:        q.UserBind.clone(db),
		UserLoginLog:    q.UserLoginLog.clone(db),
		UserPurchaseLog: q.UserPurchaseLog.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:              db,
		NewUser:         q.NewUser.replaceDB(db),
		UserBind:        q.UserBind.replaceDB(db),
		UserLoginLog:    q.UserLoginLog.replaceDB(db),
		UserPurchaseLog: q.UserPurchaseLog.replaceDB(db),
	}
}

type queryCtx struct {
	NewUser         INewUserDo
	UserBind        IUserBindDo
	UserLoginLog    IUserLoginLogDo
	UserPurchaseLog IUserPurchaseLogDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		NewUser:         q.NewUser.WithContext(ctx),
		UserBind:        q.UserBind.WithContext(ctx),
		UserLoginLog:    q.UserLoginLog.WithContext(ctx),
		UserPurchaseLog: q.UserPurchaseLog.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
