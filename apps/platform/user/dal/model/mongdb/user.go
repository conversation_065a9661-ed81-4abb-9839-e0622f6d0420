package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

// User 主用户模型
type User struct {
	ID          bson.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`
	Status      int           `bson:"status" json:"status"`
	Type        int           `bson:"type" json:"type"`
	Steam       UserSteam     `bson:"steam" json:"steam"`
	PatbgDetail PatbgDetail   `bson:"patbg_detail" json:"patbg_detail"`
	OpAuth      OpAuth        `bson:"op_auth" json:"op_auth"`
	IsPrivacy   bool          `bson:"is_privacy" json:"is_privacy"`
	Options     UserOptions   `bson:"options" json:"options"`
	Extends     UserExtends   `bson:"extends" json:"extends"`
	OpenInfo    OpenInfo      `bson:"open_info" json:"open_info"`
	Sessions    []Session     `bson:"sessions" json:"sessions"`
	LoginIP     string        `bson:"login_ip" json:"login_ip"`
	LoginTime   time.Time     `bson:"login_time" json:"login_time"`
	LoginTotal  int           `bson:"login_total" json:"login_total"`
	LoginToken  string        `bson:"login_token" json:"login_token"`
	GameConfig  GameConfig    `bson:"game_config" json:"game_config"`
	CreatedAt   time.Time     `bson:"created_at" json:"created_at"`
	UpdatedAt   time.Time     `bson:"updated_at" json:"updated_at"`
}

// Session 会话信息
type Session struct {
	Type       string    `bson:"type" json:"type"`
	LoginIP    string    `bson:"login_ip" json:"login_ip"`
	LoginTime  time.Time `bson:"login_time" json:"login_time"`
	LoginTotal int       `bson:"login_total" json:"login_total"`
	LoginToken string    `bson:"login_token" json:"login_token"`
	UserAgent  string    `bson:"user_agent" json:"user_agent"`
}

// PatbgDetail patbg用户详细信息
type PatbgDetail struct {
	MobilePhone string `bson:"mobile_phone" json:"mobile_phone"`
	Email       string `bson:"email" json:"email"`
	Nickname    string `bson:"nickname" json:"nickname"`
	Avatar      string `bson:"avatar" json:"avatar"`
	VipLevel    int    `bson:"vip_level" json:"vip_level"`
	IsMerchant  bool   `bson:"is_merchant" json:"is_merchant"`
	IsCertified bool   `bson:"is_certified" json:"is_certified"`
}

// OpAuth 操作授权
type OpAuth struct {
	DenyItemWithdraw  bool `bson:"deny_item_withdraw" json:"deny_item_withdraw"`
	DenyUnbox         bool `bson:"deny_unbox" json:"deny_unbox"`
	DenyUndoubox      bool `bson:"deny_undoubox" json:"deny_undoubox"`
	DenyLuckyitem     bool `bson:"deny_luckyitem" json:"deny_luckyitem"`
	DenyUnpackGame    bool `bson:"deny_unpack_game" json:"deny_unpack_game"`
	DenyPlayChallenge bool `bson:"deny_play_challenge" json:"deny_play_challenge"`
	DenySellItem      bool `bson:"deny_sell_item" json:"deny_sell_item"`
	DenyCheerBox      bool `bson:"deny_cheer_box" json:"deny_cheer_box"`
	DenyMallItemBuy   bool `bson:"deny_mall_item_buy" json:"deny_mall_item_buy"`
}

// UserOptions 用户选项
type UserOptions struct {
	SalesLevel                       int           `bson:"sales_level" json:"sales_level"`
	AutoSalesLevel                   bool          `bson:"auto_sales_level" json:"auto_sales_level"`
	EveryDataAllowItemWithdrawAmount float64       `bson:"every_data_allow_item_withdraw_amount" json:"every_data_allow_item_withdraw_amount"`
	UpChainStatus                    int           `bson:"up_chain_status" json:"up_chain_status"`
	ChainAddress                     string        `bson:"chain_address" json:"chain_address"`
	UpChainTime                      time.Time     `bson:"up_chain_time" json:"up_chain_time"`
	OpenUserID                       bson.ObjectID `bson:"open_user_id" json:"open_user_id"`
}

// Item 商品信息
type Item struct {
	ItemID   bson.ObjectID `bson:"item_id" json:"item_id"`
	ItemName string        `bson:"item_name" json:"item_name"`
}

// RecoveryConfig 机器人回收配置
type RecoveryConfig struct {
	ID                   string    `bson:"_id" json:"id"`
	Percent              float64   `bson:"percent" json:"percent"`
	RecoveryWay          int       `bson:"recovery_way" json:"recovery_way"`
	RecoverySecondMin    int       `bson:"recovery_second_min" json:"recovery_second_min"`
	RecoverySecondMax    int       `bson:"recovery_second_max" json:"recovery_second_max"`
	RecoveryPaySecondMin int       `bson:"recovery_pay_second_min" json:"recovery_pay_second_min"`
	RecoveryPaySecondMax int       `bson:"recovery_pay_second_max" json:"recovery_pay_second_max"`
	ItemsWay             int       `bson:"items_way" json:"items_way"`
	ItemList             []Item    `bson:"item_list" json:"item_list"`
	RecoveryTimeWay      int       `bson:"recovery_time_way" json:"recovery_time_way"`
	RecoveryTimeStart    time.Time `bson:"recovery_time_start" json:"recovery_time_start"`
	RecoveryTimeEnd      time.Time `bson:"recovery_time_end" json:"recovery_time_end"`
	PriceMin             float64   `bson:"price_min" json:"price_min"`
	PriceMax             float64   `bson:"price_max" json:"price_max"`
	Enable               bool      `bson:"enable" json:"enable"`
	RecoveryAmount       float64   `bson:"recovery_amount" json:"recovery_amount"`
}

// UserExtends 用户扩展数据
type UserExtends struct {
	Watbg struct {
		WalletID         bson.ObjectID `bson:"wallet_id" json:"wallet_id"`
		LastRechargeTime time.Time     `bson:"last_recharge_time" json:"last_recharge_time"`
	} `bson:"watbg" json:"watbg"`
	WeekConsumeAmount             float64          `bson:"week_consume_amount" json:"week_consume_amount"`
	WeekConsumeAmountTotal        float64          `bson:"week_consume_amount_total" json:"week_consume_amount_total"`
	BoxIntegral                   float64          `bson:"box_integral" json:"box_integral"`
	BoxTotalIntegral              float64          `bson:"box_total_integral" json:"box_total_integral"`
	FansCount                     int              `bson:"fans_count" json:"fans_count"`
	FollowCount                   int              `bson:"follow_count" json:"follow_count"`
	CollectionCount               int              `bson:"collection_count" json:"collection_count"`
	SyncUserItemCount             int              `bson:"sync_user_item_count" json:"sync_user_item_count"`
	OrderRiskControlCount         int              `bson:"order_risk_control_count" json:"order_risk_control_count"`
	OrderRiskControlCountNFT      int              `bson:"order_risk_control_count_nft" json:"order_risk_control_count_nft"`
	OrderRiskControlCountBargain  int              `bson:"order_risk_control_count_bargain" json:"order_risk_control_count_bargain"`
	OrderRiskControlCountWantBay  int              `bson:"order_risk_control_count_want_bay" json:"order_risk_control_count_want_bay"`
	OrderRiskControlCountBatchBuy int              `bson:"order_risk_control_count_batch_buy" json:"order_risk_control_count_batch_buy"`
	IsWantBuyRobot                bool             `bson:"is_want_buy_robot" json:"is_want_buy_robot"`
	RecoveryConfig                []RecoveryConfig `bson:"recovery_config" json:"recovery_config"`
}

// UserSteam 用户Steam信息
type UserSteam struct {
	SteamID   string `bson:"steam_id" json:"steam_id"`
	TransLink string `bson:"trans_link" json:"trans_link"`
	SteamDate string `bson:"steam_date" json:"steam_date"`
}

// GameConfig 游戏配置
type GameConfig struct {
	BoxFirstPrizeEnable bool `bson:"box_first_prize_enable" json:"box_first_prize_enable"`
	BoxFirstPrizeCount  int  `bson:"box_first_prize_count" json:"box_first_prize_count"`
}

// OpenInfo 开放平台信息
type OpenInfo struct {
	LoginCode    string        `bson:"login_code" json:"login_code"`
	AccessToken  string        `bson:"access_token" json:"access_token"`
	OpenUserID   bson.ObjectID `bson:"open_user_id" json:"open_user_id"`
	Verified     bool          `bson:"verified" json:"verified"`
	IsCertified  bool          `bson:"is_certified" json:"is_certified"`
	ChainAddress string        `bson:"chain_address" json:"chain_address"`
}
