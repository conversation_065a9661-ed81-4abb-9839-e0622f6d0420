// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameUserPurchaseLog = "user_purchase_log"

// UserPurchaseLog 用户消费记录表
type UserPurchaseLog struct {
	UserPurchaseID int64                 `gorm:"column:user_purchase_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:Id" json:"user_purchase_id"` // Id
	UserID         string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户id" json:"user_id"`                                          // 用户id
	Type           int32                 `gorm:"column:type;type:int;not null;comment:类型 1-一手消费 2-二手消费" json:"type"`                                            // 类型 1-一手消费 2-二手消费
	RelateOrderID  string                `gorm:"column:relate_order_id;type:varchar(32);not null;comment:关联订单Id" json:"relate_order_id"`                        // 关联订单Id
	OrderAmount    int32                 `gorm:"column:order_amount;type:int;not null;comment:订单金额" json:"order_amount"`                                        // 订单金额
	PurchaseTime   time.Time             `gorm:"column:purchase_time;type:datetime;not null;comment:消费时间" json:"purchase_time"`                                 // 消费时间
	Extra          datatypes.JSON        `gorm:"column:extra;type:json;not null" json:"extra"`
	CreatedAt      time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt      time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel          soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName UserPurchaseLog's table name
func (*UserPurchaseLog) TableName() string {
	return TableNameUserPurchaseLog
}
