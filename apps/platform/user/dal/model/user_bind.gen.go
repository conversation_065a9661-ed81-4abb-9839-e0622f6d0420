// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameUserBind = "user_bind"

// UserBind 用户Bind
type UserBind struct {
	UserBindID   int64                 `gorm:"column:user_bind_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:Id" json:"user_bind_id"`   // Id
	UserID       string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户id" json:"user_id"`                                    // 用户id
	TargetUserID string                `gorm:"column:target_user_id;type:varchar(32);not null;comment:被邀请用户id" json:"target_user_id"`                   // 被邀请用户id
	BindTime     *time.Time            `gorm:"column:bind_time;type:datetime;comment:绑定时间" json:"bind_time"`                                            // 绑定时间
	Type         int32                 `gorm:"column:type;type:int;not null;comment:类型 1-邀请绑定" json:"type"`                                             // 类型 1-邀请绑定
	CreatedBy    string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt    time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy    string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt    time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel        soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName UserBind's table name
func (*UserBind) TableName() string {
	return TableNameUserBind
}
