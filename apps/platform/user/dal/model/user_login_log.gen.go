// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameUserLoginLog = "user_login_log"

// UserLoginLog 用户登录日志表
type UserLoginLog struct {
	ID        string          `gorm:"column:id;type:varchar(32);primaryKey;comment:Id" json:"id"`                 // Id
	UserID    string          `gorm:"column:user_id;type:varchar(24);not null;comment:用户ID" json:"user_id"`       // 用户ID
	Type      string          `gorm:"column:type;type:varchar(20);not null;comment:登录类型" json:"type"`             // 登录类型
	Version   string          `gorm:"column:version;type:varchar(10);not null;comment:版本号" json:"version"`        // 版本号
	Address   *datatypes.JSON `gorm:"column:address;type:json;comment:地址信息(JSON格式)" json:"address"`               // 地址信息(JSON格式)
	LoginTime time.Time       `gorm:"column:login_time;type:datetime(3);not null;comment:登录时间" json:"login_time"` // 登录时间
	LoginIP   string          `gorm:"column:login_ip;type:varchar(15);not null;comment:登录IP" json:"login_ip"`     // 登录IP
	CreatedAt time.Time       `gorm:"column:created_at;type:datetime(3);not null;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt time.Time       `gorm:"column:updated_at;type:datetime(3);not null;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName UserLoginLog's table name
func (*UserLoginLog) TableName() string {
	return TableNameUserLoginLog
}
