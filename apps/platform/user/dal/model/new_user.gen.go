// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameNewUser = "new_user"

// NewUser 用户表（新）
type NewUser struct {
	ID          int64                 `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:Id" json:"id"`                       // Id
	UserID      string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户id" json:"user_id"`                                    // 用户id
	MobilePhone string                `gorm:"column:mobile_phone;type:varchar(16);comment:手机号号码" json:"mobile_phone"`                                  // 手机号号码
	Nickname    string                `gorm:"column:nickname;type:varchar(64);comment:昵称" json:"nickname"`                                             // 昵称
	IDCard      string                `gorm:"column:id_card;type:varchar(64);comment:身份证号码" json:"id_card"`                                            // 身份证号码
	Avatar      string                `gorm:"column:avatar;type:varchar(255);comment:用户头像" json:"avatar"`                                              // 用户头像
	RealName    string                `gorm:"column:real_name;type:varchar(16);comment:真实姓名" json:"real_name"`                                         // 真实姓名
	InviteCode  string                `gorm:"column:invite_code;type:varchar(16);not null;comment:邀请码" json:"invite_code"`                             // 邀请码
	CreatedAt   time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt   time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel       soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName NewUser's table name
func (*NewUser) TableName() string {
	return TableNameNewUser
}
