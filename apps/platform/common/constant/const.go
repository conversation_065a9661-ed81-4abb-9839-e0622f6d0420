package constant

import (
	"context"
	"fmt"
)

const (
	EnvDev  = "dev"
	EnvProd = "prod"
)

const (
	Yes = int32(1)
	No  = int32(2)
)

const (
	Asc  = int32(1)
	Desc = int32(2)
)

const (
	Normal  = int32(0)
	Deleted = int32(1)
)

func Bool2Int(b bool) int32 {
	if b {
		return Yes
	} else {
		return No
	}
}

// 请求头参数
const (
	AppChannel    = "app_channel"
	AppVersion    = "app_version"
	ClientType    = "client_type"
	Ip            = "ip"
	Authorization = "Authorization"
	DeviceId      = "device_id"
)

// 融合-消息Topic
const (
	SynthesisFusion       = "synthesis_fusion"
	SynthesisCancelFusion = "synthesis_cancel_fusion"
	PriorityBuyIssue      = "priority_buy_issue"
	SynthesisItemIssue    = "synthesis_item_issue"
)

// 邀友-消息Topic
const (
	UserBind     = "user_bind"
	UserLogin    = "user_login"
	UserPurchase = "user_purchase"
	UserRealAuth = "user_real_auth"
	UserSyncs    = "user_syncs"
)

// 故事玩法-消息Topic
const (
	StoryFusion       = "story_fusion"
	StoryCancelFusion = "story_cancel_fusion"
	StoryItemIssue    = "story_item_issue"
)

// 发货管理-消息Topic
const (
	// ShipmentForce 强制发货任务主题
	ShipmentForce = "shipment_force"
)

// 商品相关-消息 Topic
const (
	// CirculationControl 流通管制
	CirculationControl = "circulation_control"
	// IssueItemShelves 首发上/下架
	IssueItemShelves = "issue_item_shelves"
)

const CommonGroup = "app_service"

// GetAppChannel 获取应用渠道
func GetAppChannel(ctx context.Context) string {
	return fmt.Sprintf("%v", ctx.Value(AppChannel))
}

// GetAppVersion 获取应用版本号
func GetAppVersion(ctx context.Context) string {
	return fmt.Sprintf("%v", ctx.Value(AppVersion))
}

// GetClientType 获取客户端类型
func GetClientType(ctx context.Context) string {
	return fmt.Sprintf("%v", ctx.Value(ClientType))
}

// GetClientIP 获取客户端IP地址
func GetClientIP(ctx context.Context) string {
	return fmt.Sprintf("%v", ctx.Value(Ip))
}
