package constant

import (
	"fmt"
	"time"
)

const (
	CommonConfigKey = "app_service:config:%v"
	// MarketChangesChannelCategoryListKey 根据渠道、栏目行情异动列表
	MarketChangesChannelCategoryListKey = "app_service:market_changes:%v:%v:list"
	// MarketChangesChannelListKey 根据渠道行情异动列表
	MarketChangesChannelListKey = "app_service:market_changes:%v:list"

	IssueItemKey              = "app_service:issue_item_info:%v"
	IssueItemTTL              = time.Second * 30
	StorySceneKey             = "app_service:story_scene:%v"
	StorySceneUserNameListKey = "app_service:story_scene_user_name:%v"

	// 帖子相关缓存键
	PostDetailKey = "app_service:card_community:post:detail:%v"
	PostListKey   = "app_service:card_community:post:list:page:%v:size:%v"

	// 消息相关缓存键
	MessageIdempotentKey = "app_service:card_community:message:idempotent:%v" // 消息幂等性缓存
)

// 缓存过期时间
const (
	PostDetailTTL        = 10 * time.Minute // 帖子详情缓存（基础时间，实际会加随机时间防雪崩）
	PostListFirstTTL     = 10 * time.Minute // 帖子列表第一页缓存，与详情缓存时间保持一致避免数据不一致
	MessageIdempotentTTL = 5 * time.Minute  // 消息幂等性缓存，5分钟足够处理重复请求
)

func GetConfigKey(key string) string {
	return fmt.Sprintf(CommonConfigKey, key)
}

func GetMarketChangesChannelCategoryListKeyKey(channelId string, categoryId int64) string {
	return fmt.Sprintf(MarketChangesChannelCategoryListKey, channelId, categoryId)
}

func GetMarketChangesChannelListKey(channelId string) string {
	return fmt.Sprintf(MarketChangesChannelListKey, channelId)
}

func GetIssueItemKey(id string) string {
	return fmt.Sprintf(IssueItemKey, id)
}

func GetStorySceneKey(key int64) string {
	return fmt.Sprintf(StorySceneKey, key)
}

func GetStorySceneUserNameListKey(key int64) string {
	return fmt.Sprintf(StorySceneUserNameListKey, key)
}

// GetPostDetailKey 获取帖子详情缓存键
func GetPostDetailKey(postID string) string {
	return fmt.Sprintf(PostDetailKey, postID)
}

// GetPostListKey 获取帖子列表缓存键
func GetPostListKey(page, size int) string {
	return fmt.Sprintf(PostListKey, page, size)
}

// GetMessageIdempotentKey 获取消息幂等性缓存键
func GetMessageIdempotentKey(clientMsgID string) string {
	return fmt.Sprintf(MessageIdempotentKey, clientMsgID)
}
