package service

import (
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/define/enum"
	"app_service/apps/platform/common/repo"
	"app_service/pkg/search"
)

func (s *Service) GetOperationLogList(req *define.GetOperationLogListReq) (*define.GetOperationLogListResp, error) {
	opLogSchema := repo.GetQuery().OperationLog
	builder := search.NewQueryBuilder().OrderByDesc(opLogSchema.OperatedAt)
	builder = builder.Eq(opLogSchema.RelateID, req.RelateID)
	builder = builder.Eq(opLogSchema.RelateType, req.RelateType)

	list, err := repo.NewOperationLogRepo(opLogSchema.WithContext(s.ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}
	logList := make([]*define.OperationLogItem, len(list))
	for i, item := range list {
		logList[i] = &define.OperationLogItem{
			OperationLogID: item.OperationLogID,
			Action:         enum.OperationLogActionEnum(item.Action),
			AfterContent:   item.AfterContent,
			BeforeContent:  item.BeforeContent,
			OperatedBy:     item.OperatedBy,
			OperatedAt:     item.OperatedAt,
		}
	}
	resp := &define.GetOperationLogListResp{
		List: logList,
	}

	return resp, nil
}
