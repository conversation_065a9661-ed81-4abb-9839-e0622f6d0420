package logic

import (
	"app_service/apps/platform/common/dal/model"
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/repo"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

// AddOperationLog 添加操作日志
func AddOperationLog(ctx context.Context, params define.AddOperationLogParams) error {
	opLogSchema := repo.GetQuery().OperationLog
	opLog := &model.OperationLog{
		RelateID:      params.RelateID,
		RelateType:    int32(params.RelateType),
		Action:        int32(params.Action),
		BeforeContent: params.BeforeContent,
		AfterContent:  params.AfterContent,
		OperatedBy:    params.OperatedBy,
		OperatedAt:    params.OperatedAt,
	}
	err := repo.NewOperationLogRepo(opLogSchema.WithContext(ctx)).Save(opLog)
	if err != nil {
		log.Ctx(ctx).Errorf("添加操作日志失败，参数：%v，错误：%v", params, err)
	}
	return err
}
