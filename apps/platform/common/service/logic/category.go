package logic

import (
	"app_service/apps/platform/common/repo"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

// GetCategoryCounts 获取分类的关联数量
func GetCategoryCounts(ctx context.Context, categoryIds []int64) (map[int64]int32, error) {
	if len(categoryIds) == 0 {
		return map[int64]int32{}, nil
	}

	result := make(map[int64]int32)

	// 批量查询所有分类的关联数量
	type CategoryCount struct {
		CategoryID int64
		Count      int64
	}

	var counts []CategoryCount

	// 使用SQL查询直接获取每个分类的数量
	err := repo.GetDB().WithContext(ctx).
		Table("market_changes").
		Select("category_id, COUNT(*) as count").
		Where("category_id IN ?", categoryIds).
		Group("category_id").
		Scan(&counts).Error

	if err != nil {
		log.Ctx(ctx).Errorf("Count  by categories err:%v", err)
		return result, err
	}

	// 构建分类ID到数量的映射
	for _, count := range counts {
		result[count.CategoryID] = int32(count.Count)
	}

	// 确保所有请求的分类ID都有对应的结果，即使数量为0
	for _, cid := range categoryIds {
		if _, exists := result[cid]; !exists {
			result[cid] = 0
		}
	}

	return result, nil
}
