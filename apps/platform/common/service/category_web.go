package service

import (
	"app_service/apps/platform/common/dal/model"
	"app_service/apps/platform/common/define"
	commonEnum "app_service/apps/platform/common/define/enum"
	"app_service/apps/platform/common/repo"
)

func (s *Service) GetCategoryWebList(req *define.GetCategoryWebListReq) (*define.GetCategoryWebListResp, error) {
	var categories []model.Category
	db := repo.GetDB().WithContext(s.ctx)

	err := db.
		Table("category").
		Where("status = ?", commonEnum.CategoryStatusEnable.Val()).
		Where("relate_type = ?", req.RelateType).
		Order("priority DESC, created_at DESC").
		Limit(req.GetPageSize()).
		Offset((req.GetPage() - 1) * req.GetPageSize()).
		Find(&categories).Error

	if err != nil {
		return nil, err
	}
	resp := &define.GetCategoryWebListResp{
		List: make([]*define.GetCategoryWebListData, 0),
	}
	if len(categories) == 0 {
		return resp, nil
	}

	for _, v := range categories {
		resp.List = append(resp.List, &define.GetCategoryWebListData{
			ID:   v.CategoryID,
			Name: v.Name,
		})
	}
	resp.HasMore = len(resp.List) == req.GetPageSize()
	return resp, nil
}
