package service

import (
	"app_service/apps/platform/common/constant"
	"app_service/apps/platform/common/dal/model"
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/repo"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
)

// EditCommonConfig 编辑配置
func (s *Service) EditCommonConfig(req *define.EditCommonConfigReq) (any, error) {
	config, err := repo.GetCommonConfig(s.ctx, repo.WithCommonConfigInKeys(req.Key))
	if err != nil {
		return nil, err
	}
	if config == nil {
		config = &model.CommonConfig{
			ID:          snowflakeutl.GenerateID(),
			ConfigKey:   req.Key,
			ConfigValue: util.Obj2JsonStr(req.Value),
			CreatedBy:   s.GetAdminId(),
		}
	} else {
		config.ConfigValue = util.Obj2JsonStr(req.Value)
		config.UpdatedBy = s.GetAdminId()
	}
	err = repo.SaveCommonConfig(s.ctx, config)
	if err != nil {
		return nil, err
	}
	global.REDIS.Set(s.ctx, constant.GetConfigKey(req.Key), config.ConfigValue, 0)
	return nil, nil
}
