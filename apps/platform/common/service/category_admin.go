package service

import (
	"app_service/apps/platform/common/dal/model"
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/define/enum"
	"app_service/apps/platform/common/repo"
	"app_service/apps/platform/common/service/logic"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
)

// GetCategoryList 查询分类列表
func (s *Service) GetCategoryList(req *define.GetCategoryAdminListReq) (*define.GetCategoryAdminListResp, error) {
	categorySchema := repo.GetQuery().Category
	builder := search.NewQueryBuilder().OrderByDesc(categorySchema.Priority, categorySchema.CreatedAt)

	builder.Eq(categorySchema.RelateType, req.RelateType.Val())

	if req.Status != nil {
		builder.Eq(categorySchema.Status, (*req.Status).Val())
	}

	// 获取分类列表
	list, count, err := repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	resp := &define.GetCategoryAdminListResp{
		List:  make([]*define.GetCategoryAdminListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有分类ID
	categoryIds := make([]int64, 0, len(list))
	for _, v := range list {
		categoryIds = append(categoryIds, v.CategoryID)
	}

	// 获取每个分类的数量
	categoryCountMap, err := logic.GetCategoryCounts(s.ctx, categoryIds)
	if err != nil {
		return nil, err
	}

	// 构建响应
	dataList := make([]*define.GetCategoryAdminListData, 0)
	for _, v := range list {
		dataList = append(dataList, &define.GetCategoryAdminListData{
			ID:        v.CategoryID,
			Name:      v.Name,
			Num:       categoryCountMap[v.CategoryID], // 使用映射获取数量，如果不存在则为0
			Priority:  v.Priority,
			Status:    enum.CategoryStatus(v.Status),
			CreatedAt: v.CreatedAt,
			UpdatedAt: v.UpdatedAt,
		})
	}
	resp.List = dataList
	return resp, nil
}

// GetCategoryDetail 获取分类详情
func (s *Service) GetCategoryDetail(req *define.GetCategoryAdminDetailReq) (*define.GetCategoryAdminDetailResp, error) {
	categorySchema := repo.GetQuery().Category
	builder := search.NewQueryBuilder().Eq(categorySchema.CategoryID, req.ID)

	category, err := repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}

	return &define.GetCategoryAdminDetailResp{
		ID:              category.CategoryID,
		Name:            category.Name,
		TextColor:       category.TextColor,
		BackgroundColor: category.BackgroundColor,
		Priority:        category.Priority,
		Status:          enum.CategoryStatus(category.Status),
		CreatedAt:       category.CreatedAt,
		UpdatedAt:       category.UpdatedAt,
	}, nil
}

// AddCategory 新增分类
func (s *Service) AddCategory(req *define.AddCategoryReq) (*define.AddCategoryResp, error) {
	categorySchema := repo.GetQuery().Category

	category := &model.Category{
		CategoryID:      snowflakeutl.GenerateID(),
		Name:            req.Name,
		TextColor:       req.TextColor,
		BackgroundColor: req.BackgroundColor,
		Priority:        req.Priority,
		Status:          enum.CategoryStatusDisable.Val(),
		RelateType:      req.RelateType.Val(),
	}

	err := repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).Save(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("AddCategory err:%v", err)
		return nil, err
	}

	return &define.AddCategoryResp{
		ID: category.CategoryID,
	}, nil
}

// EditCategory 编辑分类
func (s *Service) EditCategory(req *define.EditCategoryReq) (*define.EditCategoryResp, error) {
	categorySchema := repo.GetQuery().Category

	category := &model.Category{
		CategoryID:      req.ID,
		Name:            req.Name,
		TextColor:       req.TextColor,
		BackgroundColor: req.BackgroundColor,
		Priority:        req.Priority,
	}

	err := repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).UpdateById(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditCategory err:%v", err)
		return nil, err
	}

	return &define.EditCategoryResp{
		ID: req.ID,
	}, nil
}

// EditCategoryPriority 编辑分类优先级
func (s *Service) EditCategoryPriority(req *define.EditCategoryPriorityReq) (*define.EditCategoryPriorityResp, error) {
	categorySchema := repo.GetQuery().Category
	m := &model.Category{
		Priority: req.Priority,
	}

	err := repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.CategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditCategoryPriority err:%v", err)
		return nil, err
	}

	return &define.EditCategoryPriorityResp{
		ID: req.ID,
	}, nil
}

// DelCategory 删除分类
func (s *Service) DelCategory(req *define.DelCategoryReq) (*define.DelCategoryResp, error) {
	categorySchema := repo.GetQuery().Category
	m := &model.Category{
		Status: enum.CategoryStatusDeleted.Val(),
		IsDel:  1,
	}

	// 有关联的栏目不可删除
	countMap, err := logic.GetCategoryCounts(s.ctx, []int64{req.ID})
	if err != nil {
		return nil, err
	}
	if countMap[req.ID] > 0 {
		return nil, define.CategoryDelErr
	}

	err = repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.CategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("DelCategory err:%v", err)
		return nil, err
	}

	return &define.DelCategoryResp{
		ID: req.ID,
	}, nil
}

// EditCategoryStatus 编辑分类状态
func (s *Service) EditCategoryStatus(req *define.EditCategoryStatusReq) (*define.EditCategoryStatusResp, error) {
	categorySchema := repo.GetQuery().Category
	m := &model.Category{
		Status: (*req.Status).Val(),
	}
	// 有关联的栏目不可操作
	countMap, err := logic.GetCategoryCounts(s.ctx, []int64{req.ID})
	if err != nil {
		return nil, err
	}
	if countMap[req.ID] > 0 {
		return nil, define.CategoryDisableErr
	}

	err = repo.NewCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.CategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditCategoryStatus err:%v", err)
		return nil, err
	}

	return &define.EditCategoryStatusResp{
		ID: req.ID,
	}, nil
}
