package service

import (
	"app_service/apps/platform/common/define"
	"app_service/pkg/middlewares/g/auth"
	"app_service/pkg/util/snowflakeutl"
	"context"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"go.opentelemetry.io/otel/trace"
)

// Service 业务服务
// 传递上下文
type Service struct {
	ctx context.Context
}

func New(ctx context.Context) *Service {
	return &Service{
		ctx: ctx,
	}
}

func (s *Service) NewContextWithSpanContext(parent context.Context) context.Context {
	spanContext := trace.SpanContextFromContext(parent)
	return trace.ContextWithSpanContext(context.Background(), spanContext)
}

// GetAdminId 获取当前登录用户id
func (s *Service) GetAdminId() string {
	info, ok := auth.GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetUserId 获取当前登录用户id
func (s *Service) GetUserId() string {
	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GenerateID 生成唯一ID
func (s *Service) GenerateID(req *define.GenerateIDReq) (*define.GenerateIDResp, error) {
	// 生成Snowflake ID
	id := snowflakeutl.GenerateID()

	return &define.GenerateIDResp{
		ID: id,
	}, nil
}
