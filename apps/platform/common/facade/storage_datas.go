package facade

import (
	"app_service/apps/platform/common/dal/model/mongodb"
	"app_service/global"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"github.com/go-redis/redis/v8"
	"go.mongodb.org/mongo-driver/v2/mongo"
	"time"
)

func GetStorageDataByKey(ctx context.Context, key string) (*mongodb.StorageData, error) {
	storageDatasCollection := mongox.NewCollection[mongodb.StorageData](global.Cog(), "storage_datas")
	qw := query.NewBuilder().Eq("key", key).Eq("status", mongodb.StorageDataStatusEnable).Build()
	storageData, err := storageDatasCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return storageData, nil
}

func GetCogCustomConfigByKey[T any](ctx context.Context, key string) (*T, error) {
	// 优先从缓存取
	cacheKey := fmt.Sprintf("app_service:custom_config:%s", key)
	cacheVal, err := global.REDIS.Get(ctx, cacheKey).Result()
	if err != nil && !errors.Is(err, redis.Nil) {
		return nil, err
	}
	if cacheVal != "" {
		// 命中缓存，直接反序列化返回
		var val T
		if err := json.Unmarshal([]byte(cacheVal), &val); err != nil {
			return nil, fmt.Errorf("failed to unmarshal cached data: %w", err)
		}
		return &val, nil
	}

	// 缓存未命中，从 MongoDB 查询
	sdCollection := mongox.NewCollection[mongodb.StorageData](global.Cog(), "storage_datas")
	qw := query.NewBuilder().Eq("key", key).Eq("status", mongodb.StorageDataStatusEnable).Build()
	storageData, err := sdCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		if errors.Is(err, mongo.ErrNoDocuments) {
			return nil, fmt.Errorf("config not found for key: %s", key)
		}
		return nil, fmt.Errorf("failed to query config from database: %w", err)
	}

	// 类型转换：将 interface{} 类型的 Value 转换为目标类型 T
	var result T

	// 通过 JSON 序列化/反序列化进行类型转换
	valueBytes, err := json.Marshal(storageData.Value)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal storage data value: %w", err)
	}

	if err := json.Unmarshal(valueBytes, &result); err != nil {
		return nil, fmt.Errorf("failed to convert value to target type: %w", err)
	}

	// 设置缓存（异步设置，避免影响主流程）
	go func() {
		cacheContext := context.Background()
		cacheData, err := json.Marshal(result)
		if err == nil {
			// 设置缓存，过期时间 10 秒
			global.REDIS.Set(cacheContext, cacheKey, string(cacheData), 10*time.Second)
		}
	}()

	return &result, nil
}
