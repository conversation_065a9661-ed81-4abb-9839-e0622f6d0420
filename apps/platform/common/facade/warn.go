package facade

import (
	"app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/mor"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
)

// SendDefaultWarnMsg 使用默认的Id进行告警告警
func SendDefaultWarnMsg(ctx context.Context, title, msg string) {
	env := "测试"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		env = "正式"
	}
	var message = fmt.Sprintf("【%v】 %v \n%v", env, title, msg)
	id := global.GlobalConfig.WarnId
	req := &mor.WarnReq{
		Id:  id,
		Msg: message,
	}
	res, err := mor.Warn(ctx, req)
	if err != nil {
		if err == context.Canceled {
			SendWarnMsg(context.Background(), id, msg)
		}
		log.Ctx(ctx).Errorf("[warn.SendDefaultWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
	} else {
		log.Ctx(ctx).Infof("[warn.SendDefaultWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, util.Obj2JsonStr(res))
	}
}

func SendDefaultWarnMsgWithMentionOption(ctx context.Context, title, msg string, option *define.WarnMsgMentionOption) {
	env := "测试"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		env = "正式"
	}
	var message = fmt.Sprintf("【%v】 %v \n%v", env, title, msg)
	id := global.GlobalConfig.WarnId
	req := &mor.WarnReq{
		Id:  id,
		Msg: message,
	}
	if option != nil {
		if len(option.MentionedList) > 0 {
			req.MentionedList = option.MentionedList
		}
		if len(option.MentionedMobileList) > 0 {
			req.MentionedMobileList = option.MentionedMobileList
		}
	}
	res, err := mor.Warn(ctx, req)
	if err != nil {
		if err == context.Canceled {
			SendWarnMsg(context.Background(), id, msg)
		}
		log.Ctx(ctx).Errorf("[warn.SendDefaultWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
	} else {
		log.Ctx(ctx).Infof("[warn.SendDefaultWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, util.Obj2JsonStr(res))
	}
}

// SendWarnMsg 告警
func SendWarnMsg(ctx context.Context, id, msg string) {
	go func() {
		req := &mor.WarnReq{
			Id:  id,
			Msg: msg,
		}
		res, err := mor.Warn(ctx, req)
		if err != nil {
			log.Ctx(ctx).Errorf("[warn.SendWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
		} else {
			log.Ctx(ctx).Infof("[warn.SendWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, util.Obj2JsonStr(res))
		}
	}()
}
