package repo

import (
	"app_service/apps/platform/common/dal/model"
	"app_service/pkg/search"
	"context"
	"errors"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// GetCommonConfigList 获取配置列表
func GetCommonConfigList(ctx context.Context, opts ...search.ScopeOpt) ([]*model.CommonConfig, error) {
	c := Query(ctx).CommonConfig
	list, err := c.WithContext(ctx).Scopes(search.MakeOpt(opts...)).Find()
	if err != nil {
		return nil, err
	}
	return list, nil
}

// GetCommonConfig 获取配置详情
func GetCommonConfig(ctx context.Context, opts ...search.ScopeOpt) (*model.CommonConfig, error) {
	gb := Query(ctx).CommonConfig
	config, err := gb.WithContext(ctx).Scopes(
		search.MakeOpt(opts...),
	).Take()
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return config, nil
}

// SaveCommonConfig 编辑配置
func SaveCommonConfig(ctx context.Context, ms ...*model.CommonConfig) error {
	gb := Query(ctx).CommonConfig
	return gb.WithContext(ctx).Save(ms...)
}

// UpdateCommonConfig 更新配置
func UpdateCommonConfig(ctx context.Context, ms *model.CommonConfig, opts ...search.ScopeOpt) error {
	r := Query(ctx).CommonConfig
	result, err := r.WithContext(ctx).Scopes(
		search.MakeOpt(opts...),
	).Updates(ms)
	if err != nil {
		return err
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

// WithCommonConfigInKeys 配置Key条件
func WithCommonConfigInKeys(key ...string) search.ScopeOpt {
	return func(dao gen.Dao) gen.Dao {
		r := GetQuery().CommonConfig
		return dao.Where(r.ConfigKey.In(key...))
	}
}
