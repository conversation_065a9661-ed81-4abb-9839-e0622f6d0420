package admin

import (
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetOperationLogList
// @Summary 查询操作日志列表
// @Description 查询操作日志列表
// @Tags 管理端-操作日志
// @Param data query define.GetOperationLogListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOperationLogListResp}
// @Router /admin/v1/common/operation_log/list [get]
// @Security Bearer
func GetOperationLogList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationLogListReq{}, s.GetOperationLogList)
}
