package admin

import (
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetCategoryList
// @Summary 查询分类列表
// @Description 查询分类列表
// @Tags 管理端-分类管理
// @Param data query define.GetCategoryAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCategoryAdminListResp}
// @Router /admin/v1/common/category/list [get]
// @Security Bearer
func GetCategoryList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCategoryAdminListReq{}, s.GetCategoryList)
}

// GetCategoryDetail
// @Summary 查询分类详情
// @Description 查询分类详情
// @Tags 管理端-分类管理
// @Param data query define.GetCategoryAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCategoryAdminDetailResp}
// @Router /admin/v1/common/category/detail [get]
// @Security Bearer
func GetCategoryDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCategoryAdminDetailReq{}, s.GetCategoryDetail)
}

// AddCategory
// @Summary 新增分类
// @Description 新增分类
// @Tags 管理端-分类管理
// @Param data body define.AddCategoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddCategoryResp}
// @Router /admin/v1/common/category/add [POST]
// @Security Bearer
func AddCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddCategoryReq{}, s.AddCategory)
}

// EditCategory
// @Summary 编辑分类
// @Description 编辑分类
// @Tags 管理端-分类管理
// @Param data body define.EditCategoryReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditCategoryResp}
// @Router /admin/v1/common/category/edit [POST]
// @Security Bearer
func EditCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditCategoryReq{}, s.EditCategory)
}

// EditCategoryPriority
// @Summary 分类优先级编辑
// @Description 分类优先级编辑
// @Tags 管理端-分类管理
// @Param data body define.EditCategoryPriorityReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditCategoryPriorityResp}
// @Router /admin/v1/common/category/edit_priority [POST]
// @Security Bearer
func EditCategoryPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditCategoryPriorityReq{}, s.EditCategoryPriority)
}

// DelCategory
// @Summary 分类删除
// @Description 分类删除
// @Tags 管理端-分类管理
// @Param data body define.DelCategoryReq true "删除参数"
// @Success 200 {object} response.Data{data=define.DelCategoryResp}
// @Router /admin/v1/common/category/del [POST]
// @Security Bearer
func DelCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelCategoryReq{}, s.DelCategory)
}

// EditCategoryStatus
// @Summary 编辑分类状态
// @Description 编辑分类状态
// @Tags 管理端-分类管理
// @Param data body define.EditCategoryStatusReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditCategoryStatusResp}
// @Router /admin/v1/common/category/edit_status [POST]
// @Security Bearer
func EditCategoryStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditCategoryStatusReq{}, s.EditCategoryStatus)
}
