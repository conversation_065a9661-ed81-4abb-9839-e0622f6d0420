package web

import (
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GenerateID
// @Summary 生成唯一ID
// @Description 生成Snowflake唯一ID
// @Tags 用户端-通用工具
// @x-apifox-folder "用户端/通用工具"
// @Param data query define.GenerateIDReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GenerateIDResp}
// @Router /web/v1/common/generate_id [get]
func GenerateID(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GenerateIDReq{}, s.GenerateID)
}
