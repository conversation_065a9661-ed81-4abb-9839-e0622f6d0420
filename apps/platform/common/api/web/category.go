package web

import (
	"app_service/apps/platform/common/define"
	"app_service/apps/platform/common/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetCategoryWebList
// @Summary  分类列表
// @Description  分类列表
// @Tags 用户端-分类
// @Param data query define.GetCategoryWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCategoryWebListResp}
// @Router /web/v1/common/category/list [get]
func GetCategoryWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCategoryWebListReq{}, s.GetCategoryWebList)
}
