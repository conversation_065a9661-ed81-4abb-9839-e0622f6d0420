package router

import (
	"app_service/apps/platform/common/router/web"
	"app_service/pkg/middlewares/g/auth"
	"fmt"

	"app_service/apps/platform/common/router/admin"
	"app_service/global"
	"app_service/pkg/middlewares"

	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 管理端路由
	adminRote(r)
	// 客户端路由
	webRote(r)
}

// 管理端路由
func adminRote(router *gin.Engine) {
	w := router.Group("/admin/v1/common", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Admin{
		NoAuthUrl: []string{},
	}))

	admin.OperationLog(w)
	admin.Category(w)
}

func webRote(router *gin.Engine) {
	w := router.Group("/web/v1/common", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/common/category/list",
			"/web/v1/common/generate_id",
		},
	}))

	web.Category(w)
	web.Utils(w)
}
