package admin

import (
	"app_service/apps/platform/common/api/admin"
	"github.com/gin-gonic/gin"
)

// Category 分类管理端相关
func Category(router *gin.RouterGroup) {
	group := router.Group("/category")
	{
		// 获取分类列表
		group.GET("/list", admin.GetCategoryList)
		//// 获取分类详情
		group.GET("/detail", admin.GetCategoryDetail)
		// 新增分类
		group.POST("/add", admin.AddCategory)
		// 编辑分类
		group.POST("/edit", admin.EditCategory)
		// 分类优先级编辑
		group.POST("/edit_priority", admin.EditCategoryPriority)
		// 分类删除
		group.POST("/del", admin.DelCategory)
		// 分类状态编辑
		group.POST("/edit_status", admin.EditCategoryStatus)
	}
}
