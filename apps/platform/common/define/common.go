package define

type Empty struct{}

// Resp 分页响应结构体
type Resp struct {
	List  any   `json:"list"`
	Total int64 `json:"total"`
}

type (
	ById struct {
		Id uint `form:"id"  binding:"required" json:"id"`
	}

	ByIdStr struct {
		Id string `form:"id"  binding:"required" json:"id"`
	}

	Pages struct {
		Page     int `form:"page"      json:"page"`
		PageSize int `form:"page_size" json:"page_size"`
	}

	H map[string]any
)

// GenerateIDReq 生成ID请求
type GenerateIDReq struct {
	// 无需参数
}

// GenerateIDResp 生成ID响应
type GenerateIDResp struct {
	ID int64 `json:"id"` // 生成的Snowflake ID
}
