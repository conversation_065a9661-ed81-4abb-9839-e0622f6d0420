package define

type (
	GetCommonConfigListReq struct {
		Keys string `form:"keys" json:"keys"` //key逗号
	}

	GetCommonConfigResp struct {
		Configs map[string]interface{} `json:"configs"`
	}
)

// GetCommonConfigDetailReq 获取配置详情
type (
	GetCommonConfigDetailReq struct {
		Key string `form:"key" json:"key" binding:"required"` //key
	}
)

type (
	EditCommonConfigReq struct {
		Key   string `json:"key" binding:"required"`   //key
		Value any    `json:"value" binding:"required"` //value
	}

	EditCommonConfigResp struct {
	}
)

// GetWebCommonConfigListReq 获取配置列表
type (
	GetWebCommonConfigListReq struct {
		Keys string `form:"keys" json:"keys" binding:"required"` //key逗号
	}
)

type CreationEventsConfig struct {
	InitScoreMin          int32  `json:"init_score_min"`            //初始化造物进度最小值
	InitScoreMax          int32  `json:"init_score_max"`            //初始化造物进度最大值
	RuleDesc              string `json:"rule_desc"`                 // 活动规则
	NewUserHelpRate       int32  `json:"new_user_help_rate"`        //新用户转化助力值倍数
	OldUserHelpRate       int32  `json:"old_user_help_rate"`        //老用户助力值倍数
	NewUserWindowImageUrl string `json:"new_user_window_image_url"` //新用户引导弹窗
	NewUserWindowJumpUrl  string `json:"new_user_window_jump_url"`  //跳转地址
	NewUserWindowCouponId string `json:"new_user_window_coupon_id"` //弹窗优惠券
	HelpTimesDaily        int32  `json:"help_times_daily"`          //用户每天可助力次数
	OnePrizeJoinTimes     int32  `json:"one_prize_join_times"`      //用户同一奖品可造物次数
	MeanwhileJoinTimes    int32  `json:"meanwhile_join_times"`      //用户同时可进行造物奖品数量
	JoinTimesDaily        int32  `json:"join_times_daily"`          //用户一天可发起造物次数
	StockWarning          int32  `json:"stock_warning"`             //奖品库存预警数
	HelpUserRiskGrade     int32  `json:"help_user_risk_grade"`      //助力用户风险等级达到X（风险等级）级以上助力失败
}

type CreationEventAssistConfig struct {
	HelpTimesDaily    int32 `json:"help_times_daily"`     // 用户每天可助力次数
	HelpUserRiskGrade int32 `json:"help_user_risk_grade"` // 助力用户风险等级达到X（风险等级）级以上助力失败
	NewUserHelpRate   int32 `json:"new_user_help_rate"`   // 新用户转化助力值倍数
	OldUserHelpRate   int32 `json:"old_user_help_rate"`   // 老用户助力值倍数
}

type CreationEventJoinConfig struct {
	InitScoreMax       int32 `json:"init_score_max"`       //初始化造物进度最大值
	InitScoreMin       int32 `json:"init_score_min"`       //初始化造物进度最小值
	JoinTimesDaily     int32 `json:"join_times_daily"`     //用户一天可发起造物次数
	MeanwhileJoinTimes int32 `json:"meanwhile_join_times"` //用户同时可进行造物奖品数量
	OnePrizeJoinTimes  int32 `json:"one_prize_join_times"` //用户同一奖品可造物次数
}

type CreationEventRuleConfig struct {
	Banner                string `json:"banner"`
	NewUserWindowCouponId string `json:"new_user_window_coupon_id"`
	NewUserWindowImageUrl string `json:"new_user_window_image_url"`
	NewUserWindowJumpUrl  string `json:"new_user_window_jump_url"`
	RuleDesc              string `json:"rule_desc"`
}

type CreationEventWarnConfig struct {
	StockWarning int32 `json:"stock_warning"` //奖品库存预警数
}
