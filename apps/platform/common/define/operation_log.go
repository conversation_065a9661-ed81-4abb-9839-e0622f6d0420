package define

import (
	"app_service/apps/platform/common/define/enum"
	"time"
)

type (
	GetOperationLogListReq struct {
		RelateID   int64 `form:"relate_id" json:"relate_id" binding:"required"`
		RelateType int32 `form:"relate_type" json:"relate_type" binding:"required"`
	}
	OperationLogItem struct {
		OperationLogID int64                       `json:"operation_log_id"`
		Action         enum.OperationLogActionEnum `json:"action"`
		AfterContent   string                      `json:"after_content"`
		BeforeContent  string                      `json:"before_content"`
		OperatedBy     string                      `json:"operated_by"`
		OperatedAt     time.Time                   `json:"operated_at"`
	}
	GetOperationLogListResp struct {
		List []*OperationLogItem `json:"list"`
	}

	AddOperationLogParams struct {
		RelateID      int64
		RelateType    enum.OperationLogRelateTypeEnum
		Action        enum.OperationLogActionEnum
		BeforeContent string
		AfterContent  string
		OperatedBy    string
		OperatedAt    time.Time
	}
)
