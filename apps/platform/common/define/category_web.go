package define

import (
	"app_service/apps/platform/common/define/enum"
	"app_service/pkg/pagination"
)

// 分类列表相关结构体
type (
	GetCategoryWebListReq struct {
		pagination.Pagination
		RelateType enum.CategoryRelateType `form:"relate_type" json:"relate_type" binding:"required"` // 关联类型
	}

	GetCategoryWebListData struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}

	GetCategoryWebListResp struct {
		List    []*GetCategoryWebListData `json:"list"`
		HasMore bool                      `json:"has_more"` // 判断当前页是否为最后一页
	}
)
