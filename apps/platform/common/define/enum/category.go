package enum

// CategoryStatus 分类状态 -1 // 已删除  1 // 启用 2 // 禁用
type CategoryStatus int32

func (s CategoryStatus) Val() int32 {
	return int32(s)
}

const (
	// CategoryStatusDeleted 已删除
	CategoryStatusDeleted CategoryStatus = -1 // 已删除
	// CategoryStatusEnable 启用
	CategoryStatusEnable CategoryStatus = 1 // 启用
	// CategoryStatusDisable 禁用
	CategoryStatusDisable CategoryStatus = 2 // 禁用
)

type CategoryRelateType int32

func (s CategoryRelateType) Val() int32 {
	return int32(s)
}

const (
	// CategoryRelateTypeMarketChanges 行情异动
	CategoryRelateTypeMarketChanges CategoryRelateType = 1 // 行情异动
)
