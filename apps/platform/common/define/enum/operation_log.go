package enum

type (
	OperationLogActionEnum     int32
	OperationLogRelateTypeEnum int32
)

const (
	OperationLogActionCreate       OperationLogActionEnum = 1 // 创建
	OperationLogActionUpdate       OperationLogActionEnum = 2 // 更新
	OperationLogActionDelete       OperationLogActionEnum = 3 // 删除
	OperationLogActionPublish      OperationLogActionEnum = 4 // 发布
	OperationLogActionRemove       OperationLogActionEnum = 5 // 下架
	OperationLogActionSyncMediaON  OperationLogActionEnum = 6 // 同步媒体-开
	OperationLogActionSyncMediaOFF OperationLogActionEnum = 7 // 同步媒体-关

	OperationLogRelateTypeAnn       OperationLogRelateTypeEnum = 1 // 公告
	OperationLogRelateTypeMC        OperationLogRelateTypeEnum = 2 // 行情异动
	OperationLogRelateTypeBonusItem OperationLogRelateTypeEnum = 3 // 积分商品
	OperationLogRelateTypeOperAnn   OperationLogRelateTypeEnum = 4 // 运营公告
)
