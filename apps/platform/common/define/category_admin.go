package define

import (
	"app_service/apps/platform/common/define/enum"
	"time"

	"app_service/pkg/pagination"
)

// 分类列表相关结构体
type (
	GetCategoryAdminListReq struct {
		pagination.Pagination
		RelateType enum.CategoryRelateType `form:"relate_type" json:"relate_type" binding:"required"` // 关联类型
		Status     *enum.CategoryStatus    `form:"status" json:"status"`                              // 状态
	}

	GetCategoryAdminListData struct {
		ID        int64               `json:"id,string"`  // 分类ID
		Name      string              `json:"name"`       // 分类名称
		Num       int32               `json:"num"`        // 数量
		Priority  int32               `json:"priority"`   // 优先级
		Status    enum.CategoryStatus `json:"status"`     // 状态
		CreatedAt time.Time           `json:"created_at"` // 创建时间
		UpdatedAt time.Time           `json:"updated_at"` // 更新时间
	}

	GetCategoryAdminListResp struct {
		List  []*GetCategoryAdminListData `json:"list"`
		Total int64                       `json:"total"`
	}
)

// 分类详情相关结构体
type (
	GetCategoryAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 分类ID
	}

	GetCategoryAdminDetailResp struct {
		ID              int64               `json:"id,string"`        // 分类ID
		Name            string              `json:"name"`             // 分类名称
		TextColor       string              `json:"text_color"`       // 文字颜色
		BackgroundColor string              `json:"background_color"` // 背景颜色
		Priority        int32               `json:"priority"`         // 优先级
		Status          enum.CategoryStatus `json:"status"`           // 状态
		CreatedAt       time.Time           `json:"created_at"`       // 创建时间
		UpdatedAt       time.Time           `json:"updated_at"`       // 更新时间
	}

	GetCategoryAdminLessDetailResp struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}
)

// 新增分类相关结构体
type (
	AddCategoryReq struct {
		Name            string                  `json:"name" binding:"required"`             // 分类名称
		TextColor       string                  `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string                  `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32                   `json:"priority" binding:"required"`         // 优先级
		RelateType      enum.CategoryRelateType `json:"relate_type" binding:"required"`      // 关联类型
	}

	AddCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑分类相关结构体
type (
	EditCategoryReq struct {
		ID              int64  `json:"id,string" binding:"required"`        // 分类ID
		Name            string `json:"name" binding:"required"`             // 分类名称
		TextColor       string `json:"text_color" binding:"required"`       // 文字颜色
		BackgroundColor string `json:"background_color" binding:"required"` // 背景颜色
		Priority        int32  `json:"priority" binding:"required"`         // 优先级
	}

	EditCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑分类优先级相关结构体
type (
	EditCategoryPriorityReq struct {
		ID       int64 `json:"id,string" binding:"required"` // 分类ID
		Priority int32 `json:"priority" binding:"required"`  //  优先级
	}

	EditCategoryPriorityResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 删除分类相关结构体
type (
	DelCategoryReq struct {
		ID int64 `json:"id,string" binding:"required"` // 分类ID
	}

	DelCategoryResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)

// 编辑分类状态相关结构体
type (
	EditCategoryStatusReq struct {
		ID     int64                `json:"id,string" binding:"required"` // 分类ID
		Status *enum.CategoryStatus `json:"status" binding:"required"`    // 状态
	}
	EditCategoryStatusResp struct {
		ID int64 `json:"id,string"` // 分类ID
	}
)
