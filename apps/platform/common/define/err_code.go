package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	// CommonErr 通用异常
	CommonErr = response.NewError(8111001001, "系统出了点小问题哦~请稍后再试~")
	// ParamErr 参数错误
	ParamErr = response.NewError(8111001002, "参数错误")
	// CommonWarnErr 通用告警异常
	CommonWarnErr = response.NewError(8111002002, "系统有点小状况~别担心哦~")

	CategoryDelErr     = response.NewError(8111003001, "栏目有关联的数据不可删除")
	CategoryDisableErr = response.NewError(8111003002, "栏目有关联的数据不可禁用")
)
