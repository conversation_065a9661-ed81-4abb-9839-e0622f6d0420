// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameOperationLog = "operation_log"

// OperationLog 操作日志表
type OperationLog struct {
	OperationLogID int64     `gorm:"column:operation_log_id;type:bigint;primaryKey;autoIncrement:true;comment:id" json:"operation_log_id"` // id
	RelateID       int64     `gorm:"column:relate_id;type:bigint;not null;comment:关联id" json:"relate_id"`                                  // 关联id
	RelateType     int32     `gorm:"column:relate_type;type:tinyint;not null;comment:关联类型，1公告" json:"relate_type"`                         // 关联类型，1公告
	Action         int32     `gorm:"column:action;type:tinyint;not null;comment:操作类型，1创建，2修改，3删除，4发布，5下架，6 同步媒体" json:"action"`            // 操作类型，1创建，2修改，3删除，4发布，5下架，6 同步媒体
	AfterContent   string    `gorm:"column:after_content;type:text;comment:操作后内容" json:"after_content"`                                    // 操作后内容
	BeforeContent  string    `gorm:"column:before_content;type:text;comment:操作前内容" json:"before_content"`                                  // 操作前内容
	OperatedBy     string    `gorm:"column:operated_by;type:char(24);not null;comment:操作人 id" json:"operated_by"`                          // 操作人 id
	OperatedAt     time.Time `gorm:"column:operated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:操作时间" json:"operated_at"`  // 操作时间
	CreatedAt      time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`    // 创建时间
}

// TableName OperationLog's table name
func (*OperationLog) TableName() string {
	return TableNameOperationLog
}
