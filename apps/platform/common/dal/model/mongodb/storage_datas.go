package mongodb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type StorageDataStatusEnum int32

const (
	StorageDataStatusDeleted StorageDataStatusEnum = -1 // 已删除
	StorageDataStatusDisable StorageDataStatusEnum = 0  // 已禁用
	StorageDataStatusEnable  StorageDataStatusEnum = 1  // 已启用
	StorageDataStatusClose   StorageDataStatusEnum = 2  // 已停用
)

type (
	CustomTradeFrequencyConfig struct {
		CoolDownMinutes int32 `json:"cool_down_minutes" bson:"cool_down_minutes"`
		CoolDownDay     int32 `json:"cool_down_day" bson:"cool_down_day"`
	}
	CustomTradeFeeConfig struct {
		Ratio int32 `json:"ratio" bson:"ratio"`
	}
)

type StorageData struct {
	ID              bson.ObjectID         `bson:"_id,omitempty"`
	Status          StorageDataStatusEnum `bson:"status"`                      // 状态字段
	StorageID       bson.ObjectID         `bson:"storage_id"`                  // 来源储存库
	Key             string                `bson:"key"`                         // 配置键
	Value           interface{}           `bson:"value"`                       // 配置值
	Remark          string                `bson:"remark,omitempty"`            // 配置说明
	Options         interface{}           `bson:"options,omitempty"`           // 选项
	ValueValidRules interface{}           `bson:"value_valid_rules,omitempty"` // 校验规则

	// 时间戳（自动管理）
	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`
}

// 可选的数据库操作方法示例
func (s *StorageData) BeforeCreate() {
	if s.CreatedAt.IsZero() {
		s.CreatedAt = time.Now()
	}
	s.UpdatedAt = time.Now()
}
