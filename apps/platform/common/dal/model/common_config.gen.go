// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameCommonConfig = "common_config"

// CommonConfig 通用配置表
type CommonConfig struct {
	ID          int64                 `gorm:"column:id;type:bigint;primaryKey;comment:主键" json:"id"`                                                   // 主键
	ConfigKey   string                `gorm:"column:config_key;type:varchar(255);not null;comment:key" json:"config_key"`                              // key
	ConfigValue string                `gorm:"column:config_value;type:text;not null;comment:value" json:"config_value"`                                // value
	Remark      string                `gorm:"column:remark;type:varchar(255);comment:备注" json:"remark"`                                                // 备注
	CreatedBy   string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt   time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy   string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt   time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel       soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName CommonConfig's table name
func (*CommonConfig) TableName() string {
	return TableNameCommonConfig
}
