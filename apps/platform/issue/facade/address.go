package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"context"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
)

func GetAddressByID(ctx context.Context, id string) (*mongdb.Address, error) {
	addressCollection := mongox.NewCollection[mongdb.Address](global.Tmt(), "address")
	objID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Id(objID).Build()
	address, err := addressCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return address, nil
}
