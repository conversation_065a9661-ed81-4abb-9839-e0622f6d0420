package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"github.com/jinzhu/now"
	"go.mongodb.org/mongo-driver/v2/bson"
)

func GetSaleOrderMapByUserItemIDs(ctx context.Context, userItemIDs []string) (map[string]*mongdb.SaleOrder, error) {
	if len(userItemIDs) == 0 {
		return nil, nil
	}
	saleOrderCollection := mongox.NewCollection[mongdb.SaleOrder](global.Tmt(), "sale_order")
	var objectIDs []any
	for _, itemId := range userItemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		objectIDs = append(objectIDs, itemObjectId)
	}
	qw := query.NewBuilder().In("user_item_id", objectIDs...).Build()
	saleOrders, err := saleOrderCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}

	resultMap := make(map[string]*mongdb.SaleOrder)
	for _, saleOrder := range saleOrders {
		resultMap[saleOrder.UserItemID.Hex()] = saleOrder
	}

	return resultMap, nil
}

func GetTodaySaleOrderMapByItemIDs(ctx context.Context, userID string, itemIDs []string) (map[string][]*mongdb.SaleOrder, error) {
	if len(itemIDs) == 0 {
		return nil, nil
	}
	saleOrderCollection := mongox.NewCollection[mongdb.SaleOrder](global.Tmt(), "sale_order")
	var objectIDs []any
	itemIDMap := map[string]string{}
	for _, itemId := range itemIDs {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		objectIDs = append(objectIDs, itemObjectId)
		itemIDMap[itemId] = itemId
	}
	todayBegin := now.BeginningOfDay()
	userObjID, _ := bson.ObjectIDFromHex(userID)
	qw := query.NewBuilder().
		Eq("sale_user_id", userObjID).
		Eq("order_status", 0).
		In("item_id", objectIDs...).
		Gte("order_tiem", todayBegin).Build()
	log.Ctx(ctx).Infof("GetTodaySaleOrderMapByItemIDs qw: %s", qw.String())
	saleOrders, err := saleOrderCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	resultMap := make(map[string][]*mongdb.SaleOrder)
	for _, saleOrder := range saleOrders {
		if curSaleOrders, exists := resultMap[saleOrder.ItemID.Hex()]; exists {
			curSaleOrders = append(curSaleOrders, saleOrder)
			resultMap[saleOrder.ItemID.Hex()] = curSaleOrders
		} else {
			resultMap[saleOrder.ItemID.Hex()] = []*mongdb.SaleOrder{saleOrder}
		}
	}

	return resultMap, nil
}
