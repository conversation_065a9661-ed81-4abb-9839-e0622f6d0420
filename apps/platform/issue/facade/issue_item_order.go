package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"context"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/aggregation"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
	"time"
)

// GetIssueItemTransAmountMapByTime [一手]根据时间范围获取成交额 map
func GetIssueItemTransAmountMapByTime(ctx context.Context, startTime, endTime time.Time) (map[string]int64, error) {
	type aggregationItem struct {
		ItemID            bson.ObjectID `bson:"item_id"`
		TransactionAmount int64         `bson:"transaction_amount"`
	}
	iwoCollection := mongox.NewCollection[aggregationItem](global.Tmt(), "issue_item_order")
	pipeline := aggregation.NewStageBuilder().
		Match(
			query.NewBuilder().
				Eq("status", mongdb.IssueItemOrderStatusComplete).
				Gte("created_at", startTime).
				Lte("created_at", endTime).
				Build(),
		).
		Group("$item_id",
			bson.E{
				Key:   "transaction_amount",
				Value: bson.D{bson.E{Key: "$sum", Value: "$pay_amount"}}},
		).
		Project(
			bson.M{
				"_id":                0,
				"transaction_amount": 1,
				"item_id":            "$_id",
			},
		).Build()
	opts := options.Aggregate().SetAllowDiskUse(true)
	results, err := iwoCollection.Aggregator().Pipeline(pipeline).Aggregate(ctx, opts)
	if err != nil {
		return nil, err
	}
	resultMap := map[string]int64{}
	for _, result := range results {
		resultMap[result.ItemID.Hex()] = result.TransactionAmount
	}

	return resultMap, nil
}
