package facade

import (
	"app_service/apps/platform/common/constant"
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/apps/platform/issue/define"
	"app_service/global"
	"app_service/pkg/util"
	"context"
	"encoding/json"

	"e.coding.net/g-dtay0385/common/go-util/response"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/chenmingyong0423/go-mongox/v2/bsonx"

	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
	"go.mongodb.org/mongo-driver/v2/mongo/options"
)

//
//func GetIssueItem(ctx context.Context, itemId string) (*mongdb.IssueItem, error) {
//	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
//	itemObjectId, err := bson.ObjectIDFromHex(itemId)
//	if err != nil {
//		return nil, err
//	}
//	qw := query.NewBuilder().Id(itemObjectId).Build()
//	issueItem, err := issueItemCollection.Finder().Filter(qw).FindOne(ctx)
//	if err != nil {
//		return nil, err
//	}
//	return issueItem, nil
//}

func GetIssueItems(ctx context.Context, itemIds []string) ([]*mongdb.IssueItem, error) {
	if len(itemIds) == 0 {
		return nil, nil
	}
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	itemObjectIds := make([]interface{}, 0, len(itemIds))
	for _, itemId := range itemIds {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	qw := query.NewBuilder().In("item_id", itemObjectIds...).Build()
	issueItem, err := issueItemCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	return issueItem, nil
}

func GetIssueItemMap(ctx context.Context, itemIds []string) (map[string]*mongdb.IssueItem, error) {
	if len(itemIds) == 0 {
		return nil, nil
	}
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	var itemObjectIds []interface{}
	for _, itemId := range itemIds {
		itemObjectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		itemObjectIds = append(itemObjectIds, itemObjectId)
	}
	qw := query.NewBuilder().In("item_id", itemObjectIds...).Build()
	issueItem, err := issueItemCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	issueItemMap := make(map[string]*mongdb.IssueItem, len(issueItem))
	for _, item := range issueItem {
		issueItemMap[item.ItemID.Hex()] = item
	}
	return issueItemMap, nil
}

func GetIssueItemByItemID(ctx context.Context, itemId string) (*mongdb.IssueItem, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	itemObjectId, err := bson.ObjectIDFromHex(itemId)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Eq("item_id", itemObjectId).Build()
	issueItem, err := issueItemCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return issueItem, nil
}

func GetAllValidIssueItemIDs(ctx context.Context) ([]string, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	projection := bson.M{"item_id": 1, "_id": 0}
	opts := options.Find().SetProjection(projection)
	qw := query.NewBuilder().Eq("audit_status", mongdb.IssueItemAuditStatusApproved) // 审核通过
	issueItems, err := issueItemCollection.Finder().Filter(qw.Build()).Limit(100000).Find(ctx, opts)
	if err != nil {
		return nil, err
	}
	var itemIDs []string
	for _, item := range issueItems {
		itemIDs = append(itemIDs, item.ItemID.Hex())
	}

	return itemIDs, nil
}

// GetIssueItemByCache 获取 IssueItem 信息 通过缓存 (优先从缓存获取，未命中则从 MongoDB 查询)
func GetIssueItemByCache(ctx context.Context, itemID string) (*mongdb.IssueItem, error) {
	issueItemRedisKey := constant.GetIssueItemKey(itemID)
	itemStr, _ := global.REDIS.Get(ctx, issueItemRedisKey).Result()
	if itemStr != "" {
		info := &mongdb.IssueItem{}
		err := json.Unmarshal([]byte(itemStr), info)
		if err != nil {
			log.Ctx(ctx).Errorf("redis IssueItem 信息转换结构体失败 err:%+v", err)
		} else {
			return info, nil
		}
	}

	issueItem, err := GetIssueItemByItemID(ctx, itemID)
	if err != nil {
		log.Ctx(ctx).Errorf("查询 IssueItem 错误 err:%+v", err)
		return nil, err
	}
	if issueItem == nil {
		return nil, nil
	}

	jsonData, err := json.Marshal(issueItem)
	if err != nil {
		log.Ctx(ctx).Errorf("序列化 IssueItem 信息失败 err:%+v", err)
		return nil, err
	}
	res, err := global.REDIS.Set(ctx, issueItemRedisKey, string(jsonData), constant.IssueItemTTL).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("redis设置 IssueItem 信息缓存失败 err:%+v", err)
	}
	log.Ctx(ctx).Infof("redis设置 IssueItem 信息缓存成功 res:%v", res)

	return issueItem, nil
}

// GetIssueItemMapByCache  获取 IssueItem 信息 map 通过缓存 (优先从缓存获取，未命中则从 MongoDB 查询)
func GetIssueItemMapByCache(ctx context.Context, itemIDs []string) (map[string]*mongdb.IssueItem, error) {
	itemIDs = util.UniqueStringSlice(itemIDs)

	if len(itemIDs) == 0 {
		return nil, nil
	}
	// 调用 GetIssueItemsByCache 获取列表
	itemInfos, err := GetIssueItemsByCache(ctx, itemIDs)
	log.Ctx(ctx).Infof("获取商品信息 result:%v", itemInfos)
	if err != nil {
		return nil, err
	}
	itemInfoMap := make(map[string]*mongdb.IssueItem, len(itemInfos))
	for _, item := range itemInfos {
		itemInfoMap[item.ItemID.Hex()] = item
	}
	return itemInfoMap, nil
}

// GetIssueItemsByCache 获取 IssueItem 信息列表 通过缓存 (优先从缓存获取，未命中则从 MongoDB 查询)
func GetIssueItemsByCache(ctx context.Context, itemIDs []string) ([]*mongdb.IssueItem, error) {
	itemIDs = util.UniqueStringSlice(itemIDs)

	result := make([]*mongdb.IssueItem, 0)
	var queryItemIds []string
	var itemIdRedisKey []string
	for _, itemID := range itemIDs {
		itemIdRedisKey = append(itemIdRedisKey, constant.GetIssueItemKey(itemID))
	}
	log.Ctx(ctx).Infof("redis 获取商品信息列表 keys:%v", itemIdRedisKey)
	itemInfoStrList, _ := global.REDIS.MGet(ctx, itemIdRedisKey...).Result()
	log.Ctx(ctx).Infof("redis 获取商品信息列表 res:%v", itemInfoStrList)

	if itemInfoStrList != nil && len(itemInfoStrList) > 0 {
		for k, itemStr := range itemInfoStrList {
			if itemStr != nil && itemStr != "" {
				info := &mongdb.IssueItem{}
				err := json.Unmarshal([]byte(itemStr.(string)), info)
				if err != nil {
					log.Ctx(ctx).Errorf("redis商品信息转换结构体失败 err:%+v", err)
				} else {
					result = append(result, info)
				}
			} else {
				queryItemIds = append(queryItemIds, itemIDs[k])
			}
		}
	}

	if len(queryItemIds) > 0 {
		log.Ctx(ctx).Infof("获取商品信息 queryItemIds:%v", queryItemIds)
		itemInfos, err := GetIssueItems(ctx, queryItemIds) // 调用现有的 GetIssueItems (从 MongoDB 批量查询)
		log.Ctx(ctx).Infof("获取商品信息 itemInfos:%v", itemInfos)
		if err != nil {
			log.Ctx(ctx).Errorf("查询商品列表错误 err:%+v", err)
			return nil, response.ParamErr.SetMsg("查询商品列表错误")
		}

		for _, item := range itemInfos {
			result = append(result, item)
			jsonData, err := json.Marshal(item)
			if err != nil {
				log.Ctx(ctx).Errorf("序列化商品信息失败 err:%+v", err)
				continue
			}
			res, err := global.REDIS.Set(ctx, constant.GetIssueItemKey(item.ItemID.Hex()), string(jsonData), constant.IssueItemTTL).Result()
			if err != nil {
				log.Ctx(ctx).Errorf("redis设置商品信息缓存失败 err:%+v", err)
			}
			log.Ctx(ctx).Infof("redis设置商品信息缓存成功 res:%v", res)
		}
	}
	return result, nil
}

func GetIssueItemsByStory(ctx context.Context, req *define.GetItemStoryListReq) ([]*mongdb.IssueItem, int64, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	qw := query.NewBuilder()
	if req.ItemId != nil && *req.ItemId != "" {
		itemId, err := bson.ObjectIDFromHex(*req.ItemId)
		if err != nil {
			return nil, 0, err
		}
		qw.Eq("item_id", itemId)
	}
	if req.ItemName != nil && *req.ItemName != "" {
		qw.Eq("item_name", *req.ItemName)
	}
	qw.Eq("story.status", mongdb.IssueItemStoryStatusOpen) // 1:开启玩法
	qw.Eq("audit_status", mongdb.IssueItemAuditStatusApproved)
	items, err := issueItemCollection.Finder().Filter(qw.Build()).
		Sort(bsonx.NewD().Add("_id", -1).Build()).
		Limit(int64(req.GetPageSize())).
		Skip(int64(req.GetOffset())).
		Find(ctx)
	if err != nil {
		return nil, 0, err
	}
	count, err := issueItemCollection.Finder().Filter(qw.Build()).Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	return items, count, nil
}

func GetIssueItemsBySynthesis(ctx context.Context, req *define.GetItemSynthesisListReq) ([]*mongdb.IssueItem, int64, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	qw := query.NewBuilder()
	if req.ItemId != nil && *req.ItemId != "" {
		itemId, err := bson.ObjectIDFromHex(*req.ItemId)
		if err != nil {
			return nil, 0, err
		}
		qw.Eq("item_id", itemId)
	}
	if req.ItemName != nil && *req.ItemName != "" {
		qw.Eq("item_name", *req.ItemName)
	}
	qw.Ne("priority_buy.status", mongdb.IssueItemPriorityBuyStatusOpen) // 1:开启玩法
	qw.Eq("synthesis.status", mongdb.IssueItemSynthesisStatusOpen)      // 1:开启玩法
	qw.Eq("audit_status", mongdb.IssueItemAuditStatusApproved)
	items, err := issueItemCollection.Finder().Filter(qw.Build()).
		Sort(bsonx.NewD().Add("_id", -1).Build()).
		Limit(int64(req.GetPageSize())).
		Skip(int64(req.GetOffset())).
		Find(ctx)
	if err != nil {
		return nil, 0, err
	}
	count, err := issueItemCollection.Finder().Filter(qw.Build()).Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	return items, count, nil
}

// GetIssueItemsByCirculation 获取所有开放流通的商品
func GetIssueItemsByCirculation(ctx context.Context, ignoreItemIDs []string) ([]*mongdb.IssueItem, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	qw := query.NewBuilder()
	qw.Eq("circulation_status", mongdb.CirculationStatusAllow) // 允许流通
	qw.Eq("audit_status", mongdb.IssueItemAuditStatusApproved) // 审核通过
	if len(ignoreItemIDs) > 0 {
		var objItemIDs []any
		for _, itemID := range ignoreItemIDs {
			objID, err := bson.ObjectIDFromHex(itemID)
			if err != nil {
				return nil, err
			}
			objItemIDs = append(objItemIDs, objID)
		}
		qw.Nin("item_id", objItemIDs...)
	}
	items, err := issueItemCollection.Finder().Filter(qw.Build()).Find(ctx)
	if err != nil {
		return nil, err
	}

	return items, nil
}

// GetLatestWillIssueItemList 获取即将发行的最新的一手列表（不含优先购）
func GetLatestWillIssueItemList(ctx context.Context, limit int64) ([]*mongdb.IssueItem, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	now := util.Now()
	// 构建复合查询条件
	filter := bson.M{
		"status":     mongdb.IssueItemStatusOnSale, // 已上架
		"issue_time": bson.M{"$gt": now},           // 发行时间大于当前时间
		"$or": []bson.M{
			// 条件1：非优先购商品
			{"priority_buy.status": bson.M{"$ne": 1}},
			// 条件2：是优先购且还不能买的
			{
				"$and": []bson.M{
					{"priority_buy.status": 1}, // 优先购状态为1
					// 当前时间小于 issue_time 减去 advance_minutes 分钟
					{
						"$expr": bson.M{
							"$lt": []interface{}{
								now,
								bson.M{
									"$subtract": []interface{}{
										"$issue_time",
										bson.M{
											"$multiply": []interface{}{
												bson.M{"$ifNull": []interface{}{"$priority_buy.advance_minutes", 0}},
												60000, // 转换为毫秒
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}
	sort := bson.M{"issue_time": -1}
	issueItems, err := issueItemCollection.Finder().Filter(filter).Limit(limit).Sort(sort).Find(ctx)
	if err != nil {
		return nil, err
	}

	return issueItems, nil
}

// GetLatestAlreadyIssueItemList 获取可购买的的最新的一手列表（已发行或者优先购开始）
func GetLatestAlreadyIssueItemList(ctx context.Context, limit int64) ([]*mongdb.IssueItem, error) {
	issueItemCollection := mongox.NewCollection[mongdb.IssueItem](global.Tmt(), "issue_item")
	now := util.Now()
	// 构建复合查询条件
	filter := bson.M{
		"status": mongdb.IssueItemStatusOnSale, // 已上架
		"$or": []bson.M{
			// 条件1：发行时间小于当前时间
			{"issue_time": bson.M{"$lt": now}},
			// 条件2：优先购条件
			{
				"$and": []bson.M{
					{"priority_buy.status": 1}, // 优先购状态为1
					{
						"$expr": bson.M{
							"$and": []bson.M{
								// 当前时间大于 issue_time 减去 advance_minutes 分钟
								{
									"$gt": []interface{}{
										now,
										bson.M{
											"$subtract": []interface{}{
												"$issue_time",
												bson.M{
													"$multiply": []interface{}{
														bson.M{"$ifNull": []interface{}{"$priority_buy.advance_minutes", 0}},
														60000, // 转换为毫秒
													},
												},
											},
										},
									},
								},
								// 当前时间小于 issue_time
								{
									"$lt": []interface{}{now, "$issue_time"},
								},
							},
						},
					},
				},
			},
		},
	}
	sort := bson.M{"issue_time": -1}
	issueItems, err := issueItemCollection.Finder().Filter(filter).Limit(limit).Sort(sort).Find(ctx)
	if err != nil {
		return nil, err
	}

	return issueItems, nil
}
