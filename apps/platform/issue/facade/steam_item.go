package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/global"
	"context"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
)

func GetSteamItemByID(ctx context.Context, id string) (*mongdb.SteamItem, error) {
	steamItemCollection := mongox.NewCollection[mongdb.SteamItem](global.Tmt(), "steam_items")
	objectID, err := bson.ObjectIDFromHex(id)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Id(objectID).Build()
	steamItem, err := steamItemCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return steamItem, nil
}

func GetSteamItemMap(ctx context.Context, ids []string) (map[string]*mongdb.SteamItem, error) {
	if len(ids) == 0 {
		return nil, nil
	}
	issueItemCollection := mongox.NewCollection[mongdb.SteamItem](global.Tmt(), "steam_items")
	var objectIds []interface{}
	for _, itemId := range ids {
		objectId, err := bson.ObjectIDFromHex(itemId)
		if err != nil {
			return nil, err
		}
		objectIds = append(objectIds, objectId)
	}
	qw := query.NewBuilder().In("_id", objectIds...).Build()
	steamItems, err := issueItemCollection.Finder().Filter(qw).Find(ctx)
	if err != nil {
		return nil, err
	}
	steamItemMap := make(map[string]*mongdb.SteamItem, len(steamItems))
	for _, item := range steamItems {
		steamItemMap[item.ID.Hex()] = item
	}
	return steamItemMap, nil
}
