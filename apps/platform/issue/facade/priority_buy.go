package facade

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/apps/platform/issue/define"
	"app_service/global"
	"context"
	"github.com/chenmingyong0423/go-mongox/v2"
	"github.com/chenmingyong0423/go-mongox/v2/bsonx"
	"github.com/chenmingyong0423/go-mongox/v2/builder/query"
	"go.mongodb.org/mongo-driver/v2/bson"
)

func GetPriorityBuy(ctx context.Context, priorityBuyId string) (*mongdb.PriorityBuy, error) {
	priorityBuyCollection := mongox.NewCollection[mongdb.PriorityBuy](global.Tmt(), "priority_buy")
	priorityBuyObjectId, err := bson.ObjectIDFromHex(priorityBuyId)
	if err != nil {
		return nil, err
	}
	qw := query.NewBuilder().Id(priorityBuyObjectId).Build()
	priorityBuy, err := priorityBuyCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return nil, err
	}
	return priorityBuy, nil
}

// GetPriorityBuyStock 获取优先购总库存
func GetPriorityBuyStock(ctx context.Context, priorityBuyId string) (int32, error) {
	priorityBuyCollection := mongox.NewCollection[mongdb.PriorityBuy](global.Tmt(), "priority_buy")
	priorityBuyObjectId, err := bson.ObjectIDFromHex(priorityBuyId)
	if err != nil {
		return 0, err
	}
	qw := query.NewBuilder().Id(priorityBuyObjectId).Build()
	priorityBuy, err := priorityBuyCollection.Finder().Filter(qw).FindOne(ctx)
	if err != nil {
		return 0, err
	}
	issueItem, err := GetIssueItemByItemID(ctx, priorityBuy.ItemID.Hex())
	if err != nil {
		return 0, err
	}
	if issueItem.PriorityBuy.Status == mongdb.IssueItemPriorityBuyStatusOpen {
		if issueItem.PriorityBuy.Stock != nil {
			return *issueItem.PriorityBuy.Stock, nil
		}
		return 0, nil
	}
	if issueItem.Synthesis.Stock != nil {
		return *issueItem.Synthesis.Stock, nil
	}
	return 0, nil
}

func GetPriorityBuys(ctx context.Context, req *define.GetPriorityBuysReq) ([]*mongdb.PriorityBuy, int64, error) {
	priorityBuyCollection := mongox.NewCollection[mongdb.PriorityBuy](global.Tmt(), "priority_buy")
	qw := query.NewBuilder()
	if req.PriorityBuyId != nil && *req.PriorityBuyId != "" {
		priorityBuyObjectId, err := bson.ObjectIDFromHex(*req.PriorityBuyId)
		if err != nil {
			return nil, 0, err
		}
		qw.Id(priorityBuyObjectId)
	}
	if req.PriorityBuyName != nil && *req.PriorityBuyName != "" {
		qw.Eq("name", *req.PriorityBuyName)
	}
	if req.Creator != nil && *req.Creator != "" {
		creatorObjectId, err := bson.ObjectIDFromHex(*req.Creator)
		if err != nil {
			return nil, 0, err
		}
		qw.Eq("creator", creatorObjectId)
	}
	qw.Eq("obtain_way", 2) // 2:为合成类型
	qw.In("status", mongdb.PriorityBuyStatusUp, mongdb.PriorityBuyStatusSnapshot)
	priorityBuys, err := priorityBuyCollection.Finder().Filter(qw.Build()).
		Sort(bsonx.NewD().Add("_id", -1).Build()).
		Limit(int64(req.GetPageSize())).
		Skip(int64(req.GetOffset())).
		Find(ctx)
	if err != nil {
		return nil, 0, err
	}
	count, err := priorityBuyCollection.Finder().Filter(qw.Build()).Count(ctx)
	if err != nil {
		return nil, 0, err
	}
	return priorityBuys, count, nil
}
