package define

import "app_service/pkg/pagination"

type GetItemStoryListReq struct {
	pagination.Pagination
	// 物品ID
	ItemId *string `form:"item_id" json:"item_id"`
	// 物品名称
	ItemName *string `form:"item_name" json:"item_name"`
}

type GetItemSynthesisListReq struct {
	pagination.Pagination
	// 物品ID
	ItemId *string `form:"item_id" json:"item_id"`
	// 物品名称
	ItemName *string `form:"item_name" json:"item_name"`
}
