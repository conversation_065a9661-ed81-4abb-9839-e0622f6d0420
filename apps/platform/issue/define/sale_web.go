package define

type (
	GetSaleMainReq struct {
		ItemId string `json:"item_id"`
	}
	GetSaleMainResp struct {
		ItemId   string    `json:"item_id"`
		ItemName string    `json:"item_name"`
		MatchId  string    `json:"match_id"`
		IconUrl  string    `json:"icon_url"`
		IpInfo   []*IpInfo `json:"ip_info"`

		IssueCount           int32 `json:"issue_count"`             //发行数量
		HolderQuantity       int32 `json:"holder_quantity"`         //流通数量
		MarketAmount         int32 `json:"market_amount"`           //市值
		MaxPrice             int32 `json:"max_price"`               //今日最高限价
		MinPrice             int32 `json:"min_price"`               //今日最低限价
		LowestPrice          int32 `json:"lowest_price"`            //今日最低成交价
		HighestPrice         int32 `json:"highest_price"`           //今日最高成交价
		LastOrderAmount      int32 `json:"last_order_amount"`       //最近成交价
		SaleOrderCount       int32 `json:"sale_order_count"`        //成交数
		TotalSaleOrderAmount int32 `json:"total_sale_order_amount"` //成交额
	}
	IpInfo struct {
		IpId   string `json:"ip_id"`
		IpName string `json:"ip_name"`
	}
)
