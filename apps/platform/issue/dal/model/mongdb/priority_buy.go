package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

// PriorityBuyStatusEnum 定义优先购状态枚举
type PriorityBuyStatusEnum int

const (
	// PriorityBuyStatusWaiting 待上架
	PriorityBuyStatusWaiting PriorityBuyStatusEnum = 0
	// PriorityBuyStatusUp 已上架
	PriorityBuyStatusUp PriorityBuyStatusEnum = 1
	// PriorityBuyStatusSnapshot 已快照
	PriorityBuyStatusSnapshot PriorityBuyStatusEnum = 2
	// PriorityBuyStatusDown 已下架
	PriorityBuyStatusDown PriorityBuyStatusEnum = 3
	// PriorityBuyStatusEnd 已结束
	PriorityBuyStatusEnd PriorityBuyStatusEnum = 4
)

// PriorityBuyWhiteList 优先购权益白名单
type PriorityBuyWhiteList struct {
	UserID   bson.ObjectID `bson:"user_id"`  // 用户ID
	Quantity int           `bson:"quantity"` // 数量
}

// PriorityBuyMatchRule 优先购匹配条件规则
type PriorityBuyMatchRule struct {
	ItemID       bson.ObjectID `bson:"item_id"`       // 商品ID
	HeldQuantity int           `bson:"held_quantity"` // 持有数量
}

// PriorityBuyMatchCondition 优先购匹配条件
type PriorityBuyMatchCondition struct {
	MatchRules               [][]PriorityBuyMatchRule `bson:"match_rules"`                 // 匹配规则
	MatchOverlayType         *int                     `bson:"match_overlay_type"`          // 叠加类型
	Quantity                 *int                     `bson:"quantity"`                    // 权益数量
	SnapshotTime             time.Time                `bson:"snapshot_time"`               // 系统快照时间
	SnapshotDisplayTimeStart time.Time                `bson:"snapshot_display_time_start"` // 快照展示开始时间
	SnapshotDisplayTimeEnd   time.Time                `bson:"snapshot_display_time_end"`   // 快照展示结束时间
}

// PriorityBuy 优先购权益表
type PriorityBuy struct {
	ID                bson.ObjectID              `bson:"_id,omitempty"`
	Status            PriorityBuyStatusEnum      `bson:"status"`                    // 状态
	Name              string                     `bson:"name"`                      // 名称
	ObtainWay         int                        `bson:"obtain_way"`                // 获取方式
	SpecifiedItemType int                        `bson:"specified_item_type"`       // 指定商品类型: 0:全部商品 1:指定商品
	ItemID            *bson.ObjectID             `bson:"item_id,omitempty"`         // 商品ID
	IssueItemID       *bson.ObjectID             `bson:"issue_item_id,omitempty"`   // 发行商品编码
	ExpireTime        time.Time                  `bson:"expire_time"`               // 权益失效时间
	ActiveDetail      string                     `bson:"active_detail"`             // 权益激活详情
	UsageInstructions string                     `bson:"usage_instructions"`        // 权益使用说明
	WhiteList         []PriorityBuyWhiteList     `bson:"white_list"`                // 白名单
	MatchCondition    *PriorityBuyMatchCondition `bson:"match_condition,omitempty"` // 优先购匹配条件
	UpTime            *time.Time                 `bson:"up_time,omitempty"`         // 上架时间
	Creator           *bson.ObjectID             `bson:"creator,omitempty"`         // 创建人
	CreatedAt         time.Time                  `bson:"created_at"`                // 创建时间
	UpdatedAt         time.Time                  `bson:"updated_at"`                // 更新时间
}
