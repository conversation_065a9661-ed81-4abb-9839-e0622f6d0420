package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type IPInfo struct {
	ID   bson.ObjectID `bson:"_id,omitempty"`
	Name string        `bson:"name,omitempty"`
}

type ItemClassifyInfo struct {
	ID   bson.ObjectID `bson:"_id,omitempty"`
	Name string        `bson:"name,omitempty"`
}

type TrademarkInfo struct {
	ID    bson.ObjectID `bson:"_id,omitempty"`
	Icon  string        `bson:"icon,omitempty"`
	Level int           `bson:"level,omitempty"`
	Name  string        `bson:"name,omitempty"`
}

type Match struct {
	ID               bson.ObjectID      `bson:"_id,omitempty"`
	FID              bson.ObjectID      `bson:"fid,omitempty"`
	Name             string             `bson:"name,omitempty"`
	IPInfo           []IPInfo           `bson:"ip_info,omitempty"`
	IsTop            bool               `bson:"is_top,omitempty"`
	ItemClassifyInfo []ItemClassifyInfo `bson:"item_classify_info,omitempty"`
	ItemSource       int                `bson:"item_source,omitempty"`
	Keyword          string             `bson:"keyword,omitempty"`
	LastOrderTime    time.Time          `bson:"last_order_time,omitempty"`
	LikesTotal       int                `bson:"likes_total,omitempty"`
	OriginPrice      int                `bson:"origin_price,omitempty"`
	SellPrice        int                `bson:"sell_price,omitempty"`
	Status           int                `bson:"status,omitempty"`
	TopIndex         int                `bson:"top_index,omitempty"`
	TrademarkInfo    []TrademarkInfo    `bson:"trademark_info,omitempty"`
	Type             int                `bson:"type,omitempty"`
	SaleOrderCount   int                `bson:"sale_order_count,omitempty"`
	CreatedAt        time.Time          `bson:"created_at,omitempty"`
	UpdatedAt        time.Time          `bson:"updated_at,omitempty"`
}
