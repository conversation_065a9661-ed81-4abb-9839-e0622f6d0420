package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

// IssueItemOrderStatusEnum 订单状态枚举
type IssueItemOrderStatusEnum int32

const (
	IssueItemOrderStatusClose    IssueItemOrderStatusEnum = -1 // 交易关闭
	IssueItemOrderStatusPending  IssueItemOrderStatusEnum = 0  // 待支付
	IssueItemOrderStatusComplete IssueItemOrderStatusEnum = 1  // 交易完成
)

// IssueItemOrder 发行商品订单结构体
type IssueItemOrder struct {
	// MongoDB 默认的 _id 字段
	ID bson.ObjectID `bson:"_id,omitempty" json:"id,omitempty"`

	// 订单类型
	OrderType int `bson:"order_type" json:"order_type"`

	// 用户id
	UserID bson.ObjectID `bson:"user_id" json:"user_id"`

	// 发行商品id
	IssueItemID bson.ObjectID `bson:"issue_item_id" json:"issue_item_id"`

	// 商品ID
	ItemID bson.ObjectID `bson:"item_id" json:"item_id"`

	// 商品名称
	ItemName string `bson:"item_name" json:"item_name"`

	// 商品主图
	ImageURL string `bson:"image_url" json:"image_url"`

	// 商品规格
	ItemSpecs string `bson:"item_specs" json:"item_specs"`

	// sku_no
	SkuNo string `bson:"sku_no" json:"sku_no"`

	// 发行价格
	Price int64 `bson:"price" json:"price"`

	// 数量
	Quantity int `bson:"quantity" json:"quantity"`

	// 支付金额
	PayAmount int64 `bson:"pay_amount" json:"pay_amount"`

	// 状态
	Status IssueItemOrderStatusEnum `bson:"status" json:"status"`

	// 支付时间
	PayTime *time.Time `bson:"pay_time,omitempty" json:"pay_time,omitempty"`

	// 订单过期时间
	ExpireTime *time.Time `bson:"expire_time,omitempty" json:"expire_time,omitempty"`

	// 链hash
	ChainHash string `bson:"chain_hash,omitempty" json:"chain_hash,omitempty"`

	// 优先购权益ID
	PriorityBuyID *bson.ObjectID `bson:"priority_buy_id,omitempty" json:"priority_buy_id,omitempty"`

	// 链dataId
	ChainDataID string `bson:"chain_dataId,omitempty" json:"chain_dataId,omitempty"`

	// 用户删除标记
	UserDel int `bson:"user_del" json:"user_del"`

	// 创建时间
	CreatedAt time.Time `bson:"created_at" json:"created_at"`

	// 更新时间
	UpdatedAt time.Time `bson:"updated_at" json:"updated_at"`
}
