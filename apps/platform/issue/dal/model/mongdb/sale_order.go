package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type (
	SaleOrderStatusEnum int32
)

const (
	SaleOrderStatusDelete  SaleOrderStatusEnum = -1 // 删除
	SaleOrderStatusSellOut SaleOrderStatusEnum = 0  // 已出售
	SaleOrderStatusClose   SaleOrderStatusEnum = 1  // 已取消
	SaleOrderStatusSell    SaleOrderStatusEnum = 2  // 出售中
	SaleOrderStatusOff     SaleOrderStatusEnum = 3  // 已下架
	SaleOrderStatusSelling SaleOrderStatusEnum = 4  // 交易中
	SaleOrderStatusWaiting SaleOrderStatusEnum = 5  // 上架中

	SaleOrderStatusBusinessClose   SaleOrderStatusEnum = 10 // 商人-已取消
	SaleOrderStatusBusinessSell    SaleOrderStatusEnum = 11 // 商人-出售中
	SaleOrderStatusBusinessSellOut SaleOrderStatusEnum = 12 // 商人-已出售
)

type Extends struct {
	BusinessOrderID     bson.ObjectID `bson:"business_order_id,omitempty"`
	RobotOrderID        bson.ObjectID `bson:"robot_order_id,omitempty"`
	RobotOrderTime      *time.Time    `bson:"robot_order_time,omitempty"`
	InfoFee             float64       `bson:"info_fee,omitempty"`
	AllowFee            float64       `bson:"allow_fee,omitempty"`
	SaleBatchID         bson.ObjectID `bson:"sale_batch_id,omitempty"`
	IsPreferential      bool          `bson:"is_preferential,omitempty"`
	OrderName           string        `bson:"order_name,omitempty"`
	OpenBox             int32         `bson:"open_box,omitempty"`
	Appid               int32         `bson:"appid,omitempty"`
	UserType            int32         `bson:"user_type,omitempty"`
	IsUseReferencePrice bool          `bson:"is_use_reference_price,omitempty"`
	IsBulkSale          bool          `bson:"is_bulk_sale,omitempty"`
	Profit              float64       `bson:"profit,omitempty"`
	Price               float64       `bson:"price,omitempty"`
	SellPrice           float64       `bson:"sell_price,omitempty"`
	PlatformSource      string        `bson:"platform_source,omitempty"`
}

type Bargain struct {
	Amount     float64       `bson:"amount,omitempty"`
	UserID     bson.ObjectID `bson:"user_id,omitempty"`
	Tip        bool          `bson:"tip,omitempty"`
	CreateTime *time.Time    `bson:"create_time,omitempty"`
	AgreeTime  *time.Time    `bson:"agree_time,omitempty"`
}

type RecoveryLock struct {
	RecoveryLockTime    *time.Time `bson:"recovery_lock_time,omitempty"`
	RecoveryPredictTime *time.Time `bson:"recovery_predict_time,omitempty"`
	RecoveryConfigID    string     `bson:"recovery_config_id,omitempty"`
}

type SaleOrder struct {
	ID            bson.ObjectID   `bson:"_id,omitempty"`
	SaleUserID    bson.ObjectID   `bson:"sale_user_id,omitempty"`
	NftItemID     bson.ObjectID   `bson:"nft_item_id,omitempty"`
	UserItemID    bson.ObjectID   `bson:"user_item_id,omitempty"`
	UserItemIDs   []bson.ObjectID `bson:"user_item_ids,omitempty"`
	ItemID        bson.ObjectID   `bson:"item_id,omitempty"`
	BuyUserID     bson.ObjectID   `bson:"buy_user_id,omitempty"`
	ExamineStatus int32           `bson:"examine_status,omitempty"`
	OrderStatus   int32           `bson:"order_status,omitempty"`
	OrderType     int32           `bson:"order_type"`
	RejectExplain string          `bson:"reject_explain,omitempty"`
	SaleFrom      int32           `bson:"sale_from"`
	OrderTiem     *time.Time      `bson:"order_tiem,omitempty"`
	TipFee        float64         `bson:"tip_fee,omitempty"`
	OrderAmount   int32           `bson:"order_amount"`
	SaleCount     int32           `bson:"sale_count"`
	OperationID   bson.ObjectID   `bson:"operation_id,omitempty"`
	LikesCount    int32           `bson:"likes_count,omitempty"`
	RecoveryTime  *time.Time      `bson:"recovery_time,omitempty"`
	RecoveryLock  *RecoveryLock   `bson:"recovery_lock,omitempty"`
	RobotUserID   bson.ObjectID   `bson:"robot_user_id,omitempty"`
	DelUser       []bson.ObjectID `bson:"del_user,omitempty"`
	Extends       *Extends        `bson:"extends,omitempty"`
	Bargain       *Bargain        `bson:"bargain,omitempty"`
	CreatedAt     time.Time       `bson:"created_at"`
	UpdatedAt     time.Time       `bson:"updated_at"`
}
