package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type SteamItem struct {
	ID              bson.ObjectID         `bson:"_id,omitempty"`
	Status          int32                 `bson:"status"`
	ItemName        string                `bson:"item_name"`
	Type            string                `bson:"type,omitempty"`
	NameColor       string                `bson:"name_color,omitempty"`
	BackgroundColor string                `bson:"background_color,omitempty"`
	IconURL         string                `bson:"icon_url"`
	AppID           int32                 `bson:"appid"`
	Extends         *Extends              `bson:"extends,omitempty"`
	MarketPrices    SteamItemMarketPrices `bson:"market_prices,omitempty"`
	MarketName      string                `bson:"market_name,omitempty"`

	// 电商字段
	Description string   `bson:"description,omitempty"`
	Detail      string   `bson:"detail,omitempty"`
	DetailH5    string   `bson:"detail_h5,omitempty"`
	ImageInfos  []string `bson:"image_infos,omitempty"`

	// 关联信息
	TrademarkID     bson.ObjectID   `bson:"trademark_id,omitempty"`
	ItemClassifyIDs []bson.ObjectID `bson:"item_classify_ids,omitempty"`
	IPClassifyIDs   []bson.ObjectID `bson:"ip_classify_ids,omitempty"`
	TrademarkIDs    []bson.ObjectID `bson:"trademark_ids,omitempty"`

	// NFT详情
	NFTDetail     *SteamItemNFTDetail `bson:"nft_detail,omitempty"`
	ShowNFTDetail int32               `bson:"show_nft_detail,omitempty"`

	// 关联信息结构体
	IPInfo           []IPInfo           `bson:"ip_info,omitempty"`
	TrademarkInfo    []TrademarkInfo    `bson:"trademark_info,omitempty"`
	ItemClassifyInfo []ItemClassifyInfo `bson:"item_classify_info,omitempty"`

	// 3D模型
	Model3DDetail SteamItemModel3DDetail   `bson:"model_3d_detail,omitempty"`
	ItemSource    int32                    `bson:"item_source"`
	RecommendInfo SteamItemRecommendInfo   `bson:"recommend_info,omitempty"`
	TMTShow       bool                     `bson:"tmt_show"`
	Specification []SteamItemSpecification `bson:"specification,omitempty"`
	CreatedAt     time.Time                `bson:"created_at,omitempty"`
	UpdatedAt     time.Time                `bson:"updated_at,omitempty"`
	SkuNo         string                   `bson:"sku_no,omitempty"`
	Specs         string                   `bson:"specs,omitempty"`
}

// 嵌套结构体定义
type SteamItemExtends struct {
	ShortName       string        `bson:"short_name,omitempty"`
	Type            string        `bson:"type,omitempty"`
	TypeTxt         string        `bson:"type_txt,omitempty"`
	Exterior        string        `bson:"exterior,omitempty"`
	ExteriorTxt     string        `bson:"exterior_txt,omitempty"`
	Quality         string        `bson:"quality,omitempty"`
	QualityTxt      string        `bson:"quality_txt,omitempty"`
	Category        string        `bson:"category,omitempty"`
	CategoryTxt     string        `bson:"category_txt,omitempty"`
	ClassID         string        `bson:"classid,omitempty"`
	InstanceID      string        `bson:"instanceid,omitempty"`
	Commodity       int32         `bson:"commodity,omitempty"`
	Tradable        int32         `bson:"tradable,omitempty"`
	IsPresell       bool          `bson:"is_presell,omitempty"`
	SellTime        time.Time     `bson:"sell_time,omitempty"`
	ClassifyID      []interface{} `bson:"classify_id,omitempty"`
	SupplierID      bson.ObjectID `bson:"supplier_id,omitempty"`
	ShipType        int32         `bson:"ship_type,omitempty"`
	IsStockWarned   int32         `bson:"is_stock_warned,omitempty"`
	IsWantBuy       int32         `bson:"is_want_buy,omitempty"`
	WantBuyMaxLimit int32         `bson:"want_buy_max_limit,omitempty"`
	WantBuyMinLimit int32         `bson:"want_buy_min_limit,omitempty"`
}

type SteamItemSpecification struct {
	SpecificationNo    string `bson:"specification_no,omitempty"`
	SpecificationName  string `bson:"specification_name,omitempty"`
	SpecificationValue string `bson:"specification_value,omitempty"`
}

type SteamItemNFTDetail struct {
	NFTDescription        string `bson:"nft_description,omitempty"`
	ContractAddress       string `bson:"contract_address,omitempty"`
	CertificationMark     string `bson:"certification_mark,omitempty"`
	CertificationStandard string `bson:"certification_standard,omitempty"`
	CertificationNetwork  string `bson:"certification_network,omitempty"`
	StockTotal            int32  `bson:"stock_total,omitempty"`
}

type SteamItemTrademarkInfo struct {
	ID    bson.ObjectID `bson:"_id"`
	Name  string        `bson:"name"`
	Icon  string        `bson:"icon,omitempty"`
	Level int32         `bson:"level,omitempty"`
}

type SteamItemIPInfo struct {
	ID   bson.ObjectID `bson:"_id"`
	Name string        `bson:"name"`
	Icon string        `bson:"icon,omitempty"`
}

type SteamItemItemClassifyInfo struct {
	ID   bson.ObjectID `bson:"_id"`
	Name string        `bson:"name"`
	Icon string        `bson:"icon,omitempty"`
}

type SteamItemModel3DDetail struct {
	IsAdd3DModel     int32    `bson:"is_add_3d_model"`
	Model3D          []bson.M `bson:"model_3d,omitempty"`
	Model3DGif       string   `bson:"model_3d_gif,omitempty"`
	BackgroundGif    string   `bson:"background_gif,omitempty"`
	Model3DFirstIcon string   `bson:"model_3d_first_icon,omitempty"`
}

type SteamItemRecommendInfo struct {
	RecommendDisplay int32     `bson:"recommend_display"`
	OpenTime         time.Time `bson:"open_time,omitempty"`
}

type SteamItemMarketPrices struct {
	ZeroSup MarketPricesZeroSup `bson:"zero_sup"`
	Unx     MarketPricesUnx     `bson:"unx"`
}

type MarketPricesZeroSup struct {
	SellPrice    int32 `bson:"sell_price"`
	MinPrice     int32 `bson:"min_price"`
	SupSellPrice int32 `bson:"sup_sell_price"`
	SupMinPrice  int32 `bson:"sup_min_price"`
	SellListings int32 `bson:"sell_listings"`
}

type MarketPricesUnx struct {
	SellPrice    int32 `bson:"sell_price"`
	SellListings int32 `bson:"sell_listings"`
}
