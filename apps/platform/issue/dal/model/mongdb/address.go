package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type Address struct {
	ID          bson.ObjectID `bson:"_id,omitempty"`    // 主键（自动生成）
	Status      int           `bson:"status"`           // 状态
	UserID      bson.ObjectID `bson:"user_id"`          // 用户ID
	Name        string        `bson:"name"`             // 收件人
	MobilePhone string        `bson:"mobile_phone"`     // 手机号
	Code        string        `bson:"code"`             // 邮政编码
	Area        string        `bson:"area"`             // 简要地址
	Place       string        `bson:"place"`            // 详细地址
	Remark      string        `bson:"remark,omitempty"` // 备注（可选）
	IsDefault   bool          `bson:"is_default"`       // 默认地址
	CreatedAt   time.Time     `bson:"created_at"`       // 创建时间
	UpdatedAt   time.Time     `bson:"updated_at"`       // 更新时间
}
