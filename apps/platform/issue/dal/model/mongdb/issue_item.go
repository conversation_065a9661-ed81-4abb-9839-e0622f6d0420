package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

// 定义所有枚举类型
type (
	IssueItemTypeEnum              int32
	IssueItemCategoryEnum          int32
	IssueItemAuditStatusEnum       int32
	IssueItemStatusEnum            int32
	CirculationStatusEnum          int32
	CirculationEndShowTypeEnum     int32
	IssueItemPriorityBuyStatusEnum int32
	IssueItemSynthesisStatusEnum   int32
	SaleModeEnum                   int32
	IssueDateEnum                  int32
	IssueItemStoryStatusEnum       int32
)

// 枚举值定义
const (
	// IssueItemTypeActual IssueItemTypeEnum 商品类型
	IssueItemTypeActual  IssueItemTypeEnum = 0 // 实物商品
	IssueItemTypeDigital IssueItemTypeEnum = 1 // 数字商品

	// IssueItemCategoryTrendyPlay IssueItemCategoryEnum 商品类目
	IssueItemCategoryTrendyPlay IssueItemCategoryEnum = 0 // 潮流玩物

	// IssueItemAuditStatusToBeSubmitted IssueItemAuditStatusEnum 审核状态
	IssueItemAuditStatusToBeSubmitted IssueItemAuditStatusEnum = 0 // 待提交
	IssueItemAuditStatusPending       IssueItemAuditStatusEnum = 1 // 待审核
	IssueItemAuditStatusRecall        IssueItemAuditStatusEnum = 2 // 已撤回
	IssueItemAuditStatusApproved      IssueItemAuditStatusEnum = 3 // 审核通过
	IssueItemAuditStatusRejected      IssueItemAuditStatusEnum = 4 // 审核拒绝

	// IssueItemStatusInitial IssueItemStatusEnum 商品状态
	IssueItemStatusInitial IssueItemStatusEnum = -1 // 未上架
	IssueItemStatusOffSale IssueItemStatusEnum = 0  // 已下架
	IssueItemStatusOnSale  IssueItemStatusEnum = 1  // 已上架

	// CirculationStatusProhibit CirculationStatusEnum 流通状态
	CirculationStatusProhibit CirculationStatusEnum = 2 // 禁止流通
	CirculationStatusAllow    CirculationStatusEnum = 1 // 允许流通

	CirculationEndShowTypeHidden   CirculationEndShowTypeEnum = 1 // 不在交易市场中展示
	CirculationEndShowTypeDelisted CirculationEndShowTypeEnum = 2 // 展示为已退市

	// IssueItemPriorityBuyStatusClose IssueItemPriorityBuyStatusEnum 优先购状态
	IssueItemPriorityBuyStatusClose IssueItemPriorityBuyStatusEnum = 0 // 关闭
	IssueItemPriorityBuyStatusOpen  IssueItemPriorityBuyStatusEnum = 1 // 开启

	// IssueItemSynthesisStatusClose IssueItemSynthesisStatusEnum 合成状态
	IssueItemSynthesisStatusClose IssueItemSynthesisStatusEnum = 0 // 关闭
	IssueItemSynthesisStatusOpen  IssueItemSynthesisStatusEnum = 1 // 开启

	// IssueItemStoryStatusClose IssueItemStoryStatusEnum 故事玩法状态
	IssueItemStoryStatusClose IssueItemStoryStatusEnum = 0 // 关闭
	IssueItemStoryStatusOpen  IssueItemStoryStatusEnum = 1 // 开启

	// SaleModeNormal SaleModeEnum 销售模式
	SaleModeNormal  SaleModeEnum = 0 // 普通销售
	SaleModeAuction SaleModeEnum = 1 // 拍卖

	// IssueDateToday IssueDateEnum 发行日期
	IssueDateToday    IssueDateEnum = 0 // 今天
	IssueDateTomorrow IssueDateEnum = 1 // 明天
	IssueDateNextWeek IssueDateEnum = 2 // 下周
)

// OperationLog 操作日志
type OperationLog struct {
	Time      time.Time     `bson:"time"`       // 操作时间
	AdminID   bson.ObjectID `bson:"admin_id"`   // 操作管理员ID
	AdminName string        `bson:"admin_name"` // 操作管理员名称
	Content   string        `bson:"content"`    // 操作内容
}

// PriceRange 价格范围
type PriceRange struct {
	MaxRatio int32 `bson:"max_ratio"` // 最高涨幅
	MinRatio int32 `bson:"min_ratio"` // 最大跌幅
}

// PriceLimit 价格限制
type PriceLimit struct {
	MaxRatio int32 `bson:"max_ratio"` // 上限
	MinRatio int32 `bson:"min_ratio"` // 下限
}

// SaleStockLimit 公售限量配置
type SaleStockLimit struct {
	LimitType int32  `bson:"limit_type"`      // 限制类型
	UsedStock int32  `bson:"used_stock"`      // 已用库存
	Stock     *int32 `bson:"stock,omitempty"` // 库存
}

// PriorityBuyItem  优先购配置
type PriorityBuyItem struct {
	Status         IssueItemPriorityBuyStatusEnum `bson:"status"`                    // 优先购状态
	Stock          *int32                         `bson:"stock,omitempty"`           // 优先购库存
	UsedStock      int32                          `bson:"used_stock"`                // 优先购已用库存
	AdvanceMinutes *int64                         `bson:"advance_minutes,omitempty"` // 优先购开始时间
}

// Story 故事探索配置
type Story struct {
	Status IssueItemStoryStatusEnum `bson:"status"` // 故事探索配置状态
}

// Synthesis 合成配置
type Synthesis struct {
	Status    IssueItemSynthesisStatusEnum `bson:"status"`          // 合成状态
	UsedStock int32                        `bson:"used_stock"`      // 已用库存
	Stock     *int32                       `bson:"stock,omitempty"` // 库存
}

// IssueItem 发行商品
type IssueItem struct {
	ID                     bson.ObjectID              `bson:"_id,omitempty"`                       // 文档ID
	ItemID                 bson.ObjectID              `bson:"item_id"`                             // 商品ID
	SkuNo                  string                     `bson:"sku_no"`                              // SKU编号
	ItemName               string                     `bson:"item_name"`                           // 商品名称
	ItemType               IssueItemTypeEnum          `bson:"item_type"`                           // 商品类型
	ItemCategory           IssueItemCategoryEnum      `bson:"item_category"`                       // 商品类目
	IPClassifyIDs          []bson.ObjectID            `bson:"ip_classify_ids,omitempty"`           // 商品IP分类ID
	IPClassifyNames        []string                   `bson:"ip_classify_names,omitempty"`         // 商品IP分类名称
	ItemSpecs              string                     `bson:"item_specs"`                          // 商品规格
	IssuerName             string                     `bson:"issuer_name,omitempty"`               // 发行方全称
	IssuerShortName        string                     `bson:"issuer_short_name,omitempty"`         // 发行方简称
	SellerName             string                     `bson:"seller_name,omitempty"`               // 销售方全称
	SellerShortName        string                     `bson:"seller_short_name,omitempty"`         // 销售方简称
	CopyrightName          string                     `bson:"copyright_name"`                      // 版权方全称
	CopyrightNo            string                     `bson:"copyright_no"`                        // 版权号
	Quantity               int32                      `bson:"quantity"`                            // 发行数量
	Price                  int32                      `bson:"price"`                               // 发行价格
	IssueDate              IssueDateEnum              `bson:"issue_date"`                          // 发行日期
	IssueTime              *time.Time                 `bson:"issue_time,omitempty"`                // 发行时间
	SaleMode               SaleModeEnum               `bson:"sale_mode"`                           // 销售模式
	DeliveryTime           *time.Time                 `bson:"delivery_time,omitempty"`             // 提货时间
	ImageURL               string                     `bson:"image_url"`                           // 商品主图
	ImageInfos             []string                   `bson:"image_infos,omitempty"`               // 商品展示图
	AdIDs                  []string                   `bson:"ad_ids,omitempty"`                    // 广告商推送
	Detail                 string                     `bson:"detail,omitempty"`                    // 商品详情
	AuditStatus            IssueItemAuditStatusEnum   `bson:"audit_status"`                        // 审核状态
	AdminID                bson.ObjectID              `bson:"admin_id"`                            // 创建人id
	Status                 IssueItemStatusEnum        `bson:"status"`                              // 状态
	SubmitTime             *time.Time                 `bson:"submit_time,omitempty"`               // 提审时间
	ReviewAdminID          *bson.ObjectID             `bson:"review_admin_id,omitempty"`           // 审核admin_id
	ReviewTime             *time.Time                 `bson:"review_time,omitempty"`               // 审核时间
	ShelfTime              *time.Time                 `bson:"shelf_time,omitempty"`                // 上架时间
	SaleEnd                *time.Time                 `bson:"sale_end,omitempty"`                  // 首发销售结束时间
	SaleLimit              int32                      `bson:"sale_limit"`                          // 首发限购
	CirculationStatus      CirculationStatusEnum      `bson:"circulation_status"`                  // 流通状态
	CirculationStart       *time.Time                 `bson:"circulation_start,omitempty"`         // 流通开始时间
	CirculationEnd         *time.Time                 `bson:"circulation_end,omitempty"`           // 流通结束时间
	CirculationEndShowType CirculationEndShowTypeEnum `bson:"circulation_end_show_type,omitempty"` // 流通结束后的展示方式
	SalesVolume            int32                      `bson:"sales_volume"`                        // 销量
	AirdropQuantity        int32                      `bson:"airdrop_quantity"`                    // 空投数量
	ChainHash              string                     `bson:"chain_hash,omitempty"`                // 链hash
	ChainDataID            string                     `bson:"chain_dataId,omitempty"`              // 链dataId
	PriceControl           int32                      `bson:"price_control"`                       // 商品价格控制开关
	PriceLimit             *PriceLimit                `bson:"price_limit,omitempty"`               // 出价限额
	PriceRange             *PriceRange                `bson:"price_range,omitempty"`               // 涨跌限幅
	SaleStockLimit         SaleStockLimit             `bson:"sale_stock_limit"`                    // 公售限量配置
	PriorityBuy            PriorityBuyItem            `bson:"priority_buy"`                        // 优先购配置
	Synthesis              Synthesis                  `bson:"synthesis"`                           // 合成配置
	Story                  Story                      `bson:"story"`                               // 故事探索配置
	OperationLogs          []OperationLog             `bson:"operation_logs"`                      // 操作日志
	CreatedAt              time.Time                  `bson:"created_at"`                          // 创建时间
	UpdatedAt              time.Time                  `bson:"updated_at"`                          // 更新时间
}
