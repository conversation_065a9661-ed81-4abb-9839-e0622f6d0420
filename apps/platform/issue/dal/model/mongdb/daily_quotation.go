package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type DailyQuotation struct {
	ID           bson.ObjectID `bson:"_id,omitempty"`           // 文档唯一标识符
	Date         string        `bson:"date,omitempty"`          // 日期（格式：YYYY-MM-DD）
	ItemID       bson.ObjectID `bson:"item_id,omitempty"`       // 商品 ID
	ClosePrice   int32         `bson:"close_price,omitempty"`   // 收盘价（单位：分）
	CreatedAt    time.Time     `bson:"created_at,omitempty"`    // 创建时间
	HighestPrice int32         `bson:"highest_price,omitempty"` // 最高价（单位：分）
	LowestPrice  int32         `bson:"lowest_price,omitempty"`  // 最低价（单位：分）
	UpdatedAt    time.Time     `bson:"updated_at,omitempty"`    // 更新时间
	Volume       int32         `bson:"volume,omitempty"`        // 交易量
}
