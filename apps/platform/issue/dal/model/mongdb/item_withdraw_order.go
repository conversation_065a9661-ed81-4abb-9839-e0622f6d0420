package mongdb

import (
	"go.mongodb.org/mongo-driver/v2/bson"
	"time"
)

type (
	ItemWithdrawOrderStatusEnum int32
)

const (
	// 已删除订单
	ItemWithdrawOrderStatusDeleted ItemWithdrawOrderStatusEnum = -1
	// 无效订单
	ItemWithdrawOrderStatusExpired ItemWithdrawOrderStatusEnum = 0
)

const (
	// 订单已完成
	ItemWithdrawOrderStatusDone ItemWithdrawOrderStatusEnum = iota + 1 // 从1开始自增
	// 售后中
	ItemWithdrawOrderStatusAftersaleing
	// 退款中
	ItemWithdrawOrderStatusAfterSaleRefunding
	// 已退款
	ItemWithdrawOrderStatusAfterSaleDone
)

const (
	// 已下单
	ItemWithdrawOrderStatusCreated ItemWithdrawOrderStatusEnum = 10
	// 物品已锁定
	ItemWithdrawOrderStatusLocked ItemWithdrawOrderStatusEnum = 20
	// 物品已检查
	ItemWithdrawOrderStatusChecked ItemWithdrawOrderStatusEnum = 30
	// 等待付款
	ItemWithdrawOrderStatusWaitForPay ItemWithdrawOrderStatusEnum = 35
	// 等待发货
	ItemWithdrawOrderStatusWaitForShip ItemWithdrawOrderStatusEnum = 36
	// 已发货
	ItemWithdrawOrderStatusShiped ItemWithdrawOrderStatusEnum = 40
	// 已收货
	ItemWithdrawOrderStatusReceived ItemWithdrawOrderStatusEnum = 50
	// 等待商品制作完成
	ItemWithdrawOrderStatusWaitForMake ItemWithdrawOrderStatusEnum = 60
	// 等待支付尾款
	ItemWithdrawOrderStatusWaitForDue ItemWithdrawOrderStatusEnum = 61
	// 已拆单
	ItemWithdrawOrderStatusSplitOrder ItemWithdrawOrderStatusEnum = 62
)

// 主订单结构
type ItemWithdrawOrder struct {
	ID         bson.ObjectID `bson:"_id,omitempty"`
	Status     int32         `bson:"status"` // 对应 ItemWithdrawOrderStatusEnum
	ItemID     bson.ObjectID `bson:"item_id,omitempty"`
	UserID     bson.ObjectID `bson:"user_id"`
	SaleUserID bson.ObjectID `bson:"sale_user_id,omitempty"`

	// 金额相关字段
	PayAmount       float64 `bson:"pay_amount"`
	ShouldPayAmount float64 `bson:"should_pay_amount,omitempty"`

	// 审核相关
	NeedReview  bool            `bson:"need_review"`
	AdminRemark string          `bson:"admin_remark,omitempty"`
	DelUser     []bson.ObjectID `bson:"del_user,omitempty"`

	// 市场信息
	MarketName    string `bson:"market_name,omitempty"`
	MarketOrderID string `bson:"market_order_id,omitempty"`

	// 时间戳（自动生成）
	CreatedAt time.Time `bson:"created_at"`
	UpdatedAt time.Time `bson:"updated_at"`

	// 扩展结构（对应 extendsSchema）
	Extends OrderExtends `bson:"extends,omitempty"`

	// 最新成交价（查询时重命名）
	LastPrice int32 `bson:"last_price,omitempty"`
}

// 扩展订单结构
type OrderExtends struct {
	UserItemPoolWalletID bson.ObjectID `bson:"user_item_pool_wallet_id,omitempty"`
	UserType             int32         `bson:"user_type"` // 对应 UserTypeEnum
	Items                []OrderItem   `bson:"items,omitempty"`

	// 支付相关
	RechargeOrderID bson.ObjectID `bson:"recharge_order_id,omitempty"`
	UniqueOrderNo   string        `bson:"unique_order_no,omitempty"`
	RechargeMethod  int32         `bson:"recharge_method,omitempty"`

	// 物流信息
	FreightAmount float64   `bson:"freight_amount,omitempty"`
	IsShipUrge    bool      `bson:"is_ship_urge,omitempty"`
	DeliveryTime  time.Time `bson:"delivery_time,omitempty"`

	// 区块链相关
	ChainStatus int32     `bson:"chain_status,omitempty"`
	ChainHash   string    `bson:"chain_hash,omitempty"`
	UpChainTime time.Time `bson:"up_chain_time,omitempty"`
}

// 订单商品项（对应 itemSchema）
type OrderItem struct {
	Status     int32         `bson:"status"` // 对应 ItemWithdrawOrderStatusEnum
	ItemID     bson.ObjectID `bson:"item_id,omitempty"`
	Quantity   int32         `bson:"quantity"`
	SellPrice  int32         `bson:"sell_price"`
	TotalPrice int32         `bson:"total_price"`

	// NFT相关
	NFTItemID   bson.ObjectID `bson:"nft_item_id,omitempty"`
	NFTAssetIDs []string      `bson:"nft_asset_ids,omitempty"`

	// 物流跟踪
	ShipType    int32     `bson:"ship_type,omitempty"`
	FreightTime time.Time `bson:"freight_time,omitempty"`
	CheckTime   time.Time `bson:"freight_check_time,omitempty"`

	// 外部系统关联
	OutOrderID  string `bson:"out_order_id,omitempty"`
	OutMallName string `bson:"out_mall_name,omitempty"`
}
