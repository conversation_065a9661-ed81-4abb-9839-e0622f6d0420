package logic

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"time"
)

// GetCirculationStatus 获取商品流通状态
func GetCirculationStatus(itemMap map[string]*mongdb.IssueItem) {
	loc, _ := time.LoadLocation("Asia/Shanghai")

	for _, issueItem := range itemMap {
		var startTimeUTC8 time.Time
		var endTimeUTC8 time.Time
		if issueItem.CirculationStart != nil {
			startTimeUTC8 = issueItem.CirculationStart.In(loc)
		}
		if issueItem.CirculationEnd != nil {
			endTimeUTC8 = issueItem.CirculationEnd.In(loc)
		}

		if issueItem.CirculationStatus == 1 && !startTimeUTC8.IsZero() && time.Now().After(startTimeUTC8) && !endTimeUTC8.IsZero() && time.Now().Before(endTimeUTC8) {
			issueItem.CirculationStatus = mongdb.CirculationStatusAllow
		} else {
			issueItem.CirculationStatus = mongdb.CirculationStatusProhibit
		}
	}
}
