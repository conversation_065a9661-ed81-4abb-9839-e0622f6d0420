package router

import (
	"app_service/apps/platform/system/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 客户端路由
	webRote(r)
}

// 客户端路由
func webRote(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.<PERSON>(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/system/get_js_sdk",
		},
	}))

	web.System(w)
}
