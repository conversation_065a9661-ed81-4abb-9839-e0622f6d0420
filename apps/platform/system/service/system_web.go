package service

import (
	"app_service/apps/platform/system/define"
	"app_service/third_party/set"
	"fmt"
	"net/url"
)

func (s *Service) GetJsSDK(req *define.GetJsSDKReq) (*define.GetJsSDKResp, error) {
	// URL解码处理
	decodedUrl, err := url.QueryUnescape(req.Url)
	if err != nil {
		return nil, fmt.Errorf("URL解码失败: %v", err)
	}
	resp, _ := set.GetJsSDK(s.ctx, req.AppId, decodedUrl)
	return resp, nil
}
