package web

import (
	"app_service/apps/platform/system/define"
	"app_service/apps/platform/system/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetJsSDK
// @Summary 获取公众号SDK
// @Description 获取公众号SDK
// @Tags 用户端
// @Param data body define.GetJsSDKReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetJsSDKResp}
// @Router  /web/v1/system/get_js_sdk [get]
// @Security Bearer
func GetJsSDK(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetJsSDKReq{}, s.GetJsSDK)
}
