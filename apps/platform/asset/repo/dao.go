package repo

import (
	"app_service/apps/platform/asset/dal/query"
	"context"
	"e.coding.net/g-dtay0385/common/go-mysql-gorm"
	"errors"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

const DefaultClientName = "app_service"

func GetDB() *gorm.DB {
	return mysql.GetClient(DefaultClientName).DB
}

func GetQuery() *query.Query {
	return query.Use(GetDB())
}

// 用来承载事务的上下文
type contextTxKey struct{}

func Query(ctx context.Context) *query.Query {
	tx, ok := ctx.Value(contextTxKey{}).(*query.Query)
	if ok {
		return tx
	}
	return GetQuery()
}

// ExecGenTx gorm gen Transaction
func ExecGenTx(ctx context.Context, fn func(ctx context.Context) error) error {
	return GetQuery().Transaction(func(tx *query.Query) error {
		ctx = context.WithValue(ctx, contextTxKey{}, tx)
		return fn(ctx)
	})
}

// BuildSelectField 构建字段
func BuildSelectField(tableName string, fieldNames ...string) []field.Expr {
	fields := make([]field.Expr, 0)
	if len(fieldNames) > 0 {
		for _, v := range fieldNames {
			newField := field.NewField(tableName, v)
			fields = append(fields, newField)
		}
	}
	return fields
}

var UpdateFail = errors.New("update fail,RowsAffected = 0")
