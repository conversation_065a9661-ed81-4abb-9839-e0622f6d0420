package define

import (
	"app_service/pkg/pagination"
	"time"
)

type (
	GetUserWalletReq struct {
	}
	GetUserWalletResp struct {
		UserId     string `json:"userId"`
		Bonus      int32  `json:"current_bonus"` //当前积分余额
		TotalBonus int32  `json:"total_bonus"`   //累计积分余额
	}
	GetUserBonusLogReq struct {
		ReceiveStatusList string `form:"receive_status_list" json:"receive_status_list"` // 1-未解锁 10-待领取 20-已领取 30-已使用 99-过期未领取
		pagination.Pagination
	}
	GetUserBonusLogResp struct {
		UserBonusLogId int64     `json:"user_bonus_log_id"`
		UserId         string    `json:"userId"`
		Name           string    ` json:"name"`             // 名字
		MainImg        string    `json:"main_img"`          // 图片
		Source         string    `json:"source"`            // 来源
		Amount         int32     `json:"amount"`            // 数额
		ReceiveStatus  int32     ` json:"receive_status"`   // 1-待领取 10-未解锁 20-已领取 30-已使用 99-过期未领取
		IsExpiringSoon int32     ` json:"is_expiring_soon"` //1-Yes 2-No
		CreatedAt      time.Time `json:"created_at"`
	}
	ReceiveBonusReq struct {
		IsAll          int   `json:"is_all"` //1-Yes 2-No
		UserBonusLogId int64 `json:"user_bonus_log_id"`
	}
	ReceiveBonusResp struct {
	}

	BonusExpireResp struct {
	}
)
