package enum

type BonusReceiveStatus int32

func (r BonusReceiveStatus) Val() int32 {
	return int32(r)
}

const (
	// BonusStatusLocked 未解锁
	BonusStatusLocked BonusReceiveStatus = 1
	// BonusStatusWait 待领取
	BonusStatusWait BonusReceiveStatus = 10
	// BonusStatusReceived 已领取
	BonusStatusReceived BonusReceiveStatus = 20
	// BonusStatusUsed 已使用
	BonusStatusUsed BonusReceiveStatus = 30
	// BonusStatusExpired 过期未领取
	BonusStatusExpired BonusReceiveStatus = 99
)
