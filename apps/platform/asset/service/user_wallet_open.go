package service

import (
	invite_define "app_service/apps/business/invite_reward/define"
	"app_service/apps/platform/asset/define"
	enum "app_service/apps/platform/asset/define/enums"
	"app_service/apps/platform/asset/repo"
	"app_service/apps/platform/common/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"errors"
)

func (s *Service) DoUserBonusExpire() (*define.BonusExpireResp, error) {
	//获取过期时间
	inviteRewardConfig := &invite_define.InviteRewardConfig{}
	err := facade.GetObj(s.ctx, "invite.reward.config", inviteRewardConfig)
	if err != nil {
		return nil, err
	}
	offsetDay := util.OffsetDay(util.Now(), -inviteRewardConfig.ExpireTime)

	//查询钱包信息
	userBonusLogSchema := repo.GetQuery().UserBonusLog
	queryWrapper := search.NewQueryBuilder().
		Eq(userBonusLogSchema.ReceiveStatus, enum.BonusStatusWait.Val()).
		Lte(userBonusLogSchema.CreatedAt, offsetDay).
		Build()
	update := map[string]interface{}{
		"receive_status": enum.BonusStatusExpired.Val(),
	}
	err = repo.NewUserBonusLogRepo(userBonusLogSchema.WithContext(s.ctx)).UpdateField(update, queryWrapper)
	if err != nil && !errors.Is(err, repo.UpdateFail) {
		return nil, err
	}

	return nil, nil
}
