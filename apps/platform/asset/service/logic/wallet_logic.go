package logic

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/platform/asset/dal/model"
	"app_service/apps/platform/asset/repo"
	"app_service/pkg/util"
	"context"
)

func InitUserWallet(ctx context.Context, userId string) (*model.UserWallet, error) {
	//新增
	userWallet := &model.UserWallet{
		UserID:     userId,
		Bonus:      0,
		TotalBonus: 0,
	}
	walletSchema := repo.GetQuery().UserWallet
	err := repo.NewUserWalletRepo(walletSchema.WithContext(ctx)).Save(userWallet)
	if err != nil && !util.IsMySQLDuplicateError(err) {
		return nil, err
	}

	return userWallet, nil
}

func IsExpiringSoon(userBonusLog *model.UserBonusLog, inviteConfig *define.InviteRewardConfig) (bool, error) {
	now := util.Now()
	// 计算过期时间
	expiryDate := util.OffsetDay(userBonusLog.CreatedAt, inviteConfig.ExpireTime)

	// 计算警戒时间点
	warningDays := int32(3) // 可配置的警戒天数
	warningDate := util.OffsetDay(expiryDate, -warningDays)

	// 如果当前时间距离过期时间小于警戒天数，则返回 true
	return now.After(warningDate) && now.Before(expiryDate), nil
}
