package service

import (
	invite_logic "app_service/apps/business/invite_reward/service/logic"
	"app_service/apps/platform/asset/define"
	enum "app_service/apps/platform/asset/define/enums"
	"app_service/apps/platform/asset/repo"
	"app_service/apps/platform/asset/service/logic"
	common_constant "app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"errors"
	"github.com/jinzhu/copier"
	"gorm.io/gorm"
	"strings"
	"time"
)

func (s *Service) GetUserWallet(req *define.GetUserWalletReq) (*define.GetUserWalletResp, error) {
	userId := s.GetUserId()

	//查询钱包信息
	walletSchema := repo.GetQuery().UserWallet
	queryWrapper := search.NewQueryBuilder().Eq(walletSchema.UserID, userId).Build()
	userWalletTab, err := repo.NewUserWalletRepo(walletSchema.WithContext(s.ctx)).SelectOne(queryWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if userWalletTab == nil {
		userWalletTab, err = logic.InitUserWallet(s.ctx, userId)
		if err != nil {
			return nil, common_define.CommonErr.Err(err)
		}
	}

	resp := &define.GetUserWalletResp{
		UserId:     userId,
		Bonus:      userWalletTab.Bonus,
		TotalBonus: userWalletTab.TotalBonus,
	}

	return resp, nil
}

func (s *Service) GetUserBonusLogList(req *define.GetUserBonusLogReq) (*common_define.Resp, error) {
	userId := s.GetUserId()

	//查询流水
	minTime := util.Now().Add(time.Hour * 24 * -60) // 只显示最近 60 天的数据
	bonusLogSchema := repo.GetQuery().UserBonusLog
	builder := search.NewQueryBuilder().
		Eq(bonusLogSchema.UserID, userId).
		Ne(bonusLogSchema.ReceiveStatus, enum.BonusStatusLocked.Val()).
		Gte(bonusLogSchema.CreatedAt, minTime).
		OrderByDesc(bonusLogSchema.CreatedAt)
	if req.ReceiveStatusList != "" {
		// 向下兼容
		statusStr := strings.ReplaceAll(req.ReceiveStatusList, "[", "")
		statusStr = strings.ReplaceAll(statusStr, "]", "")
		if statusStr != "" {
			statusList, err := util.NumberStr2Int32Slice(statusStr)
			if err != nil {
				return nil, err
			}
			builder = builder.In(bonusLogSchema.ReceiveStatus, statusList)
		}
	}
	qwForBl := builder.Build()
	bonusLogTabList, count, err := repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(s.ctx)).SelectPage(qwForBl, req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	inviteConfig, err := invite_logic.GetInviteConfig(s.ctx)
	if err != nil {
		return nil, err
	}

	logList := make([]*define.GetUserBonusLogResp, 0)
	for _, log := range bonusLogTabList {
		item := &define.GetUserBonusLogResp{}
		_ = copier.Copy(item, log)

		isExpiringSoon, _ := logic.IsExpiringSoon(log, inviteConfig)
		item.IsExpiringSoon = common_constant.Bool2Int(isExpiringSoon)
		logList = append(logList, item)
	}

	return &common_define.Resp{
		List:  logList,
		Total: count,
	}, nil
}

func (s *Service) ReceiveBonus(req *define.ReceiveBonusReq) (*define.ReceiveBonusResp, error) {
	userId := s.GetUserId()

	//查询BonusLog
	bonusLogSchema := repo.GetQuery().UserBonusLog
	builder := search.NewQueryBuilder().
		Eq(bonusLogSchema.UserID, userId).
		Eq(bonusLogSchema.ReceiveStatus, enum.BonusStatusWait.Val())
	if req.IsAll != 1 && req.UserBonusLogId != 0 {
		builder = builder.Eq(bonusLogSchema.UserBonusLogID, req.UserBonusLogId)
	}
	userBonusLogList, err := repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(s.ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}

	//事务执行
	inviteConfig, err := invite_logic.GetInviteConfig(s.ctx)
	if err != nil {
		return nil, err
	}
	offsetDay := util.OffsetDay(util.Now(), -inviteConfig.ExpireTime)

	for i := range userBonusLogList {
		userBonusLog := userBonusLogList[i]

		err := repo.ExecGenTx(s.ctx, func(ctx context.Context) error {
			//改Bonus状态
			updateCondition := search.NewQueryBuilder().
				Eq(bonusLogSchema.UserBonusLogID, userBonusLog.UserBonusLogID).
				Eq(bonusLogSchema.ReceiveStatus, userBonusLog.ReceiveStatus).
				Gte(bonusLogSchema.CreatedAt, offsetDay).
				Build()
			param := map[string]interface{}{
				"receive_status": enum.BonusStatusReceived.Val(),
				"updated_at":     util.Now(),
			}
			err := repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(ctx)).UpdateField(param, updateCondition)
			if err != nil {
				return err
			}

			//改bonus余额
			walletSchema := repo.GetQuery().UserWallet
			queryWrapper := search.NewQueryBuilder().Eq(walletSchema.UserID, userId).Build()
			update := map[string]interface{}{
				"bonus": gorm.Expr("bonus + ?", userBonusLog.Amount),
			}
			err = repo.NewUserWalletRepo(walletSchema.WithContext(s.ctx)).UpdateField(update, queryWrapper)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
			return nil
		})
		if err != nil {
			return nil, err
		}
	}

	return nil, nil
}
