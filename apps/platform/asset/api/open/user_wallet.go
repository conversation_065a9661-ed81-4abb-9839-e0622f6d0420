package web

import (
	"app_service/apps/platform/asset/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// DoUserBonusExpire
// @Summary 处理金币过期
// @Description 处理金币过期
// @Tags Open端
// @Success 200 {object} response.Data{data=define.BonusExpireResp}
// @Router  /open/v1/asset/user_wallet/do_user_bonus_expire [post]
// @Security Bearer
func DoUserBonusExpire(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.DoUserBonusExpire)
}
