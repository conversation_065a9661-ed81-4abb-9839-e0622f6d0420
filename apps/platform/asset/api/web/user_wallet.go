package web

import (
	"app_service/apps/platform/asset/define"
	"app_service/apps/platform/asset/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetUserBonusLogList
// @Summary 获取用户金币流水日志列表
// @Description 获取用户金币流水日志列表
// @Tags 用户端
// @Param data query define.GetUserBonusLogReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetUserBonusLogResp}
// @Router  /web/v1/asset/user_wallet/user_bonus_log_list [get]
// @Security Bearer
func GetUserBonusLogList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetUserBonusLogReq{}, s.GetUserBonusLogList)
}

// ReceiveReward
// @Summary 领取奖励
// @Description 领取奖励
// @Tags 用户端
// @Param data body define.ReceiveBonusReq true "查询参数"
// @Success 200 {object} response.Data{data=define.ReceiveBonusResp}
// @Router  /web/v1/asset/user_wallet/receive_bonus [post]
// @Security Bearer
func ReceiveReward(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ReceiveBonusReq{}, s.ReceiveBonus)
}
