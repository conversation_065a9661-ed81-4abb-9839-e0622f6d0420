// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameUserBonusLog = "user_bonus_log"

// UserBonusLog 金币流水日志
type UserBonusLog struct {
	UserBonusLogID int64                 `gorm:"column:user_bonus_log_id;type:bigint unsigned;primaryKey;comment:主键id" json:"user_bonus_log_id"`          // 主键id
	UserID         string                `gorm:"column:user_id;type:varchar(16);not null;comment:Id" json:"user_id"`                                      // Id
	Name           string                `gorm:"column:name;type:varchar(16);not null;comment:名字" json:"name"`                                            // 名字
	MainImg        string                `gorm:"column:main_img;type:varchar(256);not null;comment:图片" json:"main_img"`                                   // 图片
	Source         string                `gorm:"column:source;type:varchar(16);not null;comment:来源" json:"source"`                                        // 来源
	RelateID       string                `gorm:"column:relate_id;type:varchar(32);not null;comment:关联Id" json:"relate_id"`                                // 关联Id
	Amount         int32                 `gorm:"column:amount;type:int;not null;comment:数额" json:"amount"`                                                // 数额
	ReceiveStatus  int32                 `gorm:"column:receive_status;type:int;not null;comment:1-未解锁 10-待领取 20-已领取 99-过期未领取" json:"receive_status"`      // 1-未解锁 10-待领取 20-已领取 99-过期未领取
	CreatedBy      string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt      time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy      string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt      time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel          soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName UserBonusLog's table name
func (*UserBonusLog) TableName() string {
	return TableNameUserBonusLog
}
