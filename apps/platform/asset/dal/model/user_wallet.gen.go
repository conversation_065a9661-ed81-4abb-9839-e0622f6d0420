// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameUserWallet = "user_wallet"

// UserWallet 用户钱包
type UserWallet struct {
	UserWalletID int64                 `gorm:"column:user_wallet_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:用户id" json:"user_wallet_id"` // 用户id
	UserID       string                `gorm:"column:user_id;type:varchar(32);not null;comment:用户id" json:"user_id"`                                        // 用户id
	Bonus        int32                 `gorm:"column:bonus;type:int;not null;comment:积分" json:"bonus"`                                                      // 积分
	TotalBonus   int32                 `gorm:"column:total_bonus;type:int;not null;comment:积分" json:"total_bonus"`                                          // 积分
	CreatedAt    time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`     // 创建时间
	UpdatedAt    time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`     // 更新时间
	IsDel        soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`            // 是否删除【0->未删除; 1->删除】
}

// TableName UserWallet's table name
func (*UserWallet) TableName() string {
	return TableNameUserWallet
}
