// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/platform/asset/dal/model"
)

func newUserBonusLog(db *gorm.DB, opts ...gen.DOOption) userBonusLog {
	_userBonusLog := userBonusLog{}

	_userBonusLog.userBonusLogDo.UseDB(db, opts...)
	_userBonusLog.userBonusLogDo.UseModel(&model.UserBonusLog{})

	tableName := _userBonusLog.userBonusLogDo.TableName()
	_userBonusLog.ALL = field.NewAsterisk(tableName)
	_userBonusLog.UserBonusLogID = field.NewInt64(tableName, "user_bonus_log_id")
	_userBonusLog.UserID = field.NewString(tableName, "user_id")
	_userBonusLog.Name = field.NewString(tableName, "name")
	_userBonusLog.MainImg = field.NewString(tableName, "main_img")
	_userBonusLog.Source = field.NewString(tableName, "source")
	_userBonusLog.RelateID = field.NewString(tableName, "relate_id")
	_userBonusLog.Amount = field.NewInt32(tableName, "amount")
	_userBonusLog.ReceiveStatus = field.NewInt32(tableName, "receive_status")
	_userBonusLog.CreatedBy = field.NewString(tableName, "created_by")
	_userBonusLog.CreatedAt = field.NewTime(tableName, "created_at")
	_userBonusLog.UpdatedBy = field.NewString(tableName, "updated_by")
	_userBonusLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_userBonusLog.IsDel = field.NewField(tableName, "is_del")

	_userBonusLog.fillFieldMap()

	return _userBonusLog
}

// userBonusLog 金币流水日志
type userBonusLog struct {
	userBonusLogDo

	ALL            field.Asterisk
	UserBonusLogID field.Int64  // 主键id
	UserID         field.String // Id
	Name           field.String // 名字
	MainImg        field.String // 图片
	Source         field.String // 来源
	RelateID       field.String // 关联Id
	Amount         field.Int32  // 数额
	ReceiveStatus  field.Int32  // 1-未解锁 10-待领取 20-已领取 99-过期未领取
	CreatedBy      field.String // 创建人
	CreatedAt      field.Time   // 创建时间
	UpdatedBy      field.String // 更新人
	UpdatedAt      field.Time   // 更新时间
	IsDel          field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (u userBonusLog) Table(newTableName string) *userBonusLog {
	u.userBonusLogDo.UseTable(newTableName)
	return u.updateTableName(newTableName)
}

func (u userBonusLog) As(alias string) *userBonusLog {
	u.userBonusLogDo.DO = *(u.userBonusLogDo.As(alias).(*gen.DO))
	return u.updateTableName(alias)
}

func (u *userBonusLog) updateTableName(table string) *userBonusLog {
	u.ALL = field.NewAsterisk(table)
	u.UserBonusLogID = field.NewInt64(table, "user_bonus_log_id")
	u.UserID = field.NewString(table, "user_id")
	u.Name = field.NewString(table, "name")
	u.MainImg = field.NewString(table, "main_img")
	u.Source = field.NewString(table, "source")
	u.RelateID = field.NewString(table, "relate_id")
	u.Amount = field.NewInt32(table, "amount")
	u.ReceiveStatus = field.NewInt32(table, "receive_status")
	u.CreatedBy = field.NewString(table, "created_by")
	u.CreatedAt = field.NewTime(table, "created_at")
	u.UpdatedBy = field.NewString(table, "updated_by")
	u.UpdatedAt = field.NewTime(table, "updated_at")
	u.IsDel = field.NewField(table, "is_del")

	u.fillFieldMap()

	return u
}

func (u *userBonusLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := u.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (u *userBonusLog) fillFieldMap() {
	u.fieldMap = make(map[string]field.Expr, 13)
	u.fieldMap["user_bonus_log_id"] = u.UserBonusLogID
	u.fieldMap["user_id"] = u.UserID
	u.fieldMap["name"] = u.Name
	u.fieldMap["main_img"] = u.MainImg
	u.fieldMap["source"] = u.Source
	u.fieldMap["relate_id"] = u.RelateID
	u.fieldMap["amount"] = u.Amount
	u.fieldMap["receive_status"] = u.ReceiveStatus
	u.fieldMap["created_by"] = u.CreatedBy
	u.fieldMap["created_at"] = u.CreatedAt
	u.fieldMap["updated_by"] = u.UpdatedBy
	u.fieldMap["updated_at"] = u.UpdatedAt
	u.fieldMap["is_del"] = u.IsDel
}

func (u userBonusLog) clone(db *gorm.DB) userBonusLog {
	u.userBonusLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return u
}

func (u userBonusLog) replaceDB(db *gorm.DB) userBonusLog {
	u.userBonusLogDo.ReplaceDB(db)
	return u
}

type userBonusLogDo struct{ gen.DO }

type IUserBonusLogDo interface {
	gen.SubQuery
	Debug() IUserBonusLogDo
	WithContext(ctx context.Context) IUserBonusLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IUserBonusLogDo
	WriteDB() IUserBonusLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IUserBonusLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IUserBonusLogDo
	Not(conds ...gen.Condition) IUserBonusLogDo
	Or(conds ...gen.Condition) IUserBonusLogDo
	Select(conds ...field.Expr) IUserBonusLogDo
	Where(conds ...gen.Condition) IUserBonusLogDo
	Order(conds ...field.Expr) IUserBonusLogDo
	Distinct(cols ...field.Expr) IUserBonusLogDo
	Omit(cols ...field.Expr) IUserBonusLogDo
	Join(table schema.Tabler, on ...field.Expr) IUserBonusLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IUserBonusLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IUserBonusLogDo
	Group(cols ...field.Expr) IUserBonusLogDo
	Having(conds ...gen.Condition) IUserBonusLogDo
	Limit(limit int) IUserBonusLogDo
	Offset(offset int) IUserBonusLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBonusLogDo
	Unscoped() IUserBonusLogDo
	Create(values ...*model.UserBonusLog) error
	CreateInBatches(values []*model.UserBonusLog, batchSize int) error
	Save(values ...*model.UserBonusLog) error
	First() (*model.UserBonusLog, error)
	Take() (*model.UserBonusLog, error)
	Last() (*model.UserBonusLog, error)
	Find() ([]*model.UserBonusLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBonusLog, err error)
	FindInBatches(result *[]*model.UserBonusLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.UserBonusLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IUserBonusLogDo
	Assign(attrs ...field.AssignExpr) IUserBonusLogDo
	Joins(fields ...field.RelationField) IUserBonusLogDo
	Preload(fields ...field.RelationField) IUserBonusLogDo
	FirstOrInit() (*model.UserBonusLog, error)
	FirstOrCreate() (*model.UserBonusLog, error)
	FindByPage(offset int, limit int) (result []*model.UserBonusLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IUserBonusLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (u userBonusLogDo) Debug() IUserBonusLogDo {
	return u.withDO(u.DO.Debug())
}

func (u userBonusLogDo) WithContext(ctx context.Context) IUserBonusLogDo {
	return u.withDO(u.DO.WithContext(ctx))
}

func (u userBonusLogDo) ReadDB() IUserBonusLogDo {
	return u.Clauses(dbresolver.Read)
}

func (u userBonusLogDo) WriteDB() IUserBonusLogDo {
	return u.Clauses(dbresolver.Write)
}

func (u userBonusLogDo) Session(config *gorm.Session) IUserBonusLogDo {
	return u.withDO(u.DO.Session(config))
}

func (u userBonusLogDo) Clauses(conds ...clause.Expression) IUserBonusLogDo {
	return u.withDO(u.DO.Clauses(conds...))
}

func (u userBonusLogDo) Returning(value interface{}, columns ...string) IUserBonusLogDo {
	return u.withDO(u.DO.Returning(value, columns...))
}

func (u userBonusLogDo) Not(conds ...gen.Condition) IUserBonusLogDo {
	return u.withDO(u.DO.Not(conds...))
}

func (u userBonusLogDo) Or(conds ...gen.Condition) IUserBonusLogDo {
	return u.withDO(u.DO.Or(conds...))
}

func (u userBonusLogDo) Select(conds ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Select(conds...))
}

func (u userBonusLogDo) Where(conds ...gen.Condition) IUserBonusLogDo {
	return u.withDO(u.DO.Where(conds...))
}

func (u userBonusLogDo) Order(conds ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Order(conds...))
}

func (u userBonusLogDo) Distinct(cols ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Distinct(cols...))
}

func (u userBonusLogDo) Omit(cols ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Omit(cols...))
}

func (u userBonusLogDo) Join(table schema.Tabler, on ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Join(table, on...))
}

func (u userBonusLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.LeftJoin(table, on...))
}

func (u userBonusLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.RightJoin(table, on...))
}

func (u userBonusLogDo) Group(cols ...field.Expr) IUserBonusLogDo {
	return u.withDO(u.DO.Group(cols...))
}

func (u userBonusLogDo) Having(conds ...gen.Condition) IUserBonusLogDo {
	return u.withDO(u.DO.Having(conds...))
}

func (u userBonusLogDo) Limit(limit int) IUserBonusLogDo {
	return u.withDO(u.DO.Limit(limit))
}

func (u userBonusLogDo) Offset(offset int) IUserBonusLogDo {
	return u.withDO(u.DO.Offset(offset))
}

func (u userBonusLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IUserBonusLogDo {
	return u.withDO(u.DO.Scopes(funcs...))
}

func (u userBonusLogDo) Unscoped() IUserBonusLogDo {
	return u.withDO(u.DO.Unscoped())
}

func (u userBonusLogDo) Create(values ...*model.UserBonusLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Create(values)
}

func (u userBonusLogDo) CreateInBatches(values []*model.UserBonusLog, batchSize int) error {
	return u.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (u userBonusLogDo) Save(values ...*model.UserBonusLog) error {
	if len(values) == 0 {
		return nil
	}
	return u.DO.Save(values)
}

func (u userBonusLogDo) First() (*model.UserBonusLog, error) {
	if result, err := u.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBonusLog), nil
	}
}

func (u userBonusLogDo) Take() (*model.UserBonusLog, error) {
	if result, err := u.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBonusLog), nil
	}
}

func (u userBonusLogDo) Last() (*model.UserBonusLog, error) {
	if result, err := u.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBonusLog), nil
	}
}

func (u userBonusLogDo) Find() ([]*model.UserBonusLog, error) {
	result, err := u.DO.Find()
	return result.([]*model.UserBonusLog), err
}

func (u userBonusLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.UserBonusLog, err error) {
	buf := make([]*model.UserBonusLog, 0, batchSize)
	err = u.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (u userBonusLogDo) FindInBatches(result *[]*model.UserBonusLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return u.DO.FindInBatches(result, batchSize, fc)
}

func (u userBonusLogDo) Attrs(attrs ...field.AssignExpr) IUserBonusLogDo {
	return u.withDO(u.DO.Attrs(attrs...))
}

func (u userBonusLogDo) Assign(attrs ...field.AssignExpr) IUserBonusLogDo {
	return u.withDO(u.DO.Assign(attrs...))
}

func (u userBonusLogDo) Joins(fields ...field.RelationField) IUserBonusLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Joins(_f))
	}
	return &u
}

func (u userBonusLogDo) Preload(fields ...field.RelationField) IUserBonusLogDo {
	for _, _f := range fields {
		u = *u.withDO(u.DO.Preload(_f))
	}
	return &u
}

func (u userBonusLogDo) FirstOrInit() (*model.UserBonusLog, error) {
	if result, err := u.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBonusLog), nil
	}
}

func (u userBonusLogDo) FirstOrCreate() (*model.UserBonusLog, error) {
	if result, err := u.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.UserBonusLog), nil
	}
}

func (u userBonusLogDo) FindByPage(offset int, limit int) (result []*model.UserBonusLog, count int64, err error) {
	result, err = u.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = u.Offset(-1).Limit(-1).Count()
	return
}

func (u userBonusLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = u.Count()
	if err != nil {
		return
	}

	err = u.Offset(offset).Limit(limit).Scan(result)
	return
}

func (u userBonusLogDo) Scan(result interface{}) (err error) {
	return u.DO.Scan(result)
}

func (u userBonusLogDo) Delete(models ...*model.UserBonusLog) (result gen.ResultInfo, err error) {
	return u.DO.Delete(models)
}

func (u *userBonusLogDo) withDO(do gen.Dao) *userBonusLogDo {
	u.DO = *do.(*gen.DO)
	return u
}
