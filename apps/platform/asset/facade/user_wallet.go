package facade

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/platform/asset/dal/model"
	enum "app_service/apps/platform/asset/define/enums"
	"app_service/apps/platform/asset/repo"
	"app_service/apps/platform/asset/service/logic"
	common_define "app_service/apps/platform/common/define"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"errors"
	"gorm.io/gorm"
)

func GetUserWallet(ctx context.Context, userId string) (*model.UserWallet, error) {
	//查询钱包信息
	walletSchema := repo.GetQuery().UserWallet
	queryWrapper := search.NewQueryBuilder().Eq(walletSchema.UserID, userId).Build()
	userWalletTab, err := repo.NewUserWalletRepo(walletSchema.WithContext(ctx)).SelectOne(queryWrapper)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	if userWalletTab == nil {
		userWalletTab, err = logic.InitUserWallet(ctx, userId)
		if err != nil {
			return nil, common_define.CommonErr.Err(err)
		}
	}
	return userWalletTab, nil
}

func GetWaitReceiveAmount(ctx context.Context, userId string) (int32, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("SUM(amount) as amount").
		Table("`user_bonus_log` ubl").
		Where("ubl.user_id = ? AND receive_status = 10 AND ubl.is_del=0", userId)
	var results []*define.AmountResult
	err := exec.Find(&results).Error
	if err != nil || len(results) == 0 {
		return 0, err
	}

	return results[0].Amount, nil
}

func AddUserBonus(ctx context.Context, userId string, relateId string, name string, mainImg string, source string, amount int32) error {
	err := repo.ExecGenTx(ctx, func(ctx context.Context) error {
		//写入BonusLog
		bonusLogSchema := repo.GetQuery().UserBonusLog
		userBonusLog := &model.UserBonusLog{
			UserID:        userId,
			Name:          name,
			MainImg:       mainImg,
			Source:        source,
			Amount:        amount,
			RelateID:      relateId,
			ReceiveStatus: enum.BonusStatusWait.Val(),
			CreatedAt:     util.Now(),
		}
		err := repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(ctx)).Save(userBonusLog)
		if err != nil && !util.IsMySQLDuplicateError(err) {
			return err
		}

		//累计奖励
		if userBonusLog.ReceiveStatus != enum.BonusStatusLocked.Val() {
			walletSchema := repo.GetQuery().UserWallet
			queryWrapper := search.NewQueryBuilder().Eq(walletSchema.UserID, userId).Build()
			update := map[string]interface{}{
				"total_bonus": gorm.Expr("total_bonus + ?", userBonusLog.Amount),
			}
			err = repo.NewUserWalletRepo(walletSchema.WithContext(ctx)).UpdateField(update, queryWrapper)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}
	return nil
}
