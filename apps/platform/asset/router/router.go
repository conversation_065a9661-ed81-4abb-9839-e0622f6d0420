package router

import (
	"app_service/apps/platform/asset/router/open"
	"app_service/apps/platform/asset/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	// 客户端路由
	webRote(r)

	openRote(r)
}

// 客户端路由
func webRote(router *gin.Engine) {
	w := router.Group("/web/v1/asset", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.<PERSON>(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{},
	}))

	web.UserWallet(w)
}

// Open路由
func openRote(router *gin.Engine) {
	a := router.Group("/open/v1/asset", middlewares.ParseHead)
	a.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
	}))

	open.UserWallet(a)
}
