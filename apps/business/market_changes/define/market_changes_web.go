package define

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"time"

	"app_service/pkg/pagination"
)

// 行情异动列表相关结构体
type (
	GetMarketChangesWebListReq struct {
		pagination.Pagination
		KeyWord    string `form:"keyword" json:"keyword" binding:"max=20"` // 关键字(搜索标题,内容和商品名称，限输20个字符)
		CategoryID int64  `form:"category_id" json:"category_id,string"`   // 分类ID
		//ItemId     string `form:"item_id" json:"item_id"`                  // 商品ID
	}

	GetItemIdInfoWebList struct {
		//ID           string  `json:"id"`            // ID
		ItemId            string                       `json:"item_id"`            // 商品ID
		ItemName          string                       `json:"item_name"`          // 商品名称
		PriceChanges      float64                      `json:"price_changes"`      // 价格变动
		CirculationStatus mongdb.CirculationStatusEnum `json:"circulation_status"` // 流通状态
		Status            mongdb.IssueItemStatusEnum   `json:"status"`             // 状态
	}

	GetMarketChangesWebListData struct {
		ID         int64  `json:"id,string"`          // 行情异动ID
		Title      string `json:"title"`              // 行情异动标题
		CategoryID int64  `json:"category_id,string"` // 分类ID
		//CategoryInfo   *GetCategoryWebLessDetailResp `json:"category_info"`      // 分类信息
		ItemIdInfoList []*GetItemIdInfoWebList `json:"item_id_info_list"` // 关联商品列表
		PublishTime    *time.Time              `json:"publish_time"`      // 发布时间
		CurrentTime    string                  `json:"current_time"`      // 当前时间
		IsCanJump      bool                    `json:"is_can_jump"`       // 是否可跳转
	}

	GetMarketChangesWebListResp struct {
		List    []*GetMarketChangesWebListData `json:"list"`
		HasMore bool                           `json:"has_more"` // 判断当前页是否为最后一页
	}
)

// 行情异动详情相关结构体
type (
	GetMarketChangesWebDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 行情异动ID
	}

	GetMarketChangesWebDetailResp struct {
		ID           int64                         `json:"id,string"`          // 行情异动ID
		Title        string                        `json:"title"`              // 行情异动标题
		Content      string                        `json:"content"`            // 行情异动内容
		CategoryID   int64                         `json:"category_id,string"` // 分类ID
		CategoryInfo *GetCategoryWebLessDetailResp `json:"category_info"`      // 分类信息
		PublishTime  *time.Time                    `json:"publish_time"`       // 发布时间
		IsCanJump    bool                          `json:"is_can_jump"`        // 是否可跳转
	}
)

// GetLatestMarketChangesWebDetailReq 最新行情异动详情相关结构体
type (
	GetLatestMarketChangesWebDetailReq struct {
		ItemId string `form:"item_id" json:"item_id"` // 商品ID
	}
	// GetLatestMarketChangesWebDetailResp 复用 GetMarketChangesWebDetailResp
)

// GetCategoryWebLessDetailResp 行情异动分类详情相关结构体
type (
	GetCategoryWebLessDetailResp struct {
		ID              int64  `json:"id,string"`        // 分类ID
		Name            string `json:"name"`             // 分类名称
		TextColor       string `json:"text_color"`       // 文字颜色
		BackgroundColor string `json:"background_color"` // 背景颜色
	}
)
