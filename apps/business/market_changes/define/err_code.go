package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	// 行情异动模块错误码
	MCH210001Err = response.NewError(210001, "已下架不可修改")
	MCH210002Err = response.NewError(210002, "仅限定时中和已发布的状态的可操作下架")
	MCH210003Err = response.NewError(210003, "商品超过数量")
	MCH210004Err = response.NewError(210004, "仅已发布状态的行情异动可同步媒体")
	MCH210005Err = response.NewError(210005, "不可修改为该状态")
	MCH210006Err = response.NewError(210006, "行情异动已下架")
	MCH210007Err = response.NewError(210007, "仅限待发布的状态的可操作发布")
)
