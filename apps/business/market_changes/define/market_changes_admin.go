package define

import (
	"time"

	"app_service/apps/business/market_changes/define/enums"
	"app_service/pkg/pagination"
)

// 行情异动列表相关结构体
type (
	GetMarketChangesAdminListReq struct {
		pagination.Pagination
		ID         int64                       `form:"id" json:"id,string"`                   // 行情异动ID
		Title      string                      `form:"title" json:"title"`                    // 行情异动标题
		CategoryID int64                       `form:"category_id" json:"category_id,string"` // 分类ID
		Status     enums.MarketChangesStatus   `form:"status" json:"status"`                  // 状态
		TimeType   enums.MarketChangesTimeType `form:"time_type" json:"time_type"`            // 时间类型
		StartTime  time.Time                   `form:"start_time" json:"start_time"`          // 开始时间
		EndTime    time.Time                   `form:"end_time" json:"end_time"`              // 结束时间
		ItemId     string                      `form:"item_id" json:"item_id"`                // 商品ID
		ItemName   string                      `form:"item_name" json:"item_name"`            // 商品名称
		ChannelId  string                      `form:"channel_id" json:"channel_id"`          // 渠道ID
		CreatedBy  string                      `form:"created_by" json:"created_by"`          // 创建人
	}

	GetItemIdInfoAdminList struct {
		//ID       string `json:"id"`        // ID
		ItemId   string `json:"item_id"`   // 商品ID
		ItemName string `json:"item_name"` // 商品名称
		ImageUrl string `json:"image_url"` // 商品图片
	}

	GetMarketChangesAdminListData struct {
		ID           int64                           `json:"id,string"`          // 行情异动ID
		Title        string                          `json:"title"`              // 行情异动标题
		Content      string                          `json:"content"`            // 行情异动内容
		CategoryID   int64                           `json:"category_id,string"` // 分类ID
		CategoryInfo *GetCategoryAdminLessDetailResp `json:"category_info"`      // 分类信息
		//Priority       int32                                        `json:"priority"`           // 优先级
		Status         enums.MarketChangesStatus      `json:"status"`            // 状态
		PublishTime    *time.Time                     `json:"publish_time"`      // 发布时间
		ItemIds        []string                       `json:"item_ids"`          // 关联商品ID列表
		ItemIdInfoList []*GetItemIdInfoAdminList      `json:"item_id_info_list"` // 关联商品列表
		ChannelIds     []string                       `json:"channel_ids"`       // 关联渠道ID列表
		PublishType    enums.MarketChangesPublishType `json:"publish_type"`      // 发布类型
		CreatedBy      string                         `json:"created_by"`        // 创建人
		//AdIds          []string                       `json:"ad_ids"`            // 关联广告ID列表
		CreatedAt time.Time `json:"created_at"` // 创建时间
		UpdatedAt time.Time `json:"updated_at"` // 更新时间
	}

	GetMarketChangesAdminListResp struct {
		List  []*GetMarketChangesAdminListData `json:"list"`
		Total int64                            `json:"total"`
	}
)

// 行情异动详情相关结构体
type (
	GetMarketChangesAdminDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 行情异动ID
	}

	GetMarketChangesAdminDetailResp struct {
		ID             int64                              `json:"id,string"`          // 行情异动ID
		Title          string                             `json:"title"`              // 行情异动标题
		Content        string                             `json:"content"`            // 行情异动内容
		CategoryID     int64                              `json:"category_id,string"` // 分类ID
		CategoryInfo   *GetCategoryAdminLessDetailResp    `json:"category_info"`      // 分类信息
		Status         enums.MarketChangesStatus          `json:"status"`             // 状态
		PublishTime    *time.Time                         `json:"publish_time"`       // 发布时间
		ItemIds        []string                           `json:"item_ids"`           // 关联商品ID列表
		ItemIdInfoList []*GetItemIdInfoAdminList          `json:"item_id_info_list"`  // 关联商品列表
		ChannelIds     []string                           `json:"channel_ids"`        // 关联渠道ID列表
		PublishType    enums.MarketChangesPublishType     `json:"publish_type"`       // 发布类型
		CreatedBy      string                             `json:"created_by"`         // 创建人
		MessagePush    enums.MarketChangesMessagePushType `json:"message_push"`       // 消息推送
		CreatedAt      time.Time                          `json:"created_at"`         // 创建时间
		UpdatedAt      time.Time                          `json:"updated_at"`         // 更新时间
	}

	GetCategoryAdminLessDetailResp struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}
)

// 新增行情异动相关结构体
type (
	AddMarketChangesReq struct {
		Title       string                             `json:"title" binding:"required"`              // 行情异动标题
		Content     string                             `json:"content"`                               // 行情异动内容
		CategoryID  int64                              `json:"category_id,string" binding:"required"` // 分类ID
		PublishTime *time.Time                         `json:"publish_time"`                          // 发布时间
		ItemIds     []string                           `json:"item_ids"`                              // 关联商品ID列表
		ChannelIds  []string                           `json:"channel_ids"  binding:"required"`       // 关联渠道ID列表
		PublishType enums.MarketChangesPublishType     `json:"publish_type" binding:"required"`       // 发布类型
		MessagePush enums.MarketChangesMessagePushType `json:"message_push"`                          // 消息推送
	}

	AddMarketChangesResp struct {
		ID int64 `json:"id,string"` // 行情异动ID
	}
)

// 编辑行情异动相关结构体
type (
	EditMarketChangesReq struct {
		ID          int64                              `json:"id,string" binding:"required"`          // 行情异动ID
		Title       string                             `json:"title" binding:"required"`              // 行情异动标题
		Content     *string                            `json:"content"`                               // 行情异动内容
		CategoryID  int64                              `json:"category_id,string" binding:"required"` // 分类ID
		PublishTime *time.Time                         `json:"publish_time"`                          // 发布时间
		ItemIds     []string                           `json:"item_ids"`                              // 关联商品ID列表
		ChannelIds  []string                           `json:"channel_ids"  binding:"required"`       // 关联渠道ID列表
		PublishType enums.MarketChangesPublishType     `json:"publish_type" binding:"required"`       // 发布类型
		MessagePush enums.MarketChangesMessagePushType `json:"message_push"`                          // 消息推送
	}

	EditMarketChangesResp struct {
		ID int64 `json:"id,string"` // 行情异动ID
	}
)

// 编辑行情异动状态相关结构体
type (
	EditMarketChangesStatusReq struct {
		ID     int64                     `json:"id,string" binding:"required"` // 行情异动ID
		Status enums.MarketChangesStatus `json:"status" binding:"required"`    // 状态
	}

	EditMarketChangesStatusResp struct {
		ID int64 `json:"id,string"` // 行情异动ID
	}
)

// 删除行情异动相关结构体
type (
	DelMarketChangesReq struct {
		ID int64 `json:"id,string" binding:"required"` // 行情异动ID
	}

	DelMarketChangesResp struct {
		ID int64 `json:"id,string"` // 行情异动ID
	}
)
