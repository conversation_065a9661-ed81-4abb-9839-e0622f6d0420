package enums

// MarketChangesStatus 行情异动状态 1待发布 2定时中 3已发布 4已下架
type MarketChangesStatus int32

func (s MarketChangesStatus) Val() int32 {
	return int32(s)
}

const (
	// MarketChangesStatusDraft 待发布
	MarketChangesStatusDraft MarketChangesStatus = 1
	// MarketChangesStatusScheduled 定时中
	MarketChangesStatusScheduled MarketChangesStatus = 2
	// MarketChangesStatusPublished 已发布
	MarketChangesStatusPublished MarketChangesStatus = 3
	// MarketChangesStatusOffline 已下架
	MarketChangesStatusOffline MarketChangesStatus = 4
)

// MarketChangesPublishType 行情异动发布方式 1立即发布 2定时发布
type MarketChangesPublishType int32

func (s MarketChangesPublishType) Val() int32 {
	return int32(s)
}

const (
	// MarketChangesPublishTypeImmediate 立即发布
	MarketChangesPublishTypeImmediate MarketChangesPublishType = 1
	// MarketChangesPublishTypeTiming 定时发布
	MarketChangesPublishTypeTiming MarketChangesPublishType = 2
)

// MarketChangesTimeType 时间类型 1发布时间 2创建时间
type MarketChangesTimeType int32

func (s MarketChangesTimeType) Val() int32 {
	return int32(s)
}

const (
	// MarketChangesTimeTypePublish 发布时间
	MarketChangesTimeTypePublish MarketChangesTimeType = 1
	// MarketChangesTimeTypeCreate 创建时间
	MarketChangesTimeTypeCreate MarketChangesTimeType = 2
)

// MarketChangesMessagePushType 消息推送类型 0不推送 1推送
type MarketChangesMessagePushType int32

func (s MarketChangesMessagePushType) Val() int32 {
	return int32(s)
}

const (
	// MarketChangesMessagePushNo 不推送
	MarketChangesMessagePushNo MarketChangesMessagePushType = 0
	// MarketChangesMessagePushYes 推送
	MarketChangesMessagePushYes MarketChangesMessagePushType = 1
)
