package router

import (
	"app_service/apps/business/market_changes/router/open"
	"app_service/apps/business/market_changes/router/web"
	"fmt"

	"app_service/apps/business/market_changes/router/admin"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	adminRote(r)
	// 用户端路由
	webRote(r)

	openRoute(r)
}

// 管理端路由
func adminRote(router *gin.Engine) {
	w := router.Group("/admin/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(),
		auth.Auth(&auth.Admin{
			NoAuthUrl: []string{},
		}),
	)

	admin.MarketChanges(w)
}

// 用户端路由
func webRote(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/market_changes/list",
			"/web/v1/market_changes/detail",
			"/web/v1/market_changes/latest_detail",
		},
	}))
	web.MarketChanges(w)
}

// 对外路由
func openRoute(router *gin.Engine) {
	w := router.Group("/open/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token:     global.GlobalConfig.Service.Token,
		NoAuthUrl: []string{},
	}))

	open.MarketChanges(w)
}
