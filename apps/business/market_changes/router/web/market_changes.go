package web

import (
	"app_service/apps/business/market_changes/api/web"
	"github.com/gin-gonic/gin"
)

// MarketChanges 行情异动用户端相关
func MarketChanges(router *gin.RouterGroup) {
	group := router.Group("/market_changes")
	{
		// 行情异动列表
		group.GET("/list", web.GetMarketChangesWebList)
		// 行情异动详情
		group.GET("/detail", web.GetMarketChangesWebDetail)
		// 最新行情异动详情
		group.GET("/latest_detail", web.GetLatestMarketChangesWebDetail)
		//// 行情异动分类列表
		//group.GET("/category/list", web.GetCategoryWebList)
	}
}
