package admin

import (
	"app_service/apps/business/market_changes/api/admin"
	"github.com/gin-gonic/gin"
)

// MarketChanges 行情异动管理端相关
func MarketChanges(router *gin.RouterGroup) {
	group := router.Group("/market_changes")
	{
		// 获取行情异动列表
		group.GET("/list", admin.GetMarketChangesList)
		// 获取行情异动详情
		group.GET("/detail", admin.GetMarketChangesDetail)
		// 新增行情异动
		group.POST("/add", admin.AddMarketChanges)
		// 编辑行情异动
		group.POST("/edit", admin.EditMarketChanges)
		// 行情异动分类状态编辑
		group.POST("/edit_status", admin.EditMarketChangesStatus)
		// 行情异动删除
		//group.POST("/del", admin.DelMarketChanges)
	}
}
