package service

import (
	"app_service/apps/business/market_changes/dal/model"
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/define/enums"
	"app_service/apps/business/market_changes/repo"
	"app_service/apps/business/market_changes/service/logic"
	"errors"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// SchedulePublish 定时发布
func (s *Service) SchedulePublish(req *define.SchedulePublishReq) (*define.SchedulePublishResp, error) {
	var mcList []*model.MarketChanges
	err := repo.GetDB().WithContext(s.ctx).
		Where("status = ?", enums.MarketChangesStatusScheduled.Val()).
		Where("publish_time < ?", time.Now()).
		Preload("Items").
		Preload("Channels").
		Find(&mcList).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &define.SchedulePublishResp{}, nil
		}
		log.Ctx(s.ctx).Errorf("Failed to query scheduled market changes: %v", err)
		return nil, err
	}

	// 更新每个记录的涨跌幅和状态
	for _, m := range mcList {
		marketChangesItems := make([]*model.MarketChangesItem, 0)

		for _, item := range m.Items {
			marketChangesItems = append(marketChangesItems, &model.MarketChangesItem{
				MarketChangesID: item.MarketChangesID,
				ItemID:          item.ItemID,
				ItemName:        item.ItemName,
				ImageURL:        item.ImageURL,
			})
		}
		if err := logic.UpdatePriceChanges(s.ctx, marketChangesItems); err != nil {
			log.Ctx(s.ctx).Errorf("UpdatePriceChanges for marketChangesID %d err:%v", m.MarketChangesID, err)
			continue
		}

		// 使用事务更新状态和涨跌幅
		err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
			// 更新行情异动基本信息
			if err := tx.Model(&model.MarketChanges{}).
				Where("market_changes_id = ?", m.MarketChangesID).
				Updates(map[string]interface{}{
					"status": enums.MarketChangesStatusPublished.Val(),
				}).Error; err != nil {
				return err
			}

			// 更新关联商品
			for _, item := range marketChangesItems {
				if err := tx.Model(&model.MarketChangesItem{}).
					Where("market_changes_id = ? AND item_id = ?", m.MarketChangesID, item.ItemID).
					Updates(map[string]interface{}{"price_changes": item.PriceChanges}).Error; err != nil {
					return err
				}
			}

			return nil
		})

		if err != nil {
			log.Ctx(s.ctx).Errorf("Transaction update for marketChangesID %d err:%v", m.MarketChangesID, err)
			continue
		}
		channelIds := logic.ExtractChannelIDs(m.Channels)
		channelIds = logic.ExpandSpecialChannel(channelIds)
		// 缓存行情异动数据
		err := logic.AddMarketChangesCache(s.ctx, channelIds, m)
		if err != nil {
			log.Ctx(s.ctx).Errorf("AddMarketChangesCache failed: %+v", err)
		}

		log.Ctx(s.ctx).Infof("Transaction update for marketChangesID %d success, marketChangesItems: %v", m.MarketChangesID, marketChangesItems)
	}

	// 消息推送
	if len(mcList) > 0 {
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			for _, m := range mcList {
				if m.MessagePush == enums.MarketChangesMessagePushYes.Val() {
					pushErr := logic.PushMarketChangesMessage(spanCtx, m)
					if pushErr != nil {
						log.Ctx(spanCtx).Errorf("行情异动消息推送失败: %v", pushErr)
					}

					time.Sleep(time.Second * 5)
				}
			}
		}()
	}

	return &define.SchedulePublishResp{}, nil
}
