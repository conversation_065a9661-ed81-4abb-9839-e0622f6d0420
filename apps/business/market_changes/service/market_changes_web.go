package service

import (
	"app_service/apps/business/market_changes/dal/model"
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/define/enums"
	"app_service/apps/business/market_changes/repo"
	"app_service/apps/platform/common/constant"
	issueFacade "app_service/apps/platform/issue/facade"
	issueLogic "app_service/apps/platform/issue/logic"
	"app_service/global"
	"app_service/pkg/search"
	"errors"
	"github.com/go-redis/redis/v8"
	"strconv"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetMarketChangesWebList 获取行情异动列表（Web端）
// 支持按关键字（标题、内容、关联商品名）和分类ID搜索。
// 仅返回已发布且发布时间已到的行情异动。
// 按发布时间降序排序。
func (s *Service) GetMarketChangesWebList(req *define.GetMarketChangesWebListReq) (*define.GetMarketChangesWebListResp, error) {
	channel, _ := s.ctx.Value(constant.AppChannel).(string)
	start := time.Now()
	threeMonthsAgo := time.Now().AddDate(0, -3, 0)
	list := make([]*model.MarketChanges, 0)
	needQueryDB := true
	if req.KeyWord == "" {
		threeMonthsAgoTimestamp := float64(threeMonthsAgo.UnixNano())
		currentTimestamp := float64(start.UnixNano())
		cacheKey := constant.GetMarketChangesChannelListKey(channel)
		if req.CategoryID != 0 {
			cacheKey = constant.GetMarketChangesChannelCategoryListKeyKey(
				channel,
				req.CategoryID,
			)
		}
		offset := (req.GetPage() - 1) * req.GetPageSize()
		opt := &redis.ZRangeBy{
			Min:    strconv.FormatFloat(threeMonthsAgoTimestamp, 'f', -1, 64),
			Max:    strconv.FormatFloat(currentTimestamp, 'f', -1, 64),
			Offset: int64(offset),
			Count:  int64(req.GetPageSize()),
		}
		marketChangesIDs, err := global.REDIS.ZRevRangeByScore(s.ctx, cacheKey, opt).Result()
		if err != nil {
			log.Ctx(s.ctx).Errorf("Redis ZRevRangeByScore failed: %v", err)
			return nil, err
		}
		var mcIDs []int64
		for _, idStr := range marketChangesIDs {
			id, err := strconv.ParseInt(idStr, 10, 64)
			if err != nil {
				log.Ctx(s.ctx).Errorf("Invalid MarketChangesID in Redis: %s", idStr)
				continue
			}
			mcIDs = append(mcIDs, id)
		}
		if len(mcIDs) == 0 {
			return &define.GetMarketChangesWebListResp{
				List:    make([]*define.GetMarketChangesWebListData, 0),
				HasMore: false,
			}, nil
		}
		schema := repo.GetQuery().MarketChanges
		qw := search.NewQueryBuilder().
			Select(schema.MarketChangesID, schema.Title, schema.Content, schema.CategoryID, schema.PublishTime).
			In(schema.MarketChangesID, mcIDs).
			OrderByDesc(schema.PublishTime, schema.MarketChangesID).Build()
		list, err = repo.NewMarketChangesRepo(schema.WithContext(s.ctx).Preload(schema.Items)).SelectList(qw)
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetMarketChangesWebList SelectList err: %v, req: %+v", err, req)
			return nil, err
		}
		if len(list) > 0 {
			needQueryDB = false
		}
	}
	if needQueryDB {
		db := repo.GetDB().WithContext(s.ctx)

		start := time.Now()
		log.Ctx(s.ctx).Debugf("GetMarketChangesWebList start - param = %+v", req)

		query := db.Table(model.TableNameMarketChanges).
			//Preload("CategoryInfo").
			Preload("Items")

		// 添加基本过滤条件：status = ?
		query = query.Where("market_changes.status = ?", enums.MarketChangesStatusPublished.Val())

		// 添加时间过滤条件：publish_time >= ? (近3个月)
		query = query.Where("market_changes.publish_time >= ?", threeMonthsAgo.Format("2006-01-02 15:04:05"))

		// 添加 channel 过滤条件：使用 INNER JOIN
		query = query.Joins("INNER JOIN market_changes_channel ON market_changes.market_changes_id = market_changes_channel.market_changes_id").
			Where("market_changes_channel.channel_id = ? OR market_changes_channel.channel_id = 'all'", channel)

		// 实现分类过滤逻辑的条件判断
		if req.CategoryID != 0 {
			// 指定了分类ID，直接按分类ID过滤
			query = query.Where("market_changes.category_id = ?", req.CategoryID)
		}

		// 实现关键字搜索过滤逻辑的条件判断
		if req.KeyWord != "" {
			keyword := "%" + req.KeyWord + "%"
			// 使用 Or() 构建组合条件：标题匹配 OR 内容匹配 OR 关联商品名称匹配
			query = query.Where(
				db.Where("market_changes.title LIKE ?", keyword).
					Or("market_changes.content LIKE ?", keyword).
					Or("EXISTS(?)", db.Table(model.TableNameMarketChangesItem).
						Select("1").
						Where("market_changes_item.market_changes_id = market_changes.market_changes_id").
						Where("market_changes_item.item_name LIKE ?", keyword),
					),
			)
		}

		// 设置分页条件
		offset := (req.GetPage() - 1) * req.GetPageSize()
		if offset < 0 {
			offset = 0
		}
		query = query.Offset(offset).Limit(req.GetPageSize())

		// 设置排序条件
		query = query.Order("market_changes.publish_time DESC")

		// 执行最终的 GORM 查询
		err := query.Find(&list).Error
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetMarketChangesWebList Find err: %v, req: %+v", err, req)
			return nil, err
		}
		log.Ctx(s.ctx).Debugf("GetMarketChangesWebList db query took %v", time.Since(start))
	}

	resp := &define.GetMarketChangesWebListResp{
		List: make([]*define.GetMarketChangesWebListData, 0, len(list)),
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有需要查询的商品ID
	var itemIds []string
	for _, v := range list {
		for _, item := range v.Items {
			itemIds = append(itemIds, item.ItemID)
		}
	}

	// 批量查询商品信息
	itemMap, err := issueFacade.GetIssueItemMapByCache(s.ctx, itemIds)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetIssueItemMap err: %v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Debugf("GetMarketChangesWebList GetIssueItemMap took %v", time.Since(start))

	// 处理商品流通状态
	issueLogic.GetCirculationStatus(itemMap)

	nowStr := time.Now().Format("2006-01-02T15:04:05.000Z07:00")
	for _, v := range list {
		dataItem := &define.GetMarketChangesWebListData{
			ID:          v.MarketChangesID,
			Title:       v.Title,
			CategoryID:  v.CategoryID,
			PublishTime: v.PublishTime,
			CurrentTime: nowStr,
			IsCanJump:   v.Content != "",
		}

		if len(v.Items) > 0 {
			dataItem.ItemIdInfoList = make([]*define.GetItemIdInfoWebList, 0, len(v.Items))
			for _, item := range v.Items {
				if itemInfo, ok := itemMap[item.ItemID]; ok {
					dataItem.ItemIdInfoList = append(dataItem.ItemIdInfoList, &define.GetItemIdInfoWebList{
						ItemId:            item.ItemID,
						ItemName:          item.ItemName,
						PriceChanges:      item.PriceChanges,
						CirculationStatus: itemInfo.CirculationStatus,
						Status:            itemInfo.Status,
					})
				}
			}
		}
		resp.List = append(resp.List, dataItem)
	}

	resp.HasMore = len(resp.List) == req.GetPageSize()
	log.Ctx(s.ctx).Debugf("GetMarketChangesWebList end - total took %v", time.Since(start))
	return resp, nil
}

// GetMarketChangesWebDetail 获取行情异动详情（Web端）
// 根据行情异动ID查询，仅返回已发布的行情异动。
func (s *Service) GetMarketChangesWebDetail(req *define.GetMarketChangesWebDetailReq) (*define.GetMarketChangesWebDetailResp, error) {
	mcSchema := repo.GetQuery().MarketChanges
	query := mcSchema.WithContext(s.ctx).
		Where(mcSchema.MarketChangesID.Eq(req.ID)).
		Where(mcSchema.Status.Eq(enums.MarketChangesStatusPublished.Val()))
	//Where(mcSchema.PublishTime.Lte(time.Now())

	marketChanges, err := query.Preload(mcSchema.CategoryInfo).First()
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 或者返回自定义的未找到错误
		}
		log.Ctx(s.ctx).Errorf("GetMarketChangesWebDetail First err: %v, req: %+v", err, req)
		return nil, err
	}

	resp := &define.GetMarketChangesWebDetailResp{
		ID:          marketChanges.MarketChangesID,
		Title:       marketChanges.Title,
		Content:     marketChanges.Content,
		CategoryID:  marketChanges.CategoryID,
		PublishTime: marketChanges.PublishTime,
		IsCanJump:   marketChanges.Content != "",
	}

	if marketChanges.CategoryInfo.CategoryID > 0 {
		resp.CategoryInfo = &define.GetCategoryWebLessDetailResp{
			ID:              marketChanges.CategoryInfo.CategoryID,
			Name:            marketChanges.CategoryInfo.Name,
			TextColor:       marketChanges.CategoryInfo.TextColor,
			BackgroundColor: marketChanges.CategoryInfo.BackgroundColor,
		}
	}

	return resp, nil
}

// GetLatestMarketChangesWebDetail 根据商品ID获取最新的已发布行情异动详情（Web端）
func (s *Service) GetLatestMarketChangesWebDetail(req *define.GetLatestMarketChangesWebDetailReq) (*define.GetMarketChangesWebDetailResp, error) {
	if req.ItemId == "" {
		return nil, nil
	}

	channel, _ := s.ctx.Value(constant.AppChannel).(string)

	// 初始化 GORM 查询构建器
	query := repo.GetDB().WithContext(s.ctx).Table(model.TableNameMarketChanges)

	// 添加基本过滤条件：已发布状态
	query = query.Where("status = ?", enums.MarketChangesStatusPublished.Val())

	// 添加 item_id 过滤条件：使用 EXISTS 子查询判断是否存在关联的 market_changes_item 记录
	query = query.Where("EXISTS(?)", repo.GetDB().Table(model.TableNameMarketChangesItem).
		Select("1").
		Where("market_changes_item.market_changes_id = market_changes.market_changes_id").
		Where("market_changes_item.item_id = ?", req.ItemId),
	)

	// 添加 channel 过滤条件：使用 EXISTS 子查询判断是否存在关联的 market_changes_channel 记录
	query = query.Where("EXISTS(?)", repo.GetDB().Table(model.TableNameMarketChangesChannel).
		Select("1").
		Where("market_changes_channel.market_changes_id = market_changes.market_changes_id").
		Where("market_changes_channel.channel_id = ? OR market_changes_channel.channel_id = 'all'", channel),
	)

	// 设置排序条件：按发布时间降序
	query = query.Order("publish_time DESC")

	// 设置预加载
	query = query.Preload("CategoryInfo")

	// 执行查询，获取最新的符合条件的记录
	marketChanges := &model.MarketChanges{} // 初始化结构体用于存放结果
	err := query.First(marketChanges).Error // 使用 First() 获取单条记录

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil // 没有找到符合条件的记录
		}
		log.Ctx(s.ctx).Errorf("GetLatestMarketChangesWebDetail First err: %v, req: %+v", err, req)
		return nil, err
	}

	resp := &define.GetMarketChangesWebDetailResp{
		ID:          marketChanges.MarketChangesID,
		Title:       marketChanges.Title,
		Content:     marketChanges.Content,
		CategoryID:  marketChanges.CategoryID,
		PublishTime: marketChanges.PublishTime,
		IsCanJump:   marketChanges.Content != "",
	}

	if marketChanges.CategoryInfo.CategoryID > 0 {
		resp.CategoryInfo = &define.GetCategoryWebLessDetailResp{
			ID:              marketChanges.CategoryInfo.CategoryID,
			Name:            marketChanges.CategoryInfo.Name,
			TextColor:       marketChanges.CategoryInfo.TextColor,
			BackgroundColor: marketChanges.CategoryInfo.BackgroundColor,
		}
	}

	return resp, nil
}
