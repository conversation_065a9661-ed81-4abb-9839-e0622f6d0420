// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/market_changes/dal/model"
)

func newMarketChangesChannel(db *gorm.DB, opts ...gen.DOOption) marketChangesChannel {
	_marketChangesChannel := marketChangesChannel{}

	_marketChangesChannel.marketChangesChannelDo.UseDB(db, opts...)
	_marketChangesChannel.marketChangesChannelDo.UseModel(&model.MarketChangesChannel{})

	tableName := _marketChangesChannel.marketChangesChannelDo.TableName()
	_marketChangesChannel.ALL = field.NewAsterisk(tableName)
	_marketChangesChannel.MarketChangesID = field.NewInt64(tableName, "market_changes_id")
	_marketChangesChannel.ChannelID = field.NewString(tableName, "channel_id")
	_marketChangesChannel.CreatedAt = field.NewTime(tableName, "created_at")

	_marketChangesChannel.fillFieldMap()

	return _marketChangesChannel
}

// marketChangesChannel 行情异动与发布终端关联表
type marketChangesChannel struct {
	marketChangesChannelDo

	ALL             field.Asterisk
	MarketChangesID field.Int64  // 行情异动ID
	ChannelID       field.String // 发布终端ID
	CreatedAt       field.Time   // 创建时间

	fieldMap map[string]field.Expr
}

func (m marketChangesChannel) Table(newTableName string) *marketChangesChannel {
	m.marketChangesChannelDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m marketChangesChannel) As(alias string) *marketChangesChannel {
	m.marketChangesChannelDo.DO = *(m.marketChangesChannelDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *marketChangesChannel) updateTableName(table string) *marketChangesChannel {
	m.ALL = field.NewAsterisk(table)
	m.MarketChangesID = field.NewInt64(table, "market_changes_id")
	m.ChannelID = field.NewString(table, "channel_id")
	m.CreatedAt = field.NewTime(table, "created_at")

	m.fillFieldMap()

	return m
}

func (m *marketChangesChannel) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *marketChangesChannel) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 3)
	m.fieldMap["market_changes_id"] = m.MarketChangesID
	m.fieldMap["channel_id"] = m.ChannelID
	m.fieldMap["created_at"] = m.CreatedAt
}

func (m marketChangesChannel) clone(db *gorm.DB) marketChangesChannel {
	m.marketChangesChannelDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m marketChangesChannel) replaceDB(db *gorm.DB) marketChangesChannel {
	m.marketChangesChannelDo.ReplaceDB(db)
	return m
}

type marketChangesChannelDo struct{ gen.DO }

type IMarketChangesChannelDo interface {
	gen.SubQuery
	Debug() IMarketChangesChannelDo
	WithContext(ctx context.Context) IMarketChangesChannelDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMarketChangesChannelDo
	WriteDB() IMarketChangesChannelDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMarketChangesChannelDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMarketChangesChannelDo
	Not(conds ...gen.Condition) IMarketChangesChannelDo
	Or(conds ...gen.Condition) IMarketChangesChannelDo
	Select(conds ...field.Expr) IMarketChangesChannelDo
	Where(conds ...gen.Condition) IMarketChangesChannelDo
	Order(conds ...field.Expr) IMarketChangesChannelDo
	Distinct(cols ...field.Expr) IMarketChangesChannelDo
	Omit(cols ...field.Expr) IMarketChangesChannelDo
	Join(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo
	Group(cols ...field.Expr) IMarketChangesChannelDo
	Having(conds ...gen.Condition) IMarketChangesChannelDo
	Limit(limit int) IMarketChangesChannelDo
	Offset(offset int) IMarketChangesChannelDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesChannelDo
	Unscoped() IMarketChangesChannelDo
	Create(values ...*model.MarketChangesChannel) error
	CreateInBatches(values []*model.MarketChangesChannel, batchSize int) error
	Save(values ...*model.MarketChangesChannel) error
	First() (*model.MarketChangesChannel, error)
	Take() (*model.MarketChangesChannel, error)
	Last() (*model.MarketChangesChannel, error)
	Find() ([]*model.MarketChangesChannel, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesChannel, err error)
	FindInBatches(result *[]*model.MarketChangesChannel, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MarketChangesChannel) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMarketChangesChannelDo
	Assign(attrs ...field.AssignExpr) IMarketChangesChannelDo
	Joins(fields ...field.RelationField) IMarketChangesChannelDo
	Preload(fields ...field.RelationField) IMarketChangesChannelDo
	FirstOrInit() (*model.MarketChangesChannel, error)
	FirstOrCreate() (*model.MarketChangesChannel, error)
	FindByPage(offset int, limit int) (result []*model.MarketChangesChannel, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMarketChangesChannelDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m marketChangesChannelDo) Debug() IMarketChangesChannelDo {
	return m.withDO(m.DO.Debug())
}

func (m marketChangesChannelDo) WithContext(ctx context.Context) IMarketChangesChannelDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m marketChangesChannelDo) ReadDB() IMarketChangesChannelDo {
	return m.Clauses(dbresolver.Read)
}

func (m marketChangesChannelDo) WriteDB() IMarketChangesChannelDo {
	return m.Clauses(dbresolver.Write)
}

func (m marketChangesChannelDo) Session(config *gorm.Session) IMarketChangesChannelDo {
	return m.withDO(m.DO.Session(config))
}

func (m marketChangesChannelDo) Clauses(conds ...clause.Expression) IMarketChangesChannelDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m marketChangesChannelDo) Returning(value interface{}, columns ...string) IMarketChangesChannelDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m marketChangesChannelDo) Not(conds ...gen.Condition) IMarketChangesChannelDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m marketChangesChannelDo) Or(conds ...gen.Condition) IMarketChangesChannelDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m marketChangesChannelDo) Select(conds ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m marketChangesChannelDo) Where(conds ...gen.Condition) IMarketChangesChannelDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m marketChangesChannelDo) Order(conds ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m marketChangesChannelDo) Distinct(cols ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m marketChangesChannelDo) Omit(cols ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m marketChangesChannelDo) Join(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m marketChangesChannelDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m marketChangesChannelDo) RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m marketChangesChannelDo) Group(cols ...field.Expr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m marketChangesChannelDo) Having(conds ...gen.Condition) IMarketChangesChannelDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m marketChangesChannelDo) Limit(limit int) IMarketChangesChannelDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m marketChangesChannelDo) Offset(offset int) IMarketChangesChannelDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m marketChangesChannelDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesChannelDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m marketChangesChannelDo) Unscoped() IMarketChangesChannelDo {
	return m.withDO(m.DO.Unscoped())
}

func (m marketChangesChannelDo) Create(values ...*model.MarketChangesChannel) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m marketChangesChannelDo) CreateInBatches(values []*model.MarketChangesChannel, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m marketChangesChannelDo) Save(values ...*model.MarketChangesChannel) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m marketChangesChannelDo) First() (*model.MarketChangesChannel, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesChannel), nil
	}
}

func (m marketChangesChannelDo) Take() (*model.MarketChangesChannel, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesChannel), nil
	}
}

func (m marketChangesChannelDo) Last() (*model.MarketChangesChannel, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesChannel), nil
	}
}

func (m marketChangesChannelDo) Find() ([]*model.MarketChangesChannel, error) {
	result, err := m.DO.Find()
	return result.([]*model.MarketChangesChannel), err
}

func (m marketChangesChannelDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesChannel, err error) {
	buf := make([]*model.MarketChangesChannel, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m marketChangesChannelDo) FindInBatches(result *[]*model.MarketChangesChannel, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m marketChangesChannelDo) Attrs(attrs ...field.AssignExpr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m marketChangesChannelDo) Assign(attrs ...field.AssignExpr) IMarketChangesChannelDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m marketChangesChannelDo) Joins(fields ...field.RelationField) IMarketChangesChannelDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m marketChangesChannelDo) Preload(fields ...field.RelationField) IMarketChangesChannelDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m marketChangesChannelDo) FirstOrInit() (*model.MarketChangesChannel, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesChannel), nil
	}
}

func (m marketChangesChannelDo) FirstOrCreate() (*model.MarketChangesChannel, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesChannel), nil
	}
}

func (m marketChangesChannelDo) FindByPage(offset int, limit int) (result []*model.MarketChangesChannel, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m marketChangesChannelDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m marketChangesChannelDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m marketChangesChannelDo) Delete(models ...*model.MarketChangesChannel) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *marketChangesChannelDo) withDO(do gen.Dao) *marketChangesChannelDo {
	m.DO = *do.(*gen.DO)
	return m
}
