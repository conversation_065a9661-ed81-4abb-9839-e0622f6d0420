// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                    = new(Query)
	Category             *category
	MarketChanges        *marketChanges
	MarketChangesChannel *marketChangesChannel
	MarketChangesItem    *marketChangesItem
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Category = &Q.Category
	MarketChanges = &Q.MarketChanges
	MarketChangesChannel = &Q.MarketChangesChannel
	MarketChangesItem = &Q.MarketChangesItem
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                   db,
		Category:             newCategory(db, opts...),
		MarketChanges:        newMarketChanges(db, opts...),
		MarketChangesChannel: newMarketChangesChannel(db, opts...),
		MarketChangesItem:    newMarketChangesItem(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Category             category
	MarketChanges        marketChanges
	MarketChangesChannel marketChangesChannel
	MarketChangesItem    marketChangesItem
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		Category:             q.Category.clone(db),
		MarketChanges:        q.MarketChanges.clone(db),
		MarketChangesChannel: q.MarketChangesChannel.clone(db),
		MarketChangesItem:    q.MarketChangesItem.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                   db,
		Category:             q.Category.replaceDB(db),
		MarketChanges:        q.MarketChanges.replaceDB(db),
		MarketChangesChannel: q.MarketChangesChannel.replaceDB(db),
		MarketChangesItem:    q.MarketChangesItem.replaceDB(db),
	}
}

type queryCtx struct {
	Category             ICategoryDo
	MarketChanges        IMarketChangesDo
	MarketChangesChannel IMarketChangesChannelDo
	MarketChangesItem    IMarketChangesItemDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Category:             q.Category.WithContext(ctx),
		MarketChanges:        q.MarketChanges.WithContext(ctx),
		MarketChangesChannel: q.MarketChangesChannel.WithContext(ctx),
		MarketChangesItem:    q.MarketChangesItem.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
