// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/market_changes/dal/model"
)

func newMarketChangesItem(db *gorm.DB, opts ...gen.DOOption) marketChangesItem {
	_marketChangesItem := marketChangesItem{}

	_marketChangesItem.marketChangesItemDo.UseDB(db, opts...)
	_marketChangesItem.marketChangesItemDo.UseModel(&model.MarketChangesItem{})

	tableName := _marketChangesItem.marketChangesItemDo.TableName()
	_marketChangesItem.ALL = field.NewAsterisk(tableName)
	_marketChangesItem.MarketChangesID = field.NewInt64(tableName, "market_changes_id")
	_marketChangesItem.ItemID = field.NewString(tableName, "item_id")
	_marketChangesItem.ItemName = field.NewString(tableName, "item_name")
	_marketChangesItem.ImageURL = field.NewString(tableName, "image_url")
	_marketChangesItem.PriceChanges = field.NewFloat64(tableName, "price_changes")
	_marketChangesItem.CreatedAt = field.NewTime(tableName, "created_at")

	_marketChangesItem.fillFieldMap()

	return _marketChangesItem
}

// marketChangesItem 行情异动与商品关联表
type marketChangesItem struct {
	marketChangesItemDo

	ALL             field.Asterisk
	MarketChangesID field.Int64   // 行情异动ID
	ItemID          field.String  // 商品ID (商品挂牌编码)
	ItemName        field.String  // 商品名称
	ImageURL        field.String  // 商品图片
	PriceChanges    field.Float64 // 价格变动
	CreatedAt       field.Time    // 创建时间

	fieldMap map[string]field.Expr
}

func (m marketChangesItem) Table(newTableName string) *marketChangesItem {
	m.marketChangesItemDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m marketChangesItem) As(alias string) *marketChangesItem {
	m.marketChangesItemDo.DO = *(m.marketChangesItemDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *marketChangesItem) updateTableName(table string) *marketChangesItem {
	m.ALL = field.NewAsterisk(table)
	m.MarketChangesID = field.NewInt64(table, "market_changes_id")
	m.ItemID = field.NewString(table, "item_id")
	m.ItemName = field.NewString(table, "item_name")
	m.ImageURL = field.NewString(table, "image_url")
	m.PriceChanges = field.NewFloat64(table, "price_changes")
	m.CreatedAt = field.NewTime(table, "created_at")

	m.fillFieldMap()

	return m
}

func (m *marketChangesItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *marketChangesItem) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 6)
	m.fieldMap["market_changes_id"] = m.MarketChangesID
	m.fieldMap["item_id"] = m.ItemID
	m.fieldMap["item_name"] = m.ItemName
	m.fieldMap["image_url"] = m.ImageURL
	m.fieldMap["price_changes"] = m.PriceChanges
	m.fieldMap["created_at"] = m.CreatedAt
}

func (m marketChangesItem) clone(db *gorm.DB) marketChangesItem {
	m.marketChangesItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m marketChangesItem) replaceDB(db *gorm.DB) marketChangesItem {
	m.marketChangesItemDo.ReplaceDB(db)
	return m
}

type marketChangesItemDo struct{ gen.DO }

type IMarketChangesItemDo interface {
	gen.SubQuery
	Debug() IMarketChangesItemDo
	WithContext(ctx context.Context) IMarketChangesItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMarketChangesItemDo
	WriteDB() IMarketChangesItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMarketChangesItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMarketChangesItemDo
	Not(conds ...gen.Condition) IMarketChangesItemDo
	Or(conds ...gen.Condition) IMarketChangesItemDo
	Select(conds ...field.Expr) IMarketChangesItemDo
	Where(conds ...gen.Condition) IMarketChangesItemDo
	Order(conds ...field.Expr) IMarketChangesItemDo
	Distinct(cols ...field.Expr) IMarketChangesItemDo
	Omit(cols ...field.Expr) IMarketChangesItemDo
	Join(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo
	Group(cols ...field.Expr) IMarketChangesItemDo
	Having(conds ...gen.Condition) IMarketChangesItemDo
	Limit(limit int) IMarketChangesItemDo
	Offset(offset int) IMarketChangesItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesItemDo
	Unscoped() IMarketChangesItemDo
	Create(values ...*model.MarketChangesItem) error
	CreateInBatches(values []*model.MarketChangesItem, batchSize int) error
	Save(values ...*model.MarketChangesItem) error
	First() (*model.MarketChangesItem, error)
	Take() (*model.MarketChangesItem, error)
	Last() (*model.MarketChangesItem, error)
	Find() ([]*model.MarketChangesItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesItem, err error)
	FindInBatches(result *[]*model.MarketChangesItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MarketChangesItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMarketChangesItemDo
	Assign(attrs ...field.AssignExpr) IMarketChangesItemDo
	Joins(fields ...field.RelationField) IMarketChangesItemDo
	Preload(fields ...field.RelationField) IMarketChangesItemDo
	FirstOrInit() (*model.MarketChangesItem, error)
	FirstOrCreate() (*model.MarketChangesItem, error)
	FindByPage(offset int, limit int) (result []*model.MarketChangesItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMarketChangesItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m marketChangesItemDo) Debug() IMarketChangesItemDo {
	return m.withDO(m.DO.Debug())
}

func (m marketChangesItemDo) WithContext(ctx context.Context) IMarketChangesItemDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m marketChangesItemDo) ReadDB() IMarketChangesItemDo {
	return m.Clauses(dbresolver.Read)
}

func (m marketChangesItemDo) WriteDB() IMarketChangesItemDo {
	return m.Clauses(dbresolver.Write)
}

func (m marketChangesItemDo) Session(config *gorm.Session) IMarketChangesItemDo {
	return m.withDO(m.DO.Session(config))
}

func (m marketChangesItemDo) Clauses(conds ...clause.Expression) IMarketChangesItemDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m marketChangesItemDo) Returning(value interface{}, columns ...string) IMarketChangesItemDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m marketChangesItemDo) Not(conds ...gen.Condition) IMarketChangesItemDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m marketChangesItemDo) Or(conds ...gen.Condition) IMarketChangesItemDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m marketChangesItemDo) Select(conds ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m marketChangesItemDo) Where(conds ...gen.Condition) IMarketChangesItemDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m marketChangesItemDo) Order(conds ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m marketChangesItemDo) Distinct(cols ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m marketChangesItemDo) Omit(cols ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m marketChangesItemDo) Join(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m marketChangesItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m marketChangesItemDo) RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m marketChangesItemDo) Group(cols ...field.Expr) IMarketChangesItemDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m marketChangesItemDo) Having(conds ...gen.Condition) IMarketChangesItemDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m marketChangesItemDo) Limit(limit int) IMarketChangesItemDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m marketChangesItemDo) Offset(offset int) IMarketChangesItemDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m marketChangesItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesItemDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m marketChangesItemDo) Unscoped() IMarketChangesItemDo {
	return m.withDO(m.DO.Unscoped())
}

func (m marketChangesItemDo) Create(values ...*model.MarketChangesItem) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m marketChangesItemDo) CreateInBatches(values []*model.MarketChangesItem, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m marketChangesItemDo) Save(values ...*model.MarketChangesItem) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m marketChangesItemDo) First() (*model.MarketChangesItem, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesItem), nil
	}
}

func (m marketChangesItemDo) Take() (*model.MarketChangesItem, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesItem), nil
	}
}

func (m marketChangesItemDo) Last() (*model.MarketChangesItem, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesItem), nil
	}
}

func (m marketChangesItemDo) Find() ([]*model.MarketChangesItem, error) {
	result, err := m.DO.Find()
	return result.([]*model.MarketChangesItem), err
}

func (m marketChangesItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChangesItem, err error) {
	buf := make([]*model.MarketChangesItem, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m marketChangesItemDo) FindInBatches(result *[]*model.MarketChangesItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m marketChangesItemDo) Attrs(attrs ...field.AssignExpr) IMarketChangesItemDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m marketChangesItemDo) Assign(attrs ...field.AssignExpr) IMarketChangesItemDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m marketChangesItemDo) Joins(fields ...field.RelationField) IMarketChangesItemDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m marketChangesItemDo) Preload(fields ...field.RelationField) IMarketChangesItemDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m marketChangesItemDo) FirstOrInit() (*model.MarketChangesItem, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesItem), nil
	}
}

func (m marketChangesItemDo) FirstOrCreate() (*model.MarketChangesItem, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChangesItem), nil
	}
}

func (m marketChangesItemDo) FindByPage(offset int, limit int) (result []*model.MarketChangesItem, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m marketChangesItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m marketChangesItemDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m marketChangesItemDo) Delete(models ...*model.MarketChangesItem) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *marketChangesItemDo) withDO(do gen.Dao) *marketChangesItemDo {
	m.DO = *do.(*gen.DO)
	return m
}
