// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/market_changes/dal/model"
)

func newMarketChanges(db *gorm.DB, opts ...gen.DOOption) marketChanges {
	_marketChanges := marketChanges{}

	_marketChanges.marketChangesDo.UseDB(db, opts...)
	_marketChanges.marketChangesDo.UseModel(&model.MarketChanges{})

	tableName := _marketChanges.marketChangesDo.TableName()
	_marketChanges.ALL = field.NewAsterisk(tableName)
	_marketChanges.MarketChangesID = field.NewInt64(tableName, "market_changes_id")
	_marketChanges.Title = field.NewString(tableName, "title")
	_marketChanges.Content = field.NewString(tableName, "content")
	_marketChanges.CategoryID = field.NewInt64(tableName, "category_id")
	_marketChanges.Status = field.NewInt32(tableName, "status")
	_marketChanges.PublishTime = field.NewTime(tableName, "publish_time")
	_marketChanges.PublishType = field.NewInt32(tableName, "publish_type")
	_marketChanges.MessagePush = field.NewInt32(tableName, "message_push")
	_marketChanges.CreatedBy = field.NewString(tableName, "created_by")
	_marketChanges.UpdatedBy = field.NewString(tableName, "updated_by")
	_marketChanges.CreatedAt = field.NewTime(tableName, "created_at")
	_marketChanges.UpdatedAt = field.NewTime(tableName, "updated_at")
	_marketChanges.CategoryInfo = marketChangesBelongsToCategoryInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CategoryInfo", "model.Category"),
	}

	_marketChanges.Items = marketChangesHasManyItems{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Items", "model.MarketChangesItem"),
	}

	_marketChanges.Channels = marketChangesHasManyChannels{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Channels", "model.MarketChangesChannel"),
	}

	_marketChanges.fillFieldMap()

	return _marketChanges
}

// marketChanges 行情异动表
type marketChanges struct {
	marketChangesDo

	ALL             field.Asterisk
	MarketChangesID field.Int64  // 主键ID
	Title           field.String // 标题
	Content         field.String // 内容
	CategoryID      field.Int64  // 栏目ID
	Status          field.Int32  // 状态：1待发布 2定时中 3已发布 4已下架
	PublishTime     field.Time   // 发布时间
	PublishType     field.Int32  // 发布方式：1立即发布 2定时发布
	MessagePush     field.Int32  // 消息推送【0->不推送; 1->推送】
	CreatedBy       field.String // 创建人
	UpdatedBy       field.String // 更新人
	CreatedAt       field.Time   // 创建时间
	UpdatedAt       field.Time   // 更新时间
	CategoryInfo    marketChangesBelongsToCategoryInfo

	Items marketChangesHasManyItems

	Channels marketChangesHasManyChannels

	fieldMap map[string]field.Expr
}

func (m marketChanges) Table(newTableName string) *marketChanges {
	m.marketChangesDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m marketChanges) As(alias string) *marketChanges {
	m.marketChangesDo.DO = *(m.marketChangesDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *marketChanges) updateTableName(table string) *marketChanges {
	m.ALL = field.NewAsterisk(table)
	m.MarketChangesID = field.NewInt64(table, "market_changes_id")
	m.Title = field.NewString(table, "title")
	m.Content = field.NewString(table, "content")
	m.CategoryID = field.NewInt64(table, "category_id")
	m.Status = field.NewInt32(table, "status")
	m.PublishTime = field.NewTime(table, "publish_time")
	m.PublishType = field.NewInt32(table, "publish_type")
	m.MessagePush = field.NewInt32(table, "message_push")
	m.CreatedBy = field.NewString(table, "created_by")
	m.UpdatedBy = field.NewString(table, "updated_by")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *marketChanges) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *marketChanges) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 15)
	m.fieldMap["market_changes_id"] = m.MarketChangesID
	m.fieldMap["title"] = m.Title
	m.fieldMap["content"] = m.Content
	m.fieldMap["category_id"] = m.CategoryID
	m.fieldMap["status"] = m.Status
	m.fieldMap["publish_time"] = m.PublishTime
	m.fieldMap["publish_type"] = m.PublishType
	m.fieldMap["message_push"] = m.MessagePush
	m.fieldMap["created_by"] = m.CreatedBy
	m.fieldMap["updated_by"] = m.UpdatedBy
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt

}

func (m marketChanges) clone(db *gorm.DB) marketChanges {
	m.marketChangesDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m marketChanges) replaceDB(db *gorm.DB) marketChanges {
	m.marketChangesDo.ReplaceDB(db)
	return m
}

type marketChangesBelongsToCategoryInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a marketChangesBelongsToCategoryInfo) Where(conds ...field.Expr) *marketChangesBelongsToCategoryInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a marketChangesBelongsToCategoryInfo) WithContext(ctx context.Context) *marketChangesBelongsToCategoryInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a marketChangesBelongsToCategoryInfo) Session(session *gorm.Session) *marketChangesBelongsToCategoryInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a marketChangesBelongsToCategoryInfo) Model(m *model.MarketChanges) *marketChangesBelongsToCategoryInfoTx {
	return &marketChangesBelongsToCategoryInfoTx{a.db.Model(m).Association(a.Name())}
}

type marketChangesBelongsToCategoryInfoTx struct{ tx *gorm.Association }

func (a marketChangesBelongsToCategoryInfoTx) Find() (result *model.Category, err error) {
	return result, a.tx.Find(&result)
}

func (a marketChangesBelongsToCategoryInfoTx) Append(values ...*model.Category) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a marketChangesBelongsToCategoryInfoTx) Replace(values ...*model.Category) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a marketChangesBelongsToCategoryInfoTx) Delete(values ...*model.Category) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a marketChangesBelongsToCategoryInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a marketChangesBelongsToCategoryInfoTx) Count() int64 {
	return a.tx.Count()
}

type marketChangesHasManyItems struct {
	db *gorm.DB

	field.RelationField
}

func (a marketChangesHasManyItems) Where(conds ...field.Expr) *marketChangesHasManyItems {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a marketChangesHasManyItems) WithContext(ctx context.Context) *marketChangesHasManyItems {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a marketChangesHasManyItems) Session(session *gorm.Session) *marketChangesHasManyItems {
	a.db = a.db.Session(session)
	return &a
}

func (a marketChangesHasManyItems) Model(m *model.MarketChanges) *marketChangesHasManyItemsTx {
	return &marketChangesHasManyItemsTx{a.db.Model(m).Association(a.Name())}
}

type marketChangesHasManyItemsTx struct{ tx *gorm.Association }

func (a marketChangesHasManyItemsTx) Find() (result []*model.MarketChangesItem, err error) {
	return result, a.tx.Find(&result)
}

func (a marketChangesHasManyItemsTx) Append(values ...*model.MarketChangesItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a marketChangesHasManyItemsTx) Replace(values ...*model.MarketChangesItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a marketChangesHasManyItemsTx) Delete(values ...*model.MarketChangesItem) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a marketChangesHasManyItemsTx) Clear() error {
	return a.tx.Clear()
}

func (a marketChangesHasManyItemsTx) Count() int64 {
	return a.tx.Count()
}

type marketChangesHasManyChannels struct {
	db *gorm.DB

	field.RelationField
}

func (a marketChangesHasManyChannels) Where(conds ...field.Expr) *marketChangesHasManyChannels {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a marketChangesHasManyChannels) WithContext(ctx context.Context) *marketChangesHasManyChannels {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a marketChangesHasManyChannels) Session(session *gorm.Session) *marketChangesHasManyChannels {
	a.db = a.db.Session(session)
	return &a
}

func (a marketChangesHasManyChannels) Model(m *model.MarketChanges) *marketChangesHasManyChannelsTx {
	return &marketChangesHasManyChannelsTx{a.db.Model(m).Association(a.Name())}
}

type marketChangesHasManyChannelsTx struct{ tx *gorm.Association }

func (a marketChangesHasManyChannelsTx) Find() (result []*model.MarketChangesChannel, err error) {
	return result, a.tx.Find(&result)
}

func (a marketChangesHasManyChannelsTx) Append(values ...*model.MarketChangesChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a marketChangesHasManyChannelsTx) Replace(values ...*model.MarketChangesChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a marketChangesHasManyChannelsTx) Delete(values ...*model.MarketChangesChannel) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a marketChangesHasManyChannelsTx) Clear() error {
	return a.tx.Clear()
}

func (a marketChangesHasManyChannelsTx) Count() int64 {
	return a.tx.Count()
}

type marketChangesDo struct{ gen.DO }

type IMarketChangesDo interface {
	gen.SubQuery
	Debug() IMarketChangesDo
	WithContext(ctx context.Context) IMarketChangesDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMarketChangesDo
	WriteDB() IMarketChangesDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMarketChangesDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMarketChangesDo
	Not(conds ...gen.Condition) IMarketChangesDo
	Or(conds ...gen.Condition) IMarketChangesDo
	Select(conds ...field.Expr) IMarketChangesDo
	Where(conds ...gen.Condition) IMarketChangesDo
	Order(conds ...field.Expr) IMarketChangesDo
	Distinct(cols ...field.Expr) IMarketChangesDo
	Omit(cols ...field.Expr) IMarketChangesDo
	Join(table schema.Tabler, on ...field.Expr) IMarketChangesDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesDo
	Group(cols ...field.Expr) IMarketChangesDo
	Having(conds ...gen.Condition) IMarketChangesDo
	Limit(limit int) IMarketChangesDo
	Offset(offset int) IMarketChangesDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesDo
	Unscoped() IMarketChangesDo
	Create(values ...*model.MarketChanges) error
	CreateInBatches(values []*model.MarketChanges, batchSize int) error
	Save(values ...*model.MarketChanges) error
	First() (*model.MarketChanges, error)
	Take() (*model.MarketChanges, error)
	Last() (*model.MarketChanges, error)
	Find() ([]*model.MarketChanges, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChanges, err error)
	FindInBatches(result *[]*model.MarketChanges, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MarketChanges) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMarketChangesDo
	Assign(attrs ...field.AssignExpr) IMarketChangesDo
	Joins(fields ...field.RelationField) IMarketChangesDo
	Preload(fields ...field.RelationField) IMarketChangesDo
	FirstOrInit() (*model.MarketChanges, error)
	FirstOrCreate() (*model.MarketChanges, error)
	FindByPage(offset int, limit int) (result []*model.MarketChanges, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMarketChangesDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m marketChangesDo) Debug() IMarketChangesDo {
	return m.withDO(m.DO.Debug())
}

func (m marketChangesDo) WithContext(ctx context.Context) IMarketChangesDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m marketChangesDo) ReadDB() IMarketChangesDo {
	return m.Clauses(dbresolver.Read)
}

func (m marketChangesDo) WriteDB() IMarketChangesDo {
	return m.Clauses(dbresolver.Write)
}

func (m marketChangesDo) Session(config *gorm.Session) IMarketChangesDo {
	return m.withDO(m.DO.Session(config))
}

func (m marketChangesDo) Clauses(conds ...clause.Expression) IMarketChangesDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m marketChangesDo) Returning(value interface{}, columns ...string) IMarketChangesDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m marketChangesDo) Not(conds ...gen.Condition) IMarketChangesDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m marketChangesDo) Or(conds ...gen.Condition) IMarketChangesDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m marketChangesDo) Select(conds ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m marketChangesDo) Where(conds ...gen.Condition) IMarketChangesDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m marketChangesDo) Order(conds ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m marketChangesDo) Distinct(cols ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m marketChangesDo) Omit(cols ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m marketChangesDo) Join(table schema.Tabler, on ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m marketChangesDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m marketChangesDo) RightJoin(table schema.Tabler, on ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m marketChangesDo) Group(cols ...field.Expr) IMarketChangesDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m marketChangesDo) Having(conds ...gen.Condition) IMarketChangesDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m marketChangesDo) Limit(limit int) IMarketChangesDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m marketChangesDo) Offset(offset int) IMarketChangesDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m marketChangesDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketChangesDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m marketChangesDo) Unscoped() IMarketChangesDo {
	return m.withDO(m.DO.Unscoped())
}

func (m marketChangesDo) Create(values ...*model.MarketChanges) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m marketChangesDo) CreateInBatches(values []*model.MarketChanges, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m marketChangesDo) Save(values ...*model.MarketChanges) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m marketChangesDo) First() (*model.MarketChanges, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChanges), nil
	}
}

func (m marketChangesDo) Take() (*model.MarketChanges, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChanges), nil
	}
}

func (m marketChangesDo) Last() (*model.MarketChanges, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChanges), nil
	}
}

func (m marketChangesDo) Find() ([]*model.MarketChanges, error) {
	result, err := m.DO.Find()
	return result.([]*model.MarketChanges), err
}

func (m marketChangesDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketChanges, err error) {
	buf := make([]*model.MarketChanges, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m marketChangesDo) FindInBatches(result *[]*model.MarketChanges, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m marketChangesDo) Attrs(attrs ...field.AssignExpr) IMarketChangesDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m marketChangesDo) Assign(attrs ...field.AssignExpr) IMarketChangesDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m marketChangesDo) Joins(fields ...field.RelationField) IMarketChangesDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m marketChangesDo) Preload(fields ...field.RelationField) IMarketChangesDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m marketChangesDo) FirstOrInit() (*model.MarketChanges, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChanges), nil
	}
}

func (m marketChangesDo) FirstOrCreate() (*model.MarketChanges, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketChanges), nil
	}
}

func (m marketChangesDo) FindByPage(offset int, limit int) (result []*model.MarketChanges, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m marketChangesDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m marketChangesDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m marketChangesDo) Delete(models ...*model.MarketChanges) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *marketChangesDo) withDO(do gen.Dao) *marketChangesDo {
	m.DO = *do.(*gen.DO)
	return m
}
