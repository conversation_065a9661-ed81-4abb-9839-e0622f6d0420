// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMarketChangesChannel = "market_changes_channel"

// MarketChangesChannel 行情异动与发布终端关联表
type MarketChangesChannel struct {
	MarketChangesID int64     `gorm:"column:market_changes_id;type:bigint unsigned;primaryKey;comment:行情异动ID" json:"market_changes_id"`  // 行情异动ID
	ChannelID       string    `gorm:"column:channel_id;type:varchar(255);primaryKey;comment:发布终端ID" json:"channel_id"`                   // 发布终端ID
	CreatedAt       time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
}

// TableName MarketChangesChannel's table name
func (*MarketChangesChannel) TableName() string {
	return TableNameMarketChangesChannel
}
