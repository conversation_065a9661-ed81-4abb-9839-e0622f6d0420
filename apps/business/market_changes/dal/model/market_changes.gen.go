// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMarketChanges = "market_changes"

// MarketChanges 行情异动表
type MarketChanges struct {
	MarketChangesID int64                  `gorm:"column:market_changes_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"market_changes_id"` // 主键ID
	Title           string                 `gorm:"column:title;type:varchar(255);not null;comment:标题" json:"title"`                                                   // 标题
	Content         string                 `gorm:"column:content;type:text;comment:内容" json:"content"`                                                                // 内容
	CategoryID      int64                  `gorm:"column:category_id;type:bigint unsigned;not null;comment:栏目ID" json:"category_id"`                                  // 栏目ID
	Status          int32                  `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1待发布 2定时中 3已发布 4已下架" json:"status"`                        // 状态：1待发布 2定时中 3已发布 4已下架
	PublishTime     *time.Time             `gorm:"column:publish_time;type:datetime;comment:发布时间" json:"publish_time"`                                                // 发布时间
	PublishType     int32                  `gorm:"column:publish_type;type:tinyint;not null;default:1;comment:发布方式：1立即发布 2定时发布" json:"publish_type"`                  // 发布方式：1立即发布 2定时发布
	MessagePush     int32                  `gorm:"column:message_push;type:tinyint;not null;comment:消息推送【0->不推送; 1->推送】" json:"message_push"`                         // 消息推送【0->不推送; 1->推送】
	CreatedBy       string                 `gorm:"column:created_by;type:char(24);not null;comment:创建人" json:"created_by"`                                            // 创建人
	UpdatedBy       string                 `gorm:"column:updated_by;type:char(24);not null;comment:更新人" json:"updated_by"`                                            // 更新人
	CreatedAt       time.Time              `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                 // 创建时间
	UpdatedAt       time.Time              `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                 // 更新时间
	CategoryInfo    Category               `gorm:"foreignKey:CategoryID;references:CategoryID" json:"category_info"`
	Items           []MarketChangesItem    `gorm:"foreignKey:MarketChangesID;references:MarketChangesID" json:"items"`
	Channels        []MarketChangesChannel `gorm:"foreignKey:MarketChangesID;references:MarketChangesID" json:"channels"`
}

// TableName MarketChanges's table name
func (*MarketChanges) TableName() string {
	return TableNameMarketChanges
}
