// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMarketChangesItem = "market_changes_item"

// MarketChangesItem 行情异动与商品关联表
type MarketChangesItem struct {
	MarketChangesID int64     `gorm:"column:market_changes_id;type:bigint unsigned;primaryKey;comment:行情异动ID" json:"market_changes_id"`  // 行情异动ID
	ItemID          string    `gorm:"column:item_id;type:varchar(255);primaryKey;comment:商品ID (商品挂牌编码)" json:"item_id"`                  // 商品ID (商品挂牌编码)
	ItemName        string    `gorm:"column:item_name;type:varchar(255);not null;comment:商品名称" json:"item_name"`                         // 商品名称
	ImageURL        string    `gorm:"column:image_url;type:varchar(255);comment:商品图片" json:"image_url"`                                  // 商品图片
	PriceChanges    float64   `gorm:"column:price_changes;type:decimal(10,2);not null;comment:价格变动" json:"price_changes"`                // 价格变动
	CreatedAt       time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
}

// TableName MarketChangesItem's table name
func (*MarketChangesItem) TableName() string {
	return TableNameMarketChangesItem
}
