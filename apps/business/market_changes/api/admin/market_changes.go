package admin

import (
	"app_service/apps/business/market_changes/define"
	"app_service/apps/business/market_changes/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetMarketChangesList
// @Summary 查询行情异动列表
// @Description 查询行情异动列表
// @Tags 管理端-行情异动管理
// @Param data query define.GetMarketChangesAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketChangesAdminListResp}
// @Router /admin/v1/market_changes/list [get]
// @Security Bearer
func GetMarketChangesList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMarketChangesAdminListReq{}, s.GetMarketChangesList)
}

// GetMarketChangesDetail
// @Summary 查询行情异动详情
// @Description 查询行情异动详情
// @Tags 管理端-行情异动管理
// @Param data query define.GetMarketChangesAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketChangesAdminDetailResp}
// @Router /admin/v1/market_changes/detail [get]
// @Security Bearer
func GetMarketChangesDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMarketChangesAdminDetailReq{}, s.GetMarketChangesDetail)
}

// AddMarketChanges
// @Summary 新增行情异动
// @Description 新增行情异动
// @Tags 管理端-行情异动管理
// @Param data body define.AddMarketChangesReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddMarketChangesResp}
// @Router /admin/v1/market_changes/add [POST]
// @Security Bearer
func AddMarketChanges(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddMarketChangesReq{}, s.AddMarketChanges)
}

// EditMarketChanges
// @Summary 编辑行情异动
// @Description 编辑行情异动
// @Tags 管理端-行情异动管理
// @Param data body define.EditMarketChangesReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditMarketChangesResp}
// @Router /admin/v1/market_changes/edit [POST]
// @Security Bearer
func EditMarketChanges(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditMarketChangesReq{}, s.EditMarketChanges)
}

// EditMarketChangesStatus
// @Summary 行情异动状态编辑
// @Description 行情异动状态编辑
// @Tags 管理端-行情异动管理
// @Param data body define.EditMarketChangesStatusReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditMarketChangesStatusResp}
// @Router /admin/v1/market_changes/edit_status [POST]
// @Security Bearer
func EditMarketChangesStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditMarketChangesStatusReq{}, s.EditMarketChangesStatus)
}
