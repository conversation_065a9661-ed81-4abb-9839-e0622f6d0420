package repo

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

const (
	sceneLimit = 4
)

func (r *StoryRepository) StoryStockSelectForUpdate(storyId int64) (int32, error) {
	var currentStock int32
	storySchema := GetQuery().Story
	err := r.do.Select(storySchema.Stock).Where(storySchema.ID.Eq(storyId)).Clauses(clause.Locking{Strength: "UPDATE"}).Scan(&currentStock)
	if err != nil {
		return currentStock, err
	}
	return currentStock, nil
}

func (r *StoryRepository) IncrByStoryStock(storyId int64, qty int32) error {
	tx := GetDB().Model(&model.Story{})
	result := tx.Where("id = ? AND is_del = 0 AND stock < total_stock", storyId).UpdateColumns(map[string]interface{}{
		"stock": gorm.Expr("stock + ?", qty),
	})
	if result.Error != nil {
		return result.Error
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}

func (r *StoryRepository) GetStoryNumByScene() (storyNumList []*define.GetStoryNumBySceneData, err error) {
	data := make([]*define.GetStoryNumBySceneData, 0)
	err = GetDB().Model(&model.Story{}).Where("status = ?", enums.StoryStatusUp.Val()).
		Select("COUNT(id) as story_num, scene_id as scene_id").Group("scene_id").Scan(&data).Error
	return data, nil
}

func (r *StoryRepository) GetStorySceneWebHome() (story []*define.GetStorySceneWebHomeData, err error) {
	data := make([]*define.GetStorySceneWebHomeData, 0)
	err = GetDB().Model(&model.Story{}).Select("scene_id as id, MIN(start_time) as start_time").Where("status = ?", enums.StoryStatusUp.Val()).
		Group("scene_id").Order("start_time asc").Limit(sceneLimit).Scan(&data).Error
	return data, nil
}
