package repo

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define/enums"
)

func (r *StoryOrderRepository) GetStoryUserNameLastBySceneInDB(sceneId int64, storyIds []int64, limit int32) (userIdList []string, err error) {
	data := make([]string, 0)
	err = GetDB().Model(&model.StoryOrder{}).
		Where("scene_id = ? and status = ? and story_id in ?", sceneId, enums.StoryOrderStatusSuccess.Val(), storyIds).
		Select("DISTINCT(user_id) as user_id").Group("user_id").Scan(&data).Limit(int(limit)).Error
	return data, nil
}

func (r *StoryOrderRepository) GetOrderDetailList(userId string, storyId int64, limit int32) ([]*model.StoryOrderDetail, error) {
	var result []*model.StoryOrderDetail

	query := `
	WITH ranked_details AS (
		SELECT 
			d.id,
			d.story_order_id,
			d.materials_item_id,
			d.materials_item_name,
			d.materials_item_url,
			ROW_NUMBER() OVER (PARTITION BY d.materials_item_id ORDER BY d.created_at DESC) as rn
		FROM story_order_detail d
		JOIN story_order o ON d.story_order_id = o.id
		WHERE o.user_id = ?
		  AND o.story_id = ?
		  AND o.status = ?
	)
	SELECT id, story_order_id, materials_item_id, materials_item_name, materials_item_url
	FROM ranked_details
	WHERE rn = 1
	LIMIT ?
	`

	err := GetDB().
		Raw(query, userId, storyId, enums.StoryOrderStatusSuccess.Val(), limit).
		Scan(&result).Error

	return result, err
}
