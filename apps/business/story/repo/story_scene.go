package repo

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
)

func (r *StorySceneRepository) GetStorySceneWebLimitFour() (story []*define.GetStorySceneWebLimitFourData, err error) {
	data := make([]*define.GetStorySceneWebLimitFourData, 0)
	err = GetDB().Model(&model.StoryScene{}).Select("id, name, cover_url").Limit(sceneLimit).Scan(&data).Error
	return data, nil
}
