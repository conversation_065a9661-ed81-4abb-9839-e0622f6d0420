package open

import (
	"app_service/apps/business/story/api/open"
	"github.com/gin-gonic/gin"
)

// Story 故事玩法open相关
func Story(router *gin.RouterGroup) {
	group := router.Group("/story")
	{
		// 获取商品对应故事活动是否上架
		group.GET("/item_story_is_up", open.GetItemStoryIsUp)
		// 故事玩法超时下架
		group.POST("/finish", open.StoryFinish)
		// 故事玩法订单上链
		group.POST("/order/up_chain", open.StoryOrderUpChain)
		// 故事玩法材料释放
		group.POST("/materials/release", open.StoryMaterialsRelease)
		// 获取故事玩法材料释放时间
		group.POST("/materials/get_release_time", open.StoryMaterialsGetReleaseTime)
		// 故事玩法探索完成
		group.POST("/order/complete", open.StoryOrderComplete)
	}
}
