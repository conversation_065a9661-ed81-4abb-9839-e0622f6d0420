package router

import (
	"fmt"

	"app_service/apps/business/story/router/admin"
	"app_service/apps/business/story/router/open"
	"app_service/apps/business/story/router/web"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	adminRote(r)
	// 客户端路由
	webRote(r)
	// 客户端路由
	openRote(r)
}

// 管理端路由
func adminRote(router *gin.Engine) {
	w := router.Group("/admin/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Admin{
		NoAuthUrl: []string{},
	}))

	admin.Story(w)

	admin.StoryScene(w)
}

// 客户端路由
func webRote(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/story/rule",
			"/web/v1/story/list",
			"/web/v1/story/detail",
			"/web/v1/story_scene/list",
			"/web/v1/story_scene/home",
		},
	}))
	web.Story(w)

	web.StoryScene(w)
}

// 开放路由
func openRote(router *gin.Engine) {
	w := router.Group("/open/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
	}))
	open.Story(w)
}
