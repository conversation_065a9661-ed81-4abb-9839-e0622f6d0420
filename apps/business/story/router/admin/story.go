package admin

import (
	"app_service/apps/business/story/api/admin"
	"github.com/gin-gonic/gin"
)

// Story 故事玩法管理端相关
func Story(router *gin.RouterGroup) {
	group := router.Group("/story")
	{
		// 获取故事玩法管理列表
		group.GET("/list", admin.GetStoryList)
		// 获取故事玩法管理列表
		group.GET("/detail", admin.GetStoryDetail)
		// 新增故事玩法
		group.POST("/add", admin.AddStory)
		// 编辑故事玩法
		group.POST("/edit", admin.EditStory)
		// 故事玩法状态编辑
		group.POST("/edit_status", admin.EditStoryStatus)
		// 故事玩法删除
		group.POST("/del", admin.DelStory)

		// 故事玩法操作日志列表
		group.GET("/log_list", admin.GetStoryLogList)
		// 获取故事玩法规则
		group.GET("/rule", admin.GetStoryRule)
		// 编辑故事玩法规则
		group.POST("/rule", admin.EditStoryRule)
		// 获取故事玩法订单列表
		group.GET("/order/list", admin.GetStoryOrderList)
		// 获取故事玩法订单详情
		group.GET("/order/detail", admin.GetStoryOrderDetail)
		// 获取故事玩法订单详情列表
		group.GET("/order_detail/list", admin.GetStoryOrderDetailList)
		// 导出故事玩法订单列表
		group.GET("/order/export", admin.ExportStoryOrderList)
		// 查询故事玩法的商品列表
		group.GET("/item_story/list", admin.GetItemStoryList)
	}
}
