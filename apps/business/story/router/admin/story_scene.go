package admin

import (
	"app_service/apps/business/story/api/admin"
	"github.com/gin-gonic/gin"
)

// StoryScene 故事玩法场景管理端相关
func StoryScene(router *gin.RouterGroup) {
	group := router.Group("/story_scene")
	{
		// 获取故事玩法场景列表
		group.GET("/list", admin.GetStorySceneAdminList)
		// 获取故事玩法场景列表
		group.GET("/detail", admin.GetStorySceneAdminDetail)
		// 新增故事玩法场景
		group.POST("/add", admin.AddStoryScene)
		// 编辑故事玩法场景
		group.POST("/edit", admin.EditStoryScene)
	}
}
