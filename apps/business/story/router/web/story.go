package web

import (
	"app_service/apps/business/story/api/web"
	"github.com/gin-gonic/gin"
)

// Story 故事玩法管理端相关
func Story(router *gin.RouterGroup) {
	group := router.Group("/story")
	{
		// 获取故事玩法规则
		group.GET("/rule", web.GetStoryRule)
		// 查询故事玩法列表
		group.GET("/list", web.GetStoryWebList)
		// 查询故事玩法详情
		group.GET("/detail", web.GetStoryWebDetail)
		// 发起故事玩法探索
		group.POST("/discovery", web.StoryDiscovery)
		// 获取故事玩法订单列表
		group.GET("/order/list", web.GetStoryWebOrderList)
		// 获取故事玩法订单详情
		group.GET("/order/detail", web.GetStoryWebOrderDetail)
		// 获取故事玩法订单状态
		group.GET("/order/status", web.GetStoryWebOrderStatus)
		// 故事玩法领取奖励
		group.POST("/receive", web.ReceiveStory)
		// 获取我的故事玩法探索材料列表
		group.GET("/user/materials_list", web.GetStoryUserMaterialsList)
	}
}
