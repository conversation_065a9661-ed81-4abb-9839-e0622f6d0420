package consume

//
//import (
//	"app_service/apps/business/story/define"
//	"app_service/apps/business/story/service/logic"
//	"app_service/apps/platform/common/constant"
//	commondefine "app_service/apps/platform/common/define"
//	"app_service/global"
//	"app_service/pkg/middlewares"
//	"app_service/pkg/util"
//	"context"
//	log "e.coding.net/g-dtay0385/common/go-logger"
//	"go-micro.dev/v4/broker"
//	"go.opentelemetry.io/otel"
//	"go.opentelemetry.io/otel/trace"
//)
//
//type StoryCancelFusionConsumer struct {
//	middlewares.BaseConsumer
//}
//
//func NewStoryCancelFusionConsumer() *StoryCancelFusionConsumer {
//	return &StoryCancelFusionConsumer{
//		BaseConsumer: middlewares.NewBaseConsumer(constant.StoryCancelFusion, constant.CommonGroup),
//	}
//}
//
//func (o *StoryCancelFusionConsumer) GetTopic() string {
//	return constant.StoryCancelFusion
//}
//
//func (o *StoryCancelFusionConsumer) GetGroup() string {
//	return constant.CommonGroup
//}
//
//func (o *StoryCancelFusionConsumer) HandleFun() broker.Handler {
//	handler := func(event broker.Event) error {
//		// 初始化上下文及链路信息
//		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "StoryCancelFusionConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
//		defer span.End()
//		log.Ctx(ctx).Infof("[story_cancel_fusion]StoryCancel kafka data:%s", util.Obj2JsonStr(event.Message()))
//
//		// 解析消息
//		data := &define.StoryCancelFusion{}
//		err := util.JsonStr2Struct(string(event.Message().Body), data)
//		if err != nil {
//			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
//			return commondefine.CommonWarnErr.Err(err)
//		}
//
//		log.Ctx(ctx).Infof("[story_cancel_fusion]StoryCancel data:%s", util.Obj2JsonStr(data))
//		err = logic.StoryCancelFusion(ctx, data.StoryOrderID)
//		if err != nil {
//			log.Ctx(ctx).Errorf("故事玩法取消融合失败 err:%v", err)
//		}
//		return nil
//	}
//	return middlewares.SafeHandler(handler)
//}
