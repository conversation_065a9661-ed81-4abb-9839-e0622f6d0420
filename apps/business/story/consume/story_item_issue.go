package consume

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

type StoryItemIssueConsumer struct {
	middlewares.BaseConsumer
}

func NewStoryItemIssueConsumer() *StoryItemIssueConsumer {
	return &StoryItemIssueConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.StoryItemIssue, constant.CommonGroup),
	}
}

func (o *StoryItemIssueConsumer) GetTopic() string {
	return constant.StoryItemIssue
}

func (o *StoryItemIssueConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *StoryItemIssueConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "StoryItemIssueConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[story_item_issue]StoryItemIssue kafka data:%s", util.Obj2JsonStr(event.Message()))
		// 解析消息
		data := &define.StoryItemIssue{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}

		log.Ctx(ctx).Infof("[story_item_issue]StoryItemIssue data:%s", util.Obj2JsonStr(data))
		err = logic.StoryItemIssue(ctx, data.StoryOrderID)
		if err != nil {
			log.Ctx(ctx).Errorf("物品发放失败 err:%v", err)
		}
		return nil
	}
	return middlewares.SafeHandler(handler)
}
