package consume

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

type StoryFusionConsumer struct {
	middlewares.BaseConsumer
}

func NewStoryFusionConsumer() *StoryFusionConsumer {
	return &StoryFusionConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.StoryFusion, constant.CommonGroup),
	}
}

func (o *StoryFusionConsumer) GetTopic() string {
	return constant.StoryFusion
}

func (o *StoryFusionConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *StoryFusionConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "StoryFusionConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[story_fusion]StoryFusion kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &define.StoryFusion{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}
		log.Ctx(ctx).Infof("[story_fusion]StoryFusion data:%s", util.Obj2JsonStr(data))
		err = logic.StoryFusion(ctx, data.StoryOrderID)
		if err != nil {
			log.Ctx(ctx).Errorf("故事玩法融合失败 err:%v", err)
		}
		return nil
	}
	return middlewares.SafeHandler(handler)
}
