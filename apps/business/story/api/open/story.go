package open

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetItemStoryIsUp
// @Summary 获取商品对应故事活动是否上架
// @Description 获取商品对应故事活动是否上架
// @Tags open端-故事玩法管理
// @Param data query define.GetItemStoryIsUpReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetItemStoryIsUpResp}
// @Router  /open/v1/story/item_story_is_up [get]
// @Security Bearer
func GetItemStoryIsUp(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetItemStoryIsUpReq{}, s.GetItemStoryIsUp)
}

// StoryFinish
// @Summary 故事玩法超时下架
// @Description 故事玩法超时下架
// @Tags open端-故事玩法管理
// @Param data body define.StoryFinishReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryFinishResp}
// @Router  /open/v1/story/finish [POST]
// @Security Bearer
func StoryFinish(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.StoryFinish)
}

// StoryOrderUpChain
// @Summary 故事玩法订单上链
// @Description 故事玩法订单上链
// @Tags open端-故事玩法管理
// @Param data body define.StoryOrderUpChainReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryOrderUpChainResp}
// @Router  /open/v1/story/order/up_chain [POST]
// @Security Bearer
func StoryOrderUpChain(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.StoryOrderUpChainReq{}, s.AsyncStoryOrderUpChain)
}

// StoryMaterialsRelease
// @Summary 故事玩法材料释放
// @Description 故事玩法材料释放
// @Tags open端-故事玩法管理
// @Param data body define.StoryMaterialsReleaseReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryMaterialsReleaseResp}
// @Router  /open/v1/story/materials/release [POST]
// @Security Bearer
func StoryMaterialsRelease(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.StoryMaterialsReleaseReq{}, s.AsyncStoryMaterialsRelease)
}

// StoryMaterialsGetReleaseTime
// @Summary 获取故事玩法材料释放时间
// @Description 获取故事玩法材料释放时间
// @Tags open端-故事玩法管理
// @Param data body define.StoryMaterialsGetReleaseTimeReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryMaterialsGetReleaseTimeResp}
// @Router  /open/v1/story/materials/get_release_time [POST]
// @Security Bearer
func StoryMaterialsGetReleaseTime(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.StoryMaterialsGetReleaseTimeReq{}, s.StoryMaterialsGetReleaseTime)
}

// StoryOrderComplete
// @Summary 故事玩法探索完成
// @Description 故事玩法探索完成
// @Tags open端-故事玩法管理
// @Param data body define.StoryOrderCompleteReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryOrderCompleteResp}
// @Router  /open/v1/story/order/complete [POST]
// @Security Bearer
func StoryOrderComplete(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.StoryOrderComplete)
}
