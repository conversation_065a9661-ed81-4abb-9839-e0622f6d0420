package web

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetStorySceneWebList
// @Summary 查询故事玩法场景列表
// @Description 查询故事玩法场景列表
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStorySceneWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStorySceneWebListResp}
// @Router  /web/v1/story_scene/list [get]
// @Security Bearer
func GetStorySceneWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStorySceneWebListReq{}, s.GetStorySceneWebList)
}

// GetStorySceneWebHome
// @Summary 查询故事玩法场景首页列表
// @Description 查询故事玩法场景首页列表
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStorySceneWebHomeReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStorySceneWebHomeResp}
// @Router  /web/v1/story_scene/home [get]
// @Security Bearer
func GetStorySceneWebHome(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStorySceneWebHomeReq{}, s.GetStorySceneWebHome)
}
