package web

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetStoryRule
// @Summary 查询故事玩法规则
// @Description 查询故事玩法规则
// @Tags 用户端-故事玩法管理
// @Success 200 {object} response.Data{data=define.GetStoryRuleAdminResp}
// @Router  /web/v1/story/rule [get]
// @Security Bearer
func GetStoryRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.GetStoryRuleAdmin)
}

// GetStoryWebList
// @Summary 查询故事玩法列表
// @Description 查询故事玩法列表
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryWebListResp}
// @Router  /web/v1/story/list [get]
// @Security Bearer
func GetStoryWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryWebListReq{}, s.GetStoryWebList)
}

// GetStoryWebDetail
// @Summary 查询故事玩法详情
// @Description 查询故事玩法详情
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryWebDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryWebDetailResp}
// @Router  /web/v1/story/detail [get]
// @Security Bearer
func GetStoryWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryWebDetailReq{}, s.GetStoryWebDetail)
}

// StoryDiscovery
// @Summary 发起故事玩法探索
// @Description 发起故事玩法探索
// @Tags 用户端-故事玩法管理
// @Param data body define.StoryDiscoveryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.StoryDiscoveryResp}
// @Router  /web/v1/story/discovery [POST]
// @Security Bearer
func StoryDiscovery(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.StoryDiscoveryReq{}, s.StoryDiscovery)
}

// GetStoryWebOrderList
// @Summary 查询故事玩法订单列表
// @Description 查询故事玩法订单列表
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryWebOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryWebOrderListResp}
// @Router  /web/v1/story/order/list [get]
// @Security Bearer
func GetStoryWebOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryWebOrderListReq{}, s.GetStoryWebOrderList)
}

// GetStoryWebOrderDetail
// @Summary 查询故事玩法订单详情
// @Description 查询故事玩法订单详情
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryWebOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryWebOrderDetailResp}
// @Router  /web/v1/story/order/detail [get]
// @Security Bearer
func GetStoryWebOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryWebOrderDetailReq{}, s.GetStoryWebOrderDetail)
}

// GetStoryWebOrderStatus
// @Summary 获取故事玩法订单状态
// @Description 获取故事玩法订单状态
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryWebOrderStatusReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryWebOrderStatusResp}
// @Router  /web/v1/story/order/status [get]
// @Security Bearer
func GetStoryWebOrderStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryWebOrderStatusReq{}, s.GetStoryWebOrderStatus)
}

// ReceiveStory
// @Summary 故事玩法领取奖励
// @Description 故事玩法领取奖励
// @Tags 用户端-故事玩法管理
// @Param data body define.ReceiveStoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.ReceiveStoryResp}
// @Router  /web/v1/story/receive [POST]
// @Security Bearer
func ReceiveStory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ReceiveStoryReq{}, s.ReceiveStory)
}

// GetStoryUserMaterialsList
// @Summary 获取我的故事玩法探索材料列表
// @Description 获取我的故事玩法探索材料列表
// @Tags 用户端-故事玩法管理
// @Param data query define.GetStoryUserMaterialsListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryUserMaterialsListResp}
// @Router  /web/v1/story/user/materials_list [get]
// @Security Bearer
func GetStoryUserMaterialsList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryUserMaterialsListReq{}, s.GetStoryUserMaterialsList)
}
