package admin

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetStorySceneAdminList
// @Summary 查询故事玩法场景管理列表
// @Description 查询故事玩法场景管理列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStorySceneAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStorySceneAdminListResp}
// @Router  /admin/v1/story_scene/list [get]
// @Security Bearer
func GetStorySceneAdminList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStorySceneAdminListReq{}, s.GetStorySceneAdminList)
}

// GetStorySceneAdminDetail
// @Summary 查询故事玩法场景管理详情
// @Description 查询故事玩法场景管理详情
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStorySceneAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStorySceneAdminDetailResp}
// @Router  /admin/v1/story_scene/detail [get]
// @Security Bearer
func GetStorySceneAdminDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStorySceneAdminDetailReq{}, s.GetStorySceneAdminDetail)
}

// AddStoryScene
// @Summary 新增故事玩法场景
// @Description 新增故事玩法场景
// @Tags 管理端-故事玩法管理
// @Param data body define.AddStorySceneReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddStorySceneResp}
// @Router  /admin/v1/story_scene/add [POST]
// @Security Bearer
func AddStoryScene(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddStorySceneReq{}, s.AddStoryScene)
}

// EditStoryScene
// @Summary 编辑故事玩法场景
// @Description 编辑故事玩法场景
// @Tags 管理端-故事玩法管理
// @Param data body define.EditStorySceneReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditStorySceneResp}
// @Router  /admin/v1/story_scene/edit [POST]
// @Security Bearer
func EditStoryScene(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditStorySceneReq{}, s.EditStoryScene)
}
