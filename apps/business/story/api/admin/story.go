package admin

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
)

// GetStoryList
// @Summary 查询故事玩法管理列表
// @Description 查询故事玩法管理列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryAdminListResp}
// @Router  /admin/v1/story/list [get]
// @Security Bearer
func GetStoryList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryAdminListReq{}, s.GetStoryList)
}

// GetStoryDetail
// @Summary 查询故事玩法管理详情
// @Description 查询故事玩法管理详情
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryAdminDetailResp}
// @Router  /admin/v1/story/detail [get]
// @Security Bearer
func GetStoryDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryAdminDetailReq{}, s.GetStoryDetail)
}

// AddStory
// @Summary 新增故事玩法
// @Description 新增故事玩法
// @Tags 管理端-故事玩法管理
// @Param data body define.AddStoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddStoryResp}
// @Router  /admin/v1/story/add [POST]
// @Security Bearer
func AddStory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddStoryReq{}, s.AddStory)
}

// EditStory
// @Summary 编辑故事玩法
// @Description 编辑故事玩法
// @Tags 管理端-故事玩法管理
// @Param data body define.EditStoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditStoryResp}
// @Router  /admin/v1/story/edit [POST]
// @Security Bearer
func EditStory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditStoryReq{}, s.EditStory)
}

// EditStoryStatus
// @Summary 故事玩法状态编辑
// @Description 故事玩法状态编辑
// @Tags 管理端-故事玩法管理
// @Param data body define.EditStoryStatusReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditStoryStatusResp}
// @Router  /admin/v1/story/edit_status [POST]
// @Security Bearer
func EditStoryStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditStoryStatusReq{}, s.EditStoryStatus)
}

// DelStory
// @Summary 故事玩法删除
// @Description 故事玩法删除
// @Tags 管理端-故事玩法管理
// @Param data body define.DelStoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.DelStoryResp}
// @Router  /admin/v1/story/del [POST]
// @Security Bearer
func DelStory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelStoryReq{}, s.DelStory)
}

// GetStoryLogList
// @Summary 查询故事玩法日志列表
// @Description 查询故事玩法日志列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryLogListReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetStoryLogListResp}
// @Router  /admin/v1/story/log_list [get]
// @Security Bearer
func GetStoryLogList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryLogListReq{}, s.GetStoryLogList)
}

// GetStoryRule
// @Summary 查询故事玩法规则
// @Description 查询故事玩法规则
// @Tags 管理端-故事玩法管理
// @Success 200 {object} response.Data{data=define.GetStoryRuleAdminResp}
// @Router  /admin/v1/story/rule [get]
// @Security Bearer
func GetStoryRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.GetStoryRuleAdmin)
}

// EditStoryRule
// @Summary 编辑故事玩法规则
// @Description 编辑故事玩法规则
// @Tags 管理端-故事玩法管理
// @Param data body define.EditStoryRuleReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditStoryRuleResp}
// @Router  /admin/v1/story/rule [post]
// @Security Bearer
func EditStoryRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditStoryRuleReq{}, s.EditStoryRule)
}

// GetStoryOrderList
// @Summary 查询故事玩法订单列表
// @Description 查询故事玩法订单列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryAdminOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryAdminOrderListResp}
// @Router  /admin/v1/story/order/list [get]
// @Security Bearer
func GetStoryOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryAdminOrderListReq{}, s.GetStoryAdminOrderList)
}

// GetStoryOrderDetail
// @Summary 查询故事玩法订单详情
// @Description 查询故事玩法订单详情
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryAdminOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryAdminOrderDetailResp}
// @Router  /admin/v1/story/order/detail [get]
// @Security Bearer
func GetStoryOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryAdminOrderDetailReq{}, s.GetStoryAdminOrderDetail)
}

// GetStoryOrderDetailList
// @Summary 获取故事玩法订单详情列表
// @Description 获取故事玩法订单详情列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryOrderDetailListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryOrderDetailListResp}
// @Router  /admin/v1/story/order_detail/list [get]
// @Security Bearer
func GetStoryOrderDetailList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetStoryOrderDetailListReq{}, s.GetStoryOrderDetailList)
}

// ExportStoryOrderList
// @Summary 导出故事玩法订单列表
// @Description 导出故事玩法订单列表，下载文件
// @Tags 管理端-故事玩法管理
// @Param data query define.GetStoryAdminOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetStoryAdminOrderListResp}
// @Router  /admin/v1/story/order/export [get]
// @Security Bearer
func ExportStoryOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetStoryAdminOrderListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportStoryAdminOrderList(req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}

// GetItemStoryList
// @Summary 查询故事玩法的商品列表
// @Description 查询故事玩法的商品列表
// @Tags 管理端-故事玩法管理
// @Param data query define.GetItemStoryListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetItemStoryListResp}
// @Router  /admin/v1/story/item_story/list [get]
// @Security Bearer
func GetItemStoryList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetItemStoryListReq{}, s.GetItemStoryList)
}
