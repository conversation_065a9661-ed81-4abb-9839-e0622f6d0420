// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryOrderDetail(db *gorm.DB, opts ...gen.DOOption) storyOrderDetail {
	_storyOrderDetail := storyOrderDetail{}

	_storyOrderDetail.storyOrderDetailDo.UseDB(db, opts...)
	_storyOrderDetail.storyOrderDetailDo.UseModel(&model.StoryOrderDetail{})

	tableName := _storyOrderDetail.storyOrderDetailDo.TableName()
	_storyOrderDetail.ALL = field.NewAsterisk(tableName)
	_storyOrderDetail.ID = field.NewInt64(tableName, "id")
	_storyOrderDetail.StoryOrderID = field.NewInt64(tableName, "story_order_id")
	_storyOrderDetail.MaterialsItemID = field.NewString(tableName, "materials_item_id")
	_storyOrderDetail.MaterialsItemName = field.NewString(tableName, "materials_item_name")
	_storyOrderDetail.MaterialsItemURL = field.NewString(tableName, "materials_item_url")
	_storyOrderDetail.MaterialsItemInfo = field.NewField(tableName, "materials_item_info")
	_storyOrderDetail.UserItemID = field.NewString(tableName, "user_item_id")
	_storyOrderDetail.IsDestroy = field.NewInt32(tableName, "is_destroy")
	_storyOrderDetail.CreatedBy = field.NewString(tableName, "created_by")
	_storyOrderDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_storyOrderDetail.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyOrderDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyOrderDetail.IsDel = field.NewField(tableName, "is_del")
	_storyOrderDetail.StoryOrder = storyOrderDetailBelongsToStoryOrder{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("StoryOrder", "model.StoryOrder"),
		StoryOrderDetail: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("StoryOrder.StoryOrderDetail", "model.StoryOrderDetail"),
		},
		Story: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("StoryOrder.Story", "model.Story"),
		},
	}

	_storyOrderDetail.fillFieldMap()

	return _storyOrderDetail
}

// storyOrderDetail 故事玩法订单详情表
type storyOrderDetail struct {
	storyOrderDetailDo

	ALL               field.Asterisk
	ID                field.Int64  // id
	StoryOrderID      field.Int64  // 故事玩法订单id(story_order.id)
	MaterialsItemID   field.String // 物品id
	MaterialsItemName field.String // 物品名称
	MaterialsItemURL  field.String // 物品图片
	MaterialsItemInfo field.Field  // 物品信息
	UserItemID        field.String // 用户背包物品id
	IsDestroy         field.Int32  // 是否销毁【1->是; 2->否;】
	CreatedBy         field.String // 创建人
	CreatedAt         field.Time   // 创建时间
	UpdatedBy         field.String // 更新人
	UpdatedAt         field.Time   // 更新时间
	IsDel             field.Field  // 是否删除【0->未删除; 1->删除】
	StoryOrder        storyOrderDetailBelongsToStoryOrder

	fieldMap map[string]field.Expr
}

func (s storyOrderDetail) Table(newTableName string) *storyOrderDetail {
	s.storyOrderDetailDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyOrderDetail) As(alias string) *storyOrderDetail {
	s.storyOrderDetailDo.DO = *(s.storyOrderDetailDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyOrderDetail) updateTableName(table string) *storyOrderDetail {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryOrderID = field.NewInt64(table, "story_order_id")
	s.MaterialsItemID = field.NewString(table, "materials_item_id")
	s.MaterialsItemName = field.NewString(table, "materials_item_name")
	s.MaterialsItemURL = field.NewString(table, "materials_item_url")
	s.MaterialsItemInfo = field.NewField(table, "materials_item_info")
	s.UserItemID = field.NewString(table, "user_item_id")
	s.IsDestroy = field.NewInt32(table, "is_destroy")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyOrderDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyOrderDetail) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 14)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_order_id"] = s.StoryOrderID
	s.fieldMap["materials_item_id"] = s.MaterialsItemID
	s.fieldMap["materials_item_name"] = s.MaterialsItemName
	s.fieldMap["materials_item_url"] = s.MaterialsItemURL
	s.fieldMap["materials_item_info"] = s.MaterialsItemInfo
	s.fieldMap["user_item_id"] = s.UserItemID
	s.fieldMap["is_destroy"] = s.IsDestroy
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel

}

func (s storyOrderDetail) clone(db *gorm.DB) storyOrderDetail {
	s.storyOrderDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyOrderDetail) replaceDB(db *gorm.DB) storyOrderDetail {
	s.storyOrderDetailDo.ReplaceDB(db)
	return s
}

type storyOrderDetailBelongsToStoryOrder struct {
	db *gorm.DB

	field.RelationField

	StoryOrderDetail struct {
		field.RelationField
	}
	Story struct {
		field.RelationField
	}
}

func (a storyOrderDetailBelongsToStoryOrder) Where(conds ...field.Expr) *storyOrderDetailBelongsToStoryOrder {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a storyOrderDetailBelongsToStoryOrder) WithContext(ctx context.Context) *storyOrderDetailBelongsToStoryOrder {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a storyOrderDetailBelongsToStoryOrder) Session(session *gorm.Session) *storyOrderDetailBelongsToStoryOrder {
	a.db = a.db.Session(session)
	return &a
}

func (a storyOrderDetailBelongsToStoryOrder) Model(m *model.StoryOrderDetail) *storyOrderDetailBelongsToStoryOrderTx {
	return &storyOrderDetailBelongsToStoryOrderTx{a.db.Model(m).Association(a.Name())}
}

type storyOrderDetailBelongsToStoryOrderTx struct{ tx *gorm.Association }

func (a storyOrderDetailBelongsToStoryOrderTx) Find() (result *model.StoryOrder, err error) {
	return result, a.tx.Find(&result)
}

func (a storyOrderDetailBelongsToStoryOrderTx) Append(values ...*model.StoryOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a storyOrderDetailBelongsToStoryOrderTx) Replace(values ...*model.StoryOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a storyOrderDetailBelongsToStoryOrderTx) Delete(values ...*model.StoryOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a storyOrderDetailBelongsToStoryOrderTx) Clear() error {
	return a.tx.Clear()
}

func (a storyOrderDetailBelongsToStoryOrderTx) Count() int64 {
	return a.tx.Count()
}

type storyOrderDetailDo struct{ gen.DO }

type IStoryOrderDetailDo interface {
	gen.SubQuery
	Debug() IStoryOrderDetailDo
	WithContext(ctx context.Context) IStoryOrderDetailDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryOrderDetailDo
	WriteDB() IStoryOrderDetailDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryOrderDetailDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryOrderDetailDo
	Not(conds ...gen.Condition) IStoryOrderDetailDo
	Or(conds ...gen.Condition) IStoryOrderDetailDo
	Select(conds ...field.Expr) IStoryOrderDetailDo
	Where(conds ...gen.Condition) IStoryOrderDetailDo
	Order(conds ...field.Expr) IStoryOrderDetailDo
	Distinct(cols ...field.Expr) IStoryOrderDetailDo
	Omit(cols ...field.Expr) IStoryOrderDetailDo
	Join(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo
	Group(cols ...field.Expr) IStoryOrderDetailDo
	Having(conds ...gen.Condition) IStoryOrderDetailDo
	Limit(limit int) IStoryOrderDetailDo
	Offset(offset int) IStoryOrderDetailDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryOrderDetailDo
	Unscoped() IStoryOrderDetailDo
	Create(values ...*model.StoryOrderDetail) error
	CreateInBatches(values []*model.StoryOrderDetail, batchSize int) error
	Save(values ...*model.StoryOrderDetail) error
	First() (*model.StoryOrderDetail, error)
	Take() (*model.StoryOrderDetail, error)
	Last() (*model.StoryOrderDetail, error)
	Find() ([]*model.StoryOrderDetail, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryOrderDetail, err error)
	FindInBatches(result *[]*model.StoryOrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryOrderDetail) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryOrderDetailDo
	Assign(attrs ...field.AssignExpr) IStoryOrderDetailDo
	Joins(fields ...field.RelationField) IStoryOrderDetailDo
	Preload(fields ...field.RelationField) IStoryOrderDetailDo
	FirstOrInit() (*model.StoryOrderDetail, error)
	FirstOrCreate() (*model.StoryOrderDetail, error)
	FindByPage(offset int, limit int) (result []*model.StoryOrderDetail, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryOrderDetailDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyOrderDetailDo) Debug() IStoryOrderDetailDo {
	return s.withDO(s.DO.Debug())
}

func (s storyOrderDetailDo) WithContext(ctx context.Context) IStoryOrderDetailDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyOrderDetailDo) ReadDB() IStoryOrderDetailDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyOrderDetailDo) WriteDB() IStoryOrderDetailDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyOrderDetailDo) Session(config *gorm.Session) IStoryOrderDetailDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyOrderDetailDo) Clauses(conds ...clause.Expression) IStoryOrderDetailDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyOrderDetailDo) Returning(value interface{}, columns ...string) IStoryOrderDetailDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyOrderDetailDo) Not(conds ...gen.Condition) IStoryOrderDetailDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyOrderDetailDo) Or(conds ...gen.Condition) IStoryOrderDetailDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyOrderDetailDo) Select(conds ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyOrderDetailDo) Where(conds ...gen.Condition) IStoryOrderDetailDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyOrderDetailDo) Order(conds ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyOrderDetailDo) Distinct(cols ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyOrderDetailDo) Omit(cols ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyOrderDetailDo) Join(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyOrderDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyOrderDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyOrderDetailDo) Group(cols ...field.Expr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyOrderDetailDo) Having(conds ...gen.Condition) IStoryOrderDetailDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyOrderDetailDo) Limit(limit int) IStoryOrderDetailDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyOrderDetailDo) Offset(offset int) IStoryOrderDetailDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyOrderDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryOrderDetailDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyOrderDetailDo) Unscoped() IStoryOrderDetailDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyOrderDetailDo) Create(values ...*model.StoryOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyOrderDetailDo) CreateInBatches(values []*model.StoryOrderDetail, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyOrderDetailDo) Save(values ...*model.StoryOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyOrderDetailDo) First() (*model.StoryOrderDetail, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrderDetail), nil
	}
}

func (s storyOrderDetailDo) Take() (*model.StoryOrderDetail, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrderDetail), nil
	}
}

func (s storyOrderDetailDo) Last() (*model.StoryOrderDetail, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrderDetail), nil
	}
}

func (s storyOrderDetailDo) Find() ([]*model.StoryOrderDetail, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryOrderDetail), err
}

func (s storyOrderDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryOrderDetail, err error) {
	buf := make([]*model.StoryOrderDetail, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyOrderDetailDo) FindInBatches(result *[]*model.StoryOrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyOrderDetailDo) Attrs(attrs ...field.AssignExpr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyOrderDetailDo) Assign(attrs ...field.AssignExpr) IStoryOrderDetailDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyOrderDetailDo) Joins(fields ...field.RelationField) IStoryOrderDetailDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyOrderDetailDo) Preload(fields ...field.RelationField) IStoryOrderDetailDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyOrderDetailDo) FirstOrInit() (*model.StoryOrderDetail, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrderDetail), nil
	}
}

func (s storyOrderDetailDo) FirstOrCreate() (*model.StoryOrderDetail, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrderDetail), nil
	}
}

func (s storyOrderDetailDo) FindByPage(offset int, limit int) (result []*model.StoryOrderDetail, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyOrderDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyOrderDetailDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyOrderDetailDo) Delete(models ...*model.StoryOrderDetail) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyOrderDetailDo) withDO(do gen.Dao) *storyOrderDetailDo {
	s.DO = *do.(*gen.DO)
	return s
}
