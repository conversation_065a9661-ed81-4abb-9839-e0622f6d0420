// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryLog(db *gorm.DB, opts ...gen.DOOption) storyLog {
	_storyLog := storyLog{}

	_storyLog.storyLogDo.UseDB(db, opts...)
	_storyLog.storyLogDo.UseModel(&model.StoryLog{})

	tableName := _storyLog.storyLogDo.TableName()
	_storyLog.ALL = field.NewAsterisk(tableName)
	_storyLog.ID = field.NewInt64(tableName, "id")
	_storyLog.StoryID = field.NewInt64(tableName, "story_id")
	_storyLog.Action = field.NewInt32(tableName, "action")
	_storyLog.Content = field.NewString(tableName, "content")
	_storyLog.OldContent = field.NewString(tableName, "old_content")
	_storyLog.CreatedBy = field.NewString(tableName, "created_by")
	_storyLog.CreatedAt = field.NewTime(tableName, "created_at")
	_storyLog.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyLog.IsDel = field.NewField(tableName, "is_del")

	_storyLog.fillFieldMap()

	return _storyLog
}

// storyLog 故事玩法修改操作日志表
type storyLog struct {
	storyLogDo

	ALL        field.Asterisk
	ID         field.Int64  // id
	StoryID    field.Int64  // 故事玩法活动id(story.id)
	Action     field.Int32  // 操作类型，1创建，2修改，3删除，4上架，5下架
	Content    field.String // 修改内容
	OldContent field.String // 旧内容
	CreatedBy  field.String // 创建人
	CreatedAt  field.Time   // 创建时间
	UpdatedBy  field.String // 更新人
	UpdatedAt  field.Time   // 更新时间
	IsDel      field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s storyLog) Table(newTableName string) *storyLog {
	s.storyLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyLog) As(alias string) *storyLog {
	s.storyLogDo.DO = *(s.storyLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyLog) updateTableName(table string) *storyLog {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryID = field.NewInt64(table, "story_id")
	s.Action = field.NewInt32(table, "action")
	s.Content = field.NewString(table, "content")
	s.OldContent = field.NewString(table, "old_content")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_id"] = s.StoryID
	s.fieldMap["action"] = s.Action
	s.fieldMap["content"] = s.Content
	s.fieldMap["old_content"] = s.OldContent
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s storyLog) clone(db *gorm.DB) storyLog {
	s.storyLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyLog) replaceDB(db *gorm.DB) storyLog {
	s.storyLogDo.ReplaceDB(db)
	return s
}

type storyLogDo struct{ gen.DO }

type IStoryLogDo interface {
	gen.SubQuery
	Debug() IStoryLogDo
	WithContext(ctx context.Context) IStoryLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryLogDo
	WriteDB() IStoryLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryLogDo
	Not(conds ...gen.Condition) IStoryLogDo
	Or(conds ...gen.Condition) IStoryLogDo
	Select(conds ...field.Expr) IStoryLogDo
	Where(conds ...gen.Condition) IStoryLogDo
	Order(conds ...field.Expr) IStoryLogDo
	Distinct(cols ...field.Expr) IStoryLogDo
	Omit(cols ...field.Expr) IStoryLogDo
	Join(table schema.Tabler, on ...field.Expr) IStoryLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryLogDo
	Group(cols ...field.Expr) IStoryLogDo
	Having(conds ...gen.Condition) IStoryLogDo
	Limit(limit int) IStoryLogDo
	Offset(offset int) IStoryLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryLogDo
	Unscoped() IStoryLogDo
	Create(values ...*model.StoryLog) error
	CreateInBatches(values []*model.StoryLog, batchSize int) error
	Save(values ...*model.StoryLog) error
	First() (*model.StoryLog, error)
	Take() (*model.StoryLog, error)
	Last() (*model.StoryLog, error)
	Find() ([]*model.StoryLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryLog, err error)
	FindInBatches(result *[]*model.StoryLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryLogDo
	Assign(attrs ...field.AssignExpr) IStoryLogDo
	Joins(fields ...field.RelationField) IStoryLogDo
	Preload(fields ...field.RelationField) IStoryLogDo
	FirstOrInit() (*model.StoryLog, error)
	FirstOrCreate() (*model.StoryLog, error)
	FindByPage(offset int, limit int) (result []*model.StoryLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyLogDo) Debug() IStoryLogDo {
	return s.withDO(s.DO.Debug())
}

func (s storyLogDo) WithContext(ctx context.Context) IStoryLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyLogDo) ReadDB() IStoryLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyLogDo) WriteDB() IStoryLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyLogDo) Session(config *gorm.Session) IStoryLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyLogDo) Clauses(conds ...clause.Expression) IStoryLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyLogDo) Returning(value interface{}, columns ...string) IStoryLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyLogDo) Not(conds ...gen.Condition) IStoryLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyLogDo) Or(conds ...gen.Condition) IStoryLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyLogDo) Select(conds ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyLogDo) Where(conds ...gen.Condition) IStoryLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyLogDo) Order(conds ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyLogDo) Distinct(cols ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyLogDo) Omit(cols ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyLogDo) Join(table schema.Tabler, on ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyLogDo) Group(cols ...field.Expr) IStoryLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyLogDo) Having(conds ...gen.Condition) IStoryLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyLogDo) Limit(limit int) IStoryLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyLogDo) Offset(offset int) IStoryLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyLogDo) Unscoped() IStoryLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyLogDo) Create(values ...*model.StoryLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyLogDo) CreateInBatches(values []*model.StoryLog, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyLogDo) Save(values ...*model.StoryLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyLogDo) First() (*model.StoryLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryLog), nil
	}
}

func (s storyLogDo) Take() (*model.StoryLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryLog), nil
	}
}

func (s storyLogDo) Last() (*model.StoryLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryLog), nil
	}
}

func (s storyLogDo) Find() ([]*model.StoryLog, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryLog), err
}

func (s storyLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryLog, err error) {
	buf := make([]*model.StoryLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyLogDo) FindInBatches(result *[]*model.StoryLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyLogDo) Attrs(attrs ...field.AssignExpr) IStoryLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyLogDo) Assign(attrs ...field.AssignExpr) IStoryLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyLogDo) Joins(fields ...field.RelationField) IStoryLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyLogDo) Preload(fields ...field.RelationField) IStoryLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyLogDo) FirstOrInit() (*model.StoryLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryLog), nil
	}
}

func (s storyLogDo) FirstOrCreate() (*model.StoryLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryLog), nil
	}
}

func (s storyLogDo) FindByPage(offset int, limit int) (result []*model.StoryLog, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyLogDo) Delete(models ...*model.StoryLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyLogDo) withDO(do gen.Dao) *storyLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
