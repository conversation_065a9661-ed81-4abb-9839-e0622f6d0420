// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryOrder(db *gorm.DB, opts ...gen.DOOption) storyOrder {
	_storyOrder := storyOrder{}

	_storyOrder.storyOrderDo.UseDB(db, opts...)
	_storyOrder.storyOrderDo.UseModel(&model.StoryOrder{})

	tableName := _storyOrder.storyOrderDo.TableName()
	_storyOrder.ALL = field.NewAsterisk(tableName)
	_storyOrder.ID = field.NewInt64(tableName, "id")
	_storyOrder.OrderID = field.NewString(tableName, "order_id")
	_storyOrder.SceneID = field.NewInt64(tableName, "scene_id")
	_storyOrder.SceneName = field.NewString(tableName, "scene_name")
	_storyOrder.StoryID = field.NewInt64(tableName, "story_id")
	_storyOrder.StoryTitle = field.NewString(tableName, "story_title")
	_storyOrder.ItemID = field.NewString(tableName, "item_id")
	_storyOrder.ItemTitle = field.NewString(tableName, "item_title")
	_storyOrder.ItemCoverURL = field.NewString(tableName, "item_cover_url")
	_storyOrder.ItemInfo = field.NewField(tableName, "item_info")
	_storyOrder.UserID = field.NewString(tableName, "user_id")
	_storyOrder.Status = field.NewInt32(tableName, "status")
	_storyOrder.Qty = field.NewInt32(tableName, "qty")
	_storyOrder.AppChannel = field.NewString(tableName, "app_channel")
	_storyOrder.AppVersion = field.NewString(tableName, "app_version")
	_storyOrder.IP = field.NewString(tableName, "ip")
	_storyOrder.CompleteTime = field.NewTime(tableName, "complete_time")
	_storyOrder.FinishTime = field.NewTime(tableName, "finish_time")
	_storyOrder.ChainHash = field.NewString(tableName, "chain_hash")
	_storyOrder.ChainDataID = field.NewString(tableName, "chain_data_id")
	_storyOrder.ChainStatus = field.NewInt32(tableName, "chain_status")
	_storyOrder.CreatedBy = field.NewString(tableName, "created_by")
	_storyOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_storyOrder.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyOrder.IsDel = field.NewField(tableName, "is_del")
	_storyOrder.StoryOrderDetail = storyOrderHasManyStoryOrderDetail{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("StoryOrderDetail", "model.StoryOrderDetail"),
	}

	_storyOrder.Story = storyOrderBelongsToStory{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Story", "model.Story"),
	}

	_storyOrder.fillFieldMap()

	return _storyOrder
}

// storyOrder 故事玩法订单表
type storyOrder struct {
	storyOrderDo

	ALL              field.Asterisk
	ID               field.Int64  // id
	OrderID          field.String // 订单号
	SceneID          field.Int64  // 故事玩法场景id(scene.id)
	SceneName        field.String // 故事玩法场景名称(scene.name)
	StoryID          field.Int64  // 故事玩法活动id(story.id)
	StoryTitle       field.String // 故事玩法活动名称(story.title)
	ItemID           field.String // 故事玩法物品id(商品/故事玩法)
	ItemTitle        field.String // 故事玩法物品名称(商品/故事玩法)
	ItemCoverURL     field.String // 故事玩法物品图片，没有则为空
	ItemInfo         field.Field  // 故事玩法物品信息
	UserID           field.String // 用户id
	Status           field.Int32  // 状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);32:故事玩法探索结束(发起领奖);91:已完成】
	Qty              field.Int32  // 故事玩法数量
	AppChannel       field.String // 设备渠道
	AppVersion       field.String // 设备版本号
	IP               field.String // ip
	CompleteTime     field.Time   // 探索结束时间
	FinishTime       field.Time   // 完成时间
	ChainHash        field.String // 链hash
	ChainDataID      field.String // 链data_id
	ChainStatus      field.Int32  // 链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	CreatedBy        field.String // 创建人
	CreatedAt        field.Time   // 创建时间
	UpdatedBy        field.String // 更新人
	UpdatedAt        field.Time   // 更新时间
	IsDel            field.Field  // 是否删除【0->未删除; 1->删除】
	StoryOrderDetail storyOrderHasManyStoryOrderDetail

	Story storyOrderBelongsToStory

	fieldMap map[string]field.Expr
}

func (s storyOrder) Table(newTableName string) *storyOrder {
	s.storyOrderDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyOrder) As(alias string) *storyOrder {
	s.storyOrderDo.DO = *(s.storyOrderDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyOrder) updateTableName(table string) *storyOrder {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OrderID = field.NewString(table, "order_id")
	s.SceneID = field.NewInt64(table, "scene_id")
	s.SceneName = field.NewString(table, "scene_name")
	s.StoryID = field.NewInt64(table, "story_id")
	s.StoryTitle = field.NewString(table, "story_title")
	s.ItemID = field.NewString(table, "item_id")
	s.ItemTitle = field.NewString(table, "item_title")
	s.ItemCoverURL = field.NewString(table, "item_cover_url")
	s.ItemInfo = field.NewField(table, "item_info")
	s.UserID = field.NewString(table, "user_id")
	s.Status = field.NewInt32(table, "status")
	s.Qty = field.NewInt32(table, "qty")
	s.AppChannel = field.NewString(table, "app_channel")
	s.AppVersion = field.NewString(table, "app_version")
	s.IP = field.NewString(table, "ip")
	s.CompleteTime = field.NewTime(table, "complete_time")
	s.FinishTime = field.NewTime(table, "finish_time")
	s.ChainHash = field.NewString(table, "chain_hash")
	s.ChainDataID = field.NewString(table, "chain_data_id")
	s.ChainStatus = field.NewInt32(table, "chain_status")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyOrder) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 28)
	s.fieldMap["id"] = s.ID
	s.fieldMap["order_id"] = s.OrderID
	s.fieldMap["scene_id"] = s.SceneID
	s.fieldMap["scene_name"] = s.SceneName
	s.fieldMap["story_id"] = s.StoryID
	s.fieldMap["story_title"] = s.StoryTitle
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["item_title"] = s.ItemTitle
	s.fieldMap["item_cover_url"] = s.ItemCoverURL
	s.fieldMap["item_info"] = s.ItemInfo
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["status"] = s.Status
	s.fieldMap["qty"] = s.Qty
	s.fieldMap["app_channel"] = s.AppChannel
	s.fieldMap["app_version"] = s.AppVersion
	s.fieldMap["ip"] = s.IP
	s.fieldMap["complete_time"] = s.CompleteTime
	s.fieldMap["finish_time"] = s.FinishTime
	s.fieldMap["chain_hash"] = s.ChainHash
	s.fieldMap["chain_data_id"] = s.ChainDataID
	s.fieldMap["chain_status"] = s.ChainStatus
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel

}

func (s storyOrder) clone(db *gorm.DB) storyOrder {
	s.storyOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyOrder) replaceDB(db *gorm.DB) storyOrder {
	s.storyOrderDo.ReplaceDB(db)
	return s
}

type storyOrderHasManyStoryOrderDetail struct {
	db *gorm.DB

	field.RelationField
}

func (a storyOrderHasManyStoryOrderDetail) Where(conds ...field.Expr) *storyOrderHasManyStoryOrderDetail {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a storyOrderHasManyStoryOrderDetail) WithContext(ctx context.Context) *storyOrderHasManyStoryOrderDetail {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a storyOrderHasManyStoryOrderDetail) Session(session *gorm.Session) *storyOrderHasManyStoryOrderDetail {
	a.db = a.db.Session(session)
	return &a
}

func (a storyOrderHasManyStoryOrderDetail) Model(m *model.StoryOrder) *storyOrderHasManyStoryOrderDetailTx {
	return &storyOrderHasManyStoryOrderDetailTx{a.db.Model(m).Association(a.Name())}
}

type storyOrderHasManyStoryOrderDetailTx struct{ tx *gorm.Association }

func (a storyOrderHasManyStoryOrderDetailTx) Find() (result []*model.StoryOrderDetail, err error) {
	return result, a.tx.Find(&result)
}

func (a storyOrderHasManyStoryOrderDetailTx) Append(values ...*model.StoryOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a storyOrderHasManyStoryOrderDetailTx) Replace(values ...*model.StoryOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a storyOrderHasManyStoryOrderDetailTx) Delete(values ...*model.StoryOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a storyOrderHasManyStoryOrderDetailTx) Clear() error {
	return a.tx.Clear()
}

func (a storyOrderHasManyStoryOrderDetailTx) Count() int64 {
	return a.tx.Count()
}

type storyOrderBelongsToStory struct {
	db *gorm.DB

	field.RelationField
}

func (a storyOrderBelongsToStory) Where(conds ...field.Expr) *storyOrderBelongsToStory {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a storyOrderBelongsToStory) WithContext(ctx context.Context) *storyOrderBelongsToStory {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a storyOrderBelongsToStory) Session(session *gorm.Session) *storyOrderBelongsToStory {
	a.db = a.db.Session(session)
	return &a
}

func (a storyOrderBelongsToStory) Model(m *model.StoryOrder) *storyOrderBelongsToStoryTx {
	return &storyOrderBelongsToStoryTx{a.db.Model(m).Association(a.Name())}
}

type storyOrderBelongsToStoryTx struct{ tx *gorm.Association }

func (a storyOrderBelongsToStoryTx) Find() (result *model.Story, err error) {
	return result, a.tx.Find(&result)
}

func (a storyOrderBelongsToStoryTx) Append(values ...*model.Story) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a storyOrderBelongsToStoryTx) Replace(values ...*model.Story) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a storyOrderBelongsToStoryTx) Delete(values ...*model.Story) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a storyOrderBelongsToStoryTx) Clear() error {
	return a.tx.Clear()
}

func (a storyOrderBelongsToStoryTx) Count() int64 {
	return a.tx.Count()
}

type storyOrderDo struct{ gen.DO }

type IStoryOrderDo interface {
	gen.SubQuery
	Debug() IStoryOrderDo
	WithContext(ctx context.Context) IStoryOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryOrderDo
	WriteDB() IStoryOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryOrderDo
	Not(conds ...gen.Condition) IStoryOrderDo
	Or(conds ...gen.Condition) IStoryOrderDo
	Select(conds ...field.Expr) IStoryOrderDo
	Where(conds ...gen.Condition) IStoryOrderDo
	Order(conds ...field.Expr) IStoryOrderDo
	Distinct(cols ...field.Expr) IStoryOrderDo
	Omit(cols ...field.Expr) IStoryOrderDo
	Join(table schema.Tabler, on ...field.Expr) IStoryOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDo
	Group(cols ...field.Expr) IStoryOrderDo
	Having(conds ...gen.Condition) IStoryOrderDo
	Limit(limit int) IStoryOrderDo
	Offset(offset int) IStoryOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryOrderDo
	Unscoped() IStoryOrderDo
	Create(values ...*model.StoryOrder) error
	CreateInBatches(values []*model.StoryOrder, batchSize int) error
	Save(values ...*model.StoryOrder) error
	First() (*model.StoryOrder, error)
	Take() (*model.StoryOrder, error)
	Last() (*model.StoryOrder, error)
	Find() ([]*model.StoryOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryOrder, err error)
	FindInBatches(result *[]*model.StoryOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryOrderDo
	Assign(attrs ...field.AssignExpr) IStoryOrderDo
	Joins(fields ...field.RelationField) IStoryOrderDo
	Preload(fields ...field.RelationField) IStoryOrderDo
	FirstOrInit() (*model.StoryOrder, error)
	FirstOrCreate() (*model.StoryOrder, error)
	FindByPage(offset int, limit int) (result []*model.StoryOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyOrderDo) Debug() IStoryOrderDo {
	return s.withDO(s.DO.Debug())
}

func (s storyOrderDo) WithContext(ctx context.Context) IStoryOrderDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyOrderDo) ReadDB() IStoryOrderDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyOrderDo) WriteDB() IStoryOrderDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyOrderDo) Session(config *gorm.Session) IStoryOrderDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyOrderDo) Clauses(conds ...clause.Expression) IStoryOrderDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyOrderDo) Returning(value interface{}, columns ...string) IStoryOrderDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyOrderDo) Not(conds ...gen.Condition) IStoryOrderDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyOrderDo) Or(conds ...gen.Condition) IStoryOrderDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyOrderDo) Select(conds ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyOrderDo) Where(conds ...gen.Condition) IStoryOrderDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyOrderDo) Order(conds ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyOrderDo) Distinct(cols ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyOrderDo) Omit(cols ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyOrderDo) Join(table schema.Tabler, on ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyOrderDo) Group(cols ...field.Expr) IStoryOrderDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyOrderDo) Having(conds ...gen.Condition) IStoryOrderDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyOrderDo) Limit(limit int) IStoryOrderDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyOrderDo) Offset(offset int) IStoryOrderDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryOrderDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyOrderDo) Unscoped() IStoryOrderDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyOrderDo) Create(values ...*model.StoryOrder) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyOrderDo) CreateInBatches(values []*model.StoryOrder, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyOrderDo) Save(values ...*model.StoryOrder) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyOrderDo) First() (*model.StoryOrder, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrder), nil
	}
}

func (s storyOrderDo) Take() (*model.StoryOrder, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrder), nil
	}
}

func (s storyOrderDo) Last() (*model.StoryOrder, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrder), nil
	}
}

func (s storyOrderDo) Find() ([]*model.StoryOrder, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryOrder), err
}

func (s storyOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryOrder, err error) {
	buf := make([]*model.StoryOrder, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyOrderDo) FindInBatches(result *[]*model.StoryOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyOrderDo) Attrs(attrs ...field.AssignExpr) IStoryOrderDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyOrderDo) Assign(attrs ...field.AssignExpr) IStoryOrderDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyOrderDo) Joins(fields ...field.RelationField) IStoryOrderDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyOrderDo) Preload(fields ...field.RelationField) IStoryOrderDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyOrderDo) FirstOrInit() (*model.StoryOrder, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrder), nil
	}
}

func (s storyOrderDo) FirstOrCreate() (*model.StoryOrder, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryOrder), nil
	}
}

func (s storyOrderDo) FindByPage(offset int, limit int) (result []*model.StoryOrder, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyOrderDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyOrderDo) Delete(models ...*model.StoryOrder) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyOrderDo) withDO(do gen.Dao) *storyOrderDo {
	s.DO = *do.(*gen.DO)
	return s
}
