// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryMaterialsRelease(db *gorm.DB, opts ...gen.DOOption) storyMaterialsRelease {
	_storyMaterialsRelease := storyMaterialsRelease{}

	_storyMaterialsRelease.storyMaterialsReleaseDo.UseDB(db, opts...)
	_storyMaterialsRelease.storyMaterialsReleaseDo.UseModel(&model.StoryMaterialsRelease{})

	tableName := _storyMaterialsRelease.storyMaterialsReleaseDo.TableName()
	_storyMaterialsRelease.ALL = field.NewAsterisk(tableName)
	_storyMaterialsRelease.ID = field.NewInt64(tableName, "id")
	_storyMaterialsRelease.StoryOrderDetailID = field.NewInt64(tableName, "story_order_detail_id")
	_storyMaterialsRelease.UserID = field.NewString(tableName, "user_id")
	_storyMaterialsRelease.ItemID = field.NewString(tableName, "item_id")
	_storyMaterialsRelease.UserItemID = field.NewString(tableName, "user_item_id")
	_storyMaterialsRelease.Status = field.NewInt32(tableName, "status")
	_storyMaterialsRelease.ReleaseTime = field.NewTime(tableName, "release_time")
	_storyMaterialsRelease.RealReleaseTime = field.NewTime(tableName, "real_release_time")
	_storyMaterialsRelease.CreatedBy = field.NewString(tableName, "created_by")
	_storyMaterialsRelease.CreatedAt = field.NewTime(tableName, "created_at")
	_storyMaterialsRelease.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyMaterialsRelease.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyMaterialsRelease.IsDel = field.NewField(tableName, "is_del")

	_storyMaterialsRelease.fillFieldMap()

	return _storyMaterialsRelease
}

// storyMaterialsRelease 故事玩法材料释放表
type storyMaterialsRelease struct {
	storyMaterialsReleaseDo

	ALL                field.Asterisk
	ID                 field.Int64  // id
	StoryOrderDetailID field.Int64  // 故事玩法订单详情id(story_order_detail.id)
	UserID             field.String // 用户id
	ItemID             field.String // 物品id
	UserItemID         field.String // 用户背包物品id
	Status             field.Int32  // 状态【-1:释放失败;1:已释放;2:待释放;3:释放中】
	ReleaseTime        field.Time   // 应释放时间
	RealReleaseTime    field.Time   // 实际释放时间
	CreatedBy          field.String // 创建人
	CreatedAt          field.Time   // 创建时间
	UpdatedBy          field.String // 更新人
	UpdatedAt          field.Time   // 更新时间
	IsDel              field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s storyMaterialsRelease) Table(newTableName string) *storyMaterialsRelease {
	s.storyMaterialsReleaseDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyMaterialsRelease) As(alias string) *storyMaterialsRelease {
	s.storyMaterialsReleaseDo.DO = *(s.storyMaterialsReleaseDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyMaterialsRelease) updateTableName(table string) *storyMaterialsRelease {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryOrderDetailID = field.NewInt64(table, "story_order_detail_id")
	s.UserID = field.NewString(table, "user_id")
	s.ItemID = field.NewString(table, "item_id")
	s.UserItemID = field.NewString(table, "user_item_id")
	s.Status = field.NewInt32(table, "status")
	s.ReleaseTime = field.NewTime(table, "release_time")
	s.RealReleaseTime = field.NewTime(table, "real_release_time")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyMaterialsRelease) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyMaterialsRelease) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_order_detail_id"] = s.StoryOrderDetailID
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["user_item_id"] = s.UserItemID
	s.fieldMap["status"] = s.Status
	s.fieldMap["release_time"] = s.ReleaseTime
	s.fieldMap["real_release_time"] = s.RealReleaseTime
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s storyMaterialsRelease) clone(db *gorm.DB) storyMaterialsRelease {
	s.storyMaterialsReleaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyMaterialsRelease) replaceDB(db *gorm.DB) storyMaterialsRelease {
	s.storyMaterialsReleaseDo.ReplaceDB(db)
	return s
}

type storyMaterialsReleaseDo struct{ gen.DO }

type IStoryMaterialsReleaseDo interface {
	gen.SubQuery
	Debug() IStoryMaterialsReleaseDo
	WithContext(ctx context.Context) IStoryMaterialsReleaseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryMaterialsReleaseDo
	WriteDB() IStoryMaterialsReleaseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryMaterialsReleaseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryMaterialsReleaseDo
	Not(conds ...gen.Condition) IStoryMaterialsReleaseDo
	Or(conds ...gen.Condition) IStoryMaterialsReleaseDo
	Select(conds ...field.Expr) IStoryMaterialsReleaseDo
	Where(conds ...gen.Condition) IStoryMaterialsReleaseDo
	Order(conds ...field.Expr) IStoryMaterialsReleaseDo
	Distinct(cols ...field.Expr) IStoryMaterialsReleaseDo
	Omit(cols ...field.Expr) IStoryMaterialsReleaseDo
	Join(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo
	Group(cols ...field.Expr) IStoryMaterialsReleaseDo
	Having(conds ...gen.Condition) IStoryMaterialsReleaseDo
	Limit(limit int) IStoryMaterialsReleaseDo
	Offset(offset int) IStoryMaterialsReleaseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryMaterialsReleaseDo
	Unscoped() IStoryMaterialsReleaseDo
	Create(values ...*model.StoryMaterialsRelease) error
	CreateInBatches(values []*model.StoryMaterialsRelease, batchSize int) error
	Save(values ...*model.StoryMaterialsRelease) error
	First() (*model.StoryMaterialsRelease, error)
	Take() (*model.StoryMaterialsRelease, error)
	Last() (*model.StoryMaterialsRelease, error)
	Find() ([]*model.StoryMaterialsRelease, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryMaterialsRelease, err error)
	FindInBatches(result *[]*model.StoryMaterialsRelease, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryMaterialsRelease) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryMaterialsReleaseDo
	Assign(attrs ...field.AssignExpr) IStoryMaterialsReleaseDo
	Joins(fields ...field.RelationField) IStoryMaterialsReleaseDo
	Preload(fields ...field.RelationField) IStoryMaterialsReleaseDo
	FirstOrInit() (*model.StoryMaterialsRelease, error)
	FirstOrCreate() (*model.StoryMaterialsRelease, error)
	FindByPage(offset int, limit int) (result []*model.StoryMaterialsRelease, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryMaterialsReleaseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyMaterialsReleaseDo) Debug() IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Debug())
}

func (s storyMaterialsReleaseDo) WithContext(ctx context.Context) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyMaterialsReleaseDo) ReadDB() IStoryMaterialsReleaseDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyMaterialsReleaseDo) WriteDB() IStoryMaterialsReleaseDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyMaterialsReleaseDo) Session(config *gorm.Session) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyMaterialsReleaseDo) Clauses(conds ...clause.Expression) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyMaterialsReleaseDo) Returning(value interface{}, columns ...string) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyMaterialsReleaseDo) Not(conds ...gen.Condition) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyMaterialsReleaseDo) Or(conds ...gen.Condition) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyMaterialsReleaseDo) Select(conds ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyMaterialsReleaseDo) Where(conds ...gen.Condition) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyMaterialsReleaseDo) Order(conds ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyMaterialsReleaseDo) Distinct(cols ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyMaterialsReleaseDo) Omit(cols ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyMaterialsReleaseDo) Join(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyMaterialsReleaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyMaterialsReleaseDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyMaterialsReleaseDo) Group(cols ...field.Expr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyMaterialsReleaseDo) Having(conds ...gen.Condition) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyMaterialsReleaseDo) Limit(limit int) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyMaterialsReleaseDo) Offset(offset int) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyMaterialsReleaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyMaterialsReleaseDo) Unscoped() IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyMaterialsReleaseDo) Create(values ...*model.StoryMaterialsRelease) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyMaterialsReleaseDo) CreateInBatches(values []*model.StoryMaterialsRelease, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyMaterialsReleaseDo) Save(values ...*model.StoryMaterialsRelease) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyMaterialsReleaseDo) First() (*model.StoryMaterialsRelease, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterialsRelease), nil
	}
}

func (s storyMaterialsReleaseDo) Take() (*model.StoryMaterialsRelease, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterialsRelease), nil
	}
}

func (s storyMaterialsReleaseDo) Last() (*model.StoryMaterialsRelease, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterialsRelease), nil
	}
}

func (s storyMaterialsReleaseDo) Find() ([]*model.StoryMaterialsRelease, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryMaterialsRelease), err
}

func (s storyMaterialsReleaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryMaterialsRelease, err error) {
	buf := make([]*model.StoryMaterialsRelease, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyMaterialsReleaseDo) FindInBatches(result *[]*model.StoryMaterialsRelease, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyMaterialsReleaseDo) Attrs(attrs ...field.AssignExpr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyMaterialsReleaseDo) Assign(attrs ...field.AssignExpr) IStoryMaterialsReleaseDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyMaterialsReleaseDo) Joins(fields ...field.RelationField) IStoryMaterialsReleaseDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyMaterialsReleaseDo) Preload(fields ...field.RelationField) IStoryMaterialsReleaseDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyMaterialsReleaseDo) FirstOrInit() (*model.StoryMaterialsRelease, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterialsRelease), nil
	}
}

func (s storyMaterialsReleaseDo) FirstOrCreate() (*model.StoryMaterialsRelease, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterialsRelease), nil
	}
}

func (s storyMaterialsReleaseDo) FindByPage(offset int, limit int) (result []*model.StoryMaterialsRelease, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyMaterialsReleaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyMaterialsReleaseDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyMaterialsReleaseDo) Delete(models ...*model.StoryMaterialsRelease) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyMaterialsReleaseDo) withDO(do gen.Dao) *storyMaterialsReleaseDo {
	s.DO = *do.(*gen.DO)
	return s
}
