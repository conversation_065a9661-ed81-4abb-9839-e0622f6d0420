// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryMaterials(db *gorm.DB, opts ...gen.DOOption) storyMaterials {
	_storyMaterials := storyMaterials{}

	_storyMaterials.storyMaterialsDo.UseDB(db, opts...)
	_storyMaterials.storyMaterialsDo.UseModel(&model.StoryMaterials{})

	tableName := _storyMaterials.storyMaterialsDo.TableName()
	_storyMaterials.ALL = field.NewAsterisk(tableName)
	_storyMaterials.ID = field.NewInt64(tableName, "id")
	_storyMaterials.StoryID = field.NewInt64(tableName, "story_id")
	_storyMaterials.MaterialsType = field.NewInt32(tableName, "materials_type")
	_storyMaterials.MaterialsData = field.NewField(tableName, "materials_data")
	_storyMaterials.LimitQty = field.NewInt32(tableName, "limit_qty")
	_storyMaterials.CreatedBy = field.NewString(tableName, "created_by")
	_storyMaterials.CreatedAt = field.NewTime(tableName, "created_at")
	_storyMaterials.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyMaterials.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyMaterials.IsDel = field.NewField(tableName, "is_del")

	_storyMaterials.fillFieldMap()

	return _storyMaterials
}

// storyMaterials 故事玩法材料表
type storyMaterials struct {
	storyMaterialsDo

	ALL           field.Asterisk
	ID            field.Int64  // id
	StoryID       field.Int64  // 故事玩法活动id(story.id)
	MaterialsType field.Int32  // 材料类型【1-核心材料；2-关键材料】
	MaterialsData field.Field  // 材料内容
	LimitQty      field.Int32  // 限制数量(0全部集齐)
	CreatedBy     field.String // 创建人
	CreatedAt     field.Time   // 创建时间
	UpdatedBy     field.String // 更新人
	UpdatedAt     field.Time   // 更新时间
	IsDel         field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s storyMaterials) Table(newTableName string) *storyMaterials {
	s.storyMaterialsDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyMaterials) As(alias string) *storyMaterials {
	s.storyMaterialsDo.DO = *(s.storyMaterialsDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyMaterials) updateTableName(table string) *storyMaterials {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.StoryID = field.NewInt64(table, "story_id")
	s.MaterialsType = field.NewInt32(table, "materials_type")
	s.MaterialsData = field.NewField(table, "materials_data")
	s.LimitQty = field.NewInt32(table, "limit_qty")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyMaterials) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyMaterials) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["story_id"] = s.StoryID
	s.fieldMap["materials_type"] = s.MaterialsType
	s.fieldMap["materials_data"] = s.MaterialsData
	s.fieldMap["limit_qty"] = s.LimitQty
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s storyMaterials) clone(db *gorm.DB) storyMaterials {
	s.storyMaterialsDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyMaterials) replaceDB(db *gorm.DB) storyMaterials {
	s.storyMaterialsDo.ReplaceDB(db)
	return s
}

type storyMaterialsDo struct{ gen.DO }

type IStoryMaterialsDo interface {
	gen.SubQuery
	Debug() IStoryMaterialsDo
	WithContext(ctx context.Context) IStoryMaterialsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryMaterialsDo
	WriteDB() IStoryMaterialsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryMaterialsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryMaterialsDo
	Not(conds ...gen.Condition) IStoryMaterialsDo
	Or(conds ...gen.Condition) IStoryMaterialsDo
	Select(conds ...field.Expr) IStoryMaterialsDo
	Where(conds ...gen.Condition) IStoryMaterialsDo
	Order(conds ...field.Expr) IStoryMaterialsDo
	Distinct(cols ...field.Expr) IStoryMaterialsDo
	Omit(cols ...field.Expr) IStoryMaterialsDo
	Join(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo
	Group(cols ...field.Expr) IStoryMaterialsDo
	Having(conds ...gen.Condition) IStoryMaterialsDo
	Limit(limit int) IStoryMaterialsDo
	Offset(offset int) IStoryMaterialsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryMaterialsDo
	Unscoped() IStoryMaterialsDo
	Create(values ...*model.StoryMaterials) error
	CreateInBatches(values []*model.StoryMaterials, batchSize int) error
	Save(values ...*model.StoryMaterials) error
	First() (*model.StoryMaterials, error)
	Take() (*model.StoryMaterials, error)
	Last() (*model.StoryMaterials, error)
	Find() ([]*model.StoryMaterials, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryMaterials, err error)
	FindInBatches(result *[]*model.StoryMaterials, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryMaterials) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryMaterialsDo
	Assign(attrs ...field.AssignExpr) IStoryMaterialsDo
	Joins(fields ...field.RelationField) IStoryMaterialsDo
	Preload(fields ...field.RelationField) IStoryMaterialsDo
	FirstOrInit() (*model.StoryMaterials, error)
	FirstOrCreate() (*model.StoryMaterials, error)
	FindByPage(offset int, limit int) (result []*model.StoryMaterials, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryMaterialsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyMaterialsDo) Debug() IStoryMaterialsDo {
	return s.withDO(s.DO.Debug())
}

func (s storyMaterialsDo) WithContext(ctx context.Context) IStoryMaterialsDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyMaterialsDo) ReadDB() IStoryMaterialsDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyMaterialsDo) WriteDB() IStoryMaterialsDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyMaterialsDo) Session(config *gorm.Session) IStoryMaterialsDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyMaterialsDo) Clauses(conds ...clause.Expression) IStoryMaterialsDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyMaterialsDo) Returning(value interface{}, columns ...string) IStoryMaterialsDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyMaterialsDo) Not(conds ...gen.Condition) IStoryMaterialsDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyMaterialsDo) Or(conds ...gen.Condition) IStoryMaterialsDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyMaterialsDo) Select(conds ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyMaterialsDo) Where(conds ...gen.Condition) IStoryMaterialsDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyMaterialsDo) Order(conds ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyMaterialsDo) Distinct(cols ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyMaterialsDo) Omit(cols ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyMaterialsDo) Join(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyMaterialsDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyMaterialsDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyMaterialsDo) Group(cols ...field.Expr) IStoryMaterialsDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyMaterialsDo) Having(conds ...gen.Condition) IStoryMaterialsDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyMaterialsDo) Limit(limit int) IStoryMaterialsDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyMaterialsDo) Offset(offset int) IStoryMaterialsDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyMaterialsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryMaterialsDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyMaterialsDo) Unscoped() IStoryMaterialsDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyMaterialsDo) Create(values ...*model.StoryMaterials) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyMaterialsDo) CreateInBatches(values []*model.StoryMaterials, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyMaterialsDo) Save(values ...*model.StoryMaterials) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyMaterialsDo) First() (*model.StoryMaterials, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterials), nil
	}
}

func (s storyMaterialsDo) Take() (*model.StoryMaterials, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterials), nil
	}
}

func (s storyMaterialsDo) Last() (*model.StoryMaterials, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterials), nil
	}
}

func (s storyMaterialsDo) Find() ([]*model.StoryMaterials, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryMaterials), err
}

func (s storyMaterialsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryMaterials, err error) {
	buf := make([]*model.StoryMaterials, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyMaterialsDo) FindInBatches(result *[]*model.StoryMaterials, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyMaterialsDo) Attrs(attrs ...field.AssignExpr) IStoryMaterialsDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyMaterialsDo) Assign(attrs ...field.AssignExpr) IStoryMaterialsDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyMaterialsDo) Joins(fields ...field.RelationField) IStoryMaterialsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyMaterialsDo) Preload(fields ...field.RelationField) IStoryMaterialsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyMaterialsDo) FirstOrInit() (*model.StoryMaterials, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterials), nil
	}
}

func (s storyMaterialsDo) FirstOrCreate() (*model.StoryMaterials, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryMaterials), nil
	}
}

func (s storyMaterialsDo) FindByPage(offset int, limit int) (result []*model.StoryMaterials, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyMaterialsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyMaterialsDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyMaterialsDo) Delete(models ...*model.StoryMaterials) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyMaterialsDo) withDO(do gen.Dao) *storyMaterialsDo {
	s.DO = *do.(*gen.DO)
	return s
}
