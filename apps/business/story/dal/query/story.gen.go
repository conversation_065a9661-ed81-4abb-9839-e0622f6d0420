// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStory(db *gorm.DB, opts ...gen.DOOption) story {
	_story := story{}

	_story.storyDo.UseDB(db, opts...)
	_story.storyDo.UseModel(&model.Story{})

	tableName := _story.storyDo.TableName()
	_story.ALL = field.NewAsterisk(tableName)
	_story.ID = field.NewInt64(tableName, "id")
	_story.SceneID = field.NewInt64(tableName, "scene_id")
	_story.ActivityCode = field.NewString(tableName, "activity_code")
	_story.Title = field.NewString(tableName, "title")
	_story.ActivityType = field.NewInt32(tableName, "activity_type")
	_story.CoverURL = field.NewString(tableName, "cover_url")
	_story.Status = field.NewInt32(tableName, "status")
	_story.ItemID = field.NewString(tableName, "item_id")
	_story.ItemTitle = field.NewString(tableName, "item_title")
	_story.ItemImageURL = field.NewString(tableName, "item_image_url")
	_story.UserLimit = field.NewInt32(tableName, "user_limit")
	_story.CriticalMaterialLimit = field.NewInt32(tableName, "critical_material_limit")
	_story.CompleteUserNum = field.NewInt32(tableName, "complete_user_num")
	_story.Stock = field.NewInt32(tableName, "stock")
	_story.TotalStock = field.NewInt32(tableName, "total_stock")
	_story.StockDisplay = field.NewInt32(tableName, "stock_display")
	_story.StartTime = field.NewTime(tableName, "start_time")
	_story.EndTime = field.NewTime(tableName, "end_time")
	_story.ActivityDesc = field.NewString(tableName, "activity_desc")
	_story.ReleaseTime = field.NewField(tableName, "release_time")
	_story.CreatedBy = field.NewString(tableName, "created_by")
	_story.CreatedAt = field.NewTime(tableName, "created_at")
	_story.UpdatedBy = field.NewString(tableName, "updated_by")
	_story.UpdatedAt = field.NewTime(tableName, "updated_at")
	_story.IsDel = field.NewField(tableName, "is_del")

	_story.fillFieldMap()

	return _story
}

// story 故事玩法活动表
type story struct {
	storyDo

	ALL                   field.Asterisk
	ID                    field.Int64  // id
	SceneID               field.Int64  // 故事玩法场景id(story_scene.id)
	ActivityCode          field.String // 活动编码
	Title                 field.String // 活动名称
	ActivityType          field.Int32  // 故事玩法类型【1-组合商品；2-商品】
	CoverURL              field.String // 封面图
	Status                field.Int32  // 状态【-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束】
	ItemID                field.String // 故事玩法物品id(商品/组合商品)
	ItemTitle             field.String // 故事玩法物品名称(商品/组合商品)
	ItemImageURL          field.String // 故事玩法物品图片
	UserLimit             field.Int32  // 每人限合(0不限制)
	CriticalMaterialLimit field.Int32  // 关键材料最小值
	CompleteUserNum       field.Int32  // 已合人数(按用户去重)
	Stock                 field.Int32  // 剩余库存
	TotalStock            field.Int32  // 总库存
	StockDisplay          field.Int32  // 剩余库存是否显示【1:显示;2:不显示】
	StartTime             field.Time   // 开始时间
	EndTime               field.Time   // 结束时间
	ActivityDesc          field.String // 说明
	ReleaseTime           field.Field  // 释放时间
	CreatedBy             field.String // 创建人
	CreatedAt             field.Time   // 创建时间
	UpdatedBy             field.String // 更新人
	UpdatedAt             field.Time   // 更新时间
	IsDel                 field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s story) Table(newTableName string) *story {
	s.storyDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s story) As(alias string) *story {
	s.storyDo.DO = *(s.storyDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *story) updateTableName(table string) *story {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SceneID = field.NewInt64(table, "scene_id")
	s.ActivityCode = field.NewString(table, "activity_code")
	s.Title = field.NewString(table, "title")
	s.ActivityType = field.NewInt32(table, "activity_type")
	s.CoverURL = field.NewString(table, "cover_url")
	s.Status = field.NewInt32(table, "status")
	s.ItemID = field.NewString(table, "item_id")
	s.ItemTitle = field.NewString(table, "item_title")
	s.ItemImageURL = field.NewString(table, "item_image_url")
	s.UserLimit = field.NewInt32(table, "user_limit")
	s.CriticalMaterialLimit = field.NewInt32(table, "critical_material_limit")
	s.CompleteUserNum = field.NewInt32(table, "complete_user_num")
	s.Stock = field.NewInt32(table, "stock")
	s.TotalStock = field.NewInt32(table, "total_stock")
	s.StockDisplay = field.NewInt32(table, "stock_display")
	s.StartTime = field.NewTime(table, "start_time")
	s.EndTime = field.NewTime(table, "end_time")
	s.ActivityDesc = field.NewString(table, "activity_desc")
	s.ReleaseTime = field.NewField(table, "release_time")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *story) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *story) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 25)
	s.fieldMap["id"] = s.ID
	s.fieldMap["scene_id"] = s.SceneID
	s.fieldMap["activity_code"] = s.ActivityCode
	s.fieldMap["title"] = s.Title
	s.fieldMap["activity_type"] = s.ActivityType
	s.fieldMap["cover_url"] = s.CoverURL
	s.fieldMap["status"] = s.Status
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["item_title"] = s.ItemTitle
	s.fieldMap["item_image_url"] = s.ItemImageURL
	s.fieldMap["user_limit"] = s.UserLimit
	s.fieldMap["critical_material_limit"] = s.CriticalMaterialLimit
	s.fieldMap["complete_user_num"] = s.CompleteUserNum
	s.fieldMap["stock"] = s.Stock
	s.fieldMap["total_stock"] = s.TotalStock
	s.fieldMap["stock_display"] = s.StockDisplay
	s.fieldMap["start_time"] = s.StartTime
	s.fieldMap["end_time"] = s.EndTime
	s.fieldMap["activity_desc"] = s.ActivityDesc
	s.fieldMap["release_time"] = s.ReleaseTime
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s story) clone(db *gorm.DB) story {
	s.storyDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s story) replaceDB(db *gorm.DB) story {
	s.storyDo.ReplaceDB(db)
	return s
}

type storyDo struct{ gen.DO }

type IStoryDo interface {
	gen.SubQuery
	Debug() IStoryDo
	WithContext(ctx context.Context) IStoryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStoryDo
	WriteDB() IStoryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStoryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStoryDo
	Not(conds ...gen.Condition) IStoryDo
	Or(conds ...gen.Condition) IStoryDo
	Select(conds ...field.Expr) IStoryDo
	Where(conds ...gen.Condition) IStoryDo
	Order(conds ...field.Expr) IStoryDo
	Distinct(cols ...field.Expr) IStoryDo
	Omit(cols ...field.Expr) IStoryDo
	Join(table schema.Tabler, on ...field.Expr) IStoryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStoryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStoryDo
	Group(cols ...field.Expr) IStoryDo
	Having(conds ...gen.Condition) IStoryDo
	Limit(limit int) IStoryDo
	Offset(offset int) IStoryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryDo
	Unscoped() IStoryDo
	Create(values ...*model.Story) error
	CreateInBatches(values []*model.Story, batchSize int) error
	Save(values ...*model.Story) error
	First() (*model.Story, error)
	Take() (*model.Story, error)
	Last() (*model.Story, error)
	Find() ([]*model.Story, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Story, err error)
	FindInBatches(result *[]*model.Story, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Story) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStoryDo
	Assign(attrs ...field.AssignExpr) IStoryDo
	Joins(fields ...field.RelationField) IStoryDo
	Preload(fields ...field.RelationField) IStoryDo
	FirstOrInit() (*model.Story, error)
	FirstOrCreate() (*model.Story, error)
	FindByPage(offset int, limit int) (result []*model.Story, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStoryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storyDo) Debug() IStoryDo {
	return s.withDO(s.DO.Debug())
}

func (s storyDo) WithContext(ctx context.Context) IStoryDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storyDo) ReadDB() IStoryDo {
	return s.Clauses(dbresolver.Read)
}

func (s storyDo) WriteDB() IStoryDo {
	return s.Clauses(dbresolver.Write)
}

func (s storyDo) Session(config *gorm.Session) IStoryDo {
	return s.withDO(s.DO.Session(config))
}

func (s storyDo) Clauses(conds ...clause.Expression) IStoryDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storyDo) Returning(value interface{}, columns ...string) IStoryDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storyDo) Not(conds ...gen.Condition) IStoryDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storyDo) Or(conds ...gen.Condition) IStoryDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storyDo) Select(conds ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storyDo) Where(conds ...gen.Condition) IStoryDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storyDo) Order(conds ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storyDo) Distinct(cols ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storyDo) Omit(cols ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storyDo) Join(table schema.Tabler, on ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storyDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStoryDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storyDo) RightJoin(table schema.Tabler, on ...field.Expr) IStoryDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storyDo) Group(cols ...field.Expr) IStoryDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storyDo) Having(conds ...gen.Condition) IStoryDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storyDo) Limit(limit int) IStoryDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storyDo) Offset(offset int) IStoryDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storyDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStoryDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storyDo) Unscoped() IStoryDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storyDo) Create(values ...*model.Story) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storyDo) CreateInBatches(values []*model.Story, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storyDo) Save(values ...*model.Story) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storyDo) First() (*model.Story, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Story), nil
	}
}

func (s storyDo) Take() (*model.Story, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Story), nil
	}
}

func (s storyDo) Last() (*model.Story, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Story), nil
	}
}

func (s storyDo) Find() ([]*model.Story, error) {
	result, err := s.DO.Find()
	return result.([]*model.Story), err
}

func (s storyDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Story, err error) {
	buf := make([]*model.Story, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storyDo) FindInBatches(result *[]*model.Story, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storyDo) Attrs(attrs ...field.AssignExpr) IStoryDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storyDo) Assign(attrs ...field.AssignExpr) IStoryDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storyDo) Joins(fields ...field.RelationField) IStoryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storyDo) Preload(fields ...field.RelationField) IStoryDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storyDo) FirstOrInit() (*model.Story, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Story), nil
	}
}

func (s storyDo) FirstOrCreate() (*model.Story, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Story), nil
	}
}

func (s storyDo) FindByPage(offset int, limit int) (result []*model.Story, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storyDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storyDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storyDo) Delete(models ...*model.Story) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storyDo) withDO(do gen.Dao) *storyDo {
	s.DO = *do.(*gen.DO)
	return s
}
