// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/story/dal/model"
)

func newStoryScene(db *gorm.DB, opts ...gen.DOOption) storyScene {
	_storyScene := storyScene{}

	_storyScene.storySceneDo.UseDB(db, opts...)
	_storyScene.storySceneDo.UseModel(&model.StoryScene{})

	tableName := _storyScene.storySceneDo.TableName()
	_storyScene.ALL = field.NewAsterisk(tableName)
	_storyScene.ID = field.NewInt64(tableName, "id")
	_storyScene.Name = field.NewString(tableName, "name")
	_storyScene.CoverURL = field.NewString(tableName, "cover_url")
	_storyScene.Content = field.NewString(tableName, "content")
	_storyScene.CreatedBy = field.NewString(tableName, "created_by")
	_storyScene.CreatedAt = field.NewTime(tableName, "created_at")
	_storyScene.UpdatedBy = field.NewString(tableName, "updated_by")
	_storyScene.UpdatedAt = field.NewTime(tableName, "updated_at")
	_storyScene.IsDel = field.NewField(tableName, "is_del")

	_storyScene.fillFieldMap()

	return _storyScene
}

// storyScene 故事玩法场景表
type storyScene struct {
	storySceneDo

	ALL       field.Asterisk
	ID        field.Int64  // 主键ID
	Name      field.String // 名称
	CoverURL  field.String // 封面图
	Content   field.String // 介绍
	CreatedBy field.String // 创建人
	CreatedAt field.Time   // 创建时间
	UpdatedBy field.String // 更新人
	UpdatedAt field.Time   // 更新时间
	IsDel     field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s storyScene) Table(newTableName string) *storyScene {
	s.storySceneDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s storyScene) As(alias string) *storyScene {
	s.storySceneDo.DO = *(s.storySceneDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *storyScene) updateTableName(table string) *storyScene {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.Name = field.NewString(table, "name")
	s.CoverURL = field.NewString(table, "cover_url")
	s.Content = field.NewString(table, "content")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *storyScene) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *storyScene) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 9)
	s.fieldMap["id"] = s.ID
	s.fieldMap["name"] = s.Name
	s.fieldMap["cover_url"] = s.CoverURL
	s.fieldMap["content"] = s.Content
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s storyScene) clone(db *gorm.DB) storyScene {
	s.storySceneDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s storyScene) replaceDB(db *gorm.DB) storyScene {
	s.storySceneDo.ReplaceDB(db)
	return s
}

type storySceneDo struct{ gen.DO }

type IStorySceneDo interface {
	gen.SubQuery
	Debug() IStorySceneDo
	WithContext(ctx context.Context) IStorySceneDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IStorySceneDo
	WriteDB() IStorySceneDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IStorySceneDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IStorySceneDo
	Not(conds ...gen.Condition) IStorySceneDo
	Or(conds ...gen.Condition) IStorySceneDo
	Select(conds ...field.Expr) IStorySceneDo
	Where(conds ...gen.Condition) IStorySceneDo
	Order(conds ...field.Expr) IStorySceneDo
	Distinct(cols ...field.Expr) IStorySceneDo
	Omit(cols ...field.Expr) IStorySceneDo
	Join(table schema.Tabler, on ...field.Expr) IStorySceneDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IStorySceneDo
	RightJoin(table schema.Tabler, on ...field.Expr) IStorySceneDo
	Group(cols ...field.Expr) IStorySceneDo
	Having(conds ...gen.Condition) IStorySceneDo
	Limit(limit int) IStorySceneDo
	Offset(offset int) IStorySceneDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IStorySceneDo
	Unscoped() IStorySceneDo
	Create(values ...*model.StoryScene) error
	CreateInBatches(values []*model.StoryScene, batchSize int) error
	Save(values ...*model.StoryScene) error
	First() (*model.StoryScene, error)
	Take() (*model.StoryScene, error)
	Last() (*model.StoryScene, error)
	Find() ([]*model.StoryScene, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryScene, err error)
	FindInBatches(result *[]*model.StoryScene, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.StoryScene) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IStorySceneDo
	Assign(attrs ...field.AssignExpr) IStorySceneDo
	Joins(fields ...field.RelationField) IStorySceneDo
	Preload(fields ...field.RelationField) IStorySceneDo
	FirstOrInit() (*model.StoryScene, error)
	FirstOrCreate() (*model.StoryScene, error)
	FindByPage(offset int, limit int) (result []*model.StoryScene, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IStorySceneDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s storySceneDo) Debug() IStorySceneDo {
	return s.withDO(s.DO.Debug())
}

func (s storySceneDo) WithContext(ctx context.Context) IStorySceneDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s storySceneDo) ReadDB() IStorySceneDo {
	return s.Clauses(dbresolver.Read)
}

func (s storySceneDo) WriteDB() IStorySceneDo {
	return s.Clauses(dbresolver.Write)
}

func (s storySceneDo) Session(config *gorm.Session) IStorySceneDo {
	return s.withDO(s.DO.Session(config))
}

func (s storySceneDo) Clauses(conds ...clause.Expression) IStorySceneDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s storySceneDo) Returning(value interface{}, columns ...string) IStorySceneDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s storySceneDo) Not(conds ...gen.Condition) IStorySceneDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s storySceneDo) Or(conds ...gen.Condition) IStorySceneDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s storySceneDo) Select(conds ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s storySceneDo) Where(conds ...gen.Condition) IStorySceneDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s storySceneDo) Order(conds ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s storySceneDo) Distinct(cols ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s storySceneDo) Omit(cols ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s storySceneDo) Join(table schema.Tabler, on ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s storySceneDo) LeftJoin(table schema.Tabler, on ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s storySceneDo) RightJoin(table schema.Tabler, on ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s storySceneDo) Group(cols ...field.Expr) IStorySceneDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s storySceneDo) Having(conds ...gen.Condition) IStorySceneDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s storySceneDo) Limit(limit int) IStorySceneDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s storySceneDo) Offset(offset int) IStorySceneDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s storySceneDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IStorySceneDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s storySceneDo) Unscoped() IStorySceneDo {
	return s.withDO(s.DO.Unscoped())
}

func (s storySceneDo) Create(values ...*model.StoryScene) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s storySceneDo) CreateInBatches(values []*model.StoryScene, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s storySceneDo) Save(values ...*model.StoryScene) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s storySceneDo) First() (*model.StoryScene, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryScene), nil
	}
}

func (s storySceneDo) Take() (*model.StoryScene, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryScene), nil
	}
}

func (s storySceneDo) Last() (*model.StoryScene, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryScene), nil
	}
}

func (s storySceneDo) Find() ([]*model.StoryScene, error) {
	result, err := s.DO.Find()
	return result.([]*model.StoryScene), err
}

func (s storySceneDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StoryScene, err error) {
	buf := make([]*model.StoryScene, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s storySceneDo) FindInBatches(result *[]*model.StoryScene, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s storySceneDo) Attrs(attrs ...field.AssignExpr) IStorySceneDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s storySceneDo) Assign(attrs ...field.AssignExpr) IStorySceneDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s storySceneDo) Joins(fields ...field.RelationField) IStorySceneDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s storySceneDo) Preload(fields ...field.RelationField) IStorySceneDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s storySceneDo) FirstOrInit() (*model.StoryScene, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryScene), nil
	}
}

func (s storySceneDo) FirstOrCreate() (*model.StoryScene, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StoryScene), nil
	}
}

func (s storySceneDo) FindByPage(offset int, limit int) (result []*model.StoryScene, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s storySceneDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s storySceneDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s storySceneDo) Delete(models ...*model.StoryScene) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *storySceneDo) withDO(do gen.Dao) *storySceneDo {
	s.DO = *do.(*gen.DO)
	return s
}
