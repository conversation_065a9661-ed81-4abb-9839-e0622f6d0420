// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                     = new(Query)
	Story                 *story
	StoryLog              *storyLog
	StoryMaterials        *storyMaterials
	StoryMaterialsRelease *storyMaterialsRelease
	StoryOrder            *storyOrder
	StoryOrderDetail      *storyOrderDetail
	StoryScene            *storyScene
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Story = &Q.Story
	StoryLog = &Q.StoryLog
	StoryMaterials = &Q.StoryMaterials
	StoryMaterialsRelease = &Q.StoryMaterialsRelease
	StoryOrder = &Q.StoryOrder
	StoryOrderDetail = &Q.StoryOrderDetail
	StoryScene = &Q.StoryScene
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                    db,
		Story:                 newStory(db, opts...),
		StoryLog:              newStoryLog(db, opts...),
		StoryMaterials:        newStoryMaterials(db, opts...),
		StoryMaterialsRelease: newStoryMaterialsRelease(db, opts...),
		StoryOrder:            newStoryOrder(db, opts...),
		StoryOrderDetail:      newStoryOrderDetail(db, opts...),
		StoryScene:            newStoryScene(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Story                 story
	StoryLog              storyLog
	StoryMaterials        storyMaterials
	StoryMaterialsRelease storyMaterialsRelease
	StoryOrder            storyOrder
	StoryOrderDetail      storyOrderDetail
	StoryScene            storyScene
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		Story:                 q.Story.clone(db),
		StoryLog:              q.StoryLog.clone(db),
		StoryMaterials:        q.StoryMaterials.clone(db),
		StoryMaterialsRelease: q.StoryMaterialsRelease.clone(db),
		StoryOrder:            q.StoryOrder.clone(db),
		StoryOrderDetail:      q.StoryOrderDetail.clone(db),
		StoryScene:            q.StoryScene.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                    db,
		Story:                 q.Story.replaceDB(db),
		StoryLog:              q.StoryLog.replaceDB(db),
		StoryMaterials:        q.StoryMaterials.replaceDB(db),
		StoryMaterialsRelease: q.StoryMaterialsRelease.replaceDB(db),
		StoryOrder:            q.StoryOrder.replaceDB(db),
		StoryOrderDetail:      q.StoryOrderDetail.replaceDB(db),
		StoryScene:            q.StoryScene.replaceDB(db),
	}
}

type queryCtx struct {
	Story                 IStoryDo
	StoryLog              IStoryLogDo
	StoryMaterials        IStoryMaterialsDo
	StoryMaterialsRelease IStoryMaterialsReleaseDo
	StoryOrder            IStoryOrderDo
	StoryOrderDetail      IStoryOrderDetailDo
	StoryScene            IStorySceneDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Story:                 q.Story.WithContext(ctx),
		StoryLog:              q.StoryLog.WithContext(ctx),
		StoryMaterials:        q.StoryMaterials.WithContext(ctx),
		StoryMaterialsRelease: q.StoryMaterialsRelease.WithContext(ctx),
		StoryOrder:            q.StoryOrder.WithContext(ctx),
		StoryOrderDetail:      q.StoryOrderDetail.WithContext(ctx),
		StoryScene:            q.StoryScene.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
