// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameStoryScene = "story_scene"

// StoryScene 故事玩法场景表
type StoryScene struct {
	ID        int64                 `gorm:"column:id;type:bigint(20) unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"id"`                 // 主键ID
	Name      string                `gorm:"column:name;type:varchar(50);not null;comment:名称" json:"name"`                                            // 名称
	CoverURL  string                `gorm:"column:cover_url;type:varchar(255);comment:封面图" json:"cover_url"`                                         // 封面图
	Content   string                `gorm:"column:content;type:text;not null;comment:介绍" json:"content"`                                             // 介绍
	CreatedBy string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel     soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName StoryScene's table name
func (*StoryScene) TableName() string {
	return TableNameStoryScene
}
