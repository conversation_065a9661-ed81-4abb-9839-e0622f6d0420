// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameStoryOrder = "story_order"

// StoryOrder 故事玩法订单表
type StoryOrder struct {
	ID               int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                                                                // id
	OrderID          string                `gorm:"column:order_id;type:varchar(64);not null;comment:订单号" json:"order_id"`                                                                       // 订单号
	SceneID          int64                 `gorm:"column:scene_id;type:bigint(20);not null;comment:故事玩法场景id(scene.id)" json:"scene_id"`                                                         // 故事玩法场景id(scene.id)
	SceneName        string                `gorm:"column:scene_name;type:varchar(64);not null;comment:故事玩法场景名称(scene.name)" json:"scene_name"`                                                  // 故事玩法场景名称(scene.name)
	StoryID          int64                 `gorm:"column:story_id;type:bigint(20);not null;comment:故事玩法活动id(story.id)" json:"story_id"`                                                         // 故事玩法活动id(story.id)
	StoryTitle       string                `gorm:"column:story_title;type:varchar(64);not null;comment:故事玩法活动名称(story.title)" json:"story_title"`                                               // 故事玩法活动名称(story.title)
	ItemID           string                `gorm:"column:item_id;type:varchar(64);not null;comment:故事玩法物品id(商品/故事玩法)" json:"item_id"`                                                           // 故事玩法物品id(商品/故事玩法)
	ItemTitle        string                `gorm:"column:item_title;type:varchar(64);not null;comment:故事玩法物品名称(商品/故事玩法)" json:"item_title"`                                                     // 故事玩法物品名称(商品/故事玩法)
	ItemCoverURL     string                `gorm:"column:item_cover_url;type:varchar(255);not null;comment:故事玩法物品图片，没有则为空" json:"item_cover_url"`                                               // 故事玩法物品图片，没有则为空
	ItemInfo         datatypes.JSON        `gorm:"column:item_info;type:json;not null;comment:故事玩法物品信息" json:"item_info"`                                                                       // 故事玩法物品信息
	UserID           string                `gorm:"column:user_id;type:varchar(24);not null;comment:用户id" json:"user_id"`                                                                        // 用户id
	Status           int32                 `gorm:"column:status;type:tinyint(4);default:1;comment:状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);32:故事玩法探索结束(发起领奖);91:已完成】" json:"status"` // 状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);32:故事玩法探索结束(发起领奖);91:已完成】
	Qty              int32                 `gorm:"column:qty;type:int(11);not null;comment:故事玩法数量" json:"qty"`                                                                                  // 故事玩法数量
	AppChannel       string                `gorm:"column:app_channel;type:varchar(20);comment:设备渠道" json:"app_channel"`                                                                         // 设备渠道
	AppVersion       string                `gorm:"column:app_version;type:varchar(20);comment:设备版本号" json:"app_version"`                                                                        // 设备版本号
	IP               string                `gorm:"column:ip;type:varchar(255);comment:ip" json:"ip"`                                                                                            // ip
	CompleteTime     *time.Time            `gorm:"column:complete_time;type:datetime(3);comment:探索结束时间" json:"complete_time"`                                                                   // 探索结束时间
	FinishTime       *time.Time            `gorm:"column:finish_time;type:datetime(3);comment:完成时间" json:"finish_time"`                                                                         // 完成时间
	ChainHash        string                `gorm:"column:chain_hash;type:varchar(255);comment:链hash" json:"chain_hash"`                                                                         // 链hash
	ChainDataID      string                `gorm:"column:chain_data_id;type:varchar(255);comment:链data_id" json:"chain_data_id"`                                                                // 链data_id
	ChainStatus      int32                 `gorm:"column:chain_status;type:tinyint(4);comment:链状态【0:未上链;1:已上链;2:上链中;3:上链失败】" json:"chain_status"`                                               // 链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	CreatedBy        string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                                                            // 创建人
	CreatedAt        time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                                     // 创建时间
	UpdatedBy        string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                                                            // 更新人
	UpdatedAt        time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                                     // 更新时间
	IsDel            soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                                            // 是否删除【0->未删除; 1->删除】
	StoryOrderDetail []StoryOrderDetail    `gorm:"foreignKey:StoryOrderID" json:"story_order_detail"`
	Story            Story                 `gorm:"foreignKey:StoryID" json:"story"`
}

// TableName StoryOrder's table name
func (*StoryOrder) TableName() string {
	return TableNameStoryOrder
}
