// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameStory = "story"

// Story 故事玩法活动表
type Story struct {
	ID                    int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                            // id
	SceneID               int64                 `gorm:"column:scene_id;type:bigint(20);not null;comment:故事玩法场景id(story_scene.id)" json:"scene_id"`               // 故事玩法场景id(story_scene.id)
	ActivityCode          string                `gorm:"column:activity_code;type:varchar(64);not null;comment:活动编码" json:"activity_code"`                        // 活动编码
	Title                 string                `gorm:"column:title;type:varchar(64);comment:活动名称" json:"title"`                                                 // 活动名称
	ActivityType          int32                 `gorm:"column:activity_type;type:tinyint(4);default:1;comment:故事玩法类型【1-组合商品；2-商品】" json:"activity_type"`         // 故事玩法类型【1-组合商品；2-商品】
	CoverURL              string                `gorm:"column:cover_url;type:varchar(255);comment:封面图" json:"cover_url"`                                         // 封面图
	Status                int32                 `gorm:"column:status;type:tinyint(4);default:1;comment:状态【-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束】" json:"status"`        // 状态【-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束】
	ItemID                string                `gorm:"column:item_id;type:varchar(64);not null;comment:故事玩法物品id(商品/组合商品)" json:"item_id"`                       // 故事玩法物品id(商品/组合商品)
	ItemTitle             string                `gorm:"column:item_title;type:varchar(64);not null;comment:故事玩法物品名称(商品/组合商品)" json:"item_title"`                 // 故事玩法物品名称(商品/组合商品)
	ItemImageURL          string                `gorm:"column:item_image_url;type:varchar(255);comment:故事玩法物品图片" json:"item_image_url"`                          // 故事玩法物品图片
	UserLimit             int32                 `gorm:"column:user_limit;type:int(11);not null;comment:每人限合(0不限制)" json:"user_limit"`                            // 每人限合(0不限制)
	CriticalMaterialLimit int32                 `gorm:"column:critical_material_limit;type:int(11);not null;comment:关键材料最小值" json:"critical_material_limit"`     // 关键材料最小值
	CompleteUserNum       int32                 `gorm:"column:complete_user_num;type:int(11);comment:已合人数(按用户去重)" json:"complete_user_num"`                      // 已合人数(按用户去重)
	Stock                 int32                 `gorm:"column:stock;type:int(11);not null;comment:剩余库存" json:"stock"`                                            // 剩余库存
	TotalStock            int32                 `gorm:"column:total_stock;type:int(11);not null;comment:总库存" json:"total_stock"`                                 // 总库存
	StockDisplay          int32                 `gorm:"column:stock_display;type:int(11);default:1;comment:剩余库存是否显示【1:显示;2:不显示】" json:"stock_display"`           // 剩余库存是否显示【1:显示;2:不显示】
	StartTime             time.Time             `gorm:"column:start_time;type:datetime(3);not null;comment:开始时间" json:"start_time"`                              // 开始时间
	EndTime               time.Time             `gorm:"column:end_time;type:datetime(3);not null;comment:结束时间" json:"end_time"`                                  // 结束时间
	ActivityDesc          string                `gorm:"column:activity_desc;type:text;comment:说明" json:"activity_desc"`                                          // 说明
	ReleaseTime           *datatypes.JSON       `gorm:"column:release_time;type:json;comment:释放时间" json:"release_time"`                                          // 释放时间
	CreatedBy             string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt             time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy             string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt             time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel                 soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName Story's table name
func (*Story) TableName() string {
	return TableNameStory
}
