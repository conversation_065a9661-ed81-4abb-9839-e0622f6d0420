package define

type (
	GetItemStoryIsUpReq struct {
		ItemId string `form:"item_id" json:"item_id" binding:"required"` // 物品id
	}

	GetItemStoryIsUpResp struct {
		HasUp int32 `json:"has_up"` // 0:没有上架 1:有上架
	}
)

type (
	StoryFinishReq struct {
	}

	StoryFinishResp struct {
	}
)

type (
	StoryOrderUpChainReq struct {
		OrderId string `form:"order_id" json:"order_id"`
	}

	StoryOrderUpChainResp struct {
	}
)

type (
	StoryMaterialsReleaseReq struct {
		Id int64 `form:"id" json:"id"`
	}

	StoryMaterialsReleaseResp struct {
	}
)

type (
	StoryMaterialsGetReleaseTimeReq struct {
		UserItemIds []string `form:"user_item_id" json:"user_item_id"`
	}

	StoryMaterialsGetReleaseTimeResp struct {
		List []*StoryMaterialsGetReleaseTimeData `json:"list"`
	}

	StoryMaterialsGetReleaseTimeData struct {
		UserItemId  string `json:"user_item_id"`
		ReleaseTime int64  `json:"release_time"`
	}
)

type (
	StoryOrderCompleteReq struct {
	}

	StoryOrderCompleteResp struct {
	}
)
