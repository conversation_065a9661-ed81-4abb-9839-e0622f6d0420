package enums

type StoryStatus int32

func (s StoryStatus) Val() int32 {
	return int32(s)
}

const (
	// StoryStatusDel 已删除
	StoryStatusDel StoryStatus = -1
	// StoryStatusWaiting 待上架
	StoryStatusWaiting StoryStatus = 1
	// StoryStatusUp 已上架
	StoryStatusUp StoryStatus = 2
	// StoryStatusDown 已下架
	StoryStatusDown StoryStatus = 3
	// StoryStatusExpires 已结束
	StoryStatusExpires StoryStatus = 4

	// StoryStatusWait 即将开始
	StoryStatusWait StoryStatus = 20
	// StoryStatusIng 进行中
	StoryStatusIng StoryStatus = 21
)
