package enums

type StoryChainStatus int32

func (s StoryChainStatus) Val() int32 {
	return int32(s)
}

const (
	// StoryChainStatusDisable 未上链
	StoryChainStatusDisable StoryChainStatus = 0
	// StoryChainStatusDone 已上链
	StoryChainStatusDone StoryChainStatus = 1
	// StoryChainStatusWaiting 上链中
	StoryChainStatusWaiting StoryStatus = 2
	// StoryChainStatusFailed 上链失败
	StoryChainStatusFailed StoryStatus = 3
)
