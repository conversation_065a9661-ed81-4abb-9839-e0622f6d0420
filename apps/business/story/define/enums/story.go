package enums

// LogAction 日志操作类型
type LogAction int32

func (r LogAction) Val() int32 {
	return int32(r)
}

const (
	// LogActionCreate 创建
	LogActionCreate LogAction = 1
	// LogActionUpdate 更新
	LogActionUpdate LogAction = 2
	// LogActionDelete 删除
	LogActionDelete LogAction = 3
	// LogActionUp 上架
	LogActionUp LogAction = 4
	// LogActionDown 下架
	LogActionDown LogAction = 5
)

// StoryRuleConfigKey 故事玩法规则说明配置key
const StoryRuleConfigKey = "story.rule"

// StoryOrderStatus 订单状态
type StoryOrderStatus int32

func (r StoryOrderStatus) Val() int32 {
	return int32(r)
}

const (
	// StoryOrderStatusFail 探索失败
	StoryOrderStatusFail StoryOrderStatus = -1
	// StoryOrderStatusWaiting 待探索
	StoryOrderStatusWaiting StoryOrderStatus = 1
	// StoryOrderStatusSuccess 故事玩法探索成功 探索中
	StoryOrderStatusSuccess StoryOrderStatus = 21
	// StoryOrderStatusFinish 故事玩法探索结束
	StoryOrderStatusFinish StoryOrderStatus = 31
	// StoryOrderStatusReceive 故事玩法探索结束(发起领奖)
	StoryOrderStatusReceive StoryOrderStatus = 32
	// StoryOrderStatusDone 已完成
	StoryOrderStatusDone StoryOrderStatus = 91
)

var StoryOrderStatusMap = map[int32]string{
	StoryOrderStatusFail.Val():    "探索失败",
	StoryOrderStatusWaiting.Val(): "待探索",
	StoryOrderStatusSuccess.Val(): "探索中",
	StoryOrderStatusFinish.Val():  "探索结束",
	StoryOrderStatusReceive.Val(): "探索结束(发起领奖)",
	StoryOrderStatusDone.Val():    "已完成",
}

// StoryOrderChainStatus 订单上链状态
type StoryOrderChainStatus int32

func (r StoryOrderChainStatus) Val() int32 {
	return int32(r)
}

const (
	// StoryOrderChainStatusWaiting 未上链
	StoryOrderChainStatusWaiting StoryOrderChainStatus = 0
	// StoryOrderChainStatusSuccess 已上链
	StoryOrderChainStatusSuccess StoryOrderChainStatus = 1
	// StoryOrderChainStatusPending 上链中
	StoryOrderChainStatusPending StoryOrderChainStatus = 2
	// StoryOrderChainStatusFail 上链失败
	StoryOrderChainStatusFail StoryOrderChainStatus = 3
)

var StoryOrderChainStatusMap = map[int32]string{
	StoryOrderChainStatusWaiting.Val(): "未上链",
	StoryOrderChainStatusSuccess.Val(): "上链成功",
	StoryOrderChainStatusPending.Val(): "上链中",
	StoryOrderChainStatusFail.Val():    "上链失败",
}
