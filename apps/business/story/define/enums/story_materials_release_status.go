package enums

type StoryMaterialsReleaseStatus int32

func (s StoryMaterialsReleaseStatus) Val() int32 {
	return int32(s)
}

const (
	// ReleaseFail 释放失败
	ReleaseFail StoryMaterialsReleaseStatus = -1
	// ReleaseSuccess 已释放
	ReleaseSuccess StoryMaterialsReleaseStatus = 1
	// ReleaseCreated 待释放
	ReleaseCreated StoryMaterialsReleaseStatus = 2
	// ReleaseIng 释放中
	ReleaseIng StoryMaterialsReleaseStatus = 3
)
