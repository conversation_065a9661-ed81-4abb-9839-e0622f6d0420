package enums

type StoryMaterialsType int32

func (s StoryMaterialsType) Val() int32 {
	return int32(s)
}

const (
	// StoryMaterialsTypeCore 核心材料
	StoryMaterialsTypeCore StoryMaterialsType = 1
	// StoryMaterialsTypeCrux 关键材料
	StoryMaterialsTypeCrux StoryMaterialsType = 2
)

// StoryMaterialsLimitStatus 物品限制状态
type StoryMaterialsLimitStatus int32

func (s StoryMaterialsLimitStatus) Val() int32 {
	return int32(s)
}

const (
	// StoryMaterialsLimitStatusDestroy 派遣后销毁
	StoryMaterialsLimitStatusDestroy StoryMaterialsLimitStatus = 1
	// StoryMaterialsLimitStatusPick 限制流转，仅可提货
	StoryMaterialsLimitStatusPick StoryMaterialsLimitStatus = 2
	// StoryMaterialsLimitStatusXDay 限制流转x天
	StoryMaterialsLimitStatusXDay StoryMaterialsLimitStatus = 3
)
