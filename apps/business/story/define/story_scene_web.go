package define

import "time"

type (
	GetStorySceneWebListReq struct {
		NeedStoryNum int32 `form:"need_story_num" json:"need_story_num"` //  是否需要活动数量 0:不需要 1:需要
	}

	GetStorySceneWebListData struct {
		ID           int64    `json:"id"`             // 场景ID
		Name         string   `json:"name"`           // 场景名称
		StoryNum     int32    `json:"story_num"`      // 活动数量
		CoverURL     *string  `json:"cover_url"`      // 封面图
		UserNameList []string `json:"user_name_list"` // 用户昵称列表
	}

	GetStorySceneWebLimitFourData struct {
		ID       int64  `json:"id"`        // 场景ID
		Name     string `json:"name"`      // 场景名称
		CoverURL string `json:"cover_url"` // 封面图
	}

	GetStoryNumBySceneData struct {
		SceneId  int64 `json:"scene_id"`  // 场景ID
		StoryNum int32 `json:"story_num"` // 活动数量
	}

	GetStorySceneWebListResp struct {
		List []*GetStorySceneWebListData `json:"list"`
	}
)

type (
	GetStorySceneWebHomeReq struct {
	}

	GetStorySceneWebHomeData struct {
		ID        int64     `json:"id"` // 场景ID
		StartTime time.Time `json:"-"`
		Name      string    `json:"name"`   // 场景名称
		Status    int32     `json:"status"` // 故事玩法状态 20:即将开始;21:进行中
	}

	GetStorySceneWebHomeResp struct {
		List []*GetStorySceneWebHomeData `json:"list"`
	}
)
