package define

type (
	GetStorySceneAdminListReq struct {
		NeedStoryNum int32 `form:"need_story_num" json:"need_story_num"` //  是否需要活动数量 0:不需要 1:需要
	}

	GetStorySceneAdminListData struct {
		ID       int64   `json:"id"`        // 场景ID
		Name     string  `json:"name"`      // 场景名称
		StoryNum int32   `json:"story_num"` // 活动数量
		CoverURL *string `json:"cover_url"` // 封面图
	}

	GetStorySceneAdminListResp struct {
		List []*GetStorySceneAdminListData `json:"list"`
	}
)

type (
	GetStorySceneAdminDetailReq struct {
		ID int64 `form:"id" json:"id" binding:"required"` // 场景ID
	}

	GetStorySceneAdminDetailResp struct {
		ID       int64  `json:"id"`        // 场景ID
		Name     string `json:"name"`      // 场景名称
		Content  string `json:"content"`   // 场景介绍
		CoverURL string `json:"cover_url"` // 封面图
	}
)

type (
	AddStorySceneReq struct {
		Name     string `json:"name" binding:"required"`      // 场景名称
		Content  string `json:"content" binding:"required"`   // 场景介绍
		CoverURL string `json:"cover_url" binding:"required"` // 封面图
	}

	AddStorySceneResp struct {
		ID int64 `json:"id"` // 场景ID
	}
)

type (
	EditStorySceneReq struct {
		ID       int64  `form:"id" json:"id" binding:"required"` // 场景ID
		Name     string `json:"name" binding:"required"`         // 场景名称
		Content  string `json:"content" binding:"required"`      // 场景介绍
		CoverURL string `json:"cover_url" binding:"required"`    // 封面图
	}

	EditStorySceneResp struct {
		ID int64 `json:"id"` // 场景ID
	}
)
