package define

import (
	"encoding/json"
	"time"

	"app_service/pkg/pagination"
)

type (
	// GetStoryRuleResp 获取故事玩法规则
	GetStoryRuleResp struct {
		Content string `json:"content"`
	}
)

type (
	GetStoryWebListReq struct {
		pagination.Pagination
		SceneId int64 `form:"scene_id" json:"scene_id" binding:"required"` // 故事玩法场景id
		Status  int32 `form:"status" json:"status"`                        // 故事玩法状态 4:已结束
	}

	GetStoryWebListData struct {
		ActivityCode    string               `json:"activity_code"`     // 活动编码
		Title           string               `json:"title"`             // 活动名称
		ActivityType    int32                `json:"activity_type"`     // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl        string               `json:"cover_url"`         // 封面图
		Status          int32                `json:"status"`            // 故事玩法状态 20:即将开始;21:进行中;4:已结束
		TotalStock      int32                `json:"total_stock"`       // 总库存
		IssuerName      string               `json:"issuer_name"`       // 发行名称
		IssuerShortName string               `json:"issuer_short_name"` // 发行方简称
		SellerName      string               `json:"seller_name"`       // 销售方全称
		SellerShortName string               `json:"seller_short_name"` // 销售方简称
		StartTime       time.Time            `json:"start_time"`        // 开始时间
		EndTime         time.Time            `json:"end_time"`          // 结束时间
		StoryJoinData   *GetStoryJoinWebData `json:"story_join_data"`   // 用户参与数据
	}

	GetStorySceneWebData struct {
		Id       int64  `json:"id"`        // 场景ID
		Name     string `json:"name"`      // 场景名称
		CoverUrl string `json:"cover_url"` // 封面图
		Content  string `json:"content"`   // 介绍
	}

	GetStoryJoinWebData struct {
		Count         *int64    `json:"count"`      // 探索中的次数
		ItemImageUrls *[]string `json:"image_urls"` // 商品图片列表
	}

	GetStoryWebListResp struct {
		List           []*GetStoryWebListData `json:"list"`
		Total          int64                  `json:"total"`
		CurrentTime    string                 `json:"current_time"`     // 服务器当前时间
		StorySceneData *GetStorySceneWebData  `json:"story_scene_data"` // 故事玩法场景数据
	}
)

type (
	GetStoryWebDetailReq struct {
		ActivityCode string `form:"activity_code" json:"activity_code" binding:"required"` // 活动编码
	}

	GetStoryWebDetailResp struct {
		ActivityCode       string                `json:"activity_code"`        // 活动编码
		Title              string                `json:"title"`                // 活动名称
		ActivityType       int32                 `json:"activity_type"`        // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl           string                `json:"cover_url"`            // 封面图
		Status             int32                 `json:"status"`               // 故事玩法状态 20:即将开始;21:进行中;4:已结束
		ItemId             string                `json:"item_id"`              // 故事玩法物品id(商品/组合商品)
		ItemTitle          string                `json:"item_title"`           // 故事玩法物品名称(商品/组合商品)
		ItemImageUrl       string                `json:"item_image_url"`       // 故事玩法物品图片
		UserLimit          int32                 `json:"user_limit"`           // 每人限合(0不限制)
		Stock              *int32                `json:"stock"`                // 剩余库存
		TotalStock         int32                 `json:"total_stock"`          // 总库存
		StartTime          time.Time             `json:"start_time"`           // 开始时间
		EndTime            time.Time             `json:"end_time"`             // 结束时间
		ActivityDesc       string                `json:"activity_desc"`        // 说明
		CurrentTime        string                `json:"current_time"`         // 服务器当前时间
		UserStoryMaterials []*UserStoryMaterials `json:"user_story_materials"` //用户故事玩法材料
		MaxLimit           int32                 `json:"max_limit"`            // 一次限合最大数量
	}

	UserStoryMaterials struct {
		Id                      int64                     `json:"id"`                   // 故事玩法材料id
		MaterialsType           int32                     `json:"materials_type"`       // 材料类型【1-核心材料；2-关键材料】
		Qty                     int32                     `json:"qty"`                  // 集齐数量
		UserStoryMaterialsDatas []*UserStoryMaterialsData `json:"story_materials_data"` //故事玩法材料
	}

	UserStoryMaterialsData struct {
		ItemId            string                       `json:"item_id"`            // 商品id
		ItemTitle         string                       `json:"item_title"`         // 商品名称
		ItemImageUrl      string                       `json:"image_url"`          // 商品图片
		Qty               int32                        `json:"qty"`                // 集齐数量
		UserQty           int32                        `json:"user_qty"`           // 用户持有数量
		CirculationStatus int32                        `json:"circulation_status"` // 流通状态1:不限流通;2:禁止流通
		Loop              int32                        `json:"-"`                  // 二次流转【1-开启；2-关闭】
		Destroy           int32                        `json:"-"`                  // 材料消耗【1-销毁；2-不销毁】
		Limit             *UserStoryMaterialsLimitData `json:"limit"`              // 物品限制信息
	}

	UserStoryMaterialsLimitData struct {
		Status int32  `json:"status"` // 限制状态【1-派遣后销毁;2-限制流转，仅可提货;3-限制流转x天】
		Day    *int32 `json:"day"`    // x天
	}
)

type (
	LaunchStoryReq struct {
		ActivityCode         string                  `json:"activity_code" binding:"required"`   // 活动编码
		Qty                  int32                   `json:"qty" binding:"required,min=1"`       // 故事玩法数量
		LaunchStoryMaterials []*LaunchStoryMaterials `json:"story_materials" binding:"required"` // 选中故事玩法材料
	}

	LaunchStoryMaterials struct {
		Id      int64    `json:"id"`      // 故事玩法材料id
		ItemIds []string `json:"itemIds"` // 故事玩法材料物品id集合
	}

	LaunchStoryResp struct {
		ItemTitle    string `json:"item_title"`     // 故事玩法物品名称
		ItemId       string `json:"item_id"`        // 故事玩法物品id
		ItemCoverUrl string `json:"item_cover_url"` // 故事玩法物品图片
		Qty          int32  `json:"qty"`            // 故事玩法数量
		OrderId      string `json:"order_id"`       // 订单号
	}
)

type (
	StoryDiscoveryReq struct {
		ActivityCode   string                        `json:"activity_code" binding:"required"`   // 活动编码
		Qty            int32                         `json:"qty" binding:"required,min=1"`       // 故事玩法数量
		StoryMaterials []*SelectedStoryMaterialsData `json:"story_materials" binding:"required"` // 选中故事玩法材料
	}

	SelectedStoryMaterialsData struct {
		Id                int64                          `json:"id"`                  // 故事玩法材料id
		MaterialsItemInfo []*StoryDiscoveryUserMaterials `json:"materials_item_info"` // 故事玩法用户选的物品id集合
	}

	StoryDiscoveryUserMaterials struct {
		ItemId         string   `json:"item_id"`                                  // 物品id
		UserItemIds    []string `json:"user_item_ids"`                            // 故事玩法材料物品id集合
		SelectedAll    int32    `json:"selected_all"`                             // 是否全选 【1-全选；2-不选】
		CostPriceOrder int32    `form:"cost_price_order" json:"cost_price_order"` // 成本价排序顺序 1:升序 -1:降序
	}

	StoryDiscoveryResp struct {
		ItemTitle    string    `json:"item_title"`     // 故事玩法物品名称
		ItemId       string    `json:"item_id"`        // 故事玩法物品id
		ItemCoverUrl string    `json:"item_cover_url"` // 故事玩法物品图片
		Qty          int32     `json:"qty"`            // 故事玩法数量
		EndTime      time.Time `json:"end_time"`       // 探索结束时间
		OrderId      string    `json:"order_id"`       // 订单号
	}
)

// UnmarshalJSON 实现自定义UnmarshalJSON
func (r *StoryDiscoveryReq) UnmarshalJSON(data []byte) error {
	// 定义临时类型避免递归调用
	type Alias StoryDiscoveryReq
	tmp := &struct {
		*Alias
	}{
		Alias: (*Alias)(r),
	}

	// 先解析原始数据
	if err := json.Unmarshal(data, tmp); err != nil {
		return err
	}

	// 对StoryMaterials进行去重处理
	seenMaterials := make(map[int64]bool)
	uniqueMaterials := make([]*SelectedStoryMaterialsData, 0, len(r.StoryMaterials))

	for _, material := range r.StoryMaterials {
		if !seenMaterials[material.Id] {
			seenMaterials[material.Id] = true

			// 对MaterialsItemInfo去重
			seenItems := make(map[string]bool)
			uniqueItems := make([]*StoryDiscoveryUserMaterials, 0, len(material.MaterialsItemInfo))

			for _, item := range material.MaterialsItemInfo {
				if !seenItems[item.ItemId] {
					seenItems[item.ItemId] = true

					// 对UserItemIds去重
					seenUserItems := make(map[string]bool)
					uniqueUserItems := make([]string, 0, len(item.UserItemIds))
					for _, userItem := range item.UserItemIds {
						if !seenUserItems[userItem] {
							seenUserItems[userItem] = true
							uniqueUserItems = append(uniqueUserItems, userItem)
						}
					}
					item.UserItemIds = uniqueUserItems

					uniqueItems = append(uniqueItems, item)
				}
			}
			material.MaterialsItemInfo = uniqueItems

			uniqueMaterials = append(uniqueMaterials, material)
		}
	}
	r.StoryMaterials = uniqueMaterials
	return nil
}

type (
	GetStoryWebOrderListReq struct {
		pagination.Pagination
		SceneId *int64 `form:"scene_id" json:"scene_id"` // 故事玩法场景id
		Status  *int32 `form:"status" json:"status"`     // 订单状态【21:探索中(云仓材料消耗成功);91:已完成】
	}
	GetStoryOrderListData struct {
		OrderID      string    `json:"order_id"`             // 故事玩法订单编号
		ActivityType int32     `json:"activity_type"`        // 故事玩法类型【1-组合商品；2-商品】
		ItemTitle    string    `json:"item_title"`           // 故事玩法物品名称(商品/组合商品)
		ItemCoverURL string    `json:"item_cover_url"`       // 故事玩法物品图片(商品/组合商品)
		Qty          int32     `json:"qty"`                  // 集齐数量
		SceneName    string    `json:"scene_name"`           // 场景名称
		Status       int32     `form:"status" json:"status"` // 订单状态【21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
		CreatedAt    time.Time `json:"created_at"`           // 派遣时间
		EndTime      time.Time `json:"end_time"`             // 结束时间
		Title        string    `json:"title"`                // 活动名称
		CoverUrl     string    `json:"cover_url"`            // 封面图
	}
	GetStoryWebOrderListResp struct {
		List  []*GetStoryOrderListData `json:"list"`
		Total int64                    `json:"total"`
	}
)

type (
	GetStoryWebOrderDetailReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 故事玩法订单编号
	}
	GetStoryWebOrderDetailResp struct {
		OrderID      string                          `json:"order_id"`             // 故事玩法订单编号
		ActivityType int32                           `json:"activity_type"`        // 故事玩法类型【1-组合商品；2-商品】
		ItemTitle    string                          `json:"item_title"`           // 故事玩法物品名称(商品/组合商品)
		ItemCoverURL string                          `json:"item_cover_url"`       // 故事玩法物品图片(商品/组合商品)
		Qty          int32                           `json:"qty"`                  // 集齐数量
		CreatedAt    time.Time                       `json:"created_at"`           // 派遣时间
		ChainHash    string                          `json:"chain_hash"`           // 故事玩法链上hash
		Materials    []*GetStoryOrderDetailMaterials `json:"materials"`            // 故事玩法材料
		Status       int32                           `form:"status" json:"status"` // 订单状态【21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
		EndTime      time.Time                       `json:"end_time"`             // 结束时间
		CoverUrl     string                          `json:"cover_url"`            // 封面图
	}
	GetStoryOrderDetailMaterials struct {
		ItemName string `json:"item_name"` // 故事玩法材料名称
		ItemUrl  string `json:"item_url"`  // 故事玩法材料图片
		Qty      int32  `json:"qty"`       // 故事玩法材料数量
	}
)

type (
	StoryCancelFusion struct {
		StoryOrderID int64 `json:"id"` // 订单主键
	}

	StoryFusion struct {
		StoryOrderID int64 `json:"id"` // 订单主键
	}

	StoryItemIssue struct {
		StoryOrderID int64 `json:"id"` // 订单主键
	}
)

type (
	GetStoryWebOrderStatusReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 故事玩法订单编号
	}

	GetStoryWebOrderStatusResp struct {
		ItemTitle    string `json:"item_title"`     // 故事玩法物品名称
		ItemId       string `json:"item_id"`        // 故事玩法物品id
		ItemCoverUrl string `json:"item_cover_url"` // 故事玩法物品图片
		Qty          int32  `json:"qty"`            // 故事玩法数量
		Status       int32  `json:"status"`         // 状态【-1:失败;21:成功】
	}
)

type (
	ReceiveStoryReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 故事玩法订单编号
	}

	ReceiveStoryResp struct {
		ItemTitle    string `json:"item_title"`     // 故事玩法物品名称(商品/组合商品)
		ItemCoverURL string `json:"item_cover_url"` // 故事玩法物品图片(商品/组合商品)
		Qty          int32  `json:"qty"`            // 集齐数量
	}
)

type (
	GetStoryUserMaterialsListReq struct {
		pagination.Pagination
		ItemId         string `form:"item_id" json:"item_id" binding:"required"` // 物品id
		CostPriceOrder int32  `form:"cost_price_order" json:"cost_price_order"`  // 成本价排序顺序 1:升序 -1:降序
	}

	GetStoryUserMaterialsListResp struct {
		List  []*GetStoryUserMaterialsListData `json:"list"`
		Total int64                            `json:"total"`
	}

	GetStoryUserMaterialsListData struct {
		UserItemId   string `json:"user_item_id"`   // 用户物品id
		ItemId       string `json:"item_id"`        // 故事玩法物品id
		ItemTitle    string `json:"item_title"`     // 故事玩法物品名称
		ItemCoverUrl string `json:"item_cover_url"` // 故事玩法物品图片
		CostPrice    int64  `json:"cost_price"`     // 持仓成本
		StoryTags    int32  `json:"story_tags"`     // 故事玩法标签【0:否;1:是】
		FusionTags   int32  `json:"fusion_tags"`    // 融合标签【0:否;1:是】
	}
)
