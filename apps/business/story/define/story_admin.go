package define

import (
	"time"

	"app_service/pkg/pagination"
)

type (
	GetStoryAdminListReq struct {
		pagination.Pagination
		ID             *int64    `form:"id" json:"id"`                             // 故事玩法ID
		Title          string    `form:"title" json:"title"`                       // 活动名称
		ItemId         string    `form:"item_id" json:"item_id"`                   // 故事玩法物品id(商品/组合商品)
		ItemTitle      string    `form:"item_title" json:"item_title"`             // 故事玩法物品名称(商品/组合商品)
		SceneId        *int64    `form:"scene_id" json:"scene_id"`                 // 故事玩法场景id
		Status         *int32    `form:"status" json:"status"`                     // 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		CreatedBy      string    `form:"created_by" json:"created_by"`             // 创建人
		CreatedAtStart time.Time `form:"created_at_start" json:"created_at_start"` // 创建时间开始
		CreatedAtEnd   time.Time `form:"created_at_end" json:"created_at_end"`     // 创建时间结束
		StartTimeStart time.Time `form:"start_time_start" json:"start_time_start"` // 开始时间开始
		StartTimeEnd   time.Time `form:"start_time_end" json:"start_time_end"`     // 开始时间结束
	}

	GetStoryAdminListData struct {
		ID              int64     `json:"id"`                // 故事玩法ID
		ActivityCode    string    `json:"activity_code"`     // 活动编码
		Title           string    `json:"title"`             // 活动名称
		ActivityType    int32     `json:"activity_type"`     // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl        string    `json:"cover_url"`         // 封面图
		Status          int32     `json:"status"`            // 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		ItemId          string    `json:"item_id"`           // 故事玩法物品id(商品/组合商品)
		ItemTitle       string    `json:"item_title"`        // 故事玩法物品名称(商品/组合商品)
		UserLimit       int32     `json:"user_limit"`        // 每人限合(0不限制)
		CompleteUserNum int32     `json:"complete_user_num"` // 已合人数(按用户去重)
		Stock           int32     `json:"stock"`             // 剩余库存
		TotalStock      int32     `json:"total_stock"`       // 总库存
		SceneName       string    `json:"scene_name"`        // 场景名称
		StartTime       time.Time `json:"start_time"`        // 开始时间
		EndTime         time.Time `json:"end_time"`          // 结束时间
		CreatedBy       string    `json:"created_by"`        // 创建人
		CreatedAt       time.Time `json:"created_at"`        // 创建时间
		UpdatedBy       string    `json:"updated_by"`        // 更新人
		UpdatedAt       time.Time `json:"updated_at"`        // 更新时间
	}

	GetStoryAdminListResp struct {
		List  []*GetStoryAdminListData `json:"list"`
		Total int64                    `json:"total"`
	}
)

type (
	GetStoryAdminDetailReq struct {
		ID int64 `form:"id" json:"id" binding:"required"` // 故事玩法ID
	}

	GetStoryAdminDetailResp struct {
		ID                 int64                       `json:"id"`                // 故事玩法ID
		ActivityCode       string                      `json:"activity_code"`     // 活动编码
		Title              string                      `json:"title"`             // 活动名称
		ActivityType       int32                       `json:"activity_type"`     // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl           string                      `json:"cover_url"`         // 封面图
		Status             int32                       `json:"status"`            // 故事玩法状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		ItemId             string                      `json:"item_id"`           // 故事玩法物品id(商品/组合商品)
		ItemTitle          string                      `json:"item_title"`        // 故事玩法物品名称(商品/组合商品)
		UserLimit          int32                       `json:"user_limit"`        // 每人限合(0不限制)
		CompleteUserNum    int32                       `json:"complete_user_num"` // 已合人数(按用户去重)
		Stock              int32                       `json:"stock"`             // 剩余库存
		TotalStock         int32                       `json:"total_stock"`       // 总库存
		StockDisplay       int32                       `json:"stock_display"`     // 剩余库存是否显示【1:显示;2:不显示】
		SceneId            int64                       `json:"scene_id"`          // 故事玩法场景id
		SceneName          string                      `json:"scene_name"`        // 场景名称
		StartTime          time.Time                   `json:"start_time"`        // 开始时间
		EndTime            time.Time                   `json:"end_time"`          // 结束时间
		ActivityDesc       string                      `json:"activity_desc"`     // 说明
		CreatedBy          string                      `json:"created_by"`        // 创建人
		CreatedAt          time.Time                   `json:"created_at"`        // 创建时间
		UpdatedBy          string                      `json:"updated_by"`        // 更新人
		UpdatedAt          time.Time                   `json:"updated_at"`        // 更新时间
		StoryMaterialsData []*StoryMaterialsDetailData `json:"story_materials"`   // 故事玩法材料
		Release            *StoryReleaseTime           `json:"release_data"`      // 故事玩法材料释放时间
		StoryItemData      *StoryItemData              `json:"story_item_data"`   // 故事玩法奖品
	}
	StoryItemData struct {
		ItemImageUrl string `json:"image_url"`    // 商品图片
		ItemName     string `json:"item_name"`    // 商品名称
		ItemPrice    int32  `json:"price"`        // 商品发行价
		UsableStock  int32  `json:"usable_stock"` // 可用库存
	}

	StoryMaterialsDetailData struct {
		StoryMaterialsId     int64                 `json:"story_materials_id"`                      // 故事玩法材料ID
		MaterialsType        int32                 `json:"materials_type" binding:"required"`       // 材料类型【1-核心材料；2-关键材料】
		Qty                  int32                 `json:"qty" binding:"required"`                  // 集齐数量
		StoryMaterialsDetail []*StoryMaterialsData `json:"story_materials_data" binding:"required"` // 故事玩法材料
	}
)

type (
	GetStoryLogListReq struct {
		StoryID int64 `form:"story_id" json:"story_id"` // 故事玩法ID
	}
	// GetStoryLogListResp 故事玩法修改日志列表
	GetStoryLogListResp struct {
		CreatedAt time.Time `json:"created_at" example:"2025-04-01T11:44:22.384+08:00"` // 创建时间
		CreatedBy string    `json:"created_by" example:"张三"`                            // 创建人
		Action    int32     `json:"action" example:"1"`                                 // 操作类型【1-创建；2-修改；3-删除；4-上架；5-下架】
		Content   []string  `json:"content" example:"activity_desc,title"`              // 变动字段列表，修改才有，故事玩法条件有修改会返回materials，只需要展示文字"修改故事玩法条件"
	}
)

type (
	StoryMaterialsData struct {
		ItemId       string `json:"item_id" binding:"required"`     // 商品id
		ItemTitle    string `json:"item_title" binding:"required"`  // 商品名称
		ItemImageUrl string `json:"image_url" binding:"required"`   // 商品图片
		ItemPrice    int32  `json:"price" binding:"required"`       // 商品发行价
		Qty          int32  `json:"qty" binding:"required,max=500"` // 集齐数量
		Loop         int32  `json:"loop"`                           // 二次流转【1-开启；2-关闭】
		Destroy      int32  `json:"destroy"`                        // 材料消耗【1-销毁；2-不销毁】
	}
)

type (
	AddStoryReq struct {
		SceneId           int64                    `json:"scene_id" binding:"required"`                // 故事玩法场景id
		Title             string                   `json:"title" binding:"required"`                   // 活动名称
		ActivityType      int32                    `json:"activity_type" binding:"required,eq=1|eq=2"` // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl          string                   `json:"cover_url" binding:"required"`               // 封面图
		ItemId            string                   `json:"item_id" binding:"required"`                 // 故事玩法物品id(商品/组合商品)
		ItemTitle         string                   `json:"item_title" binding:"required"`              // 故事玩法物品名称(商品/组合商品)
		TotalStock        int32                    `json:"total_stock" binding:"required"`             // 总库存
		StockDisplay      int32                    `json:"stock_display" binding:"required,eq=1|eq=2"` // 剩余库存是否显示【1:显示;2:不显示】
		UserLimit         *int32                   `json:"user_limit" binding:"required"`              // 每人限合(0不限制)
		ActivityDesc      string                   `json:"activity_desc" binding:"required"`           // 说明
		StartTime         time.Time                `json:"start_time" binding:"required"`              // 开始时间
		EndTime           time.Time                `json:"end_time" binding:"required"`                // 结束时间
		AddStoryMaterials []*StoryMaterialsAddData `json:"add_story_materials" binding:"required"`     // 故事玩法材料
		Release           *StoryReleaseTime        `json:"release_data"`                               // 故事玩法材料释放时间
	}

	StoryMaterialsAddData struct {
		MaterialsType     int32                 `json:"materials_type" binding:"required"`       // 材料类型【1-核心材料；2-关键材料】
		Qty               int32                 `json:"qty" binding:"required"`                  // 集齐数量
		AddStoryMaterials []*StoryMaterialsData `json:"story_materials_data" binding:"required"` // 故事玩法材料
	}

	AddStoryResp struct {
		ID int64 `json:"id"` // 故事玩法ID
	}
)

type (
	EditStoryReq struct {
		ID                 int64                     `json:"id" binding:"required"`                   // 故事玩法ID
		SceneId            int64                     `json:"scene_id" binding:"required"`             // 故事玩法场景id
		Title              string                    `json:"title" binding:"required"`                // 活动名称
		ActivityType       int32                     `json:"activity_type" binding:"required"`        // 故事玩法类型【1-组合商品；2-商品】
		CoverUrl           string                    `json:"cover_url" binding:"required"`            // 封面图
		ItemId             string                    `json:"item_id" binding:"required"`              // 故事玩法物品id(商品/组合商品)
		ItemTitle          string                    `json:"item_title" binding:"required"`           // 故事玩法物品名称(商品/组合商品)
		TotalStock         int32                     `json:"total_stock" binding:"required"`          // 总库存
		StockDisplay       int32                     `json:"stock_display" binding:"required"`        // 剩余库存是否显示【1:显示;2:不显示】
		UserLimit          *int32                    `json:"user_limit" binding:"required"`           // 每人限合(0不限制)
		ActivityDesc       string                    `json:"activity_desc" binding:"required"`        // 说明
		StartTime          time.Time                 `json:"start_time" binding:"required"`           // 开始时间
		EndTime            time.Time                 `json:"end_time" binding:"required"`             // 结束时间
		EditStoryMaterials []*StoryMaterialsEditData `json:"edit_story_materials" binding:"required"` // 故事玩法材料
		Release            *StoryReleaseTime         `json:"release_data"`                            // 故事玩法材料释放时间
	}

	StoryMaterialsEditData struct {
		MaterialsType      int32                 `json:"materials_type" binding:"required"`       // 材料类型【1-核心材料；2-关键材料】
		Qty                int32                 `json:"qty" binding:"required"`                  // 集齐数量
		EditStoryMaterials []*StoryMaterialsData `json:"story_materials_data" binding:"required"` // 故事玩法材料
	}

	EditStoryResp struct {
		ID int64 `json:"id"` // 故事玩法ID
	}
)

type (
	EditStoryStatusReq struct {
		ID     int64 `form:"id" json:"id"`                                      // 故事玩法ID
		Status int32 `form:"status" json:"status" binding:"required,eq=2|eq=3"` // 状态 2:上架;3:下架
	}

	EditStoryStatusResp struct {
	}
)

type (
	DelStoryReq struct {
		ID int64 `form:"id" json:"id"` // 故事玩法ID
	}

	DelStoryResp struct {
	}
)

type (
	// GetStoryRuleAdminResp 获取故事玩法规则
	GetStoryRuleAdminResp struct {
		Content string `json:"content"`
	}

	// EditStoryRuleReq 编辑故事玩法规则
	EditStoryRuleReq struct {
		Content string `json:"content" binding:"required"`
	}
	EditStoryRuleResp struct {
	}
)

type (
	GetStoryAdminOrderListReq struct {
		pagination.Pagination
		CreatedAtStart time.Time `form:"created_at_start" json:"created_at_start"` // 创建时间开始
		CreatedAtEnd   time.Time `form:"created_at_end" json:"created_at_end"`     // 创建时间结束
		ActivityID     int64     `form:"activity_id" json:"activity_id"`           // 活动ID
		SceneId        *int64    `form:"scene_id" json:"scene_id"`                 // 故事玩法场景id
		ActivityTitle  string    `form:"activity_title" json:"activity_title"`     // 活动名称
		ItemId         string    `form:"item_id" json:"item_id"`                   // 故事玩法物品id(商品/组合商品)
		ItemTitle      string    `form:"item_title" json:"item_title"`             // 故事玩法物品名称(商品/组合商品)
		OrderID        string    `form:"order_id" json:"order_id"`                 // 订单ID
		UserID         string    `form:"user_id" json:"user_id"`                   // 用户ID
		Status         int32     `form:"status" json:"status"`                     // 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
		ChainStatus    *int32    `form:"chain_status" json:"chain_status"`         // 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	}
	GetStoryAdminOrderListData struct {
		ID            int64     `json:"id,string"`      // 订单ID
		OrderID       string    `json:"order_id"`       // 订单号
		ActivityTitle string    `json:"activity_title"` // 活动名称
		ActivityID    int64     `json:"activity_id"`    // 故事玩法ID
		ActivityType  int32     `json:"activity_type"`  // 故事玩法类型【1-组合商品；2-商品】
		ItemTitle     string    `json:"item_title"`     // 故事玩法物品
		ItemCoverURL  string    `json:"item_cover_url"` // 故事玩法物品封面图
		Qty           int32     `json:"qty"`            // 故事玩法数量
		UserID        string    `json:"user_id"`        // 用户ID
		Status        int32     `json:"status"`         // 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
		ChainStatus   int32     `json:"chain_status"`   // 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
		SceneName     string    `json:"scene_name"`     // 故事玩法场景名称
		CreatedAt     time.Time `json:"created_at"`     // 故事玩法时间
	}
	GetStoryAdminOrderListResp struct {
		List  []*GetStoryAdminOrderListData `json:"list"`
		Total int64                         `json:"total"`
	}
)

type (
	GetStoryAdminOrderDetailReq struct {
		ID int64 `form:"id,string" json:"id,string"` // 订单ID
	}
	GetStoryAdminOrderItemDetail struct {
		ID         string `json:"id"`          // 故事玩法材料物品ID
		CoverURL   string `json:"cover_url"`   // 物品封面图
		ItemTitle  string `json:"item_title"`  // 物品名称
		IP         string `json:"ip"`          // IP
		Code       string `json:"code"`        // 编码
		Issuer     string `json:"issuer"`      // 发行方
		IssuePrice int64  `json:"issue_price"` // 发行价格
	}
	GetStoryAdminOrderMaterialsDetail struct {
		ID         string `json:"id"`           // 故事玩法材料物品ID
		UserItemID string `json:"user_item_id"` // 用户物品ID
		Code       string `json:"code"`         // 编码
		ItemName   string `json:"item_name"`    // 物品名称
		CoverURL   string `json:"cover_url"`    // 物品封面图
		IP         string `json:"ip"`           // IP
		Issuer     string `json:"issuer"`       // 发行方
		IssuePrice int64  `json:"issue_price"`  // 发行价格
		CostPrice  int64  `json:"cost_price"`   // 成本价
	}
	GetStoryAdminOrderDetailResp struct {
		ID              int64                                `json:"id,string"`        // 订单ID
		OrderID         string                               `json:"order_id"`         // 订单号
		ActivityType    int32                                `json:"activity_type"`    // 故事玩法类型【1-组合商品；2-商品】
		ActivityTitle   string                               `json:"activity_title"`   // 活动名称
		ActivityID      int64                                `json:"activity_id"`      // 故事玩法ID
		UserID          string                               `json:"user_id"`          // 用户ID
		UserNickname    string                               `json:"user_nickname"`    // 用户昵称
		Status          int32                                `json:"status"`           // 订单状态【-1:失败;1:待探索;21:探索中(云仓材料消耗成功);31:探索结束(活动结束,未领取奖励);91:已完成】
		Qty             int32                                `json:"qty"`              // 故事玩法数量
		ChainHash       string                               `json:"chain_hash"`       // 上链哈希
		CreatedAt       time.Time                            `json:"created_at"`       // 创建时间
		CompleteTime    *time.Time                           `json:"complete_time"`    // 探索结束时间
		FinishTime      *time.Time                           `json:"finish_time"`      // 完成时间
		ItemDetail      *GetStoryAdminOrderItemDetail        `json:"item_detail"`      // 故事玩法物品
		MaterialsDetail []*GetStoryAdminOrderMaterialsDetail `json:"materials_detail"` // 故事玩法材料
	}
)

type (
	GetItemStoryListReq struct {
		pagination.Pagination
		// 物品ID
		ItemId *string `form:"item_id" json:"item_id"`
		// 物品名称
		ItemName *string `form:"item_name" json:"item_name"`
	}

	GetItemStoryListData struct {
		ItemID    string `json:"item_id"`    // 物品ID
		ItemName  string `json:"item_name"`  // 物品名称
		ImageURL  string `json:"item_url"`   // 物品图片
		Price     int32  `json:"price"`      // 发行价格
		Stock     int32  `json:"stock"`      // 总库存
		UsedStock int32  `json:"used_stock"` // 已用库存
	}

	GetItemStoryListResp struct {
		List  []*GetItemStoryListData `json:"list"`
		Total int64                   `json:"total"`
	}
)

type (
	StoryReleaseTime struct {
		ReleaseTime *time.Time `json:"release_time"` // 释放时间
		ReleaseDay  *int32     `json:"release_day"`  // 故事玩法X天后释放
	}
)

type (
	GetStoryOrderDetailListReq struct {
		ID int64 `form:"id,string" json:"id,string"` // 订单ID
		pagination.Pagination
	}

	GetStoryOrderDetailListData struct {
		ID         string `json:"id"`           // 合成材料物品ID
		UserItemID string `json:"user_item_id"` // 用户物品ID
		Code       string `json:"code"`         // 编码
		ItemName   string `json:"item_name"`    // 物品名称
		CoverURL   string `json:"cover_url"`    // 物品封面图
		IP         string `json:"ip"`           // IP
		Issuer     string `json:"issuer"`       // 发行方
		IssuePrice int64  `json:"issue_price"`  // 发行价格
		CostPrice  int64  `json:"cost_price"`   // 成本价
	}

	GetStoryOrderDetailListResp struct {
		List  []*GetStoryOrderDetailListData `json:"list"`
		Total int64                          `json:"total"`
	}
)
