package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	SH110001Err  = response.NewError(110001, "核心材料有且最多存在一组")
	SH110002Err  = response.NewError(110002, "材料一组最多添加20件商品")
	SH110003Err  = response.NewError(110003, "关键材料集齐数量最多为10件")
	SH110004Err  = response.NewError(110004, "关键材料集齐数量不能大于等于商品数")
	SH110005Err  = response.NewError(110005, "关键材料最多为10组")
	SH110006Err  = response.NewError(110006, "活动名称不允许重复")
	SH110007Err  = response.NewError(110007, "不允许修改内容")
	SH110008Err  = response.NewError(110008, "探索结束后可开启")
	SH110009Err  = response.NewError(110009, "库存不足")
	SH1100010Err = response.NewError(1100010, "活动未开始")
	SH1100011Err = response.NewError(1100011, "活动已结束")
	SH1100012Err = response.NewError(1100012, "非交易时间不可参与")
	SH1100013Err = response.NewError(1100013, "你已超限，每人限制探索%d件")
	SH1100014Err = response.NewError(1100014, "队伍数量不足")
	SH1100015Err = response.NewError(1100015, "库存不足")
	SH1100016Err = response.NewError(1100016, "开启二次流转后，释放时间必填")
	SH1100017Err = response.NewError(1100017, "释放时间必须在当前日期之前")
	SH1100018Err = response.NewError(1100018, "商品未开启故事玩法")
)
