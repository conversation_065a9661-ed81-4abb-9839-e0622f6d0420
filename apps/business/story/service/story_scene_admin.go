package service

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/repo"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/constant"
	"app_service/pkg/search"
)

// GetStorySceneAdminList 查询故事玩法场景管理列表
func (s *Service) GetStorySceneAdminList(req *define.GetStorySceneAdminListReq) (*define.GetStorySceneAdminListResp, error) {
	storySceneSchema := repo.GetQuery().StoryScene
	builder := search.NewQueryBuilder()
	list, err := repo.NewStorySceneRepo(storySceneSchema.WithContext(s.ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetStorySceneAdminListResp{}
	if len(list) == 0 {
		return resp, nil
	}
	flag := req.NeedStoryNum
	// 查询故事玩法场景下的故事数量
	storyNumMap := make(map[int64]int32)
	if flag == constant.Yes {
		storyNumMap, err = logic.GetStoryNumByScene(s.ctx)
		if err != nil {
			return nil, err
		}
	}
	dataList := make([]*define.GetStorySceneAdminListData, 0)
	for _, v := range list {
		data := &define.GetStorySceneAdminListData{
			ID:   v.ID,
			Name: v.Name,
		}
		if flag == constant.Yes {
			if _, ok := storyNumMap[v.ID]; ok {
				data.StoryNum = storyNumMap[v.ID]
			}
			data.CoverURL = &v.CoverURL
		}
		dataList = append(dataList, data)
	}
	resp.List = dataList
	return resp, nil
}

// GetStorySceneAdminDetail 查询故事玩法场景管理详情
func (s *Service) GetStorySceneAdminDetail(req *define.GetStorySceneAdminDetailReq) (*define.GetStorySceneAdminDetailResp, error) {
	storySceneSchema := repo.GetQuery().StoryScene
	builder := search.NewQueryBuilder().Eq(storySceneSchema.ID, req.ID)
	storyScene, err := repo.NewStorySceneRepo(storySceneSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetStorySceneAdminDetailResp{
		ID:       storyScene.ID,
		Name:     storyScene.Name,
		Content:  storyScene.Content,
		CoverURL: storyScene.CoverURL,
	}
	return resp, nil
}

// AddStoryScene 新增故事玩法场景
func (s *Service) AddStoryScene(req *define.AddStorySceneReq) (*define.AddStorySceneResp, error) {
	scene := &model.StoryScene{
		Name:      req.Name,
		CoverURL:  req.CoverURL,
		Content:   req.Content,
		CreatedBy: s.GetAdminId(),
	}
	storySceneSchema := repo.GetQuery().StoryScene
	err := repo.NewStorySceneRepo(storySceneSchema.WithContext(s.ctx)).Save(scene)
	if err != nil {
		return nil, err
	}
	_ = logic.SetStorySceneCache(s.ctx, scene.ID, scene)
	return &define.AddStorySceneResp{ID: scene.ID}, nil
}

// EditStoryScene 编辑故事玩法
func (s *Service) EditStoryScene(req *define.EditStorySceneReq) (*define.EditStorySceneResp, error) {
	scene := &model.StoryScene{
		ID:        req.ID,
		Name:      req.Name,
		CoverURL:  req.CoverURL,
		Content:   req.Content,
		UpdatedBy: s.GetAdminId(),
	}
	storySceneSchema := repo.GetQuery().StoryScene
	err := repo.NewStorySceneRepo(storySceneSchema.WithContext(s.ctx)).UpdateById(scene)
	if err != nil {
		return nil, err
	}
	_ = logic.SetStorySceneCache(s.ctx, scene.ID, scene)
	return &define.EditStorySceneResp{ID: scene.ID}, nil
}
