package service

import (
	"app_service/apps/platform/common/constant"
	"app_service/apps/platform/issue/dal/model/mongdb"
	issueDefine "app_service/apps/platform/issue/define"
	"app_service/third_party/yc_open"
	"context"
	"encoding/json"
	"github.com/jinzhu/copier"
	"time"

	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"
	"app_service/pkg/util/snowflakeutl"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/gin-gonic/gin"
	"gorm.io/datatypes"
)

// GetStoryList 查询故事玩法管理列表
func (s *Service) GetStoryList(req *define.GetStoryAdminListReq) (*define.GetStoryAdminListResp, error) {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().OrderByDesc(storySchema.ID)
	if !req.StartTimeStart.IsZero() && !req.StartTimeEnd.IsZero() {
		builder = builder.Gte(storySchema.StartTime, req.StartTimeStart)
		builder = builder.Lte(storySchema.StartTime, req.StartTimeEnd)
	}
	if !req.CreatedAtStart.IsZero() && !req.CreatedAtEnd.IsZero() {
		builder = builder.Gte(storySchema.CreatedAt, req.CreatedAtStart)
		builder = builder.Lte(storySchema.CreatedAt, req.CreatedAtEnd)
	}
	if req.ID != nil {
		builder = builder.Eq(storySchema.ID, *req.ID)
	}
	if req.Title != "" {
		builder = builder.Like(storySchema.Title, "%"+req.Title+"%")
	}
	if req.ItemId != "" {
		builder = builder.Eq(storySchema.ItemID, req.ItemId)
	}
	if req.ItemTitle != "" {
		builder = builder.Like(storySchema.ItemTitle, "%"+req.ItemTitle+"%")
	}
	if req.SceneId != nil {
		builder = builder.Eq(storySchema.SceneID, *req.SceneId)
	}
	if req.Status != nil {
		builder = builder.Eq(storySchema.Status, *req.Status)
	}
	if req.CreatedBy != "" {
		builder = builder.Eq(storySchema.CreatedBy, req.CreatedBy)
	}
	list, count, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	resp := &define.GetStoryAdminListResp{}
	if len(list) == 0 {
		return resp, nil
	}
	dataList := make([]*define.GetStoryAdminListData, 0)
	itemIds := make([]string, 0)
	for _, v := range list {
		itemIds = append(itemIds, v.ItemID)
	}
	goodsItemMap, err := issueFacade.GetIssueItemMap(s.ctx, itemIds)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		data := &define.GetStoryAdminListData{
			ID:              v.ID,
			ActivityCode:    v.ActivityCode,
			Title:           v.Title,
			ActivityType:    v.ActivityType,
			CoverUrl:        v.CoverURL,
			Status:          v.Status,
			ItemId:          v.ItemID,
			ItemTitle:       v.ItemTitle,
			UserLimit:       v.UserLimit,
			CompleteUserNum: v.CompleteUserNum,
			Stock:           v.Stock,
			TotalStock:      v.TotalStock,
			StartTime:       v.StartTime,
			EndTime:         v.EndTime,
			CreatedAt:       v.CreatedAt,
			CreatedBy:       v.CreatedBy,
			UpdatedAt:       v.UpdatedAt,
			UpdatedBy:       v.UpdatedBy,
		}
		scene, _ := logic.GetStoryScene(s.ctx, v.SceneID)
		data.SceneName = scene.Name
		dataList = append(dataList, data)
		if goodsItemMap[v.ItemID] != nil {
			data.CoverUrl = goodsItemMap[v.ItemID].ImageURL
		}
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetStoryDetail 查询故事玩法管理详情
func (s *Service) GetStoryDetail(req *define.GetStoryAdminDetailReq) (*define.GetStoryAdminDetailResp, error) {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ID, req.ID)
	story, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetStoryAdminDetailResp{
		ID:              story.ID,
		ActivityCode:    story.ActivityCode,
		Title:           story.Title,
		ActivityType:    story.ActivityType,
		CoverUrl:        story.CoverURL,
		Status:          story.Status,
		ItemId:          story.ItemID,
		ItemTitle:       story.ItemTitle,
		UserLimit:       story.UserLimit,
		CompleteUserNum: story.CompleteUserNum,
		Stock:           story.Stock,
		TotalStock:      story.TotalStock,
		StockDisplay:    story.StockDisplay,
		SceneId:         story.SceneID,
		StartTime:       story.StartTime,
		EndTime:         story.EndTime,
		ActivityDesc:    story.ActivityDesc,
		CreatedAt:       story.CreatedAt,
		CreatedBy:       story.CreatedBy,
		UpdatedAt:       story.UpdatedAt,
		UpdatedBy:       story.UpdatedBy,
	}
	storyMaterialsSchema := repo.GetQuery().StoryMaterials
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, story.ID)
	storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(s.ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	for _, material := range storyMaterialsList {
		materialsData, err := logic.UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}

		resp.StoryMaterialsData = append(resp.StoryMaterialsData, &define.StoryMaterialsDetailData{
			StoryMaterialsId:     material.ID,
			MaterialsType:        material.MaterialsType,
			Qty:                  material.LimitQty,
			StoryMaterialsDetail: materialsData,
		})
	}
	issueItem, err := issueFacade.GetIssueItemByItemID(s.ctx, story.ItemID)
	if err != nil {
		return nil, err
	}
	resp.StoryItemData = &define.StoryItemData{
		ItemImageUrl: issueItem.ImageURL,
		ItemName:     issueItem.ItemName,
		ItemPrice:    issueItem.Price,
	}
	usableStock, err := logic.GetStoryUsableStock(s.ctx, story.ItemID)
	if err != nil {
		return nil, err
	}
	resp.StoryItemData.UsableStock = usableStock
	releaseTimeData, err := logic.GetStoryReleaseTime(story.ReleaseTime)
	if err != nil {
		return nil, err
	}
	resp.Release = releaseTimeData
	scene, _ := logic.GetStoryScene(s.ctx, story.SceneID)
	resp.SceneName = scene.Name
	return resp, nil
}

// AddStory 新增故事玩法
func (s *Service) AddStory(req *define.AddStoryReq) (*define.AddStoryResp, error) {
	storySchema := repo.GetQuery().Story
	getStory, _ := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(storySchema.Title, req.Title).Build())
	if getStory != nil {
		return nil, define.SH110006Err
	}
	resp := &define.AddStoryResp{}

	Story := &model.Story{
		ActivityCode: util.StrVal(snowflakeutl.GenerateID()),
		SceneID:      req.SceneId,
		Title:        req.Title,
		ActivityType: req.ActivityType,
		CoverURL:     req.CoverUrl,
		ItemID:       req.ItemId,
		ItemTitle:    req.ItemTitle,
		Stock:        req.TotalStock,
		TotalStock:   req.TotalStock,
		StockDisplay: req.StockDisplay,
		UserLimit:    *req.UserLimit,
		ActivityDesc: req.ActivityDesc,
		StartTime:    req.StartTime,
		EndTime:      req.EndTime,
		CreatedBy:    s.GetAdminId(),
	}
	url, err := logic.GetStoryItemImageUrl(s.ctx, req.ItemId)
	if err != nil {
		return nil, err
	}
	Story.ItemImageURL = url
	// materialsType 1核心材料 2 crux材料
	// 关键材料组数
	cruxMaterialGroupLength := 0
	// 核心材料组数
	coreMaterialGroupLength := 0
	// 核心材料最大组数
	coreMaterialMaxGroupLength := 1
	// 关键材料最大组数
	cruxMaterialMaxGroupLength := 10
	// 关键一组材料最大集齐数量
	cruxMaterialLimitMaxQty := int32(10)
	// 一组材料最大商品数量
	materialLimitMaxQty := 20

	for _, item := range req.AddStoryMaterials {
		if item.MaterialsType == enums.StoryMaterialsTypeCore.Val() {
			item.Qty = int32(len(item.AddStoryMaterials))
			coreMaterialGroupLength++
		} else {
			if int32(len(item.AddStoryMaterials)) <= item.Qty {
				return nil, define.SH110004Err
			}
			cruxMaterialGroupLength++
			if item.Qty > cruxMaterialLimitMaxQty {
				return nil, define.SH110003Err
			}
		}
		if len(item.AddStoryMaterials) > materialLimitMaxQty {
			return nil, define.SH110002Err
		}
	}
	if coreMaterialGroupLength > coreMaterialMaxGroupLength {
		return nil, define.SH110001Err
	}
	if cruxMaterialGroupLength > cruxMaterialMaxGroupLength {
		return nil, define.SH110005Err
	}
	usableStock, err := logic.GetStoryUsableStock(s.ctx, Story.ItemID)
	if err != nil {
		return nil, err
	}
	if usableStock < req.TotalStock {
		return nil, define.SH110009Err
	}
	releaseFlag := false
OuterLoop:
	for _, material := range req.AddStoryMaterials {
		for _, item := range material.AddStoryMaterials {
			if item.Loop == constant.Yes {
				releaseFlag = true
				break OuterLoop
			}
		}
	}
	if releaseFlag {
		if req.Release == nil {
			return nil, define.SH1100016Err
		} else {
			if req.Release.ReleaseTime != nil && !req.Release.ReleaseTime.IsZero() {
				req.Release.ReleaseTime = util.GetBeijingStartOfDay(req.Release.ReleaseTime)
				if req.Release.ReleaseTime.Before(time.Now()) {
					return nil, define.SH1100017Err
				}
			}
			if req.Release.ReleaseDay != nil && *req.Release.ReleaseDay < 0 {
				return nil, define.SH1100017Err
			}
			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(req.Release))
			Story.ReleaseTime = &jsonData
		}
	}
	err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
		err := repo.NewStoryRepo(repo.Query(tx).Story.WithContext(tx)).Save(Story)
		if err != nil {
			return err
		}
		storyMaterialsList := make([]*model.StoryMaterials, 0)
		for _, item := range req.AddStoryMaterials {
			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(item.AddStoryMaterials))
			storyMaterials := &model.StoryMaterials{
				StoryID:       Story.ID,
				MaterialsType: item.MaterialsType,
				LimitQty:      item.Qty,
				MaterialsData: &jsonData,
				CreatedBy:     s.GetAdminId(),
			}
			storyMaterialsList = append(storyMaterialsList, storyMaterials)
		}
		err = repo.NewStoryMaterialsRepo(repo.Query(tx).StoryMaterials.WithContext(tx)).BatchSave(storyMaterialsList, 100)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	// 日志
	_ = logic.NewStoryLog(s.ctx, enums.LogActionCreate, Story.ID).Save(s.GetAdminId())
	resp.ID = Story.ID
	return resp, nil
}

// EditStory 编辑故事玩法
func (s *Service) EditStory(req *define.EditStoryReq) (*define.EditStoryResp, error) {
	resp := &define.EditStoryResp{}
	storySchema := repo.GetQuery().Story
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(storySchema.ID, req.ID).Build())
	if err != nil {
		return nil, err
	}
	if getStory != nil && getStory.Title != req.Title {
		count, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).Count(search.NewQueryBuilder().Eq(storySchema.Title, req.Title).
			Ne(storySchema.ID, req.ID).Build())
		if err != nil {
			return nil, err
		}
		if count > 0 {
			return nil, define.SH110006Err
		}
	}

	// 初始化int数组
	statusList := []int32{enums.StoryStatusDown.Val(), enums.StoryStatusExpires.Val(), enums.StoryStatusDel.Val()}
	storyLog := logic.NewStoryLog(s.ctx, enums.LogActionUpdate, req.ID)
	if util.FindIntInSlice(getStory.Status, statusList) {
		return nil, define.SH110007Err
	} else if getStory.Status == enums.StoryStatusUp.Val() {
		// 仅可修改-故事玩法时间，故事玩法说明，封面图
		updateStory := &model.Story{
			ID:           getStory.ID,
			StartTime:    req.StartTime,
			EndTime:      req.EndTime,
			ActivityDesc: req.ActivityDesc,
			CoverURL:     req.CoverUrl,
			UpdatedBy:    s.GetAdminId(),
		}
		err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).UpdateById(updateStory)
		if err != nil {
			return resp, err
		}
		storyLog.SetMain(getStory, updateStory)
	} else if getStory.Status == enums.StoryStatusWaiting.Val() {
		// 所有内容都可以修改
		storyMaterialsSchema := repo.GetQuery().StoryMaterials
		storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, req.ID)
		storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(s.ctx)).SelectList(storyMaterialsBuilder.Build())
		if err != nil {
			return nil, err
		}
		// 活动信息修改
		updateStory := &model.Story{
			ID:           getStory.ID,
			SceneID:      req.SceneId,
			Title:        req.Title,
			ActivityType: req.ActivityType,
			CoverURL:     req.CoverUrl,
			ItemID:       req.ItemId,
			ItemTitle:    req.ItemTitle,
			Stock:        req.TotalStock,
			TotalStock:   req.TotalStock,
			StockDisplay: req.StockDisplay,
			UserLimit:    *req.UserLimit,
			ActivityDesc: req.ActivityDesc,
			StartTime:    req.StartTime,
			EndTime:      req.EndTime,
			UpdatedBy:    s.GetAdminId(),
		}
		url, err := logic.GetStoryItemImageUrl(s.ctx, req.ItemId)
		if err != nil {
			return nil, err
		}
		updateStory.ItemImageURL = url

		// materialsType 1核心材料 2 crux材料
		// 关键材料组数
		cruxMaterialGroupLength := 0
		// 核心材料组数
		coreMaterialGroupLength := 0
		// 核心材料最大组数
		coreMaterialMaxGroupLength := 1
		// 关键材料最大组数
		cruxMaterialMaxGroupLength := 10
		// 关键一组材料最大集齐数量
		cruxMaterialLimitMaxQty := int32(10)
		// 一组材料最大商品数量
		materialLimitMaxQty := 20

		addStoryMaterials := make([]*model.StoryMaterials, 0)
		delStoryMaterials := make([]*model.StoryMaterials, 0)
		for _, item := range req.EditStoryMaterials {
			if item.MaterialsType == enums.StoryMaterialsTypeCore.Val() {
				item.Qty = int32(len(item.EditStoryMaterials))
				coreMaterialGroupLength++
			} else {
				if int32(len(item.EditStoryMaterials)) <= item.Qty {
					return nil, define.SH110004Err
				}
				cruxMaterialGroupLength++
				if item.Qty > cruxMaterialLimitMaxQty {
					return nil, define.SH110003Err
				}
			}
			if len(item.EditStoryMaterials) > materialLimitMaxQty {
				return nil, define.SH110002Err
			}
			if coreMaterialGroupLength > coreMaterialMaxGroupLength {
				return nil, define.SH110001Err
			}
			if cruxMaterialGroupLength > cruxMaterialMaxGroupLength {
				return nil, define.SH110005Err
			}

			var jsonData datatypes.JSON
			jsonData = datatypes.JSON(util.Obj2JsonStr(item.EditStoryMaterials))
			addStoryMaterials = append(addStoryMaterials, &model.StoryMaterials{
				StoryID:       getStory.ID,
				MaterialsType: item.MaterialsType,
				LimitQty:      item.Qty,
				MaterialsData: &jsonData,
				CreatedBy:     s.GetAdminId(),
			})
		}
		for _, item := range storyMaterialsList {
			delStoryMaterials = append(delStoryMaterials, &model.StoryMaterials{
				ID: item.ID,
			})
		}
		log.Ctx(s.ctx).Debugf("编辑活动材料 addStoryMaterials:%+v", util.Obj2JsonStr(addStoryMaterials))
		log.Ctx(s.ctx).Debugf("编辑活动材料 delStoryMaterials:%+v", util.Obj2JsonStr(delStoryMaterials))
		usableStock, err := logic.GetStoryUsableStock(s.ctx, req.ItemId)
		if err != nil {
			return nil, err
		}
		if usableStock < req.TotalStock {
			return nil, define.SH110009Err
		}
		releaseFlag := false
	OuterLoop:
		for _, material := range req.EditStoryMaterials {
			for _, item := range material.EditStoryMaterials {
				if item.Loop == constant.Yes {
					releaseFlag = true
					break OuterLoop
				}
			}
		}
		if releaseFlag {
			if req.Release == nil {
				return nil, define.SH1100016Err
			} else {
				var jsonData datatypes.JSON
				if req.Release.ReleaseTime != nil && !req.Release.ReleaseTime.IsZero() {
					req.Release.ReleaseTime = util.GetBeijingStartOfDay(req.Release.ReleaseTime)
					if req.Release.ReleaseTime.Before(time.Now()) {
						return nil, define.SH1100017Err
					}
				}
				if req.Release.ReleaseDay != nil && *req.Release.ReleaseDay < 0 {
					return nil, define.SH1100017Err
				}
				jsonData = datatypes.JSON(util.Obj2JsonStr(req.Release))
				updateStory.ReleaseTime = &jsonData
			}
		}
		// db操作
		err = repo.ExecGenTx(s.ctx, func(tx context.Context) error {
			err = repo.NewStoryRepo(repo.Query(tx).Story.WithContext(tx)).UpdateById(updateStory)
			if err != nil {
				return err
			}
			err = repo.NewStoryMaterialsRepo(repo.Query(tx).StoryMaterials.WithContext(tx)).BatchSave(addStoryMaterials, 100)
			if err != nil {
				log.Ctx(s.ctx).Errorf("编辑活动失败,新增活动材料失败 err:%v", err)
				return err
			}
			err = repo.NewStoryMaterialsRepo(repo.Query(tx).StoryMaterials.WithContext(tx)).RemoveByIds(delStoryMaterials...)
			if err != nil {
				log.Ctx(s.ctx).Errorf("编辑活动失败,删除活动材料失败 err:%v", err)
				return err
			}
			return nil
		})
		if err != nil {
			log.Ctx(s.ctx).Errorf("编辑活动失败 err:%v", err)
			return nil, err
		}
		storyLog.SetMaterials(storyMaterialsList, addStoryMaterials)
	}
	// 保存日志
	_ = storyLog.Save(s.GetAdminId())

	resp.ID = req.ID
	return resp, nil
}

// EditStoryStatus 故事玩法状态编辑
func (s *Service) EditStoryStatus(req *define.EditStoryStatusReq) (*define.EditStoryStatusResp, error) {
	logAction := enums.LogActionUp
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ID, req.ID)
	m := &model.Story{
		Status: req.Status,
	}
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).SelectOne(search.NewQueryBuilder().Eq(storySchema.ID, req.ID).Build())
	if err != nil {
		return nil, err
	}
	if req.Status == enums.StoryStatusUp.Val() {
		builder = builder.Eq(storySchema.Status, enums.StoryStatusWaiting.Val())
		if req.Status == enums.StoryStatusUp.Val() {
			item, err := issueFacade.GetIssueItemByItemID(s.ctx, getStory.ItemID)
			if err != nil {
				return nil, err
			}
			if item.Story.Status != mongdb.IssueItemStoryStatusOpen {
				return nil, define.SH1100018Err
			}
			// 查询故事玩法已用库存
			usableStock, err := logic.GetStoryUsableStock(s.ctx, getStory.ItemID)
			if err != nil {
				return nil, err
			}
			if usableStock-getStory.TotalStock < 0 {
				return nil, define.SH110009Err
			}
		}
	} else if req.Status == enums.StoryStatusDown.Val() {
		builder = builder.Eq(storySchema.Status, enums.StoryStatusUp.Val())
		logAction = enums.LogActionDown
		// 删除缓存
		err = logic.DelStorySceneUserNameListCache(s.ctx, getStory.SceneID)
		if err != nil {
			log.Ctx(s.ctx).Errorf("EditStoryStatus Success, DelStorySceneUserNameListCache err:%v, sceneId:%v", err, getStory.SceneID)
		}
	}
	err = repo.NewStoryRepo(storySchema.WithContext(s.ctx)).Update(m, builder.Build())
	if err != nil {
		return nil, err
	}
	// 日志
	_ = logic.NewStoryLog(s.ctx, logAction, req.ID).Save(s.GetAdminId())
	return &define.EditStoryStatusResp{}, nil
}

// DelStory 故事玩法删除
func (s *Service) DelStory(req *define.DelStoryReq) (*define.DelStoryResp, error) {
	storySchema := repo.GetQuery().Story
	m := &model.Story{
		Status: enums.StoryStatusDel.Val(),
	}
	err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).Update(m, search.NewQueryBuilder().
		Eq(storySchema.ID, req.ID).Eq(storySchema.Status, enums.StoryStatusDown.Val()).Build())
	if err != nil {
		return nil, err
	}
	// 日志
	_ = logic.NewStoryLog(s.ctx, enums.LogActionDelete, req.ID).Save(s.GetAdminId())
	return &define.DelStoryResp{}, nil
}

// GetStoryLogList 查询故事玩法操作日志列表
func (s *Service) GetStoryLogList(req *define.GetStoryLogListReq) ([]*define.GetStoryLogListResp, error) {
	logSchema := repo.GetQuery().StoryLog
	builder := search.NewQueryBuilder().Eq(logSchema.StoryID, req.StoryID).OrderByDesc(logSchema.ID)
	listData, err := repo.NewStoryLogRepo(logSchema.WithContext(s.ctx)).SelectList(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := make([]*define.GetStoryLogListResp, 0)
	for _, v := range listData {
		resp = append(resp, &define.GetStoryLogListResp{
			Action:    v.Action,
			Content:   logic.GetLogContent(s.ctx, v),
			CreatedAt: v.CreatedAt,
			CreatedBy: v.CreatedBy,
		})
	}
	return resp, nil
}

// GetStoryRuleAdmin 查询故事玩法规则
func (s *Service) GetStoryRuleAdmin() (*define.GetStoryRuleAdminResp, error) {
	resp := &define.GetStoryRuleAdminResp{}
	err := facade.GetObj(s.ctx, enums.StoryRuleConfigKey, &resp.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryRuleAdmin err:%v", err)
		return nil, err
	}
	return resp, nil
}

// EditStoryRule 编辑故事玩法规则
func (s *Service) EditStoryRule(req *define.EditStoryRuleReq) (*define.EditStoryRuleResp, error) {
	err := facade.SaveOrUpdate(s.ctx, enums.StoryRuleConfigKey, req.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditStoryRule err:%v", err)
		return nil, err
	}
	return nil, nil
}

// GetStoryAdminOrderList 故事玩法订单列表
func (s *Service) GetStoryAdminOrderList(req *define.GetStoryAdminOrderListReq) (*define.GetStoryAdminOrderListResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().OrderByDesc(storyOrderSchema.ID)
	if !req.CreatedAtStart.IsZero() && !req.CreatedAtEnd.IsZero() {
		builder = builder.Gte(storyOrderSchema.CreatedAt, req.CreatedAtStart)
		builder = builder.Lte(storyOrderSchema.CreatedAt, req.CreatedAtEnd)
	}
	if req.ActivityID > 0 {
		builder = builder.Eq(storyOrderSchema.StoryID, req.ActivityID)
	}
	if req.SceneId != nil {
		builder = builder.Eq(storyOrderSchema.SceneID, *req.SceneId)
	}
	if req.ActivityTitle != "" {
		builder = builder.Like(storyOrderSchema.StoryTitle, "%"+req.ActivityTitle+"%")
	}
	if req.ItemId != "" {
		builder = builder.Eq(storyOrderSchema.ItemID, req.ItemId)
	}
	if req.ItemTitle != "" {
		builder = builder.Like(storyOrderSchema.ItemTitle, "%"+req.ItemTitle+"%")
	}
	if req.OrderID != "" {
		builder = builder.Eq(storyOrderSchema.OrderID, req.OrderID)
	}
	if req.UserID != "" {
		builder = builder.Eq(storyOrderSchema.UserID, req.UserID)
	}
	if req.Status != 0 {
		builder = builder.Eq(storyOrderSchema.Status, req.Status)
	}
	if req.ChainStatus != nil && *req.ChainStatus >= 0 {
		builder = builder.Eq(storyOrderSchema.ChainStatus, *req.ChainStatus)
	}
	list, count, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetStoryAdminOrderListResp{
		List:  make([]*define.GetStoryAdminOrderListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	storyIDs := make([]int64, 0)
	for _, item := range list {
		storyIDs = append(storyIDs, item.StoryID)
	}
	activityList, err := logic.GetStoryMap(s.ctx, storyIDs)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderList err:%v", err)
		return nil, err
	}
	for _, item := range list {
		aType := int32(0)
		if activityList[item.StoryID] != nil {
			aType = activityList[item.StoryID].ActivityType
		}
		resp.List = append(resp.List, &define.GetStoryAdminOrderListData{
			ID:            item.ID,
			OrderID:       item.OrderID,
			ActivityID:    item.StoryID,
			ActivityTitle: item.StoryTitle,
			ActivityType:  aType,
			ItemTitle:     item.ItemTitle,
			ItemCoverURL:  item.ItemCoverURL,
			Qty:           item.Qty,
			UserID:        item.UserID,
			Status:        item.Status,
			ChainStatus:   item.ChainStatus,
			SceneName:     item.SceneName,
			CreatedAt:     item.CreatedAt,
		})
	}
	return resp, nil
}

// GetStoryAdminOrderDetail 故事玩法订单详情
func (s *Service) GetStoryAdminOrderDetail(req *define.GetStoryAdminOrderDetailReq) (*define.GetStoryAdminOrderDetailResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().Eq(storyOrderSchema.ID, req.ID)
	m, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}
	err = repo.GetDB().Model(m).Association("StoryOrderDetail").Find(&m.StoryOrderDetail)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}
	if err = repo.GetDB().Model(m).Association("Story").Find(&m.Story); err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
		return nil, err
	}
	itemDetail := &define.GetStoryAdminOrderItemDetail{
		ID:        m.ItemID,
		ItemTitle: m.ItemTitle,
		CoverURL:  m.ItemCoverURL,
	}
	if err = json.Unmarshal(m.ItemInfo, &itemDetail); err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail err:%v", err)
	}
	userNickname := ""
	userInfo, err := userFacade.GetNodeUser(s.ctx, m.UserID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderDetail GetNodeUser err:%v", err)
	}
	if userInfo != nil {
		userNickname = userInfo.PatbgDetail.Nickname
	}
	resp := &define.GetStoryAdminOrderDetailResp{
		ID:            m.ID,
		OrderID:       m.OrderID,
		ActivityID:    m.StoryID,
		ActivityTitle: m.StoryTitle,
		ActivityType:  m.Story.ActivityType,
		Qty:           m.Qty,
		UserID:        m.UserID,
		UserNickname:  userNickname,
		Status:        m.Status,
		ChainHash:     m.ChainHash,
		CreatedAt:     m.CreatedAt,
		CompleteTime:  m.CompleteTime,
		FinishTime:    m.FinishTime,
		ItemDetail:    itemDetail,
	}
	return resp, nil
}

// GetStoryOrderDetailList 获取故事玩法订单详情列表
func (s *Service) GetStoryOrderDetailList(req *define.GetStoryOrderDetailListReq) (*define.GetStoryOrderDetailListResp, error) {
	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	builder := search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, req.ID).OrderByDesc(storyOrderDetailSchema.ID)
	list, count, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(s.ctx)).
		SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetStoryAdminOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetStoryOrderDetailListResp{
		List:  make([]*define.GetStoryOrderDetailListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	var userItemIds []string
	for _, item := range list {
		userItemIds = append(userItemIds, item.UserItemID)
	}
	costMap := make(map[string]int64)
	if len(userItemIds) > 0 {
		// 成本价
		costResList, err := yc_open.QueryUserItemsCostPrice(s.ctx, userItemIds)
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetStoryOrderDetailList QueryUserItemsCostPrice err:%v", err)
		}
		for _, item := range costResList {
			costMap[item.ID] = item.Cost
		}
	}
	materialsDetail := make([]*define.GetStoryOrderDetailListData, 0)
	for _, item := range list {
		detail := &define.GetStoryOrderDetailListData{
			ID:       item.MaterialsItemID,
			ItemName: item.MaterialsItemName,
			CoverURL: item.MaterialsItemURL,
		}
		if item.MaterialsItemInfo != nil {
			if err = json.Unmarshal(*item.MaterialsItemInfo, &detail); err != nil {
				log.Ctx(s.ctx).Errorf("GetStoryOrderDetailList Unmarshal err:%v", err)
			}
		}
		detail.Code = item.UserItemID
		detail.UserItemID = item.UserItemID
		detail.CostPrice = costMap[item.UserItemID]
		materialsDetail = append(materialsDetail, detail)
	}
	resp.List = materialsDetail
	return resp, nil
}

// ExportStoryAdminOrderList 导出故事玩法订单
func (s *Service) ExportStoryAdminOrderList(req *define.GetStoryAdminOrderListReq) error {
	dataList := make([]*define.GetStoryAdminOrderListData, 0)
	for i := 1; i < 11000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetStoryAdminOrderList(req)
		if err != nil {
			return err
		}
		if len(page.List) == 0 {
			break
		}
		dataList = append(dataList, page.List...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "created_at", "title": "创建时间", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "order_id", "title": "订单号", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "activity_title", "title": "活动名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "activity_id", "title": "活动ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_title", "title": "奖品名称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_cover_url", "title": "商品图", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "qty", "title": "探索数量", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_id", "title": "用户ID", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "订单状态", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "chain_status", "title": "上链状态", "width": "20", "is_num": "0"})

	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {

		data = append(data, map[string]interface{}{
			"created_at":     util.GetDateTimeFormatStr(idata.CreatedAt),
			"order_id":       idata.OrderID,
			"activity_title": idata.ActivityTitle,
			"activity_id":    idata.ActivityID,
			"item_title":     idata.ItemTitle,
			"item_cover_url": idata.ItemCoverURL,
			"qty":            idata.Qty,
			"user_id":        idata.UserID,
			"status":         enums.StoryOrderStatusMap[idata.Status],
			"chain_status":   enums.StoryOrderChainStatusMap[idata.ChainStatus],
		})
	}

	err := excel.ExportToStream(dataKey, data, s.ctx.(*gin.Context))
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportStoryAdminOrderList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}

// GetItemStoryList 查询故事玩法的商品列表
func (s *Service) GetItemStoryList(req *define.GetItemStoryListReq) (*define.GetItemStoryListResp, error) {
	buysReq := &issueDefine.GetItemStoryListReq{}
	_ = copier.Copy(buysReq, req)
	itemList, count, err := issueFacade.GetIssueItemsByStory(s.ctx, buysReq)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.GetItemStoryList] GetIssueItemsByStory err:%v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Infof("[Service.GetItemStoryList] GetIssueItemsByStory req:%+v, resp:%+v, count:%+v", buysReq, itemList, count)
	list := make([]*define.GetItemStoryListData, 0)
	for _, item := range itemList {
		data := &define.GetItemStoryListData{
			ItemName: item.ItemName,
			ImageURL: item.ImageURL,
			Price:    item.Price,
			Stock:    item.Quantity,
		}
		if !item.ItemID.IsZero() {
			data.ItemID = item.ItemID.Hex()
		}
		usedStock, _ := logic.GetStoryUsableStock(s.ctx, item.ItemID.Hex())
		data.UsedStock = usedStock
		list = append(list, data)
	}
	resp := &define.GetItemStoryListResp{
		List:  list,
		Total: count,
	}
	return resp, nil
}
