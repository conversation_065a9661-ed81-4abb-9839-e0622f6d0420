package service

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/service/locker"
	"app_service/apps/business/story/service/logic"
	"app_service/global"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

// ReceiveStory 故事玩法领取奖励
func (s *Service) ReceiveStory(req *define.ReceiveStoryReq) (*define.ReceiveStoryResp, error) {
	// 初始化锁 用户不能同时故事玩法领取奖励
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewStoryLock(s.GetUserId(), locker.Receive)))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)
	resp, err := logic.ReceiveStory(s.ctx, s.GetUserId(), req.OrderID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("ReceiveStory err:%v", err)
		return nil, err
	}
	return resp, nil
}
