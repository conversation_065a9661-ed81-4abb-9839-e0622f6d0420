package service

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/constant"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/govvii/go-hutool/async"
	"time"
)

// GetItemStoryIsUp 获取商品对应故事活动是否上架
func (s *Service) GetItemStoryIsUp(req *define.GetItemStoryIsUpReq) (*define.GetItemStoryIsUpResp, error) {
	synthesisSchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ItemID, req.ItemId).Ne(synthesisSchema.Status, enums.StoryStatusWaiting.Val())
	count, err := repo.NewStoryRepo(synthesisSchema.WithContext(s.ctx)).Count(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetItemStoryIsUpResp{}
	if count > 0 {
		resp.HasUp = constant.Yes
	}
	return resp, nil
}

// StoryFinish 故事玩法超时下架
func (s *Service) StoryFinish() (*define.StoryFinishResp, error) {
	storySchema := repo.GetQuery().Story
	updateStory := &model.Story{
		Status: enums.StoryStatusExpires.Val(),
	}
	_ = repo.NewStoryRepo(storySchema.WithContext(s.ctx)).Update(updateStory, search.NewQueryBuilder().Eq(storySchema.Status,
		enums.StoryStatusUp.Val()).Lt(storySchema.EndTime, time.Now()).Build())
	_ = repo.NewStoryRepo(storySchema.WithContext(s.ctx)).Update(updateStory, search.NewQueryBuilder().Eq(storySchema.Status,
		enums.StoryStatusUp.Val()).Eq(storySchema.Stock, int32(0)).Build())
	return &define.StoryFinishResp{}, nil
}

// AsyncStoryOrderUpChain 异步故事玩法订单上链
func (s *Service) AsyncStoryOrderUpChain(req *define.StoryOrderUpChainReq) (*define.StoryOrderUpChainResp, error) {
	executor := asyncutil.NewAsyncExecutor(1, asyncutil.WithContext(util.NewCtx(s.ctx)))
	spanContext := s.NewContextWithSpanContext(s.ctx)
	executor.Execute(func(ctx context.Context) (interface{}, error) {
		_, err := logic.StoryOrderUpChain(spanContext, req)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	return &define.StoryOrderUpChainResp{}, nil
}

// AsyncStoryMaterialsRelease 异步故事玩法材料释放
func (s *Service) AsyncStoryMaterialsRelease(req *define.StoryMaterialsReleaseReq) (*define.StoryMaterialsReleaseResp, error) {
	executor := asyncutil.NewAsyncExecutor(1, asyncutil.WithContext(util.NewCtx(s.ctx)))
	spanContext := s.NewContextWithSpanContext(s.ctx)
	executor.Execute(func(ctx context.Context) (interface{}, error) {
		_, err := logic.StoryMaterialsRelease(spanContext, req)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	return &define.StoryMaterialsReleaseResp{}, nil
}

// StoryMaterialsGetReleaseTime 获取故事玩法材料释放时间
func (s *Service) StoryMaterialsGetReleaseTime(req *define.StoryMaterialsGetReleaseTimeReq) (*define.StoryMaterialsGetReleaseTimeResp, error) {
	resp := &define.StoryMaterialsGetReleaseTimeResp{}
	if len(req.UserItemIds) == 0 {
		return resp, nil
	}
	storyMaterialsReleaseSchema := repo.GetQuery().StoryMaterialsRelease
	list, err := repo.NewStoryMaterialsReleaseRepo(storyMaterialsReleaseSchema.WithContext(s.ctx)).
		SelectList(search.NewQueryBuilder().In(storyMaterialsReleaseSchema.UserItemID, req.UserItemIds).
			Eq(storyMaterialsReleaseSchema.Status, enums.ReleaseCreated.Val()).Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("StoryMaterialsGetReleaseTime SelectList err:%v", err)
		return resp, nil
	}
	for _, item := range list {
		if item.ReleaseTime != nil {
			resp.List = append(resp.List, &define.StoryMaterialsGetReleaseTimeData{
				ReleaseTime: item.ReleaseTime.UnixMilli(),
				UserItemId:  item.UserItemID,
			})
		}
	}
	return resp, nil
}

// StoryOrderComplete 探索完成
func (s *Service) StoryOrderComplete() (*define.StoryOrderCompleteResp, error) {
	executor := asyncutil.NewAsyncExecutor(1, asyncutil.WithContext(util.NewCtx(s.ctx)))
	spanContext := s.NewContextWithSpanContext(s.ctx)
	executor.Execute(func(ctx context.Context) (interface{}, error) {
		_, err := logic.StoryOrderComplete(spanContext)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	return nil, nil
}
