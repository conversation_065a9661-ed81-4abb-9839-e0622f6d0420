package service

import (
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/business/story/service/logic"
	"app_service/apps/platform/common/constant"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"golang.org/x/sync/errgroup"
	"time"
)

// GetStorySceneWebList 查询故事玩法场景列表
func (s *Service) GetStorySceneWebList(req *define.GetStorySceneWebListReq) (*define.GetStorySceneWebListResp, error) {
	// 1. 获取场景列表基础数据
	storySceneSchema := repo.GetQuery().StoryScene
	scenes, err := repo.NewStorySceneRepo(storySceneSchema.WithContext(s.ctx)).GetStorySceneWebLimitFour()
	if err != nil {
		return nil, fmt.Errorf("获取场景列表失败: %w", err)
	}

	if len(scenes) == 0 {
		return &define.GetStorySceneWebListResp{}, nil
	}

	// 2. 并行获取需要的数据
	var (
		storyNumMap          map[int64]int32
		sceneId2UserNameList map[int64][]string
		eg                   errgroup.Group
	)

	if req.NeedStoryNum == constant.Yes {
		// 并行获取故事数量和用户名列表
		eg.Go(func() error {
			var err error
			storyNumMap, err = logic.GetStoryNumByScene(s.ctx)
			if err != nil {
				return fmt.Errorf("获取故事数量失败: %w", err)
			}
			return nil
		})

		eg.Go(func() error {
			sceneIds := make([]int64, len(scenes))
			for i, v := range scenes {
				sceneIds[i] = v.ID
			}

			var err error
			sceneId2UserNameList, err = logic.GetStoryUserNameLastTwentyByScenesWithCache(s.ctx, sceneIds)
			if err != nil {
				log.Ctx(s.ctx).Errorf("获取用户名列表失败: %v", err)
				// 不阻断主流程，返回nil错误
			}
			return nil
		})

		if err := eg.Wait(); err != nil {
			return nil, err
		}
	}

	// 3. 构建响应数据
	resp := &define.GetStorySceneWebListResp{
		List: make([]*define.GetStorySceneWebListData, 0, len(scenes)),
	}

	for _, scene := range scenes {
		data := &define.GetStorySceneWebListData{
			ID:   scene.ID,
			Name: scene.Name,
		}

		if req.NeedStoryNum == constant.Yes {
			// 故事数量
			if num, exists := storyNumMap[scene.ID]; exists {
				data.StoryNum = num
			}

			// 封面URL
			if scene.CoverURL != "" {
				data.CoverURL = &scene.CoverURL
			}

			// 用户名列表
			if data.StoryNum > 0 {
				if names, exists := sceneId2UserNameList[scene.ID]; exists {
					data.UserNameList = names
				}
			}
		}

		resp.List = append(resp.List, data)
	}

	return resp, nil
}

// GetStorySceneWebHome 查询故事玩法场景首页列表
func (s *Service) GetStorySceneWebHome(req *define.GetStorySceneWebHomeReq) (*define.GetStorySceneWebHomeResp, error) {
	storySchema := repo.GetQuery().Story
	dataList, err := repo.NewStoryRepo(storySchema.WithContext(s.ctx)).GetStorySceneWebHome()
	resp := &define.GetStorySceneWebHomeResp{}
	if err != nil {
		log.Ctx(s.ctx).Errorf("获取故事列表失败: %v", err)
		return resp, err
	}
	list := make([]*define.GetStorySceneWebHomeData, 0, len(dataList))
	now := time.Now()
	for _, v := range dataList {
		data := &define.GetStorySceneWebHomeData{
			ID: v.ID,
		}
		scene, _ := logic.GetStoryScene(s.ctx, v.ID)
		data.Name = scene.Name
		if now.Before(v.StartTime) {
			data.Status = enums.StoryStatusWait.Val() // 未开始
		} else {
			data.Status = enums.StoryStatusIng.Val()
		}
		list = append(list, data)
	}
	resp.List = list
	return resp, nil
}
