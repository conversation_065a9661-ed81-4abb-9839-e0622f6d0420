package locker

import (
	"fmt"
	"time"
)

// StoryAction 故事玩法相关行为枚举
type StoryAction string

const (
	Discovery           StoryAction = "discovery"           // 发起故事探索
	DiscoveryUserItemId StoryAction = "discovery_user_item" // 发起故事探索的物品
	Receive             StoryAction = "receive"             // 领取故事玩法奖励
)

type StoryLock struct {
	ac  StoryAction // 行为
	tag string      // 唯一标识
}

func (p *StoryLock) GetCacheKey() string {
	return fmt.Sprintf("story:%s:%s", p.ac, p.tag)
}

func (p *StoryLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewStoryLock(tag string, ac StoryAction) *StoryLock {
	return &StoryLock{tag: tag, ac: ac}
}
