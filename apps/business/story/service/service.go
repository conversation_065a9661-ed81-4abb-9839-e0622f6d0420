package service

import (
	"app_service/pkg/middlewares/g/auth"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go.opentelemetry.io/otel/trace"
)

// Service 业务服务
// 传递上下文
type Service struct {
	ctx context.Context
}

func New(ctx context.Context) *Service {
	return &Service{
		ctx: ctx,
	}
}

func (s *Service) NewContextWithSpanContext(parent context.Context) context.Context {
	spanContext := trace.SpanContextFromContext(parent)
	return trace.ContextWithSpanContext(context.Background(), spanContext)
}

// GetAdminId 获取当前登录用户id
func (s *Service) GetAdminId() string {
	info, ok := auth.GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetAdmin 获取当前登录用户
func (s *Service) GetAdmin() *pat.CheckAdmJwtUserInfo {
	info, ok := auth.GetAdminFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil
	}
	return info
}

// GetUserId 获取当前登录用户id
func (s *Service) GetUserId() string {
	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}

// GetUserFromCtx 获取当前登录用户id
func (s *Service) GetUserFromCtx() *pat.CheckUserJwtUserInfo {
	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return nil
	}
	return info
}
