package logic

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	commonFacade "app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"time"
)

// StoryOrderUpChain 故事玩法订单上链
func StoryOrderUpChain(ctx context.Context, req *define.StoryOrderUpChainReq) (*define.StoryOrderUpChainResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder()
	if req.OrderId != "" {
		builder.Eq(storyOrderSchema.OrderID, req.OrderId)
	}
	statusList := []int32{enums.StoryChainStatusDisable.Val(), enums.StoryChainStatusWaiting.Val()}
	builder = builder.In(storyOrderSchema.ChainStatus, statusList).Eq(storyOrderSchema.Status, enums.StoryOrderStatusDone.Val())
	pageSize := 50
	page := 1

	for {
		list, _, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectPage(builder.Build(), page, pageSize)
		if err != nil {
			log.Ctx(ctx).Errorf("Failed to get story admin order list: %v", err)
			return nil, err
		}

		if len(list) == 0 {
			break
		}

		for _, item := range list {
			if err := processStoryOrder(ctx, item); err != nil {
				log.Ctx(ctx).Errorf("Failed to process story order %v: %v", item.OrderID, err)
			}
		}

		if len(list) < pageSize {
			break
		}

		time.Sleep(1000 * time.Millisecond)
		page++
	}
	return &define.StoryOrderUpChainResp{}, nil
}

func processStoryOrder(ctx context.Context, order *model.StoryOrder) error {
	storyOrderSchema := repo.GetQuery().StoryOrder
	statusList := []int32{enums.StoryChainStatusDisable.Val(), enums.StoryChainStatusWaiting.Val()}
	// 更新订单状态为等待上链
	updateOrder := &model.StoryOrder{ChainStatus: enums.StoryChainStatusWaiting.Val()}
	if err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).Update(updateOrder,
		search.NewQueryBuilder().Eq(storyOrderSchema.ID, order.ID).In(storyOrderSchema.ChainStatus, statusList).Build()); err != nil {
		if err != repo.UpdateFail {
			return fmt.Errorf("failed to update order status to waiting: %v", err)
		}
	}
	storySchema := repo.GetQuery().Story
	story, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectOne(search.NewQueryBuilder().Eq(storySchema.ID, order.StoryID).Build())
	if err != nil {
		return err
	}
	userInfo, err := userFacade.GetNodeUser(ctx, order.UserID)
	if err != nil {
		log.Ctx(ctx).Errorf("GetStoryAdminOrderDetail GetNodeUser err:%v", err)
	}
	// 上链请求
	req := &tmt.SynthesisOrderUpChainReq{
		OrderId:     order.OrderID,
		OrderType:   1,
		ChainDataId: order.ChainDataID,
	}
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, story.ItemID)
	if err != nil {
		return err
	}

	req.ItemName = issueItem.ItemName
	req.ItemId = issueItem.ItemID.Hex()
	req.SkuNo = issueItem.SkuNo
	req.ItemChainHash = issueItem.ChainHash
	req.BenefitName = ""
	req.BenefitID = ""
	req.Price = issueItem.Price
	req.Quantity = order.Qty
	req.TotalAmount = issueItem.Price * order.Qty
	req.PayAmount = 0
	req.TradeType = 5
	req.Fee = 0
	req.FromUserName = ""
	req.FromUserId = ""
	req.FromItemHash = ""
	req.ToUserName = userInfo.PatbgDetail.Nickname
	req.ToUserId = order.UserID
	req.CreatedAt = time.Unix(order.CreatedAt.Unix(), 0).UTC().Format("2006-01-02 15:04:05")
	req.PaidAt = ""
	if order.FinishTime != nil && !order.FinishTime.IsZero() {
		req.FinishedAt = time.Unix(order.FinishTime.Unix(), 0).UTC().Format("2006-01-02 15:04:05")
	} else {
		req.FinishedAt = time.Now().UTC().Format("2006-01-02 15:04:05")
	}
	req.CancelApplyAt = ""
	req.CancelDoneAt = ""

	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	builder := search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, order.ID)
	orderDetailList, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		return err
	}
	var itemIds []string
	itemIds2Qty := make(map[string]int32)
	for _, item := range orderDetailList {
		itemIds = append(itemIds, item.MaterialsItemID)
		itemIds2Qty[item.MaterialsItemID]++

	}
	issueItems, err := issueFacade.GetIssueItemMap(ctx, itemIds)
	if err != nil {
		log.Ctx(ctx).Errorf("processStoryOrder GetIssueItemMap err:%v", err)
		return nil
	}
	var fusionMaterials string
	for _, item := range orderDetailList {
		if issueItem, ok := issueItems[item.MaterialsItemID]; ok {
			fusionMaterials += fmt.Sprintf("%s-%s-%d;", issueItem.ID.Hex(), item.MaterialsItemName, itemIds2Qty[item.MaterialsItemID])
		}
	}
	req.FusionMaterial = "{" + fusionMaterials + "}"

	resp, err := tmt.SynthesisOrderUpChain(ctx, req)
	if err != nil {
		log.Ctx(ctx).Errorf("processStoryOrder StoryOrderUpChain req:%+v, err:%v", req, err)
	}

	switch resp.Status {
	case tmt.SynthesisOrderUpChainSuccess:
		log.Ctx(ctx).Infof("StoryOrderUpChain Success, OrderId:%v", order.OrderID)
		// 更新订单状态为完成
		updateOrder = &model.StoryOrder{
			ID:          order.ID,
			ChainHash:   resp.ChainHash,
			ChainDataID: resp.ChainDataId,
		}
		if resp.ChainHash != "" {
			updateOrder.ChainStatus = enums.StoryChainStatusDone.Val()
		}
		if err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).UpdateById(updateOrder); err != nil {
			return fmt.Errorf("failed to update order status to done: %v", err)
		}
	case tmt.SynthesisOrderUpChainFail:
		log.Ctx(ctx).Errorf("StoryOrderUpChain Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 更新订单状态为失败
		updateOrder = &model.StoryOrder{
			ID:          order.ID,
			ChainStatus: enums.StoryChainStatusFailed.Val(),
		}
		if err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).UpdateById(updateOrder); err != nil {
			return fmt.Errorf("failed to update order status to failed: %v", err)
		}
	default:
		log.Ctx(ctx).Errorf("StoryOrderUpChain Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
	}

	return nil
}

// StoryMaterialsRelease 故事玩法材料释放
func StoryMaterialsRelease(ctx context.Context, req *define.StoryMaterialsReleaseReq) (*define.StoryMaterialsReleaseResp, error) {
	storyMaterialsReleaseSchema := repo.GetQuery().StoryMaterialsRelease

	builder := search.NewQueryBuilder()
	if req.Id != 0 {
		builder.Eq(storyMaterialsReleaseSchema.ID, req.Id)
	}
	statusList := []int32{enums.ReleaseCreated.Val(), enums.ReleaseIng.Val()}
	builder = builder.In(storyMaterialsReleaseSchema.Status, statusList)
	builder = builder.Lt(storyMaterialsReleaseSchema.ReleaseTime, time.Now())
	pageSize := 1000
	page := 1

	for {
		list, count, err := repo.NewStoryMaterialsReleaseRepo(storyMaterialsReleaseSchema.WithContext(ctx)).SelectPage(builder.Build(), page, pageSize)
		if err != nil {
			log.Ctx(ctx).Errorf("Failed to get storyMaterialsRelease list: %v", err)
			return nil, err
		}

		if count == 0 {
			break
		}

		if err = processStoryMaterialsRelease(ctx, list); err != nil {
			log.Ctx(ctx).Errorf("Failed to process storyMaterialsRelease page:%v, err:%v", page, err)
		}

		if len(list) < pageSize {
			break
		}

		time.Sleep(1000 * time.Millisecond)
	}
	return nil, nil
}

func processStoryMaterialsRelease(ctx context.Context, materialsReleases []*model.StoryMaterialsRelease) error {
	var userItemIds []string
	var ids []int64
	for _, item := range materialsReleases {
		userItemIds = append(userItemIds, item.UserItemID)
		ids = append(ids, item.ID)
	}
	fusionRes, err := yc_open.CancelStory(ctx, "", "", userItemIds)
	if fusionRes == yc_open.CancelFusionUnknown {
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("processStoryMaterialsRelease Unknown Exception, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法材料，释放失败，未知异常】", fmt.Sprintf("userItemIds: %+v, 错误信息: %v", util.Obj2JsonStr(userItemIds), err))
	} else {
		updateStatus := enums.ReleaseSuccess.Val()
		if fusionRes == yc_open.CancelFusionSuccess {
			// 明确成功
			log.Ctx(ctx).Infof("processStoryMaterialsRelease Success, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		} else if fusionRes == yc_open.CancelFusionFail {
			updateStatus = enums.ReleaseFail.Val()
			// 明确失败
			log.Ctx(ctx).Infof("processStoryMaterialsRelease Fail, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 发起告警【故事玩法材料，释放失败，明确失败】
			commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法材料，释放失败，明确失败】", fmt.Sprintf("userItemIds: %+v, 错误信息: %v", util.Obj2JsonStr(userItemIds), err))
		}
		now := time.Now()
		updateStoryMaterialsRelease := &model.StoryMaterialsRelease{
			Status:          updateStatus,
			RealReleaseTime: &now,
		}
		statusList := []int32{enums.ReleaseCreated.Val(), enums.ReleaseIng.Val()}
		storyMaterialsReleaseSchema := repo.GetQuery().StoryMaterialsRelease
		err = repo.NewStoryMaterialsReleaseRepo(storyMaterialsReleaseSchema.WithContext(ctx)).Update(updateStoryMaterialsRelease,
			search.NewQueryBuilder().In(storyMaterialsReleaseSchema.ID, ids).In(storyMaterialsReleaseSchema.Status, statusList).Build())
		if err != nil && err != repo.UpdateFail {
			log.Ctx(ctx).Errorf("processStoryMaterialsRelease Success, Update Status err:%v, id:%v", err, util.Obj2JsonStr(ids))
			return err
		}
		return nil
	}
	return nil
}

// StoryOrderComplete 探索完成
func StoryOrderComplete(ctx context.Context) (*define.StoryOrderCompleteResp, error) {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Gt(storySchema.EndTime, time.Now().Add(-24*time.Hour)).Lt(storySchema.EndTime, time.Now())
	storyList, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		log.Ctx(ctx).Errorf("Failed to get story list: %v", err)
		return nil, err
	}
	storyOrderSchema := repo.GetQuery().StoryOrder
	var sceneIds []int64
	for _, story := range storyList {
		sceneIds = append(sceneIds, story.SceneID)
		now := time.Now()
		updateOrder := &model.StoryOrder{
			Status:       enums.StoryOrderStatusFinish.Val(),
			CompleteTime: &now,
		}
		updateBuilder := search.NewQueryBuilder().Eq(storyOrderSchema.StoryID, story.ID).Eq(storyOrderSchema.Status, enums.StoryOrderStatusSuccess.Val())
		err = repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).Update(updateOrder, updateBuilder.Build())
		if err != nil && err != repo.UpdateFail {
			log.Ctx(ctx).Errorf("Failed to update order status to done: %v", err)
			return nil, err
		}
	}
	for _, sceneId := range sceneIds {
		// 删除缓存
		err = DelStorySceneUserNameListCache(ctx, sceneId)
		if err != nil {
			log.Ctx(ctx).Errorf("StoryOrderComplete Success, DelStorySceneUserNameListCache err:%v, sceneId:%v", err, sceneId)
		}
	}
	return nil, nil
}
