package logic

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"gorm.io/datatypes"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"time"
)

type StoryOrderDetailUQData struct {
	MaterialsItemID   string
	MaterialsItemName string
	MaterialsItemURL  string
	Qty               int32
}

type SaveStoryOrderData struct {
	UserId        string
	Qty           int32
	Story         *model.Story
	UserItemIdMap map[string][]string
}

type MaterialsItemInfo struct {
	Ip         string `json:"ip"`
	Issuer     string `json:"issuer"`
	IssuePrice int32  `json:"issue_price"`
	Code       string `json:"code"`
}

// GetStoryOrderDetailUQ 根据故事玩法订单ID获取订单故事玩法材料列表，根据物品id去重
func GetStoryOrderDetailUQ(ctx context.Context, id int64) ([]StoryOrderDetailUQData, error) {
	data := make([]StoryOrderDetailUQData, 0)
	s := repo.GetQuery().StoryOrderDetail
	gq := s.WithContext(ctx).
		Select(s.ID.Count().As("qty"), s.ID.Min().As("id"), s.MaterialsItemID).
		Where(s.StoryOrderID.Eq(id)).
		Group(s.StoryOrderID, s.MaterialsItemID)
	err := s.WithContext(ctx).
		Select(s.ID, s.MaterialsItemName, s.MaterialsItemURL, field.NewString("gquery", "qty")).
		Join(gq.As("gquery"), s.ID.EqCol(field.NewString("gquery", "id"))).
		Scan(&data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// GetStoryOrderUserNum 查询故事玩法数量
func GetStoryOrderUserNum(ctx context.Context, storyId int64, userId string) (int32, error) {
	statusList := []int32{enums.StoryOrderStatusWaiting.Val(), enums.StoryOrderStatusSuccess.Val(), enums.StoryOrderStatusFinish.Val(), enums.StoryOrderStatusDone.Val()}
	exec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum(qty), 0) AS num").
		Table("`story_order`").
		Where("story_id = ? and user_id = ? and status in ? and is_del = 0", storyId, userId, statusList)
	totalNum := int32(0)
	err := exec.Find(&totalNum).Error
	if err != nil {
		return 0, err
	}
	return totalNum, err
}

// SaveStoryOrder 保存故事玩法订单
func SaveStoryOrder(ctx context.Context, data *SaveStoryOrderData) (*model.StoryOrder, error) {
	userId := data.UserId
	qty := data.Qty
	story := data.Story
	userItemIdMap := data.UserItemIdMap
	order := &model.StoryOrder{
		OrderID:      util.StrVal(snowflakeutl.GenerateID()),
		StoryID:      story.ID,
		StoryTitle:   story.Title,
		ItemID:       story.ItemID,
		ItemTitle:    story.ItemTitle,
		ItemCoverURL: story.ItemImageURL,
		UserID:       userId,
		Status:       enums.StoryOrderStatusWaiting.Val(),
		Qty:          qty,
		AppChannel:   fmt.Sprintf("%v", ctx.Value(constant.AppChannel)),
		AppVersion:   fmt.Sprintf("%v", ctx.Value(constant.AppVersion)),
		IP:           fmt.Sprintf("%v", ctx.Value(constant.Ip)),
	}
	scene, _ := GetStoryScene(ctx, story.SceneID)
	order.SceneID = story.SceneID
	order.SceneName = scene.Name
	json, err := GetItemInfoJson(ctx, story)
	if err != nil {
		return nil, err
	}
	order.ItemInfo = json
	orderDetailList, err := buildStoryOrderDetailList(ctx, userItemIdMap, story.ID)
	if err != nil {
		return nil, err
	}
	// 3 扣减总库存
	stockRes, _ := StoryStock(ctx, story.ItemID, qty)
	if stockRes != tmt.StoryStockSuccess {
		return nil, define.SH1100015Err
	}
	err = repo.ExecGenTx(ctx, func(tx context.Context) error {
		storySchema := repo.GetQuery().Story
		// 1. 查询库存并加锁（SELECT FOR UPDATE）
		currentStock, err := repo.NewStoryRepo(repo.Query(tx).Story.WithContext(tx)).StoryStockSelectForUpdate(story.ID)
		if err != nil {
			return err
		}

		// 2. 检查库存是否足够
		if currentStock < qty {
			return define.SH1100015Err // 库存不足
		}
		// 扣故事玩法库存
		updateParams := map[string]interface{}{
			"stock": gorm.Expr("stock - ?", qty),
		}
		err = repo.NewStoryRepo(repo.Query(tx).Story.WithContext(tx)).UpdateField(updateParams, search.NewQueryBuilder().Eq(storySchema.ID, story.ID).
			Gte(storySchema.Stock, qty).Build())
		if err != nil {
			return define.SH1100015Err
		}
		err = repo.NewStoryOrderRepo(repo.Query(tx).StoryOrder.WithContext(tx)).Save(order)
		if err != nil {
			return err
		}
		for _, detail := range orderDetailList {
			detail.StoryOrderID = order.ID
		}
		err = repo.NewStoryOrderDetailRepo(repo.Query(tx).StoryOrderDetail.WithContext(tx)).BatchSave(orderDetailList, 100)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		// 扣减失败，恢复库存
		stockRes, _ = StoryStock(ctx, story.ItemID, -qty)
	}
	return order, err
}

func GetItemInfoJson(ctx context.Context, story *model.Story) (datatypes.JSON, error) {
	itemId := story.ItemID
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	if err != nil {
		return nil, err
	}
	materialsItemInfo := &MaterialsItemInfo{
		Issuer:     issueItem.IssuerName,
		IssuePrice: issueItem.Price,
		Code:       issueItem.ID.Hex(),
	}
	var jsonData datatypes.JSON
	if len(issueItem.IPClassifyNames) > 0 {
		materialsItemInfo.Ip = issueItem.IPClassifyNames[0]
	}
	jsonData = datatypes.JSON(util.Obj2JsonStr(materialsItemInfo))
	return jsonData, err
}

func buildStoryOrderDetailList(ctx context.Context, userItemIdMap map[string][]string, storyId int64) ([]*model.StoryOrderDetail, error) {
	goodsItemIds := make([]string, 0)
	for itemId, _ := range userItemIdMap {
		goodsItemIds = append(goodsItemIds, itemId)
	}
	goodsItemMap, err := issueFacade.GetIssueItemMap(ctx, goodsItemIds)
	if err != nil {
		return nil, err
	}
	itemDestroyMap, err := GetItemMaterialsDestroyMap(ctx, storyId)
	if err != nil {
		return nil, err
	}
	orderDetailList := make([]*model.StoryOrderDetail, 0)
	for itemId, userItemIds := range userItemIdMap {
		// 根据itemId获取物品信息
		var materialsItemID string
		var materialsItemName string
		var materialsItemURL string
		var jsonData datatypes.JSON
		if _, ok := goodsItemMap[itemId]; ok {
			issueItem := goodsItemMap[itemId]
			materialsItemID = itemId
			materialsItemName = issueItem.ItemName
			materialsItemURL = issueItem.ImageURL
			materialsItemInfo := &MaterialsItemInfo{
				Issuer:     issueItem.IssuerName,
				IssuePrice: issueItem.Price,
				Code:       issueItem.ID.Hex(),
			}
			if len(issueItem.IPClassifyNames) > 0 {
				materialsItemInfo.Ip = issueItem.IPClassifyNames[0]
			}
			jsonData = datatypes.JSON(util.Obj2JsonStr(materialsItemInfo))
		} else {
			return nil, define.SH1100014Err
		}
		for _, userItemId := range userItemIds {
			orderDetail := &model.StoryOrderDetail{
				MaterialsItemID:   materialsItemID,
				MaterialsItemName: materialsItemName,
				MaterialsItemURL:  materialsItemURL,
				MaterialsItemInfo: &jsonData,
				UserItemID:        userItemId,
			}
			if itemDestroyMap != nil && itemDestroyMap[itemId] {
				orderDetail.IsDestroy = constant.Yes
			} else {
				orderDetail.IsDestroy = constant.No
			}
			orderDetailList = append(orderDetailList, orderDetail)
		}
	}
	return orderDetailList, nil
}

// StoryFusion 故事玩法融合调用yc
func StoryFusion(ctx context.Context, storyOrderID int64) error {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().Eq(storyOrderSchema.ID, storyOrderID).Eq(storyOrderSchema.Status, enums.StoryOrderStatusWaiting.Val())
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, order.ID)
	orderDetail, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	var userItemIds = make([]string, 0)
	var storyDestroyUserItemIds = make([]string, 0)
	for _, detail := range orderDetail {
		userItemIds = append(userItemIds, detail.UserItemID)
		if detail.IsDestroy == constant.Yes {
			storyDestroyUserItemIds = append(storyDestroyUserItemIds, detail.UserItemID)
		}
	}
	log.Ctx(ctx).Infof("StoryFusion openUserId:%+v, orderId:%+v, userItemIds:%+v", openUserId, order.OrderID, util.Obj2JsonStr(userItemIds))
	fusionRes, err := yc_open.StoryFusion(ctx, openUserId, order.OrderID, userItemIds, storyDestroyUserItemIds)
	var updateStatus int32
	switch fusionRes {
	case yc_open.FusionSuccess:
		log.Ctx(ctx).Infof("StoryFusion Fusion Success, OrderId:%v", order.OrderID)
		updateStatus = enums.StoryOrderStatusSuccess.Val()
	case yc_open.FusionFail:
		log.Ctx(ctx).Errorf("StoryFusion Fusion Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		updateStatus = enums.StoryOrderStatusFail.Val()
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("StoryFusion Fusion Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 告警 材料消耗，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法消耗材料未知异常】", fmt.Sprintf("故事玩法订单Id: %+v, 错误信息: %v", storyOrderID, err))
		return common_define.CommonErr
	}
	// 材料消耗成功,订单状态变更
	updateStoryOrder := &model.StoryOrder{ID: storyOrderID, Status: updateStatus}
	err = repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).UpdateById(updateStoryOrder)
	if err != nil {
		log.Ctx(ctx).Errorf("StoryFusion Fusion Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
		return err
	}
	//材料消耗成功,等活动到期再发放奖励
	if updateStatus == enums.StoryOrderStatusSuccess.Val() {
		// 新增故事玩法材料退回
		err = SaveStoryMaterialsRelease(ctx, order.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, SaveStoryMaterialsRelease err:%v, OrderId:%v", err, order.OrderID)
		}
		// 删除缓存
		err = DelStorySceneUserNameListCache(ctx, order.SceneID)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, DelStorySceneUserNameListCache err:%v, sceneId:%v", err, order.SceneID)
		}
	} else if updateStatus == enums.StoryOrderStatusFail.Val() {
		// 退库存
		err = IncrByStoryStock(ctx, order)
		if err != nil {
			log.Ctx(ctx).Errorf("StoryFusion Fusion Fail, IncrByStoryStock err:%v, OrderId:%v", err, order.OrderID)
		}
	}
	return nil
}

// StoryCancelFusion 取消物品融合调用yc
func StoryCancelFusion(ctx context.Context, storyOrderID int64) error {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().Eq(storyOrderSchema.ID, storyOrderID).Eq(storyOrderSchema.Status, enums.StoryOrderStatusSuccess.Val())
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, order.ID)
	orderDetail, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	var userItemIds = make([]string, 0)
	for _, detail := range orderDetail {
		userItemIds = append(userItemIds, detail.UserItemID)
	}
	fusionRes, err := yc_open.CancelStory(ctx, openUserId, order.OrderID, userItemIds)
	if fusionRes == yc_open.CancelFusionUnknown {
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("StoryCancelFusion CancelFusion Unknown Exception, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法物品发放失败，材料已消耗，退回失败，未知异常】", fmt.Sprintf("故事玩法订单Id: %+v, 错误信息: %v", storyOrderID, err))
		return common_define.CommonErr
	} else {
		if fusionRes == yc_open.CancelFusionSuccess {
			// 明确成功
			log.Ctx(ctx).Infof("StoryCancelFusion CancelFusion Success, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 退库存
			err = IncrByStoryStock(ctx, order)
			if err != nil {
				log.Ctx(ctx).Errorf("StoryCancelFusion Fail, IncrByStoryStock err:%v, OrderId:%v", err, order.OrderID)
			}
		} else if fusionRes == yc_open.CancelFusionFail {
			// 明确失败
			log.Ctx(ctx).Infof("StoryCancelFusion CancelFusion Fail, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 发起告警【故事玩法物品发放失败，材料已消耗，退回失败】
			commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法物品发放失败，材料已消耗，退回失败，明确失败】", fmt.Sprintf("故事玩法订单Id: %+v, 错误信息: %v", storyOrderID, err))
		}
		// 不管材料退回成功还是失败,订单状态都变成融合失败
		updateStoryOrder := &model.StoryOrder{ID: storyOrderID, Status: enums.StoryOrderStatusFail.Val()}
		err = repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).UpdateById(updateStoryOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("StoryCancelFusion CancelFusion Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
			return err
		}
		return nil
	}
}

// StoryItemIssue 奖励发放调用yc
func StoryItemIssue(ctx context.Context, storyOrderID int64) error {
	storyOrderSchema := repo.GetQuery().StoryOrder
	builder := search.NewQueryBuilder().Eq(storyOrderSchema.ID, storyOrderID).Eq(storyOrderSchema.Status, enums.StoryOrderStatusReceive.Val())
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	items, err := GetBatchInsertItems(ctx, order, openUserId)
	if err != nil {
		return err
	}
	form := &yc_open.BatchInsertUserItemReq{
		OpenUserId:    openUserId,
		ReceiveType:   127,
		Items:         items,
		SourceOrderId: order.OrderID,
	}

	res, _ := yc_open.BatchInsertUserItem(ctx, form)
	switch res {
	case yc_open.BatchInsertUserItemSuccess:
		log.Ctx(ctx).Infof("BatchInsertUserItem Success, OrderId:%v", order.OrderID)
		// 材料消耗成功,订单状态变更
		now := time.Now()
		updateStoryOrder := &model.StoryOrder{ID: storyOrderID, Status: enums.StoryOrderStatusDone.Val(), FinishTime: &now}
		err = repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).UpdateById(updateStoryOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
			return err
		}
		// 新增已合人数
		err = StoryCompleteUserNum(ctx, order.StoryID)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, StoryCompleteUserNum err:%v, OrderId:%v", err, order.OrderID)
		}
		return nil
	case yc_open.BatchInsertUserItemFail:
		log.Ctx(ctx).Errorf("BatchInsertUserItem Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 明确失败,材料不退回 发送告警
		commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法物品发放失败，材料已消耗，材料不退回，明确失败】", fmt.Sprintf("故事玩法订单Id: %+v, 错误信息: %v", storyOrderID, err))
		return nil
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("BatchInsertUserItem Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 告警 材料消耗，未发放成功，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【故事玩法物品发放失败，材料已消耗，未知异常】", fmt.Sprintf("故事玩法订单Id: %+v, 错误信息: %v", storyOrderID, err))
		return common_define.CommonErr
	}
}

// GetItemMaterialsDestroyMap 获取物品材料是否销毁
func GetItemMaterialsDestroyMap(ctx context.Context, storyId int64) (map[string]bool, error) {
	storyMaterialsSchema := repo.GetQuery().StoryMaterials
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, storyId)
	storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	data := make(map[string]bool, 0)
	for _, material := range storyMaterialsList {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, materials := range materialsData {
			data[materials.ItemId] = materials.Destroy == constant.Yes
		}
	}
	return data, nil
}

// StoryStock 物品库存扣减
func StoryStock(ctx context.Context, itemId string, quantity int32) (int32, error) {
	res, _ := tmt.StoryStock(ctx, &tmt.StoryStockReq{ItemId: itemId, Quantity: quantity})
	actionDesc := "【探索物品库存扣减"
	if quantity < 0 {
		actionDesc = "库存增加"
	} else {
		actionDesc = "库存扣减"
	}
	switch res.Status {
	case tmt.StoryStockSuccess:
		log.Ctx(ctx).Infof("StoryStock Success, itemId:%v, quantity:%v", itemId, quantity)
	case tmt.StoryStockFail:
		commonFacade.SendDefaultWarnMsg(ctx, fmt.Sprintf("【探索物品%s失败】", actionDesc), fmt.Sprintf("物品Id: %v, 数量: %v", itemId, quantity))
		log.Ctx(ctx).Errorf("StoryStock Fail, itemId:%v, quantity:%v", itemId, quantity)
	default:
		// 告警
		commonFacade.SendDefaultWarnMsg(ctx, fmt.Sprintf("【探索物品%s响应未知】", actionDesc), fmt.Sprintf("物品Id: %v, 数量: %v", itemId, quantity))
		log.Ctx(ctx).Errorf("StoryStock Unknown Exception, itemId:%v, quantity:%v", itemId, quantity)
	}
	return res.Status, nil
}

func GetBatchInsertItems(ctx context.Context, order *model.StoryOrder, openUserId string) ([]*yc_open.BatchInsertItem, error) {
	storySchema := repo.GetQuery().Story
	story, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectOne(
		search.NewQueryBuilder().Eq(storySchema.ID, order.StoryID).Build(),
	)
	if err != nil {
		return nil, err
	}

	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, story.ItemID)
	if err != nil {
		return nil, err
	}

	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	orderDetailList, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(ctx)).SelectList(
		search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, order.ID).Build(),
	)
	if err != nil {
		return nil, err
	}

	var needUserItemIds []string
	for _, detail := range orderDetailList {
		if detail.IsDestroy == constant.Yes {
			needUserItemIds = append(needUserItemIds, detail.UserItemID)
		}
	}

	var totalCost int64
	if len(needUserItemIds) > 0 {
		totalCost, err = yc_open.SumUserItemsCostPrice(ctx, openUserId, needUserItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("SumUserItemsCostPrice failed: %v", err)
			return nil, fmt.Errorf("failed to sum user item cost price: %w", err)
		}
	}

	qty := int(order.Qty)
	if qty <= 0 {
		return nil, common_define.CommonWarnErr
	}

	// 平均分配成本
	costPerItem := totalCost / int64(qty)
	remainder := totalCost % int64(qty)

	items := make([]*yc_open.BatchInsertItem, 0, qty)
	for i := 0; i < qty; i++ {
		item := &yc_open.BatchInsertItem{
			ItemId:     story.ItemID,
			ItemNum:    1,
			SaleMode:   int32(issueItem.SaleMode),
			StoryTags:  int32(issueItem.Story.Status),
			FusionTags: int32(issueItem.Synthesis.Status),
		}

		if issueItem.DeliveryTime != nil {
			item.DeliveryTime = issueItem.DeliveryTime
		}

		// 前 n-1 个使用平均值，最后一个加上余数
		if i < qty-1 {
			item.BuyPrice = costPerItem
		} else {
			item.BuyPrice = costPerItem + remainder
		}

		items = append(items, item)
	}

	return items, nil
}
