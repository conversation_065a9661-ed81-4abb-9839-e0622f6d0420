package logic

import (
	"app_service/apps/business/story/define/enums"
	"context"
	"encoding/json"

	"app_service/apps/business/story/define"
	"gorm.io/datatypes"

	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/repo"
	issueFacade "app_service/apps/platform/issue/facade"
)

// GetStoryMap 根据id获取故事玩法
func GetStoryMap(ctx context.Context, ids []int64) (map[int64]*model.Story, error) {
	s := repo.GetQuery().Story
	list, err := s.WithContext(ctx).
		Where(s.ID.In(ids...)).Find()
	if err != nil {
		return nil, err
	}
	res := make(map[int64]*model.Story)
	for _, item := range list {
		res[item.ID] = item
	}
	return res, nil
}

// UnmarshalMaterialsData 解析材料数据
func UnmarshalMaterialsData(materialsData datatypes.JSON) ([]*define.StoryMaterialsData, error) {
	var data []*define.StoryMaterialsData
	if err := json.Unmarshal(materialsData, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// GetStoryTotalStock 查询故事玩法上架的总库存(加上已使用的库存)
func GetStoryTotalStock(ctx context.Context, itemId string) (int32, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum( total_stock ), 0) AS sum").
		Table("`story`").
		Where("status = ? and item_id = ? and is_del = 0", enums.StoryStatusUp.Val(), itemId)
	totalStock := int32(0)
	err := exec.Find(&totalStock).Error
	if err != nil {
		return 0, err
	}

	useExec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum( total_stock - stock ), 0) AS sum").
		Table("`story`").
		Where("status != ? and item_id = ? and is_del = 0", enums.StoryStatusUp.Val(), itemId)
	totalUseStock := int32(0)
	err = useExec.Find(&totalUseStock).Error
	if err != nil {
		return 0, err
	}
	return totalStock + totalUseStock, err
}

// GetStoryUsableStock 获取可用库存（总库存-故事玩法已使用总库存-空投使用的）
func GetStoryUsableStock(ctx context.Context, itemId string) (int32, error) {
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	if err != nil {
		return 0, err
	}
	totalStock, err := GetStoryTotalStock(ctx, itemId)
	if err != nil {
		return 0, err
	}
	return issueItem.Quantity - issueItem.AirdropQuantity - totalStock, nil
}

// GetStoryItemImageUrl 获取故事玩法故事玩法物品图片
func GetStoryItemImageUrl(ctx context.Context, itemId string) (string, error) {
	item, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	if err != nil {
		return "", err
	}
	return item.ImageURL, nil
}
