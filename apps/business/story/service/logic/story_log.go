package logic

import (
	"context"
	"encoding/json"
	"reflect"

	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

type StoryLog struct {
	ctx        context.Context
	action     enums.LogAction
	id         int64
	content    map[string]any
	oldContent map[string]any
}

func NewStoryLog(ctx context.Context, action enums.LogAction, storyID int64) *StoryLog {
	return &StoryLog{
		ctx:        ctx,
		action:     action,
		id:         storyID,
		content:    make(map[string]any),
		oldContent: make(map[string]any),
	}
}

// SetMain 比较活动修改内容
func (s *StoryLog) SetMain(old *model.Story, newData *model.Story) {
	if s.action != enums.LogActionUpdate {
		return
	}
	if newData.Title != "" && old.Title != newData.Title {
		s.oldContent["title"] = old.Title
		s.content["title"] = newData.Title
	}
	if newData.ActivityType != 0 && old.ActivityType != newData.ActivityType {
		s.oldContent["activity_type"] = old.ActivityType
		s.content["activity_type"] = newData.ActivityType
	}
	if newData.CoverURL != "" && old.CoverURL != newData.CoverURL {
		s.oldContent["cover_url"] = old.CoverURL
		s.content["cover_url"] = newData.CoverURL
	}
	if newData.ItemID != "" && old.ItemID != newData.ItemID {
		s.oldContent["item_id"] = old.ItemID
		s.content["item_id"] = newData.ItemID
	}
	if newData.ItemTitle != "" && old.ItemTitle != newData.ItemTitle {
		s.oldContent["item_title"] = old.ItemTitle
		s.content["item_title"] = newData.ItemTitle
	}
	if newData.UserLimit != 0 && old.UserLimit != newData.UserLimit {
		s.oldContent["user_limit"] = old.UserLimit
		s.content["user_limit"] = newData.UserLimit
	}
	if newData.Stock != 0 && old.Stock != newData.Stock {
		s.oldContent["stock"] = old.Stock
		s.content["stock"] = newData.Stock
	}
	if newData.TotalStock != 0 && old.TotalStock != newData.TotalStock {
		s.oldContent["total_stock"] = old.TotalStock
		s.content["total_stock"] = newData.TotalStock
	}
	if newData.StockDisplay != 0 && old.StockDisplay != newData.StockDisplay {
		s.oldContent["stock_display"] = old.StockDisplay
		s.content["stock_display"] = newData.StockDisplay
	}
	if !newData.StartTime.IsZero() && !old.StartTime.Equal(newData.StartTime) {
		s.oldContent["start_time"] = old.StartTime
		s.content["start_time"] = newData.StartTime
	}
	if !newData.EndTime.IsZero() && !old.EndTime.Equal(newData.EndTime) {
		s.oldContent["end_time"] = old.EndTime
		s.content["end_time"] = newData.EndTime
	}
	if newData.ActivityDesc != "" && old.ActivityDesc != newData.ActivityDesc {
		s.oldContent["activity_desc"] = old.ActivityDesc
		s.content["activity_desc"] = newData.ActivityDesc
	}
}

// SetMaterials 比较材料修改内容
func (s *StoryLog) SetMaterials(old []*model.StoryMaterials, newData []*model.StoryMaterials) {
	if s.action != enums.LogActionUpdate {
		return
	}
	if len(old) != len(newData) {
		s.content["materials"] = newData
		s.oldContent["materials"] = old
	} else {
		for key, item := range old {
			oldMaterialsData, err := UnmarshalMaterialsData(*item.MaterialsData)
			if err != nil {
				log.Ctx(s.ctx).Warnf("unmarshal story log materials data error: %v", err)
				continue
			}
			newMaterialsData, err := UnmarshalMaterialsData(*newData[key].MaterialsData)
			if err != nil {
				log.Ctx(s.ctx).Warnf("unmarshal story log materials data error: %v", err)
				continue
			}
			if item.MaterialsType != newData[key].MaterialsType || item.LimitQty != newData[key].LimitQty ||
				!reflect.DeepEqual(oldMaterialsData, newMaterialsData) {
				s.content["materials"] = newData
				s.oldContent["materials"] = old
				break
			}
		}
	}
}

// Save 保存日志
func (s *StoryLog) Save(createdBy string) error {
	var err error
	var content []byte
	var oldContent []byte
	if len(s.content) > 0 {
		content, err = json.Marshal(s.content)
		if err != nil {
			log.Ctx(s.ctx).Warnf("marshal story log content error: %v", err)
		}
	}
	if len(s.oldContent) > 0 {
		oldContent, err = json.Marshal(s.oldContent)
		if err != nil {
			log.Ctx(s.ctx).Warnf("marshal story log old content error: %v", err)
		}
	}
	// 更新操作，没修改，不记录
	if len(content) == 0 && len(oldContent) == 0 && s.action == enums.LogActionUpdate {
		return nil
	}
	m := &model.StoryLog{
		StoryID:    s.id,
		Action:     s.action.Val(),
		Content:    string(content),
		OldContent: string(oldContent),
		UpdatedBy:  createdBy,
		CreatedBy:  createdBy,
	}
	if err = repo.GetQuery().StoryLog.WithContext(s.ctx).Create(m); err != nil {
		log.Ctx(s.ctx).Errorf("save story log error: %v, data: %#v", err, *m)
	}
	return nil
}

func GetLogContent(ctx context.Context, logModel *model.StoryLog) []string {
	content := make([]string, 0)
	if logModel.Action == enums.LogActionUpdate.Val() && logModel.Content != "" {
		updates := map[string]any{}
		if err := json.Unmarshal([]byte(logModel.Content), &updates); err != nil {
			log.Ctx(ctx).Errorf("unmarshal story log content error: %v", err)
			return content
		}
		for field, _ := range updates {
			content = append(content, field)
		}
	}
	return content
}
