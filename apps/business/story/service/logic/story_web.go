package logic

import (
	"app_service/apps/business/story/dal/model"
	"app_service/apps/business/story/define"
	"app_service/apps/business/story/define/enums"
	"app_service/apps/business/story/repo"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/pagination"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"fmt"
	"gorm.io/datatypes"
	"math"
	"sort"
	"time"
)

// GetStoryWebList 查询故事玩法列表
func GetStoryWebList(ctx context.Context, req *define.GetStoryWebListReq) ([]*model.Story, error) {
	statusList := []int32{enums.StoryStatusUp.Val()}
	if req.Status == enums.StoryStatusExpires.Val() {
		statusList = []int32{enums.StoryStatusDown.Val(), enums.StoryStatusExpires.Val()}
	}
	listExec := repo.GetDB().WithContext(ctx).
		Select("activity_code as activity_code, title as title, activity_type as activity_type, cover_url as cover_url,status as status, "+
			"item_id as item_id, stock as stock, total_stock as total_stock,start_time as start_time,end_time as end_time").
		Table("`story`").
		Where("status in (?)", statusList).
		Scopes(pagination.Paginate(req.Pagination, false)).
		Order(" CASE WHEN `status` = 2 THEN 1 ELSE 2 END, CASE WHEN `status` = 2 THEN UNIX_TIMESTAMP(start_time) ELSE -UNIX_TIMESTAMP(end_time) END ASC")
	var results []*model.Story
	err := listExec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// UserStoryMaterialsHold 持有量设置
func UserStoryMaterialsHold(ctx context.Context, userId string, itemIds []string, userStoryMaterials []*define.UserStoryMaterials) error {
	if userId == "" {
		return nil
	}
	openUserId, err := facade.GetOpenUserId(ctx, userId)
	if openUserId == "" {
		return nil
	}
	queryFusionItemIdsRes, err := yc_open.QueryFusionItemIds(ctx, openUserId, util.UniqueStringSlice(itemIds))
	if err != nil {
		log.Ctx(ctx).Errorf("CancelFusion QueryFusionItemIds err:%v", err)
		return err
	}
	log.Ctx(ctx).Infof("UserStoryMaterialsHold queryFusionItemIdsRes:%+v", util.Obj2JsonStr(queryFusionItemIdsRes))
	item2Count := make(map[string]int32, 0)
	for _, item := range queryFusionItemIdsRes {
		item2Count[item.ItemId] = item.Count
	}
	for _, material := range userStoryMaterials {
		for _, data := range material.UserStoryMaterialsDatas {
			if _, ok := item2Count[data.ItemId]; ok {
				data.UserQty = item2Count[data.ItemId]
			}
		}
	}
	return nil
}

// GetStoryMaterialItemIds 根据故事玩法code获取故事玩法材料物品id
func GetStoryMaterialItemIds(storyMaterials []*model.StoryMaterials) ([]string, error) {
	itemIds := make([]string, 0)
	for _, material := range storyMaterials {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, materials := range materialsData {
			itemIds = append(itemIds, materials.ItemId)

		}
	}
	return itemIds, nil
}

// GetStoryMaterialItemIdsAndQty 根据故事玩法code获取故事玩法材料物品id和数量
func GetStoryMaterialItemIdsAndQty(storyMaterials []*model.StoryMaterials, qty int32, selectedStoryMaterialsData []*define.SelectedStoryMaterialsData) (map[string]int32, error) {
	// 初始化数据结构
	needItemIdQtyMap := make(map[string]int32)
	id2StoryMaterial := make(map[int64]*model.StoryMaterials, len(storyMaterials))
	selectedItemIdQtyMap := make(map[string]int32)
	storyMaterialsExist := make(map[int64]bool)
	for _, materials := range selectedStoryMaterialsData {
		if !storyMaterialsExist[materials.Id] {
			storyMaterialsExist[materials.Id] = true
		} else {
			return nil, common_define.CommonWarnErr
		}
	}
	if len(storyMaterials) != len(selectedStoryMaterialsData) {
		return nil, common_define.CommonWarnErr
	}

	// 预处理故事玩法材料数据
	for _, material := range storyMaterials {
		id2StoryMaterial[material.ID] = material

		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}

		for _, materials := range materialsData {
			needItemIdQtyMap[util.StrVal(material.ID)+materials.ItemId] = materials.Qty * qty
		}
	}

	// 处理用户选择的材料
	for _, material := range selectedStoryMaterialsData {
		storyMaterial, ok := id2StoryMaterial[material.Id]
		if !ok {
			return nil, define.SH1100014Err
		}

		materialsData, err := UnmarshalMaterialsData(*storyMaterial.MaterialsData)
		if err != nil {
			return nil, err
		}

		switch storyMaterial.MaterialsType {
		case enums.StoryMaterialsTypeCore.Val():
			// 核心材料 - 需要全部选中
			for _, materials := range materialsData {
				selectedItemIdQtyMap[materials.ItemId] += needItemIdQtyMap[util.StrVal(storyMaterial.ID)+materials.ItemId]
			}

		case enums.StoryMaterialsTypeCrux.Val():
			// 关键材料 - 需要选中指定数量
			needItemIds := make(map[string]struct{}, len(materialsData))
			for _, materials := range materialsData {
				needItemIds[materials.ItemId] = struct{}{}
			}
			// 选中的物品数量
			var userSelectedQty int32
			for _, materialsItemInfo := range material.MaterialsItemInfo {
				itemId := materialsItemInfo.ItemId
				// 用户选中的
				if _, ok := needItemIds[itemId]; ok {
					userSelectedQty++
					selectedItemIdQtyMap[itemId] += needItemIdQtyMap[util.StrVal(storyMaterial.ID)+itemId]
				} else {
					return nil, define.SH1100014Err
				}
			}

			if userSelectedQty != storyMaterial.LimitQty {
				return nil, define.SH1100014Err
			}
		}
	}

	return selectedItemIdQtyMap, nil
}

// GetStoryMaterialUserItemIdMap 根据故事玩法材料的物品背包id和物品id
func GetStoryMaterialUserItemIdMap(ctx context.Context, userId string, storyMaterials []*model.StoryMaterials, storyDiscoveryReq *define.StoryDiscoveryReq) (map[string][]string, error) {
	openUserId, err := facade.GetOpenUserId(ctx, userId)
	if openUserId == "" {
		return nil, common_define.CommonErr
	}
	// 获取选中的物品id
	selectedItemIds, selectedUserItemIds, selectedItemIds2Qty := GetStoryMaterialSelectedItemIdInfo(storyDiscoveryReq.StoryMaterials)
	if err != nil {
		log.Ctx(ctx).Errorf("GetStoryMaterialUserItemIdMap GetStoryMaterialSelectedItemIdInfo err:%v", err)
		return nil, err
	}
	// 获取持仓用户物品id
	queryFusionItemIdsRes, err := yc_open.QueryFusionItemIds(ctx, openUserId, util.UniqueStringSlice(selectedItemIds))
	if err != nil {
		log.Ctx(ctx).Errorf("GetStoryMaterialUserItemIdMap QueryFusionItemIds err:%v", err)
		return nil, err
	}
	log.Ctx(ctx).Infof("GetStoryMaterialUserItemIdMap queryFusionItemIdsRes:%+v", util.Obj2JsonStr(queryFusionItemIdsRes))
	userItemIdExistMap := make(map[string]struct{})
	for _, item := range queryFusionItemIdsRes {
		for _, userItemId := range item.UserItemIds {
			userItemIdExistMap[userItemId] = struct{}{}
		}
	}
	// 检查所有选中的物品是否存在
	for _, userItemId := range selectedUserItemIds {
		if _, exists := userItemIdExistMap[userItemId]; !exists {
			log.Ctx(ctx).Errorf("user item not found: %s", userItemId)
			return nil, define.SH1100014Err
		}
	}
	needItemIdsQtyMap, err := GetStoryMaterialItemIdsAndQty(storyMaterials, storyDiscoveryReq.Qty, storyDiscoveryReq.StoryMaterials)
	if err != nil {
		log.Ctx(ctx).Errorf("GetStoryMaterialUserItemIds GetStoryMaterialItemIdsAndQty err:%v", err)
		return nil, err
	}
	for itemId, needQty := range needItemIdsQtyMap {
		selectedQty, exists := selectedItemIds2Qty[itemId]
		if !exists || selectedQty != needQty {
			log.Ctx(ctx).Errorf("quantity mismatch for item %s: need %d, have %d", itemId, needQty, selectedQty)
			return nil, define.SH1100014Err
		}
	}
	userItemIdMap := make(map[string][]string, 0)
	for _, material := range storyDiscoveryReq.StoryMaterials {
		for _, materials := range material.MaterialsItemInfo {
			userItemIdMap[materials.ItemId] = materials.UserItemIds
		}
	}
	return userItemIdMap, err
}

// GetStoryMaterialSelectedItemIdInfo 获取选中的物品信息
func GetStoryMaterialSelectedItemIdInfo(materialsDataList []*define.SelectedStoryMaterialsData) ([]string, []string, map[string]int32) {
	var itemIds []string
	var userItemIds []string
	itemIds2Qty := make(map[string]int32)
	for _, materials := range materialsDataList {
		for _, userMaterials := range materials.MaterialsItemInfo {
			itemIds = append(itemIds, userMaterials.ItemId)
			itemIds2Qty[userMaterials.ItemId] = int32(len(userMaterials.UserItemIds))
			for _, id := range userMaterials.UserItemIds {
				userItemIds = append(userItemIds, id)
			}
		}
	}
	return itemIds, userItemIds, itemIds2Qty
}

func VerifyLaunchStory(ctx context.Context, story *model.Story, req *define.StoryDiscoveryReq, userId string) error {
	qty := req.Qty
	if story.Status != enums.StoryStatusUp.Val() {
		return define.SH1100011Err
	}
	if time.Now().Before(story.StartTime) {
		return define.SH1100010Err
	} else if time.Now().After(story.EndTime) {
		return define.SH1100011Err
	}
	if story.Stock < qty {
		return define.SH1100015Err
	}
	sale, err := tmt.AllowSale(ctx)
	if err != nil {
		return err
	}
	if !sale {
		return define.SH1100012Err
	}
	if story.UserLimit != 0 {
		if qty > story.UserLimit {
			return define.SH1100013Err.SetMsg(fmt.Sprintf(define.SH1100013Err.Msg, story.UserLimit))
		}
		// 查询是否超过限合数量
		totalNum, err := GetStoryOrderUserNum(ctx, story.ID, userId)
		if err != nil {
			return err
		}
		if totalNum+qty > story.UserLimit {
			return define.SH1100013Err.SetMsg(fmt.Sprintf(define.SH1100013Err.Msg, story.UserLimit))
		}
	}
	return nil
}
func StoryStatusHandler(story *model.Story) int32 {
	now := time.Now()
	switch story.Status {
	case enums.StoryStatusUp.Val():
		if now.Before(story.StartTime) {
			return enums.StoryStatusWait.Val() // 未开始
		}
		if now.After(story.EndTime) || story.Stock == 0 {
			return enums.StoryStatusExpires.Val() // 已结束或库存为0
		}
		return enums.StoryStatusIng.Val() // 进行中

	case enums.StoryStatusDown.Val():
		return enums.StoryStatusExpires.Val() // 手动下架视为已结束

	default:
		return story.Status // 未知状态保持原样
	}
}

func IncrByStoryStock(ctx context.Context, order *model.StoryOrder) error {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ID, order.StoryID)
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	// 增加故事玩法库存
	err = repo.NewStoryRepo(storySchema.WithContext(ctx)).IncrByStoryStock(getStory.ID, order.Qty)
	if err != nil {
		return err
	}

	// 增加物品库存
	stockRes, _ := StoryStock(ctx, order.ItemID, -order.Qty)
	if stockRes != tmt.StoryStockSuccess {
		log.Ctx(ctx).Errorf("IncrByStoryStock Fail, StoryStock err:%v, OrderId:%v", err, order.OrderID)
	}
	return nil
}

func StoryCompleteUserNum(ctx context.Context, storyId int64) error {
	exec := repo.GetDB().WithContext(ctx).
		Select("COUNT(DISTINCT(user_id)) as num").
		Table("`story_order`").
		Where("status = ? and story_id = ? and is_del = 0", enums.StoryOrderStatusDone.Val(), storyId)
	userNum := int32(0)
	err := exec.Find(&userNum).Error
	if err != nil {
		return err
	}
	storySchema := repo.GetQuery().Story
	updateStory := &model.Story{ID: storyId, CompleteUserNum: userNum}
	err = repo.NewStoryRepo(storySchema.WithContext(ctx)).UpdateById(updateStory)
	if err != nil {
		return err
	}
	return nil
}

func GetStoryItemsReleaseTime(ctx context.Context, storyId int64) (map[string]*time.Time, error) {
	storySchema := repo.GetQuery().Story
	builder := search.NewQueryBuilder().Eq(storySchema.ID, storyId)
	getStory, err := repo.NewStoryRepo(storySchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	releaseTimeData, err := GetStoryReleaseTime(getStory.ReleaseTime)
	if err != nil {
		return nil, err
	}
	var releaseTime *time.Time
	if releaseTimeData != nil {
		// 二次流转开启
		if releaseTimeData.ReleaseTime != nil && !releaseTimeData.ReleaseTime.IsZero() {
			releaseTime = util.GetBeijingStartOfDay(releaseTimeData.ReleaseTime)
		} else if releaseTimeData.ReleaseDay != nil && *releaseTimeData.ReleaseDay != 0 {
			now := time.Now()
			nowDay := util.GetBeijingStartOfDay(&now)
			day := int(*releaseTimeData.ReleaseDay) + nowDay.Day()
			truncate := time.Date(nowDay.Year(), nowDay.Month(), day, 0, 0, 0, 0, nowDay.Location())
			releaseTime = &truncate
		}
	} else {
		releaseTime = nil
	}

	data := make(map[string]*time.Time)
	storyMaterialsSchema := repo.GetQuery().StoryMaterials
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyMaterialsSchema.StoryID, getStory.ID)
	storyMaterialsList, err := repo.NewStoryMaterialsRepo(storyMaterialsSchema.WithContext(ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	for _, material := range storyMaterialsList {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, item := range materialsData {
			if item.Loop == constant.Yes && releaseTime != nil {
				data[item.ItemId] = releaseTime
			}
		}
	}
	return data, nil
}

func GetStoryReleaseTime(releaseTime *datatypes.JSON) (*define.StoryReleaseTime, error) {
	storyReleaseTime := &define.StoryReleaseTime{}
	if releaseTime != nil {
		err := json.Unmarshal([]byte(util.StrVal(releaseTime)), &storyReleaseTime)
		if err != nil {
			return nil, err
		}
		return storyReleaseTime, nil
	}
	return nil, nil
}

func CirculationStatusHold(ctx context.Context, itemIds []string, userStoryMaterials []*define.UserStoryMaterials) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	itemMap, err := issueFacade.GetIssueItemMap(ctx, util.UniqueStringSlice(itemIds))
	if err != nil {
		return
	}
	for _, material := range userStoryMaterials {
		for _, item := range material.UserStoryMaterialsDatas {
			issueItem, exists := itemMap[item.ItemId]
			if !exists {
				item.CirculationStatus = 2
				continue
			}

			var startTimeUTC8 time.Time
			var endTimeUTC8 time.Time
			if issueItem.CirculationStart != nil {
				startTimeUTC8 = issueItem.CirculationStart.In(loc)
			}
			if issueItem.CirculationEnd != nil {
				endTimeUTC8 = issueItem.CirculationEnd.In(loc)
			}

			if issueItem.CirculationStatus == 1 && !startTimeUTC8.IsZero() && time.Now().After(startTimeUTC8) && !endTimeUTC8.IsZero() && time.Now().Before(endTimeUTC8) {
				item.CirculationStatus = 1
			} else {
				item.CirculationStatus = 2
			}
		}
	}
}

func SaveStoryMaterialsRelease(ctx context.Context, storyOrderId int64) error {
	storyOrderSchema := repo.GetQuery().StoryOrder
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectOne(search.NewQueryBuilder().Eq(storyOrderSchema.ID, storyOrderId).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("SaveStoryMaterialsRelease err:%v", err)
		return err
	}
	storyOrderDetailSchema := repo.GetQuery().StoryOrderDetail
	storyMaterialsBuilder := search.NewQueryBuilder().Eq(storyOrderDetailSchema.StoryOrderID, order.ID)
	orderDetail, err := repo.NewStoryOrderDetailRepo(storyOrderDetailSchema.WithContext(ctx)).SelectList(storyMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	releaseTimeMap, err := GetStoryItemsReleaseTime(ctx, order.StoryID)
	if err != nil {
		return err
	}
	// 保存故事玩法释放记录
	materialsReleaseList := make([]*model.StoryMaterialsRelease, 0)
	userItemIdList := make([]string, 0)
	for _, detail := range orderDetail {
		if releaseTime, ok := releaseTimeMap[detail.MaterialsItemID]; ok {
			if releaseTime != nil {
				release := &model.StoryMaterialsRelease{
					StoryOrderDetailID: detail.ID,
					UserID:             order.UserID,
					ItemID:             detail.MaterialsItemID,
					UserItemID:         detail.UserItemID,
					Status:             enums.ReleaseCreated.Val(),
				}
				release.ReleaseTime = util.GetBeijingStartOfDay(releaseTime)
				materialsReleaseList = append(materialsReleaseList, release)
				userItemIdList = append(userItemIdList, detail.UserItemID)
			}
		}
	}
	if len(materialsReleaseList) > 0 {
		err = repo.ExecGenTx(ctx, func(tx context.Context) error {
			storyMaterialsReleaseSchema := repo.GetQuery().StoryMaterialsRelease
			list, err := repo.NewStoryMaterialsReleaseRepo(repo.Query(tx).StoryMaterialsRelease.WithContext(tx)).
				SelectList(search.NewQueryBuilder().In(storyMaterialsReleaseSchema.UserItemID, userItemIdList).Build())
			if err != nil {
				log.Ctx(ctx).Errorf("SelectListStoryMaterialsRelease err:%v", err)
				return err
			}
			if len(list) > 0 {
				err = repo.NewStoryMaterialsReleaseRepo(repo.Query(tx).StoryMaterialsRelease.WithContext(tx)).RemoveByIds(list...)
				if err != nil {
					log.Ctx(ctx).Errorf("RemoveByIdsStoryMaterialsRelease err:%v", err)
					return err
				}
			}
			err = repo.NewStoryMaterialsReleaseRepo(repo.Query(tx).StoryMaterialsRelease.WithContext(tx)).BatchSave(materialsReleaseList, 100)
			if err != nil {
				log.Ctx(ctx).Errorf("SaveStoryMaterialsRelease err:%v", err)
				return err
			}
			return nil
		})
	}
	return nil
}

// StoryJoinDataHandler 设置用户参与数据
func StoryJoinDataHandler(ctx context.Context, storyId int64, userId string) (*define.GetStoryJoinWebData, error) {
	if userId == "" {
		return nil, nil
	}
	storyOrderSchema := repo.GetQuery().StoryOrder
	count, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).Count(search.NewQueryBuilder().
		Eq(storyOrderSchema.StoryID, storyId).Eq(storyOrderSchema.UserID, userId).
		Eq(storyOrderSchema.Status, enums.StoryOrderStatusSuccess.Val()).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("StoryJoinDataHandler err:%v", err)
		return nil, err
	}

	data := &define.GetStoryJoinWebData{
		Count: &count,
	}
	if count > 0 {
		orderDetailList, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).GetOrderDetailList(userId, storyId, 3)
		if err != nil {
			log.Ctx(ctx).Errorf("GetOrderDetailList err:%v", err)
			return nil, err
		}
		var imageUrls []string
		for _, detail := range orderDetailList {
			imageUrls = append(imageUrls, detail.MaterialsItemURL)
		}
		data.ItemImageUrls = &imageUrls
	}
	return data, nil
}

// ReceiveStory 故事玩法领取奖励
func ReceiveStory(ctx context.Context, userId string, orderId string) (*define.ReceiveStoryResp, error) {
	storyOrderSchema := repo.GetQuery().StoryOrder
	order, err := repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).SelectOne(search.NewQueryBuilder().
		Eq(storyOrderSchema.OrderID, orderId).Eq(storyOrderSchema.UserID, userId).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("ReceiveStory order SelectOne err:%v", err)
		return nil, err
	}
	if order.Status == enums.StoryOrderStatusSuccess.Val() {
		return nil, define.SH110008Err
	} else if order.Status == enums.StoryOrderStatusFinish.Val() {
		updateOrder := &model.StoryOrder{Status: enums.StoryOrderStatusReceive.Val()}
		err = repo.NewStoryOrderRepo(storyOrderSchema.WithContext(ctx)).Update(updateOrder, search.NewQueryBuilder().
			Eq(storyOrderSchema.ID, order.ID).Eq(storyOrderSchema.Status, enums.StoryOrderStatusFinish.Val()).Build())
		if err != nil {
			log.Ctx(ctx).Errorf("ReceiveStory order Update err:%v", err)
			return nil, err
		}
		// 发送Kafka(发放奖励)
		fusion := &define.StoryItemIssue{StoryOrderID: order.ID}
		_ = kafka_util.SendMsg(ctx, constant.StoryItemIssue, fusion)
		return &define.ReceiveStoryResp{
			ItemCoverURL: order.ItemCoverURL,
			ItemTitle:    order.ItemTitle,
			Qty:          order.Qty,
		}, nil
	} else {
		return nil, common_define.CommonErr
	}
}

// UserStoryMaterialsLimitDataHold 物品流转设置
func UserStoryMaterialsLimitDataHold(story *model.Story, userStoryMaterials []*define.UserStoryMaterials) {
	for _, userStoryMaterial := range userStoryMaterials {
		for _, materials := range userStoryMaterial.UserStoryMaterialsDatas {
			if materials.Destroy == constant.Yes {
				// 需要销毁
				materials.Limit = &define.UserStoryMaterialsLimitData{
					Status: enums.StoryMaterialsLimitStatusDestroy.Val(),
				}
				continue
			}

			// 非销毁情况处理
			if materials.Loop != constant.Yes {
				materials.Limit = &define.UserStoryMaterialsLimitData{
					Status: enums.StoryMaterialsLimitStatusPick.Val(),
				}
				continue
			}

			// Loop == Yes 的情况
			if story.ReleaseTime == nil {
				materials.Limit = &define.UserStoryMaterialsLimitData{
					Status: enums.StoryMaterialsLimitStatusPick.Val(),
				}
				continue
			}

			releaseTimeData, err := GetStoryReleaseTime(story.ReleaseTime)
			if err != nil {
				materials.Limit = &define.UserStoryMaterialsLimitData{
					Status: enums.StoryMaterialsLimitStatusPick.Val(),
				}
				continue
			}

			var limitDay *int32
			if releaseTimeData.ReleaseDay != nil && *releaseTimeData.ReleaseDay > 0 {
				limitDay = releaseTimeData.ReleaseDay
			} else if releaseTimeData.ReleaseTime != nil && !releaseTimeData.ReleaseTime.IsZero() {
				now := time.Now()
				duration := releaseTimeData.ReleaseTime.Sub(now)
				var daysLeft int32
				if duration <= 0 {
					daysLeft = 0
				} else {
					daysLeft = int32(math.Ceil(duration.Hours() / 24))
				}
				limitDay = &daysLeft
			}

			materials.Limit = &define.UserStoryMaterialsLimitData{
				Status: enums.StoryMaterialsLimitStatusXDay.Val(),
				Day:    limitDay,
			}
		}
	}
}

// GetMaxLimit 获取物品一次性融合次数
func GetMaxLimit(ctx context.Context, storyMaterialsList []*model.StoryMaterials) int32 {
	if len(storyMaterialsList) == 0 {
		return 0
	}

	var totalMaxLimit int32
	const maxUserItems = global.MaxStoryUserItemCount

	for _, material := range storyMaterialsList {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			log.Ctx(ctx).Errorf("UnmarshalMaterialsData failed for material %v: %v", material.ID, err)
			continue
		}

		if material.MaterialsType == enums.StoryMaterialsTypeCore.Val() {
			// 核心材料全部必选，直接累加数量
			for _, data := range materialsData {
				totalMaxLimit += data.Qty
			}
			continue
		}

		// 非核心材料处理
		limitQty := int(material.LimitQty)
		if limitQty <= 0 || len(materialsData) == 0 {
			continue
		}

		// 准备数量数组并排序
		qtyArray := make([]int, 0, len(materialsData))
		for _, data := range materialsData {
			qtyArray = append(qtyArray, int(data.Qty))
		}

		sort.Sort(sort.Reverse(sort.IntSlice(qtyArray)))

		// 计算前limitQty个最大值的和
		effectiveQty := util.Min(limitQty, len(qtyArray))
		sum := 0
		for _, qty := range qtyArray[:effectiveQty] {
			sum += qty
		}

		totalMaxLimit += int32(sum)
	}

	if totalMaxLimit == 0 {
		return 0
	}

	// 计算最终限制值
	return maxUserItems / totalMaxLimit
}

// SetSelectedAllUserItemIds 处理故事探索全选物品逻辑
func SetSelectedAllUserItemIds(ctx context.Context, userId string, storyMaterials []*model.StoryMaterials, storyDiscoveryReq *define.StoryDiscoveryReq) error {
	var selectedAllItemIds []string
	itemId2CostPriceOrder := make(map[string]int32)
	for _, material := range storyDiscoveryReq.StoryMaterials {
		for _, info := range material.MaterialsItemInfo {
			if info.SelectedAll == constant.Yes {
				selectedAllItemIds = append(selectedAllItemIds, info.ItemId)
				itemId2CostPriceOrder[info.ItemId] = info.CostPriceOrder
			}
		}
	}
	if len(selectedAllItemIds) == 0 {
		return nil
	}

	openUserId, err := facade.GetOpenUserId(ctx, userId)
	if err != nil || openUserId == "" {
		log.Ctx(ctx).Errorf("获取OpenUserId失败: %v", err)
		return common_define.CommonErr
	}

	// 获取每个物品需要的数量
	itemQtyMap, err := GetStoryMaterialSelectedAllItemIdsAndQty(storyMaterials, storyDiscoveryReq.Qty, storyDiscoveryReq.StoryMaterials)
	if err != nil {
		log.Ctx(ctx).Errorf("获取物品数量失败: %v", err)
		return err
	}

	// 查询用户库存
	uniqueItemIds := util.UniqueStringSlice(selectedAllItemIds)

	// 构建物品ID到用户物品列表的映射
	inventoryMap := make(map[string][]string)
	for _, itemId := range uniqueItemIds {
		var costPriceOrder = int32(-1)
		if _, ok := itemId2CostPriceOrder[itemId]; ok {
			costPriceOrder = itemId2CostPriceOrder[itemId]
		}
		userInventory, err := yc_open.QueryFusionItemIdsSortCostPrice(ctx, openUserId, []string{itemId}, costPriceOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("查询用户库存失败: %v", err)
			return err
		}
		for _, item := range userInventory {
			inventoryMap[item.ItemId] = item.UserItemIds
		}
	}

	// 验证并分配UserItemIds
	for _, material := range storyDiscoveryReq.StoryMaterials {
		for _, info := range material.MaterialsItemInfo {
			if info.SelectedAll != constant.Yes {
				continue
			}

			requiredQty, ok := itemQtyMap[info.ItemId]
			if !ok {
				return define.SH1100014Err
			}

			availableItems, exists := inventoryMap[info.ItemId]
			if !exists || len(availableItems) < int(requiredQty) {
				return define.SH1100014Err
			}

			// 分配前N个UserItemId
			info.UserItemIds = availableItems[:requiredQty]
		}
	}
	return nil
}

// GetStoryMaterialSelectedAllItemIdsAndQty 根据全选的物品id获取合成材料物品id和数量
func GetStoryMaterialSelectedAllItemIdsAndQty(storyMaterials []*model.StoryMaterials, qty int32, selectedStoryMaterialsData []*define.SelectedStoryMaterialsData) (map[string]int32, error) {
	// 初始化数据结构
	needItemIdQtyMap := make(map[string]int32)
	id2StoryMaterial := make(map[int64]*model.StoryMaterials, len(storyMaterials))
	selectedItemIdQtyMap := make(map[string]int32)
	storyMaterialsExist := make(map[int64]bool)
	for _, materials := range selectedStoryMaterialsData {
		if !storyMaterialsExist[materials.Id] {
			storyMaterialsExist[materials.Id] = true
		} else {
			return nil, common_define.CommonWarnErr
		}
	}
	if len(storyMaterials) != len(selectedStoryMaterialsData) {
		return nil, common_define.CommonWarnErr
	}

	// 预处理合成材料数据
	for _, material := range storyMaterials {
		id2StoryMaterial[material.ID] = material

		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}

		for _, materials := range materialsData {
			needItemIdQtyMap[util.StrVal(material.ID)+materials.ItemId] = materials.Qty * qty
		}
	}

	// 处理用户选择的材料
	for _, material := range selectedStoryMaterialsData {
		storyMaterial, ok := id2StoryMaterial[material.Id]
		if !ok {
			return nil, define.SH1100014Err
		}

		materialsData, err := UnmarshalMaterialsData(*storyMaterial.MaterialsData)
		if err != nil {
			return nil, err
		}

		switch storyMaterial.MaterialsType {
		case enums.StoryMaterialsTypeCore.Val():
			// 核心材料 - 需要全部选中
			for _, materials := range materialsData {
				selectedItemIdQtyMap[materials.ItemId] += needItemIdQtyMap[util.StrVal(storyMaterial.ID)+materials.ItemId]
			}

		case enums.StoryMaterialsTypeCrux.Val():
			// 关键材料 - 需要选中指定数量
			needItemIds := make(map[string]struct{}, len(materialsData))
			for _, materials := range materialsData {
				needItemIds[materials.ItemId] = struct{}{}
			}

			var userSelectedQty int32
			for _, materials := range material.MaterialsItemInfo {
				itemId := materials.ItemId
				// 用户选中的
				if _, ok := needItemIds[itemId]; ok {
					userSelectedQty++
					selectedItemIdQtyMap[itemId] += needItemIdQtyMap[util.StrVal(storyMaterial.ID)+itemId]
				} else {
					return nil, define.SH1100014Err
				}
			}

			if userSelectedQty != storyMaterial.LimitQty {
				return nil, define.SH1100014Err
			}
		}
	}

	return selectedItemIdQtyMap, nil
}
