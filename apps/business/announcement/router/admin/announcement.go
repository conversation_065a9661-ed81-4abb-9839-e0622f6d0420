package admin

import (
	"app_service/apps/business/announcement/api/admin"
	"github.com/gin-gonic/gin"
)

// Announcement 公告管理端相关
func Announcement(router *gin.RouterGroup) {
	group := router.Group("/announcement")
	{
		// 获取公告列表
		group.GET("/list", admin.GetAnnouncementList)
		// 获取公告详情
		group.GET("/detail", admin.GetAnnouncementDetail)
		// 新增公告
		group.POST("/add", admin.AddAnnouncement)
		// 编辑公告
		group.POST("/edit", admin.EditAnnouncement)
		// 公告分类状态编辑
		group.POST("/edit_status", admin.EditAnnouncementStatus)
		// 公告优先级编辑
		group.POST("/edit_priority", admin.EditAnnouncementPriority)
		// 公告媒体同步编辑
		group.POST("/edit_ad_ids", admin.EditAnnouncementAdIds)
		// 公告删除
		//group.POST("/del", admin.DelAnnouncement)
	}
}
