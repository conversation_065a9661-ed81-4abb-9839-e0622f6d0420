package web

import (
	"app_service/apps/business/announcement/api/web"
	"github.com/gin-gonic/gin"
)

// Announcement 公告管理端相关
func Announcement(router *gin.RouterGroup) {
	group := router.Group("/announcement")
	{
		// 公告列表
		group.GET("/list", web.GetAnnouncementWebList)
		// 公告详情
		group.GET("/detail", web.GetAnnouncementWebDetail)
		// 公告分类列表
		group.GET("/category/list", web.GetAnnCategoryWebList)
	}
}
