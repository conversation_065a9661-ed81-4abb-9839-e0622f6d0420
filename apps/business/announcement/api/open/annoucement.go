package open

import (
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// SchedulePublish
// @Summary 定时发布公告
// @Description 定时发布公告
// @Tags Open-公告管理
// @Param data body define.SchedulePublishReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SchedulePublishResp}
// @Router /open/v1/announcement/schedule_publish [POST]
// @Security Bearer
func SchedulePublish(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SchedulePublishReq{}, s.SchedulePublish)
}

// NoticeAggregate
// @Summary 聚合公告和活动
// @Description 聚合公告和活动
// @Tags Open-公告管理
// @Param ad_id query string true "广告商ID"
// @Success 200 {object} response.Data{data=define.NoticeAggregateResp}
// @Router /open/v1/notice/aggregate [get]
// @Security Bearer
func NoticeAggregate(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.NoticeAggregateReq{}, s.NoticeAggregate)
}
