package web

import (
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetAnnCategoryWebList
// @Summary 查询公告分类列表
// @Description 查询公告分类列表（Web端）
// @Tags 用户端-平台方公告分类
// @Param data query define.GetAnnCategoryWebListReq true "查询参数"
// @Success 200 {object} define.GetAnnCategoryWebListResp
// @Router /web/v1/announcement/category/list [get]
func GetAnnCategoryWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnCategoryWebListReq{}, s.GetAnnCategoryWebList)
}
