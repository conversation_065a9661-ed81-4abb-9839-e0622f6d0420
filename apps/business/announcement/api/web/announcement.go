package web

import (
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetAnnouncementWebList
// @Summary 查询公告列表
// @Description 查询公告列表（Web端）
// @Tags 用户端-平台方公告
// @Param data query define.GetAnnouncementWebListReq true "查询参数"
// @Success 200 {object} define.GetAnnouncementWebListResp
// @Router /web/v1/announcement/list [get]
func GetAnnouncementWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnouncementWebListReq{}, s.GetAnnouncementWebList)
}

// GetAnnouncementWebDetail
// @Summary 查询公告详情
// @Description 查询公告详情（Web端）
// @Tags 用户端-平台方公告
// @Param data query define.GetAnnouncementWebDetailReq true "查询参数"
// @Success 200 {object} define.GetAnnouncementWebDetailResp
// @Router /web/v1/announcement/detail [get]
func GetAnnouncementWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnouncementWebDetailReq{}, s.GetAnnouncementWebDetail)
}
