package admin

import (
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetAnnCategoryList
// @Summary 查询公告分类列表
// @Description 查询公告分类列表
// @Tags 管理端-平台方公告分类管理
// @Param data query define.GetAnnCategoryAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAnnCategoryAdminListResp}
// @Router /admin/v1/announcement/category/list [get]
// @Security Bearer
func GetAnnCategoryList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnCategoryAdminListReq{}, s.GetAnnCategoryList)
}

// GetAnnCategoryDetail
// @Summary 查询公告分类详情
// @Description 查询公告分类详情
// @Tags 管理端-平台方公告分类管理
// @Param data query define.GetAnnCategoryAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetAnnCategoryAdminDetailResp}
// @Router /admin/v1/announcement/category/detail [get]
// @Security Bearer
func GetAnnCategoryDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetAnnCategoryAdminDetailReq{}, s.GetAnnCategoryDetail)
}

// AddAnnCategory
// @Summary 新增公告分类
// @Description 新增公告分类
// @Tags 管理端-平台方公告分类管理
// @Param data body define.AddAnnCategoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddAnnCategoryResp}
// @Router /admin/v1/announcement/category/add [POST]
// @Security Bearer
func AddAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddAnnCategoryReq{}, s.AddAnnCategory)
}

// EditAnnCategory
// @Summary 编辑公告分类
// @Description 编辑公告分类
// @Tags 管理端-平台方公告分类管理
// @Param data body define.EditAnnCategoryReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnCategoryResp}
// @Router /admin/v1/announcement/category/edit [POST]
// @Security Bearer
func EditAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnCategoryReq{}, s.EditAnnCategory)
}

// EditAnnCategoryPriority
// @Summary 公告分类优先级编辑
// @Description 公告分类优先级编辑
// @Tags 管理端-平台方公告分类管理
// @Param data body define.EditAnnCategoryPriorityReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditAnnCategoryPriorityResp}
// @Router /admin/v1/announcement/category/edit_priority [POST]
// @Security Bearer
func EditAnnCategoryPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditAnnCategoryPriorityReq{}, s.EditAnnCategoryPriority)
}

// DelAnnCategory
// @Summary 公告分类删除
// @Description 公告分类删除
// @Tags 管理端-平台方公告分类管理
// @Param data body define.DelAnnCategoryReq true "删除参数"
// @Success 200 {object} response.Data{data=define.DelAnnCategoryResp}
// @Router /admin/v1/announcement/category/del [POST]
// @Security Bearer
func DelAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelAnnCategoryReq{}, s.DelAnnCategory)
}
