// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/announcement/dal/model"
)

func newAnnCategory(db *gorm.DB, opts ...gen.DOOption) annCategory {
	_annCategory := annCategory{}

	_annCategory.annCategoryDo.UseDB(db, opts...)
	_annCategory.annCategoryDo.UseModel(&model.AnnCategory{})

	tableName := _annCategory.annCategoryDo.TableName()
	_annCategory.ALL = field.NewAsterisk(tableName)
	_annCategory.AnnCategoryID = field.NewInt64(tableName, "ann_category_id")
	_annCategory.Name = field.NewString(tableName, "name")
	_annCategory.TextColor = field.NewString(tableName, "text_color")
	_annCategory.BackgroundColor = field.NewString(tableName, "background_color")
	_annCategory.Priority = field.NewInt32(tableName, "priority")
	_annCategory.Status = field.NewInt32(tableName, "status")
	_annCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_annCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_annCategory.IsDel = field.NewField(tableName, "is_del")

	_annCategory.fillFieldMap()

	return _annCategory
}

// annCategory 公告栏目表
type annCategory struct {
	annCategoryDo

	ALL             field.Asterisk
	AnnCategoryID   field.Int64  // 主键ID
	Name            field.String // 栏目名称
	TextColor       field.String // 文字颜色
	BackgroundColor field.String // 背景颜色
	Priority        field.Int32  // 优先级，最大值9999
	Status          field.Int32  // 状态：-1:已删除 1启用
	CreatedAt       field.Time   // 创建时间
	UpdatedAt       field.Time   // 更新时间
	IsDel           field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (a annCategory) Table(newTableName string) *annCategory {
	a.annCategoryDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a annCategory) As(alias string) *annCategory {
	a.annCategoryDo.DO = *(a.annCategoryDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *annCategory) updateTableName(table string) *annCategory {
	a.ALL = field.NewAsterisk(table)
	a.AnnCategoryID = field.NewInt64(table, "ann_category_id")
	a.Name = field.NewString(table, "name")
	a.TextColor = field.NewString(table, "text_color")
	a.BackgroundColor = field.NewString(table, "background_color")
	a.Priority = field.NewInt32(table, "priority")
	a.Status = field.NewInt32(table, "status")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")
	a.IsDel = field.NewField(table, "is_del")

	a.fillFieldMap()

	return a
}

func (a *annCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *annCategory) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 9)
	a.fieldMap["ann_category_id"] = a.AnnCategoryID
	a.fieldMap["name"] = a.Name
	a.fieldMap["text_color"] = a.TextColor
	a.fieldMap["background_color"] = a.BackgroundColor
	a.fieldMap["priority"] = a.Priority
	a.fieldMap["status"] = a.Status
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt
	a.fieldMap["is_del"] = a.IsDel
}

func (a annCategory) clone(db *gorm.DB) annCategory {
	a.annCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a annCategory) replaceDB(db *gorm.DB) annCategory {
	a.annCategoryDo.ReplaceDB(db)
	return a
}

type annCategoryDo struct{ gen.DO }

type IAnnCategoryDo interface {
	gen.SubQuery
	Debug() IAnnCategoryDo
	WithContext(ctx context.Context) IAnnCategoryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAnnCategoryDo
	WriteDB() IAnnCategoryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAnnCategoryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAnnCategoryDo
	Not(conds ...gen.Condition) IAnnCategoryDo
	Or(conds ...gen.Condition) IAnnCategoryDo
	Select(conds ...field.Expr) IAnnCategoryDo
	Where(conds ...gen.Condition) IAnnCategoryDo
	Order(conds ...field.Expr) IAnnCategoryDo
	Distinct(cols ...field.Expr) IAnnCategoryDo
	Omit(cols ...field.Expr) IAnnCategoryDo
	Join(table schema.Tabler, on ...field.Expr) IAnnCategoryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAnnCategoryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAnnCategoryDo
	Group(cols ...field.Expr) IAnnCategoryDo
	Having(conds ...gen.Condition) IAnnCategoryDo
	Limit(limit int) IAnnCategoryDo
	Offset(offset int) IAnnCategoryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnCategoryDo
	Unscoped() IAnnCategoryDo
	Create(values ...*model.AnnCategory) error
	CreateInBatches(values []*model.AnnCategory, batchSize int) error
	Save(values ...*model.AnnCategory) error
	First() (*model.AnnCategory, error)
	Take() (*model.AnnCategory, error)
	Last() (*model.AnnCategory, error)
	Find() ([]*model.AnnCategory, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AnnCategory, err error)
	FindInBatches(result *[]*model.AnnCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.AnnCategory) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAnnCategoryDo
	Assign(attrs ...field.AssignExpr) IAnnCategoryDo
	Joins(fields ...field.RelationField) IAnnCategoryDo
	Preload(fields ...field.RelationField) IAnnCategoryDo
	FirstOrInit() (*model.AnnCategory, error)
	FirstOrCreate() (*model.AnnCategory, error)
	FindByPage(offset int, limit int) (result []*model.AnnCategory, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAnnCategoryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a annCategoryDo) Debug() IAnnCategoryDo {
	return a.withDO(a.DO.Debug())
}

func (a annCategoryDo) WithContext(ctx context.Context) IAnnCategoryDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a annCategoryDo) ReadDB() IAnnCategoryDo {
	return a.Clauses(dbresolver.Read)
}

func (a annCategoryDo) WriteDB() IAnnCategoryDo {
	return a.Clauses(dbresolver.Write)
}

func (a annCategoryDo) Session(config *gorm.Session) IAnnCategoryDo {
	return a.withDO(a.DO.Session(config))
}

func (a annCategoryDo) Clauses(conds ...clause.Expression) IAnnCategoryDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a annCategoryDo) Returning(value interface{}, columns ...string) IAnnCategoryDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a annCategoryDo) Not(conds ...gen.Condition) IAnnCategoryDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a annCategoryDo) Or(conds ...gen.Condition) IAnnCategoryDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a annCategoryDo) Select(conds ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a annCategoryDo) Where(conds ...gen.Condition) IAnnCategoryDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a annCategoryDo) Order(conds ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a annCategoryDo) Distinct(cols ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a annCategoryDo) Omit(cols ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a annCategoryDo) Join(table schema.Tabler, on ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a annCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a annCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a annCategoryDo) Group(cols ...field.Expr) IAnnCategoryDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a annCategoryDo) Having(conds ...gen.Condition) IAnnCategoryDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a annCategoryDo) Limit(limit int) IAnnCategoryDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a annCategoryDo) Offset(offset int) IAnnCategoryDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a annCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnCategoryDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a annCategoryDo) Unscoped() IAnnCategoryDo {
	return a.withDO(a.DO.Unscoped())
}

func (a annCategoryDo) Create(values ...*model.AnnCategory) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a annCategoryDo) CreateInBatches(values []*model.AnnCategory, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a annCategoryDo) Save(values ...*model.AnnCategory) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a annCategoryDo) First() (*model.AnnCategory, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnCategory), nil
	}
}

func (a annCategoryDo) Take() (*model.AnnCategory, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnCategory), nil
	}
}

func (a annCategoryDo) Last() (*model.AnnCategory, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnCategory), nil
	}
}

func (a annCategoryDo) Find() ([]*model.AnnCategory, error) {
	result, err := a.DO.Find()
	return result.([]*model.AnnCategory), err
}

func (a annCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.AnnCategory, err error) {
	buf := make([]*model.AnnCategory, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a annCategoryDo) FindInBatches(result *[]*model.AnnCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a annCategoryDo) Attrs(attrs ...field.AssignExpr) IAnnCategoryDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a annCategoryDo) Assign(attrs ...field.AssignExpr) IAnnCategoryDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a annCategoryDo) Joins(fields ...field.RelationField) IAnnCategoryDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a annCategoryDo) Preload(fields ...field.RelationField) IAnnCategoryDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a annCategoryDo) FirstOrInit() (*model.AnnCategory, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnCategory), nil
	}
}

func (a annCategoryDo) FirstOrCreate() (*model.AnnCategory, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.AnnCategory), nil
	}
}

func (a annCategoryDo) FindByPage(offset int, limit int) (result []*model.AnnCategory, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a annCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a annCategoryDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a annCategoryDo) Delete(models ...*model.AnnCategory) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *annCategoryDo) withDO(do gen.Dao) *annCategoryDo {
	a.DO = *do.(*gen.DO)
	return a
}
