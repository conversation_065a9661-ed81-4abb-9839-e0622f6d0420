// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/announcement/dal/model"
)

func newAnnouncement(db *gorm.DB, opts ...gen.DOOption) announcement {
	_announcement := announcement{}

	_announcement.announcementDo.UseDB(db, opts...)
	_announcement.announcementDo.UseModel(&model.Announcement{})

	tableName := _announcement.announcementDo.TableName()
	_announcement.ALL = field.NewAsterisk(tableName)
	_announcement.AnnID = field.NewInt64(tableName, "ann_id")
	_announcement.Title = field.NewString(tableName, "title")
	_announcement.Content = field.NewString(tableName, "content")
	_announcement.CategoryID = field.NewInt64(tableName, "category_id")
	_announcement.Priority = field.NewInt32(tableName, "priority")
	_announcement.Status = field.NewInt32(tableName, "status")
	_announcement.PublishTime = field.NewTime(tableName, "publish_time")
	_announcement.ItemIds = field.NewField(tableName, "item_ids")
	_announcement.ChannelIds = field.NewField(tableName, "channel_ids")
	_announcement.PublishType = field.NewInt32(tableName, "publish_type")
	_announcement.MessagePush = field.NewInt32(tableName, "message_push")
	_announcement.CreatedBy = field.NewString(tableName, "created_by")
	_announcement.AdIds = field.NewField(tableName, "ad_ids")
	_announcement.CreatedAt = field.NewTime(tableName, "created_at")
	_announcement.UpdatedAt = field.NewTime(tableName, "updated_at")
	_announcement.CategoryInfo = announcementBelongsToCategoryInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CategoryInfo", "model.AnnCategory"),
	}

	_announcement.fillFieldMap()

	return _announcement
}

// announcement 公告表
type announcement struct {
	announcementDo

	ALL          field.Asterisk
	AnnID        field.Int64  // 主键ID
	Title        field.String // 公告标题
	Content      field.String // 公告内容
	CategoryID   field.Int64  // 栏目ID
	Priority     field.Int32  // 优先级
	Status       field.Int32  // 状态：1待发布 2定时中 3已发布 4已下架
	PublishTime  field.Time   // 发布时间
	ItemIds      field.Field  // 关联商品挂牌编码
	ChannelIds   field.Field  // 发布终端列表
	PublishType  field.Int32  // 发布方式：1立即发布 2定时发布
	MessagePush  field.Int32  // 消息推送【1:推送, 2:不推送】
	CreatedBy    field.String // 创建人
	AdIds        field.Field  // 广告商ids，关联tmt-ad_id
	CreatedAt    field.Time   // 创建时间
	UpdatedAt    field.Time   // 更新时间
	CategoryInfo announcementBelongsToCategoryInfo

	fieldMap map[string]field.Expr
}

func (a announcement) Table(newTableName string) *announcement {
	a.announcementDo.UseTable(newTableName)
	return a.updateTableName(newTableName)
}

func (a announcement) As(alias string) *announcement {
	a.announcementDo.DO = *(a.announcementDo.As(alias).(*gen.DO))
	return a.updateTableName(alias)
}

func (a *announcement) updateTableName(table string) *announcement {
	a.ALL = field.NewAsterisk(table)
	a.AnnID = field.NewInt64(table, "ann_id")
	a.Title = field.NewString(table, "title")
	a.Content = field.NewString(table, "content")
	a.CategoryID = field.NewInt64(table, "category_id")
	a.Priority = field.NewInt32(table, "priority")
	a.Status = field.NewInt32(table, "status")
	a.PublishTime = field.NewTime(table, "publish_time")
	a.ItemIds = field.NewField(table, "item_ids")
	a.ChannelIds = field.NewField(table, "channel_ids")
	a.PublishType = field.NewInt32(table, "publish_type")
	a.MessagePush = field.NewInt32(table, "message_push")
	a.CreatedBy = field.NewString(table, "created_by")
	a.AdIds = field.NewField(table, "ad_ids")
	a.CreatedAt = field.NewTime(table, "created_at")
	a.UpdatedAt = field.NewTime(table, "updated_at")

	a.fillFieldMap()

	return a
}

func (a *announcement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := a.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (a *announcement) fillFieldMap() {
	a.fieldMap = make(map[string]field.Expr, 16)
	a.fieldMap["ann_id"] = a.AnnID
	a.fieldMap["title"] = a.Title
	a.fieldMap["content"] = a.Content
	a.fieldMap["category_id"] = a.CategoryID
	a.fieldMap["priority"] = a.Priority
	a.fieldMap["status"] = a.Status
	a.fieldMap["publish_time"] = a.PublishTime
	a.fieldMap["item_ids"] = a.ItemIds
	a.fieldMap["channel_ids"] = a.ChannelIds
	a.fieldMap["publish_type"] = a.PublishType
	a.fieldMap["message_push"] = a.MessagePush
	a.fieldMap["created_by"] = a.CreatedBy
	a.fieldMap["ad_ids"] = a.AdIds
	a.fieldMap["created_at"] = a.CreatedAt
	a.fieldMap["updated_at"] = a.UpdatedAt

}

func (a announcement) clone(db *gorm.DB) announcement {
	a.announcementDo.ReplaceConnPool(db.Statement.ConnPool)
	return a
}

func (a announcement) replaceDB(db *gorm.DB) announcement {
	a.announcementDo.ReplaceDB(db)
	return a
}

type announcementBelongsToCategoryInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a announcementBelongsToCategoryInfo) Where(conds ...field.Expr) *announcementBelongsToCategoryInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a announcementBelongsToCategoryInfo) WithContext(ctx context.Context) *announcementBelongsToCategoryInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a announcementBelongsToCategoryInfo) Session(session *gorm.Session) *announcementBelongsToCategoryInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a announcementBelongsToCategoryInfo) Model(m *model.Announcement) *announcementBelongsToCategoryInfoTx {
	return &announcementBelongsToCategoryInfoTx{a.db.Model(m).Association(a.Name())}
}

type announcementBelongsToCategoryInfoTx struct{ tx *gorm.Association }

func (a announcementBelongsToCategoryInfoTx) Find() (result *model.AnnCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a announcementBelongsToCategoryInfoTx) Append(values ...*model.AnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a announcementBelongsToCategoryInfoTx) Replace(values ...*model.AnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a announcementBelongsToCategoryInfoTx) Delete(values ...*model.AnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a announcementBelongsToCategoryInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a announcementBelongsToCategoryInfoTx) Count() int64 {
	return a.tx.Count()
}

type announcementDo struct{ gen.DO }

type IAnnouncementDo interface {
	gen.SubQuery
	Debug() IAnnouncementDo
	WithContext(ctx context.Context) IAnnouncementDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IAnnouncementDo
	WriteDB() IAnnouncementDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IAnnouncementDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IAnnouncementDo
	Not(conds ...gen.Condition) IAnnouncementDo
	Or(conds ...gen.Condition) IAnnouncementDo
	Select(conds ...field.Expr) IAnnouncementDo
	Where(conds ...gen.Condition) IAnnouncementDo
	Order(conds ...field.Expr) IAnnouncementDo
	Distinct(cols ...field.Expr) IAnnouncementDo
	Omit(cols ...field.Expr) IAnnouncementDo
	Join(table schema.Tabler, on ...field.Expr) IAnnouncementDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IAnnouncementDo
	RightJoin(table schema.Tabler, on ...field.Expr) IAnnouncementDo
	Group(cols ...field.Expr) IAnnouncementDo
	Having(conds ...gen.Condition) IAnnouncementDo
	Limit(limit int) IAnnouncementDo
	Offset(offset int) IAnnouncementDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnouncementDo
	Unscoped() IAnnouncementDo
	Create(values ...*model.Announcement) error
	CreateInBatches(values []*model.Announcement, batchSize int) error
	Save(values ...*model.Announcement) error
	First() (*model.Announcement, error)
	Take() (*model.Announcement, error)
	Last() (*model.Announcement, error)
	Find() ([]*model.Announcement, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Announcement, err error)
	FindInBatches(result *[]*model.Announcement, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Announcement) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IAnnouncementDo
	Assign(attrs ...field.AssignExpr) IAnnouncementDo
	Joins(fields ...field.RelationField) IAnnouncementDo
	Preload(fields ...field.RelationField) IAnnouncementDo
	FirstOrInit() (*model.Announcement, error)
	FirstOrCreate() (*model.Announcement, error)
	FindByPage(offset int, limit int) (result []*model.Announcement, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IAnnouncementDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (a announcementDo) Debug() IAnnouncementDo {
	return a.withDO(a.DO.Debug())
}

func (a announcementDo) WithContext(ctx context.Context) IAnnouncementDo {
	return a.withDO(a.DO.WithContext(ctx))
}

func (a announcementDo) ReadDB() IAnnouncementDo {
	return a.Clauses(dbresolver.Read)
}

func (a announcementDo) WriteDB() IAnnouncementDo {
	return a.Clauses(dbresolver.Write)
}

func (a announcementDo) Session(config *gorm.Session) IAnnouncementDo {
	return a.withDO(a.DO.Session(config))
}

func (a announcementDo) Clauses(conds ...clause.Expression) IAnnouncementDo {
	return a.withDO(a.DO.Clauses(conds...))
}

func (a announcementDo) Returning(value interface{}, columns ...string) IAnnouncementDo {
	return a.withDO(a.DO.Returning(value, columns...))
}

func (a announcementDo) Not(conds ...gen.Condition) IAnnouncementDo {
	return a.withDO(a.DO.Not(conds...))
}

func (a announcementDo) Or(conds ...gen.Condition) IAnnouncementDo {
	return a.withDO(a.DO.Or(conds...))
}

func (a announcementDo) Select(conds ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Select(conds...))
}

func (a announcementDo) Where(conds ...gen.Condition) IAnnouncementDo {
	return a.withDO(a.DO.Where(conds...))
}

func (a announcementDo) Order(conds ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Order(conds...))
}

func (a announcementDo) Distinct(cols ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Distinct(cols...))
}

func (a announcementDo) Omit(cols ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Omit(cols...))
}

func (a announcementDo) Join(table schema.Tabler, on ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Join(table, on...))
}

func (a announcementDo) LeftJoin(table schema.Tabler, on ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.LeftJoin(table, on...))
}

func (a announcementDo) RightJoin(table schema.Tabler, on ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.RightJoin(table, on...))
}

func (a announcementDo) Group(cols ...field.Expr) IAnnouncementDo {
	return a.withDO(a.DO.Group(cols...))
}

func (a announcementDo) Having(conds ...gen.Condition) IAnnouncementDo {
	return a.withDO(a.DO.Having(conds...))
}

func (a announcementDo) Limit(limit int) IAnnouncementDo {
	return a.withDO(a.DO.Limit(limit))
}

func (a announcementDo) Offset(offset int) IAnnouncementDo {
	return a.withDO(a.DO.Offset(offset))
}

func (a announcementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IAnnouncementDo {
	return a.withDO(a.DO.Scopes(funcs...))
}

func (a announcementDo) Unscoped() IAnnouncementDo {
	return a.withDO(a.DO.Unscoped())
}

func (a announcementDo) Create(values ...*model.Announcement) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Create(values)
}

func (a announcementDo) CreateInBatches(values []*model.Announcement, batchSize int) error {
	return a.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (a announcementDo) Save(values ...*model.Announcement) error {
	if len(values) == 0 {
		return nil
	}
	return a.DO.Save(values)
}

func (a announcementDo) First() (*model.Announcement, error) {
	if result, err := a.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Announcement), nil
	}
}

func (a announcementDo) Take() (*model.Announcement, error) {
	if result, err := a.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Announcement), nil
	}
}

func (a announcementDo) Last() (*model.Announcement, error) {
	if result, err := a.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Announcement), nil
	}
}

func (a announcementDo) Find() ([]*model.Announcement, error) {
	result, err := a.DO.Find()
	return result.([]*model.Announcement), err
}

func (a announcementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Announcement, err error) {
	buf := make([]*model.Announcement, 0, batchSize)
	err = a.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (a announcementDo) FindInBatches(result *[]*model.Announcement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return a.DO.FindInBatches(result, batchSize, fc)
}

func (a announcementDo) Attrs(attrs ...field.AssignExpr) IAnnouncementDo {
	return a.withDO(a.DO.Attrs(attrs...))
}

func (a announcementDo) Assign(attrs ...field.AssignExpr) IAnnouncementDo {
	return a.withDO(a.DO.Assign(attrs...))
}

func (a announcementDo) Joins(fields ...field.RelationField) IAnnouncementDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Joins(_f))
	}
	return &a
}

func (a announcementDo) Preload(fields ...field.RelationField) IAnnouncementDo {
	for _, _f := range fields {
		a = *a.withDO(a.DO.Preload(_f))
	}
	return &a
}

func (a announcementDo) FirstOrInit() (*model.Announcement, error) {
	if result, err := a.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Announcement), nil
	}
}

func (a announcementDo) FirstOrCreate() (*model.Announcement, error) {
	if result, err := a.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Announcement), nil
	}
}

func (a announcementDo) FindByPage(offset int, limit int) (result []*model.Announcement, count int64, err error) {
	result, err = a.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = a.Offset(-1).Limit(-1).Count()
	return
}

func (a announcementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = a.Count()
	if err != nil {
		return
	}

	err = a.Offset(offset).Limit(limit).Scan(result)
	return
}

func (a announcementDo) Scan(result interface{}) (err error) {
	return a.DO.Scan(result)
}

func (a announcementDo) Delete(models ...*model.Announcement) (result gen.ResultInfo, err error) {
	return a.DO.Delete(models)
}

func (a *announcementDo) withDO(do gen.Dao) *announcementDo {
	a.DO = *do.(*gen.DO)
	return a
}
