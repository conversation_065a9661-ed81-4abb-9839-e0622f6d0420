package service

import (
	"app_service/apps/business/announcement/dal/model"
	"app_service/apps/business/announcement/define"
	"app_service/apps/business/announcement/define/enums"
	"app_service/apps/business/announcement/repo"
	"app_service/apps/business/announcement/service/logic"
	commonDefine "app_service/apps/platform/common/define"
	commonEnum "app_service/apps/platform/common/define/enum"
	commonLogic "app_service/apps/platform/common/service/logic"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"context"

	"encoding/json"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/datatypes"
)

// GetAnnouncementList 查询公告列表
func (s *Service) GetAnnouncementList(req *define.GetAnnouncementAdminListReq) (*define.GetAnnouncementAdminListResp, error) {
	db := repo.GetDB().WithContext(s.ctx)

	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		switch req.TimeType {
		case enums.AnnouncementTimeTypePublish:
			db = db.Where("publish_time >= ? AND publish_time <= ?", req.StartTime, req.EndTime)
		case enums.AnnouncementTimeTypeCreate:
			db = db.Where("created_at >= ? AND created_at <= ?", req.StartTime, req.EndTime)
		}
	}
	if req.ID != 0 {
		db = db.Where("ann_id = ?", req.ID)
	}
	if req.Title != "" {
		db = db.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.ItemId != "" {
		db = db.Where("JSON_CONTAINS(item_ids, ?)", "\""+req.ItemId+"\"")
	}
	if req.CategoryID != 0 {
		db = db.Where("category_id = ?", req.CategoryID)
	}
	if req.ChannelId != "" {
		db = db.Where("JSON_CONTAINS(channel_ids, ?)", "\""+req.ChannelId+"\"")
	}
	if req.Status != 0 {
		db = db.Where("status = ?", req.Status)
	}
	if req.CreatedBy != "" {
		db = db.Where("created_by = ?", req.CreatedBy)
	}

	// 排序、分页
	db = db.Order("created_at DESC")
	offset := (req.GetPage() - 1) * req.GetPageSize()
	if offset < 0 {
		offset = 0
	}
	var list []*model.Announcement
	var count int64
	db.Model(&model.Announcement{}).Count(&count)
	err := db.Offset(offset).Limit(req.GetPageSize()).Find(&list).Error
	if err != nil {
		return nil, err
	}

	resp := &define.GetAnnouncementAdminListResp{
		List:  make([]*define.GetAnnouncementAdminListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有需要查询的ID
	var categoryIds []int64
	var allItemIds []string
	for _, v := range list {
		categoryIds = append(categoryIds, v.CategoryID)
		if v.ItemIds != nil {
			itemIds, err := logic.UnmarshalIds(*v.ItemIds)
			if err != nil {
				return nil, err
			}
			allItemIds = append(allItemIds, itemIds...)
		}
	}

	// 批量查询分类信息
	categorySchema := repo.GetQuery().AnnCategory
	categories, _, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(
		search.NewQueryBuilder().In(categorySchema.AnnCategoryID, categoryIds).Build(),
		1, len(categoryIds),
	)
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[int64]*model.AnnCategory)
	for _, category := range categories {
		categoryMap[category.AnnCategoryID] = category
	}

	// 批量查询商品信息
	itemMap, err := issueFacade.GetIssueItemMap(s.ctx, allItemIds)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetIssueItemMap err:%v", err)
		return nil, err
	}

	dataList := make([]*define.GetAnnouncementAdminListData, 0)
	for _, v := range list {
		// 解析ItemIds
		var itemIds []string
		var itemIdInfoList []*define.GetItemIdInfoList
		if v.ItemIds != nil {
			itemIds, err = logic.UnmarshalIds(*v.ItemIds)
			if err != nil {
				return nil, err
			}
			for _, itemId := range itemIds {
				if item, ok := itemMap[itemId]; ok {
					itemIdInfoList = append(itemIdInfoList, &define.GetItemIdInfoList{
						ID:       item.ID.Hex(),
						ItemId:   item.ItemID.Hex(),
						ItemName: item.ItemName,
						ImageUrl: item.ImageURL,
					})
				}
			}
		}

		// 解析ChannelIds
		var channelIds []string
		if len(v.ChannelIds) > 0 {
			channelIds, err = logic.UnmarshalIds(v.ChannelIds)
			if err != nil {
				return nil, err
			}
		}

		// 解析AdIds
		var adIds []string
		if v.AdIds != nil {
			adIds, err = logic.UnmarshalIds(*v.AdIds)
			if err != nil {
				return nil, err
			}
		}

		// 获取分类信息
		var categoryInfo *define.GetAnnCategoryAdminLessDetailResp
		if category, ok := categoryMap[v.CategoryID]; ok {
			categoryInfo = &define.GetAnnCategoryAdminLessDetailResp{
				ID:   category.AnnCategoryID,
				Name: category.Name,
			}
		}

		dataList = append(dataList, &define.GetAnnouncementAdminListData{
			ID:             v.AnnID,
			Title:          v.Title,
			Content:        v.Content,
			CategoryID:     v.CategoryID,
			CategoryInfo:   categoryInfo,
			Priority:       v.Priority,
			Status:         enums.AnnouncementStatus(v.Status),
			PublishTime:    v.PublishTime,
			PublishType:    enums.AnnouncementPublishType(v.PublishType),
			ItemIds:        itemIds,
			ItemIdInfoList: itemIdInfoList,
			ChannelIds:     channelIds,
			CreatedBy:      v.CreatedBy,
			AdIds:          adIds,
			CreatedAt:      v.CreatedAt,
			UpdatedAt:      v.UpdatedAt,
		})
	}
	resp.List = dataList
	return resp, nil
}

// GetAnnouncementDetail 查询公告详情
func (s *Service) GetAnnouncementDetail(req *define.GetAnnouncementAdminDetailReq) (*define.GetAnnouncementAdminDetailResp, error) {
	annSchema := repo.GetQuery().Announcement
	builder := search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID)
	announcement, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}

	// Get item info list
	var itemInfoList []*define.GetItemIdInfoList
	var itemIds []string
	if announcement.ItemIds != nil {
		itemIds, err = logic.UnmarshalIds(*announcement.ItemIds)
		if err != nil {
			return nil, err
		}
		if len(itemIds) > 0 {
			issueItems, err := issueFacade.GetIssueItems(s.ctx, itemIds)
			if err != nil {
				log.Ctx(s.ctx).Errorf("GetIssueItems err:%v", err)
				return nil, err
			}

			for _, item := range issueItems {
				itemInfoList = append(itemInfoList, &define.GetItemIdInfoList{
					ID:       item.ID.Hex(),
					ItemId:   item.ItemID.Hex(),
					ItemName: item.ItemName,
					ImageUrl: item.ImageURL,
				})
			}
		}
	}

	// 解析ChannelIds
	var channelIds []string
	if len(announcement.ChannelIds) > 0 {
		channelIds, err = logic.UnmarshalIds(announcement.ChannelIds)
		if err != nil {
			return nil, err
		}
	}

	// CategoryID 查CategoryInfo
	categorySchema := repo.GetQuery().AnnCategory
	category, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, announcement.CategoryID).Build(),
	)
	if err != nil {
		return nil, err
	}

	categoryInfo := &define.GetAnnCategoryAdminLessDetailResp{
		ID:   category.AnnCategoryID,
		Name: category.Name,
	}

	messagePush := enums.AnnouncementMessagePushNo // 历史数据都处理成 不推送
	if announcement.MessagePush != 0 {
		messagePush = enums.AnnouncementMessagePushEnum(announcement.MessagePush)
	}
	resp := &define.GetAnnouncementAdminDetailResp{
		ID:             announcement.AnnID,
		Title:          announcement.Title,
		Content:        announcement.Content,
		CategoryID:     announcement.CategoryID,
		CategoryInfo:   categoryInfo,
		ItemIds:        itemIds,
		ItemIdInfoList: itemInfoList,
		ChannelIds:     channelIds,
		Priority:       announcement.Priority,
		Status:         enums.AnnouncementStatus(announcement.Status),
		PublishTime:    announcement.PublishTime,
		PublishType:    enums.AnnouncementPublishType(announcement.PublishType),
		MessagePush:    messagePush,
		CreatedBy:      announcement.CreatedBy,
		CreatedAt:      announcement.CreatedAt,
		UpdatedAt:      announcement.UpdatedAt,
	}

	return resp, nil
}

// AddAnnouncement 新增公告
func (s *Service) AddAnnouncement(req *define.AddAnnouncementReq) (*define.AddAnnouncementResp, error) {
	annSchema := repo.GetQuery().Announcement

	var itemIdsJsonData datatypes.JSON
	itemIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ItemIds))
	var channelIdsJsonData datatypes.JSON
	channelIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ChannelIds))

	announcement := &model.Announcement{
		AnnID:       snowflakeutl.GenerateID(),
		Title:       req.Title,
		Content:     req.Content,
		CategoryID:  req.CategoryID,
		Status:      enums.AnnouncementStatusDraft.Val(),
		PublishTime: req.PublishTime,
		PublishType: req.PublishType.Val(),
		MessagePush: req.MessagePush.Val(),
		CreatedBy:   s.userService.GetAdminId(),
		ItemIds:     &itemIdsJsonData,
		ChannelIds:  channelIdsJsonData,
		Priority:    req.Priority,
	}

	err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).Save(announcement)
	if err != nil {
		log.Ctx(s.ctx).Errorf("AddAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     announcement.AnnID,
			RelateType:   commonEnum.OperationLogRelateTypeAnn,
			Action:       commonEnum.OperationLogActionCreate,
			AfterContent: announcement.Content,
			OperatedBy:   announcement.CreatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.AddAnnouncementResp{
		ID: announcement.AnnID,
	}, nil
}

// EditAnnouncement 编辑公告
func (s *Service) EditAnnouncement(req *define.EditAnnouncementReq) (*define.EditAnnouncementResp, error) {
	annSchema := repo.GetQuery().Announcement
	getAnn, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID).Build(),
	)
	if err != nil {
		return nil, err
	}

	if getAnn.Status == enums.AnnouncementStatusOffline.Val() {
		return nil, define.AH200001Err
	}

	var status = enums.AnnouncementStatus(getAnn.Status)
	if status == enums.AnnouncementStatusDraft || status == enums.AnnouncementStatusScheduled {
		if req.PublishType.Val() == enums.AnnouncementPublishTypeImmediate.Val() {
			status = enums.AnnouncementStatusDraft
		} else if req.PublishType.Val() == enums.AnnouncementPublishTypeTiming.Val() {
			status = enums.AnnouncementStatusScheduled
		}
	}

	var itemIdsJsonData datatypes.JSON
	itemIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ItemIds))
	var channelIdsJsonData datatypes.JSON
	channelIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ChannelIds))

	announcement := &model.Announcement{
		AnnID:       req.ID,
		Title:       req.Title,
		Content:     req.Content,
		CategoryID:  req.CategoryID,
		Priority:    req.Priority,
		Status:      status.Val(),
		PublishTime: req.PublishTime,
		PublishType: req.PublishType.Val(),
		MessagePush: req.MessagePush.Val(),
		ItemIds:     &itemIdsJsonData,
		ChannelIds:  channelIdsJsonData,
	}

	err = repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).UpdateById(announcement)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(getAnn)
	afterContent, _ := json.Marshal(announcement)
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      announcement.AnnID,
			RelateType:    commonEnum.OperationLogRelateTypeAnn,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditAnnouncementResp{
		ID: req.ID,
	}, nil
}

// EditAnnouncementStatus 编辑公告状态
func (s *Service) EditAnnouncementStatus(req *define.EditAnnouncementStatusReq) (*define.EditAnnouncementStatusResp, error) {
	annSchema := repo.GetQuery().Announcement
	builder := search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID)

	m := &model.Announcement{
		Status: req.Status.Val(),
	}

	getAnn, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	isImmediatePublish := false
	if req.Status.Val() == enums.AnnouncementStatusOffline.Val() {
		// 仅限定时中和已发布的状态的可操作下架
		if !(getAnn.Status == enums.AnnouncementStatusScheduled.Val() ||
			getAnn.Status == enums.AnnouncementStatusPublished.Val()) {
			return nil, define.AH200002Err
		}
	} else if req.Status.Val() == enums.AnnouncementStatusPublished.Val() {
		// 仅限待发布的状态的可操作发布
		if getAnn.Status != enums.AnnouncementStatusDraft.Val() {
			return nil, define.AH200007Err
		}
		if getAnn.PublishType == enums.AnnouncementPublishTypeImmediate.Val() {
			isImmediatePublish = true
			m.Status = int32(enums.AnnouncementStatusPublished)
			// 更新发布时间
			now := util.Now()
			m.PublishTime = &now
		} else if getAnn.PublishType == enums.AnnouncementPublishTypeTiming.Val() {
			m.Status = int32(enums.AnnouncementStatusScheduled)
		}
	} else {
		return nil, define.AH200005Err
	}

	err = repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m, builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnouncementStatus err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(map[string]any{"status": getAnn.Status})
	afterContent, _ := json.Marshal(map[string]any{"status": req.Status})
	go func() {
		var action commonEnum.OperationLogActionEnum
		if req.Status.Val() == enums.AnnouncementStatusOffline.Val() {
			action = commonEnum.OperationLogActionRemove
		} else if req.Status.Val() == enums.AnnouncementStatusPublished.Val() {
			action = commonEnum.OperationLogActionPublish
		}
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeAnn,
			Action:        action,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	// 消息推送
	if getAnn.MessagePush == enums.AnnouncementMessagePushYes.Val() && isImmediatePublish {
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			pushErr := logic.PushAnnouncementMessage(spanCtx, getAnn)
			if pushErr != nil {
				log.Ctx(spanCtx).Errorf("平台方公告消息推送失败: %v", pushErr)
			}
		}()
	}

	return &define.EditAnnouncementStatusResp{
		ID: req.ID,
	}, nil
}

// EditAnnouncementPriority 编辑公告优先级
func (s *Service) EditAnnouncementPriority(req *define.EditAnnouncementPriorityReq) (*define.EditAnnouncementPriorityResp, error) {
	annSchema := repo.GetQuery().Announcement
	builder := search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID)

	getAnn, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	m := &model.Announcement{
		Priority: req.Priority,
	}

	err = repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		builder.Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnouncementPriority err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(map[string]any{"priority": getAnn.Status})
	afterContent, _ := json.Marshal(map[string]any{"priority": req.Priority})
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeAnn,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditAnnouncementPriorityResp{
		ID: req.ID,
	}, nil
}

// EditAnnouncementAdIds 编辑公告关联广告ID
func (s *Service) EditAnnouncementAdIds(req *define.EditAnnouncementAdIdsReq) (*define.EditAnnouncementAdIdsResp, error) {
	annSchema := repo.GetQuery().Announcement

	//// 仅已发布状态的公告可同步媒体
	//if len(req.AdIds) > 0 {
	//	getAnn, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
	//		search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID).Build(),
	//	)
	//	if err != nil {
	//		return nil, err
	//	}
	//	if getAnn.Status != enums.AnnouncementStatusPublished.Val() {
	//		return nil, define.AH200004Err
	//	}
	//}

	var adIdsJson datatypes.JSON = datatypes.JSON(util.Obj2JsonStr(req.AdIds))
	m := &model.Announcement{
		AdIds: &adIdsJson,
	}

	err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnouncementAdIds err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	afterContent, _ := json.Marshal(req)
	go func() {
		var action commonEnum.OperationLogActionEnum
		if len(req.AdIds) > 0 {
			action = commonEnum.OperationLogActionSyncMediaON
		} else {
			action = commonEnum.OperationLogActionSyncMediaOFF
		}
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     req.ID,
			RelateType:   commonEnum.OperationLogRelateTypeAnn,
			Action:       action,
			AfterContent: string(afterContent),
			OperatedBy:   operatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.EditAnnouncementAdIdsResp{
		ID: req.ID,
	}, nil
}

// DelAnnouncement 删除公告
func (s *Service) DelAnnouncement(req *define.DelAnnouncementReq) (*define.DelAnnouncementResp, error) {
	annSchema := repo.GetQuery().Announcement
	builder := search.NewQueryBuilder().Eq(annSchema.AnnID, req.ID)

	getAnn, err := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	m := &model.Announcement{
		//Status: enums.AnnouncementStatusOffline.Val(),
		//IsDel:  1,
	}

	err = repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		builder.Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("DelAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(getAnn)
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeAnn,
			Action:        commonEnum.OperationLogActionDelete,
			BeforeContent: string(beforeContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.DelAnnouncementResp{
		ID: req.ID,
	}, nil
}

// GetAnnCategoryList 查询公告分类列表
func (s *Service) GetAnnCategoryList(req *define.GetAnnCategoryAdminListReq) (*define.GetAnnCategoryAdminListResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	builder := search.NewQueryBuilder().OrderByDesc(categorySchema.Priority, categorySchema.CreatedAt)

	// 获取分类列表
	//builder = builder.Eq(categorySchema.IsDel, 0)
	list, count, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	resp := &define.GetAnnCategoryAdminListResp{}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有分类ID
	categoryIds := make([]int64, 0, len(list))
	for _, v := range list {
		categoryIds = append(categoryIds, v.AnnCategoryID)
	}

	// 获取每个分类的公告数量
	categoryCountMap, err := logic.GetCategoryAnnCounts(s.ctx, categoryIds)
	if err != nil {
		return nil, err
	}

	// 构建响应
	dataList := make([]*define.GetAnnCategoryAdminListData, 0)
	for _, v := range list {
		dataList = append(dataList, &define.GetAnnCategoryAdminListData{
			ID:        v.AnnCategoryID,
			Name:      v.Name,
			AnnNum:    categoryCountMap[v.AnnCategoryID], // 使用映射获取数量，如果不存在则为0
			Priority:  v.Priority,
			Status:    enums.AnnCategoryStatus(v.Status),
			CreatedAt: v.CreatedAt,
			UpdatedAt: v.UpdatedAt,
		})
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetAnnCategoryDetail 获取公告分类详情
func (s *Service) GetAnnCategoryDetail(req *define.GetAnnCategoryAdminDetailReq) (*define.GetAnnCategoryAdminDetailResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	builder := search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, req.ID)

	category, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}

	return &define.GetAnnCategoryAdminDetailResp{
		ID:              category.AnnCategoryID,
		Name:            category.Name,
		TextColor:       category.TextColor,
		BackgroundColor: category.BackgroundColor,
		Priority:        category.Priority,
		Status:          enums.AnnCategoryStatus(category.Status),
		CreatedAt:       category.CreatedAt,
		UpdatedAt:       category.UpdatedAt,
	}, nil
}

// AddAnnCategory 新增公告分类
func (s *Service) AddAnnCategory(req *define.AddAnnCategoryReq) (*define.AddAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	//getCategory, _ := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
	//	search.NewQueryBuilder().Eq(categorySchema.Name, req.Name).Build(),
	//)
	//if getCategory != nil {
	//	return nil, define.AH200003Err
	//}

	category := &model.AnnCategory{
		AnnCategoryID:   snowflakeutl.GenerateID(),
		Name:            req.Name,
		TextColor:       req.TextColor,
		BackgroundColor: req.BackgroundColor,
		Priority:        req.Priority,
		Status:          enums.AnnCategoryStatusEnable.Val(),
	}

	err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Save(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("AddAnnCategory err:%v", err)
		return nil, err
	}

	return &define.AddAnnCategoryResp{
		ID: category.AnnCategoryID,
	}, nil
}

// EditAnnCategory 编辑公告分类
func (s *Service) EditAnnCategory(req *define.EditAnnCategoryReq) (*define.EditAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	//getCategory, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
	//	search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, req.ID).Build(),
	//)
	//if err != nil {
	//	return nil, err
	//}
	//
	//if getCategory != nil && getCategory.Name != req.Name {
	//	count, err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Count(
	//		search.NewQueryBuilder().
	//			Eq(categorySchema.Name, req.Name).
	//			Ne(categorySchema.AnnCategoryID, req.ID).
	//			Build(),
	//	)
	//	if err != nil {
	//		return nil, err
	//	}
	//	if count > 0 {
	//		return nil, define.AH200001Err
	//	}
	//}

	category := &model.AnnCategory{
		AnnCategoryID:   req.ID,
		Name:            req.Name,
		TextColor:       req.TextColor,
		BackgroundColor: req.BackgroundColor,
		Priority:        req.Priority,
	}

	err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).UpdateById(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnCategory err:%v", err)
		return nil, err
	}

	return &define.EditAnnCategoryResp{
		ID: req.ID,
	}, nil
}

// EditAnnCategoryPriority 编辑公告分类优先级
func (s *Service) EditAnnCategoryPriority(req *define.EditAnnCategoryPriorityReq) (*define.EditAnnCategoryPriorityResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	m := &model.AnnCategory{
		Priority: req.Priority,
	}

	err := repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditAnnCategoryPriority err:%v", err)
		return nil, err
	}

	return &define.EditAnnCategoryPriorityResp{
		ID: req.ID,
	}, nil
}

// DelAnnCategory 删除公告分类
func (s *Service) DelAnnCategory(req *define.DelAnnCategoryReq) (*define.DelAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().AnnCategory
	m := &model.AnnCategory{
		Status: enums.AnnCategoryStatusDeleted.Val(),
		IsDel:  1,
	}

	// 有关联公告的栏目不可删除
	annSchema := repo.GetQuery().Announcement
	announcementRepo := repo.NewAnnouncementRepo(annSchema.WithContext(s.ctx))
	count, err := announcementRepo.Count(
		search.NewQueryBuilder().Eq(annSchema.CategoryID, req.ID).Build(),
	)
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, define.AH200003Err
	}

	err = repo.NewAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.AnnCategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("DelAnnCategory err:%v", err)
		return nil, err
	}

	return &define.DelAnnCategoryResp{
		ID: req.ID,
	}, nil
}
