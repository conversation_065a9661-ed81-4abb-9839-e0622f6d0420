package enums

// AnnouncementStatus 公告状态 1待发布 2定时中 3已发布 4已下架
type AnnouncementStatus int32

func (s AnnouncementStatus) Val() int32 {
	return int32(s)
}

const (
	// AnnouncementStatusDraft 待发布
	AnnouncementStatusDraft AnnouncementStatus = 1
	// AnnouncementStatusScheduled 定时中
	AnnouncementStatusScheduled AnnouncementStatus = 2
	// AnnouncementStatusPublished 已发布
	AnnouncementStatusPublished AnnouncementStatus = 3
	// AnnouncementStatusOffline 已下架
	AnnouncementStatusOffline AnnouncementStatus = 4
)

// AnnouncementPublishType 公告发布方式 1立即发布 2定时发布
type AnnouncementPublishType int32

func (s AnnouncementPublishType) Val() int32 {
	return int32(s)
}

const (
	// AnnouncementPublishTypeImmediate 立即发布
	AnnouncementPublishTypeImmediate AnnouncementPublishType = 1
	// AnnouncementPublishTypeTiming 定时发布
	AnnouncementPublishTypeTiming AnnouncementPublishType = 2
)

// AnnouncementTimeType 时间类型 1发布时间 2创建时间
type AnnouncementTimeType int32

func (s AnnouncementTimeType) Val() int32 {
	return int32(s)
}

const (
	// AnnouncementTimeTypePublish 发布时间
	AnnouncementTimeTypePublish AnnouncementTimeType = 1
	// AnnouncementTimeTypeCreate 创建时间
	AnnouncementTimeTypeCreate AnnouncementTimeType = 2
)

type AnnouncementMessagePushEnum int32

func (s AnnouncementMessagePushEnum) Val() int32 {
	return int32(s)
}

const (
	AnnouncementMessagePushYes AnnouncementMessagePushEnum = 1 // 推送
	AnnouncementMessagePushNo  AnnouncementMessagePushEnum = 2 // 不推送
)
