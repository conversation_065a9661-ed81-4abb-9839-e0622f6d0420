package define

import (
	"time"

	"app_service/pkg/pagination"
)

// 公告列表相关结构体
type (
	GetAnnouncementWebListReq struct {
		pagination.Pagination
		KeyWord    string `form:"keyword" json:"keyword" binding:"max=20"` // 关键字(搜索标题和内容，限输20个字符)
		CategoryID int64  `form:"category_id" json:"category_id,string"`   // 分类ID
		ItemId     string `form:"item_id" json:"item_id"`                  // 商品ID
		OrderBy    string `form:"order_by" json:"order_by"`                // 排序字段，支持：publish_time
		SortOrder  string `form:"sort_order" json:"sort_order"`            // 排序方式，desc: 倒序，asc: 升序
	}

	GetAnnouncementWebListData struct {
		ID           int64                            `json:"id,string"`          // 公告ID
		Title        string                           `json:"title"`              // 公告标题
		CategoryID   int64                            `json:"category_id,string"` // 分类ID
		CategoryInfo *GetAnnCategoryWebLessDetailResp `json:"category_info"`      // 分类信息
		PublishTime  *time.Time                       `json:"publish_time"`       // 发布时间
		CurrentTime  string                           `json:"current_time"`       // 当前时间
	}

	GetAnnouncementWebListResp struct {
		List    []*GetAnnouncementWebListData `json:"list"`
		HasMore bool                          `json:"has_more"` // 判断当前页是否为最后一页
	}
)

// 公告详情相关结构体
type (
	GetAnnouncementWebDetailReq struct {
		ID int64 `form:"id" json:"id,string" binding:"required"` // 公告ID
	}

	GetAnnouncementWebDetailResp struct {
		ID           int64                            `json:"id,string"`          // 公告ID
		Title        string                           `json:"title"`              // 公告标题
		Content      string                           `json:"content"`            // 公告内容
		CategoryID   int64                            `json:"category_id,string"` // 分类ID
		CategoryInfo *GetAnnCategoryWebLessDetailResp `json:"category_info"`      // 分类信息
		PublishTime  *time.Time                       `json:"publish_time"`       // 发布时间
		ItemIds      []string                         `json:"item_ids"`           // 关联商品ID列表
	}
)

// 公告分类列表相关结构体
type (
	GetAnnCategoryWebListReq struct {
		pagination.Pagination
		//Name   string `form:"name" json:"name"`     // 分类名称
		//Status int32  `form:"status" json:"status"` // 状态
	}

	GetAnnCategoryWebListData struct {
		ID   int64  `json:"id,string"` // 分类ID
		Name string `json:"name"`      // 分类名称
	}

	GetAnnCategoryWebListResp struct {
		List    []*GetAnnCategoryWebListData `json:"list"`
		HasMore bool                         `json:"has_more"` // 判断当前页是否为最后一页
	}
)

// 公告分类详情相关结构体
type (
	//GetAnnCategoryWebDetailReq struct {
	//	ID int64 `form:"id" json:"id" binding:"required"` // 分类ID
	//}
	//
	//GetAnnCategoryWebDetailResp struct {
	//	ID              int64                   `json:"id"`               // 分类ID
	//	Name            string                  `json:"name"`             // 分类名称
	//	TextColor       string                  `json:"text_color"`       // 文字颜色
	//	BackgroundColor string                  `json:"background_color"` // 背景颜色
	//	Priority        int32                   `json:"priority"`         // 优先级
	//	Status          enums.AnnCategoryStatus `json:"status"`           // 状态
	//	CreatedAt       time.Time               `json:"created_at"`       // 创建时间
	//	UpdatedAt       time.Time               `json:"updated_at"`       // 更新时间
	//}

	GetAnnCategoryWebLessDetailResp struct {
		ID              int64  `json:"id,string"`        // 分类ID
		Name            string `json:"name"`             // 分类名称
		TextColor       string `json:"text_color"`       // 文字颜色
		BackgroundColor string `json:"background_color"` // 背景颜色
	}
)
