package define

import (
	"time"

	"app_service/pkg/pagination"
)

type (
	// GetSynthesisRuleResp 获取合成规则
	GetSynthesisRuleResp struct {
		Content string `json:"content"`
	}
)

type (
	GetSynthesisWebListReq struct {
		pagination.Pagination
	}

	GetSynthesisWebListData struct {
		ActivityCode    string    `json:"activity_code"`     // 活动编码
		Title           string    `json:"title"`             // 活动名称
		ActivityType    int32     `json:"activity_type"`     // 合成类型【1-优先购权益；2-商品】
		CoverUrl        string    `json:"cover_url"`         // 封面图
		Status          int32     `json:"status"`            // 合成活动状态2:已上架;4:已结束
		TotalStock      int32     `json:"total_stock"`       // 总库存
		IssuerName      string    `json:"issuer_name"`       // 发行名称
		IssuerShortName string    `json:"issuer_short_name"` // 发行方简称
		SellerName      string    `json:"seller_name"`       // 销售方全称
		SellerShortName string    `json:"seller_short_name"` // 销售方简称
		StartTime       time.Time `json:"start_time"`        // 开始时间
		EndTime         time.Time `json:"end_time"`          // 结束时间
	}

	GetSynthesisWebListResp struct {
		List        []*GetSynthesisWebListData `json:"list"`
		Total       int64                      `json:"total"`
		CurrentTime string                     `json:"current_time"` // 服务器当前时间
	}
)

type (
	GetSynthesisWebDetailReq struct {
		ActivityCode string `form:"activity_code" json:"activity_code" binding:"required"` // 活动编码
	}

	GetSynthesisWebDetailResp struct {
		ActivityCode           string                    `json:"activity_code"`            // 活动编码
		Title                  string                    `json:"title"`                    // 活动名称
		ActivityType           int32                     `json:"activity_type"`            // 合成类型【1-优先购权益；2-商品】
		CoverUrl               string                    `json:"cover_url"`                // 封面图
		Status                 int32                     `json:"status"`                   // 合成活动状态2:已上架;4:已结束
		ItemId                 string                    `json:"item_id"`                  // 合成物品id(商品/优先购)
		ItemTitle              string                    `json:"item_title"`               // 合成物品名称(商品/优先购)
		ItemImageUrl           string                    `json:"item_image_url"`           // 合成物品图片
		UserLimit              int32                     `json:"user_limit"`               // 每人限合(0不限制)
		Stock                  *int32                    `json:"stock"`                    // 剩余库存
		TotalStock             int32                     `json:"total_stock"`              // 总库存
		StartTime              time.Time                 `json:"start_time"`               // 开始时间
		EndTime                time.Time                 `json:"end_time"`                 // 结束时间
		ActivityDesc           string                    `json:"activity_desc"`            // 说明
		CurrentTime            string                    `json:"current_time"`             // 服务器当前时间
		UserSynthesisMaterials []*UserSynthesisMaterials `json:"user_synthesis_materials"` //用户融合材料
		MaxLimit               int32                     `json:"max_limit"`                // 一次限合最大数量
	}

	UserSynthesisMaterials struct {
		Id                          int64                         `json:"id"`                       // 合成材料id
		MaterialsType               int32                         `json:"materials_type"`           // 材料类型【1-核心材料；2-关键材料】
		Qty                         int32                         `json:"qty"`                      // 集齐数量
		UserSynthesisMaterialsDatas []*UserSynthesisMaterialsData `json:"synthesis_materials_data"` //融合材料
	}

	UserSynthesisMaterialsData struct {
		ItemId            string `json:"item_id"`            // 商品id
		ItemTitle         string `json:"item_title"`         // 商品名称
		ItemImageUrl      string `json:"image_url"`          // 商品图片
		Qty               int32  `json:"qty"`                // 集齐数量
		UserQty           int32  `json:"user_qty"`           // 用户持有数量
		CirculationStatus int32  `json:"circulation_status"` // 流通状态1:不限流通;2:禁止流通
	}
)

type (
	LaunchSynthesisReq struct {
		ActivityCode             string                      `json:"activity_code" binding:"required"`       // 活动编码
		Qty                      int32                       `json:"qty" binding:"required,min=1"`           // 合成数量
		LaunchSynthesisMaterials []*LaunchSynthesisMaterials `json:"synthesis_materials" binding:"required"` // 选中合成材料
	}

	LaunchSynthesisMaterials struct {
		Id      int64    `json:"id"`      // 合成材料id
		ItemIds []string `json:"itemIds"` // 融合材料物品id集合
	}

	LaunchSynthesisResp struct {
		ItemTitle    string `json:"item_title"`     // 合成物品名称
		ItemId       string `json:"item_id"`        // 合成物品id
		ItemCoverUrl string `json:"item_cover_url"` // 合成物品图片
		Qty          int32  `json:"qty"`            // 合成数量
		OrderId      string `json:"order_id"`       // 订单号
	}
)

type (
	GetSynthesisWebOrderListReq struct {
		pagination.Pagination
	}
	GetSynthesisOrderListData struct {
		OrderID      string    `json:"order_id"`       // 合成订单编号
		ActivityType int32     `json:"activity_type"`  // 合成类型【1-优先购权益；2-商品】
		ItemTitle    string    `json:"item_title"`     // 合成物品名称(商品/优先购)
		ItemCoverURL string    `json:"item_cover_url"` // 合成物品图片(商品/优先购)
		Qty          int32     `json:"qty"`            // 集齐数量
		CreatedAt    time.Time `json:"created_at"`     // 创建时间
	}
	GetSynthesisWebOrderListResp struct {
		List  []*GetSynthesisOrderListData `json:"list"`
		Total int64                        `json:"total"`
	}
)

type (
	GetSynthesisWebOrderDetailReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 合成订单编号
	}
	GetSynthesisWebOrderDetailResp struct {
		OrderID      string                              `json:"order_id"`       // 合成订单编号
		ActivityType int32                               `json:"activity_type"`  // 合成类型【1-优先购权益；2-商品】
		ItemTitle    string                              `json:"item_title"`     // 合成物品名称(商品/优先购)
		ItemCoverURL string                              `json:"item_cover_url"` // 合成物品图片(商品/优先购)
		Qty          int32                               `json:"qty"`            // 集齐数量
		CreatedAt    time.Time                           `json:"created_at"`     // 创建时间
		ChainHash    string                              `json:"chain_hash"`     // 合成链上hash
		Materials    []*GetSynthesisOrderDetailMaterials `json:"materials"`      // 合成材料
	}
	GetSynthesisOrderDetailMaterials struct {
		ItemName string `json:"item_name"` // 合成材料名称
		ItemUrl  string `json:"item_url"`  // 合成材料图片
		Qty      int32  `json:"qty"`       // 合成材料数量
	}
)

type (
	SynthesisCancelFusion struct {
		SynthesisOrderID int64 `json:"id"` // 订单主键
	}

	SynthesisFusion struct {
		SynthesisOrderID int64 `json:"id"` // 订单主键
	}

	PriorityBuyIssue struct {
		SynthesisOrderID int64 `json:"id"` // 订单主键
	}

	SynthesisItemIssue struct {
		SynthesisOrderID int64 `json:"id"` // 订单主键
	}
)

type (
	GetSynthesisWebOrderStatusReq struct {
		OrderID string `form:"order_id" json:"order_id" binding:"required"` // 合成订单编号
	}

	GetSynthesisWebOrderStatusResp struct {
		ItemTitle    string `json:"item_title"`     // 合成物品名称
		ItemId       string `json:"item_id"`        // 合成物品id
		ItemCoverUrl string `json:"item_cover_url"` // 合成物品图片
		Qty          int32  `json:"qty"`            // 合成数量
		Status       int32  `json:"status"`         // 状态【-1:失败;91:成功】
	}
)
