package enums

type SynthesisStatus int32

func (s SynthesisStatus) Val() int32 {
	return int32(s)
}

const (
	// SynthesisStatusDel 已删除
	SynthesisStatusDel SynthesisStatus = -1
	// SynthesisStatusWaiting 待上架
	SynthesisStatusWaiting SynthesisStatus = 1
	// SynthesisStatusUp 已上架
	SynthesisStatusUp SynthesisStatus = 2
	// SynthesisStatusDown 已下架
	SynthesisStatusDown SynthesisStatus = 3
	// SynthesisStatusExpires 已结束
	SynthesisStatusExpires SynthesisStatus = 4
)
