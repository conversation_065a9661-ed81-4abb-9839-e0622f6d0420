package enums

type SynthesisMaterialsReleaseStatus int32

func (s SynthesisMaterialsReleaseStatus) Val() int32 {
	return int32(s)
}

const (
	// ReleaseFail 释放失败
	ReleaseFail SynthesisMaterialsReleaseStatus = -1
	// ReleaseSuccess 已释放
	ReleaseSuccess SynthesisMaterialsReleaseStatus = 1
	// ReleaseCreated 待释放
	ReleaseCreated SynthesisMaterialsReleaseStatus = 2
	// ReleaseIng 释放中
	ReleaseIng SynthesisMaterialsReleaseStatus = 3
)
