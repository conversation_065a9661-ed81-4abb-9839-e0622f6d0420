package enums

type SynthesisChainStatus int32

func (s SynthesisChainStatus) Val() int32 {
	return int32(s)
}

const (
	// SynthesisChainStatusDisable 未上链
	SynthesisChainStatusDisable SynthesisChainStatus = 0
	// SynthesisChainStatusDone 已上链
	SynthesisChainStatusDone SynthesisChainStatus = 1
	// SynthesisChainStatusWaiting 上链中
	SynthesisChainStatusWaiting SynthesisStatus = 2
	// SynthesisChainStatusFailed 上链失败
	SynthesisChainStatusFailed SynthesisStatus = 3
)
