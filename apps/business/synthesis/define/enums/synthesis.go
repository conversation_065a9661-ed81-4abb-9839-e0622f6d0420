package enums

// LogAction 日志操作类型
type LogAction int32

func (r LogAction) Val() int32 {
	return int32(r)
}

const (
	// LogActionCreate 创建
	LogActionCreate LogAction = 1
	// LogActionUpdate 更新
	LogActionUpdate LogAction = 2
	// LogActionDelete 删除
	LogActionDelete LogAction = 3
	// LogActionUp 上架
	LogActionUp LogAction = 4
	// LogActionDown 下架
	LogActionDown LogAction = 5
)

// SynthesisRuleConfigKey 合成规则说明配置key
const SynthesisRuleConfigKey = "synthesis.rule"

// SynthesisOrderStatus 订单状态
type SynthesisOrderStatus int32

func (r SynthesisOrderStatus) Val() int32 {
	return int32(r)
}

const (
	// SynthesisOrderStatusFail 融合失败
	SynthesisOrderStatusFail SynthesisOrderStatus = -1
	// SynthesisOrderStatusWaiting 待融合
	SynthesisOrderStatusWaiting SynthesisOrderStatus = 1
	// SynthesisOrderStatusSuccess 融合成功
	SynthesisOrderStatusSuccess SynthesisOrderStatus = 21
	// SynthesisOrderStatusDone 融合完成
	SynthesisOrderStatusDone SynthesisOrderStatus = 91
)

var SynthesisOrderStatusMap = map[int32]string{
	SynthesisOrderStatusFail.Val():    "融合失败",
	SynthesisOrderStatusWaiting.Val(): "待融合",
	SynthesisOrderStatusSuccess.Val(): "融合中",
	SynthesisOrderStatusDone.Val():    "已完成",
}

// SynthesisOrderChainStatus 订单上链状态
type SynthesisOrderChainStatus int32

func (r SynthesisOrderChainStatus) Val() int32 {
	return int32(r)
}

const (
	// SynthesisOrderChainStatusWaiting 未上链
	SynthesisOrderChainStatusWaiting SynthesisOrderChainStatus = 0
	// SynthesisOrderChainStatusSuccess 已上链
	SynthesisOrderChainStatusSuccess SynthesisOrderChainStatus = 1
	// SynthesisOrderChainStatusPending 上链中
	SynthesisOrderChainStatusPending SynthesisOrderChainStatus = 2
	// SynthesisOrderChainStatusFail 上链失败
	SynthesisOrderChainStatusFail SynthesisOrderChainStatus = 3
)

var SynthesisOrderChainStatusMap = map[int32]string{
	SynthesisOrderChainStatusWaiting.Val(): "未上链",
	SynthesisOrderChainStatusSuccess.Val(): "上链成功",
	SynthesisOrderChainStatusPending.Val(): "上链中",
	SynthesisOrderChainStatusFail.Val():    "上链失败",
}
