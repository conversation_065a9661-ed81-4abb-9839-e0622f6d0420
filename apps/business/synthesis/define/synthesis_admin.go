package define

import (
	"time"

	"app_service/pkg/pagination"
)

type (
	GetSynthesisAdminListReq struct {
		pagination.Pagination
		ID             *int64    `form:"id" json:"id"`                             // 合成活动ID
		Title          string    `form:"title" json:"title"`                       // 活动名称
		ItemId         string    `form:"item_id" json:"item_id"`                   // 合成物品id(商品/优先购)
		ItemTitle      string    `form:"item_title" json:"item_title"`             // 合成物品名称(商品/优先购)
		Status         *int32    `form:"status" json:"status"`                     // 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		ActivityType   *int32    `form:"activity_type" json:"activity_type"`       // 合成类型【1-优先购权益；2-商品】
		CreatedBy      string    `form:"created_by" json:"created_by"`             // 创建人
		CreatedAtStart time.Time `form:"created_at_start" json:"created_at_start"` // 创建时间开始
		CreatedAtEnd   time.Time `form:"created_at_end" json:"created_at_end"`     // 创建时间结束
		StartTimeStart time.Time `form:"start_time_start" json:"start_time_start"` // 开始时间开始
		StartTimeEnd   time.Time `form:"start_time_end" json:"start_time_end"`     // 开始时间结束
	}

	GetSynthesisAdminListData struct {
		ID              int64     `json:"id"`                // 合成活动ID
		ActivityCode    string    `json:"activity_code"`     // 活动编码
		Title           string    `json:"title"`             // 活动名称
		ActivityType    int32     `json:"activity_type"`     // 合成类型【1-优先购权益；2-商品】
		CoverUrl        string    `json:"cover_url"`         // 封面图
		Status          int32     `json:"status"`            // 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		ItemId          string    `json:"item_id"`           // 合成物品id(商品/优先购)
		ItemTitle       string    `json:"item_title"`        // 合成物品名称(商品/优先购)
		UserLimit       int32     `json:"user_limit"`        // 每人限合(0不限制)
		CompleteUserNum int32     `json:"complete_user_num"` // 已合人数(按用户去重)
		Stock           int32     `json:"stock"`             // 剩余库存
		TotalStock      int32     `json:"total_stock"`       // 总库存
		StartTime       time.Time `json:"start_time"`        // 开始时间
		EndTime         time.Time `json:"end_time"`          // 结束时间
		CreatedBy       string    `json:"created_by"`        // 创建人
		CreatedAt       time.Time `json:"created_at"`        // 创建时间
		UpdatedBy       string    `json:"updated_by"`        // 更新人
		UpdatedAt       time.Time `json:"updated_at"`        // 更新时间
	}

	GetSynthesisAdminListResp struct {
		List  []*GetSynthesisAdminListData `json:"list"`
		Total int64                        `json:"total"`
	}
)

type (
	GetSynthesisAdminDetailReq struct {
		ID int64 `form:"id" json:"id" binding:"required"` // 合成活动ID
	}

	GetSynthesisAdminDetailResp struct {
		ID                     int64                           `json:"id"`                  // 合成活动ID
		ActivityCode           string                          `json:"activity_code"`       // 活动编码
		Title                  string                          `json:"title"`               // 活动名称
		ActivityType           int32                           `json:"activity_type"`       // 合成类型【1-优先购权益；2-商品】
		CoverUrl               string                          `json:"cover_url"`           // 封面图
		Status                 int32                           `json:"status"`              // 合成活动状态-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束
		ItemId                 string                          `json:"item_id"`             // 合成物品id(商品/优先购)
		ItemTitle              string                          `json:"item_title"`          // 合成物品名称(商品/优先购)
		UserLimit              int32                           `json:"user_limit"`          // 每人限合(0不限制)
		CompleteUserNum        int32                           `json:"complete_user_num"`   // 已合人数(按用户去重)
		Stock                  int32                           `json:"stock"`               // 剩余库存
		TotalStock             int32                           `json:"total_stock"`         // 总库存
		StockDisplay           int32                           `json:"stock_display"`       // 剩余库存是否显示【1:显示;2:不显示】
		StartTime              time.Time                       `json:"start_time"`          // 开始时间
		EndTime                time.Time                       `json:"end_time"`            // 结束时间
		ActivityDesc           string                          `json:"activity_desc"`       // 说明
		CreatedBy              string                          `json:"created_by"`          // 创建人
		CreatedAt              time.Time                       `json:"created_at"`          // 创建时间
		UpdatedBy              string                          `json:"updated_by"`          // 更新人
		UpdatedAt              time.Time                       `json:"updated_at"`          // 更新时间
		SynthesisMaterialsData []*SynthesisMaterialsDetailData `json:"synthesis_materials"` //融合材料
		Release                *SynthesisReleaseTime           `json:"release_data"`        //融合材料释放时间
		PriorityBuyData        *SynthesisPrizeData             `json:"priority_buy_data"`   // 优先购信息
		ItemData               *SynthesisPrizeData             `json:"item_data"`           // 融合商品信息
	}
	SynthesisPrizeData struct {
		ItemImageUrl string `json:"image_url"`    // 商品图片
		ItemName     string `json:"item_name"`    // 商品名称
		ItemPrice    int32  `json:"price"`        // 商品发行价
		UsableStock  int32  `json:"usable_stock"` // 可用库存
	}

	SynthesisMaterialsDetailData struct {
		SynthesisMaterialsId     int64                     `json:"synthesis_materials_id"`                      // 合成材料ID
		MaterialsType            int32                     `json:"materials_type" binding:"required"`           // 材料类型【1-核心材料；2-关键材料】
		Qty                      int32                     `json:"qty" binding:"required"`                      // 集齐数量
		SynthesisMaterialsDetail []*SynthesisMaterialsData `json:"synthesis_materials_data" binding:"required"` //融合材料
	}
)

type (
	GetSynthesisLogListReq struct {
		SynthesisID int64 `form:"synthesis_id"` // 合成活动ID
	}
	// GetSynthesisLogListResp 合成活动修改日志列表
	GetSynthesisLogListResp struct {
		CreatedAt time.Time `json:"created_at" example:"2025-04-01T11:44:22.384+08:00"` // 创建时间
		CreatedBy string    `json:"created_by" example:"张三"`                            // 创建人
		Action    int32     `json:"action" example:"1"`                                 // 操作类型【1-创建；2-修改；3-删除；4-上架；5-下架】
		Content   []string  `json:"content" example:"activity_desc,title"`              // 变动字段列表，修改才有，融合条件有修改会返回materials，只需要展示文字"修改融合条件"
	}
)

type (
	SynthesisMaterialsData struct {
		ItemId       string `json:"item_id" binding:"required"`     // 商品id
		ItemTitle    string `json:"item_title" binding:"required"`  // 商品名称
		ItemImageUrl string `json:"image_url" binding:"required"`   // 商品图片
		ItemPrice    int32  `json:"price" binding:"required"`       // 商品发行价
		Qty          int32  `json:"qty" binding:"required,max=500"` // 集齐数量
		Loop         int32  `json:"loop"`                           // 二次流转【1-开启；2-关闭】
		Destroy      int32  `json:"destroy"`                        // 材料消耗【1-销毁；2-不销毁】
	}
)

type (
	AddSynthesisReq struct {
		Title                 string                       `json:"title" binding:"required"`                   // 活动名称
		ActivityType          int32                        `json:"activity_type" binding:"required,eq=1|eq=2"` // 合成类型【1-优先购权益；2-商品】
		CoverUrl              string                       `json:"cover_url" binding:"required"`               // 封面图
		ItemId                string                       `json:"item_id" binding:"required"`                 // 合成物品id(商品/优先购)
		ItemTitle             string                       `json:"item_title" binding:"required"`              // 合成物品名称(商品/优先购)
		TotalStock            int32                        `json:"total_stock" binding:"required"`             // 总库存
		StockDisplay          int32                        `json:"stock_display" binding:"required,eq=1|eq=2"` // 剩余库存是否显示【1:显示;2:不显示】
		UserLimit             *int32                       `json:"user_limit" binding:"required"`              // 每人限合(0不限制)
		ActivityDesc          string                       `json:"activity_desc" binding:"required"`           // 说明
		StartTime             time.Time                    `json:"start_time" binding:"required"`              // 开始时间
		EndTime               time.Time                    `json:"end_time" binding:"required"`                // 结束时间
		AddSynthesisMaterials []*SynthesisMaterialsAddData `json:"add_synthesis_materials" binding:"required"` //融合材料
		Release               *SynthesisReleaseTime        `json:"release_data"`                               //融合材料释放时间
	}

	SynthesisMaterialsAddData struct {
		MaterialsType         int32                     `json:"materials_type" binding:"required"`           // 材料类型【1-核心材料；2-关键材料】
		Qty                   int32                     `json:"qty" binding:"required"`                      // 集齐数量
		AddSynthesisMaterials []*SynthesisMaterialsData `json:"synthesis_materials_data" binding:"required"` //融合材料
	}

	AddSynthesisResp struct {
		ID int64 `json:"id"` // 合成活动ID
	}
)

type (
	EditSynthesisReq struct {
		ID                     int64                         `form:"id" json:"id"`                                // 合成活动ID
		Title                  string                        `json:"title" binding:"required"`                    // 活动名称
		ActivityType           int32                         `json:"activity_type" binding:"required"`            // 合成类型【1-优先购权益；2-商品】
		CoverUrl               string                        `json:"cover_url" binding:"required"`                // 封面图
		ItemId                 string                        `json:"item_id" binding:"required"`                  // 合成物品id(商品/优先购)
		ItemTitle              string                        `json:"item_title" binding:"required"`               // 合成物品名称(商品/优先购)
		TotalStock             int32                         `json:"total_stock" binding:"required"`              // 总库存
		StockDisplay           int32                         `json:"stock_display" binding:"required"`            // 剩余库存是否显示【1:显示;2:不显示】
		UserLimit              *int32                        `json:"user_limit" binding:"required"`               // 每人限合(0不限制)
		ActivityDesc           string                        `json:"activity_desc" binding:"required"`            // 说明
		StartTime              time.Time                     `json:"start_time" binding:"required"`               // 开始时间
		EndTime                time.Time                     `json:"end_time" binding:"required"`                 // 结束时间
		EditSynthesisMaterials []*SynthesisMaterialsEditData `json:"edit_synthesis_materials" binding:"required"` //融合材料
		Release                *SynthesisReleaseTime         `json:"release_data"`                                //融合材料释放时间
	}

	SynthesisMaterialsEditData struct {
		MaterialsType          int32                     `json:"materials_type" binding:"required"`           // 材料类型【1-核心材料；2-关键材料】
		Qty                    int32                     `json:"qty" binding:"required"`                      // 集齐数量
		EditSynthesisMaterials []*SynthesisMaterialsData `json:"synthesis_materials_data" binding:"required"` //融合材料
	}

	EditSynthesisResp struct {
		ID int64 `json:"id"` // 合成活动ID
	}
)

type (
	EditSynthesisStatusReq struct {
		ID     int64 `form:"id" json:"id"`                                      // 合成活动ID
		Status int32 `form:"status" json:"status" binding:"required,eq=2|eq=3"` // 状态 2:上架;3:下架
	}

	EditSynthesisStatusResp struct {
	}
)

type (
	DelSynthesisReq struct {
		ID int64 `form:"id" json:"id"` // 合成活动ID
	}

	DelSynthesisResp struct {
	}
)

type (
	// GetSynthesisRuleAdminResp 获取合成规则
	GetSynthesisRuleAdminResp struct {
		Content string `json:"content"`
	}

	// EditSynthesisRuleReq 编辑合成规则
	EditSynthesisRuleReq struct {
		Content string `json:"content" binding:"required"`
	}
	EditSynthesisRuleResp struct {
	}
)

type (
	GetSynthesisAdminOrderListReq struct {
		pagination.Pagination
		CreatedAtStart time.Time `form:"created_at_start" json:"created_at_start"` // 创建时间开始
		CreatedAtEnd   time.Time `form:"created_at_end" json:"created_at_end"`     // 创建时间结束
		ActivityID     int64     `form:"activity_id" json:"activity_id"`           // 活动ID
		ActivityTitle  string    `form:"activity_title" json:"activity_title"`     // 活动名称
		ItemId         string    `form:"item_id" json:"item_id"`                   // 合成物品id(商品/优先购)
		ItemTitle      string    `form:"item_title" json:"item_title"`             // 合成物品名称(商品/优先购)
		ActivityType   *int32    `form:"activity_type" json:"activity_type"`       // 合成类型【1-优先购权益；2-商品】
		OrderID        string    `form:"order_id" json:"order_id"`                 // 订单ID
		UserID         string    `form:"user_id" json:"user_id"`                   // 用户ID
		Status         int32     `form:"status" json:"status"`                     // 订单状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
		ChainStatus    *int32    `form:"chain_status" json:"chain_status"`         // 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	}
	GetSynthesisAdminOrderListData struct {
		ID            int64     `json:"id,string"`      // 订单ID
		OrderID       string    `json:"order_id"`       // 订单号
		ActivityTitle string    `json:"activity_title"` // 活动名称
		ActivityID    int64     `json:"activity_id"`    // 合成活动ID
		ActivityType  int32     `json:"activity_type"`  // 合成类型【1-优先购权益；2-商品】
		ItemTitle     string    `json:"item_title"`     // 融合物品
		ItemCoverURL  string    `json:"item_cover_url"` // 融合物品封面图
		Qty           int32     `json:"qty"`            // 合成数量
		UserID        string    `json:"user_id"`        // 用户ID
		Status        int32     `json:"status"`         // 订单状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
		ChainStatus   int32     `json:"chain_status"`   // 上链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
		CreatedAt     time.Time `json:"created_at"`     // 融合时间
	}
	GetSynthesisAdminOrderListResp struct {
		List  []*GetSynthesisAdminOrderListData `json:"list"`
		Total int64                             `json:"total"`
	}
)

type (
	GetSynthesisAdminOrderDetailReq struct {
		ID int64 `form:"id,string" json:"id,string"` // 订单ID
	}
	GetSynthesisAdminOrderItemDetail struct {
		ID         string `json:"id"`          // 合成材料物品ID
		CoverURL   string `json:"cover_url"`   // 物品封面图
		ItemTitle  string `json:"item_title"`  // 物品名称
		IP         string `json:"ip"`          // IP
		Code       string `json:"code"`        // 编码
		Issuer     string `json:"issuer"`      // 发行方
		IssuePrice int64  `json:"issue_price"` // 发行价格
	}
	GetSynthesisAdminOrderMaterialsDetail struct {
		ID         string `json:"id"`           // 合成材料物品ID
		UserItemID string `json:"user_item_id"` // 用户物品ID
		Code       string `json:"code"`         // 编码
		ItemName   string `json:"item_name"`    // 物品名称
		CoverURL   string `json:"cover_url"`    // 物品封面图
		IP         string `json:"ip"`           // IP
		Issuer     string `json:"issuer"`       // 发行方
		IssuePrice int64  `json:"issue_price"`  // 发行价格
		CostPrice  int64  `json:"cost_price"`   // 成本价
	}
	GetSynthesisAdminOrderDetailResp struct {
		ID              int64                                    `json:"id,string"`        // 订单ID
		OrderID         string                                   `json:"order_id"`         // 订单号
		ActivityType    int32                                    `json:"activity_type"`    // 合成类型【1-优先购权益；2-商品】
		ActivityTitle   string                                   `json:"activity_title"`   // 活动名称
		ActivityID      int64                                    `json:"activity_id"`      // 合成活动ID
		UserID          string                                   `json:"user_id"`          // 用户ID
		UserNickname    string                                   `json:"user_nickname"`    // 用户昵称
		Status          int32                                    `json:"status"`           // 订单状态
		Qty             int32                                    `json:"qty"`              // 合成数量
		ChainHash       string                                   `json:"chain_hash"`       // 上链哈希
		CreatedAt       time.Time                                `json:"created_at"`       // 创建时间
		ItemDetail      *GetSynthesisAdminOrderItemDetail        `json:"item_detail"`      // 融合物品
		MaterialsDetail []*GetSynthesisAdminOrderMaterialsDetail `json:"materials_detail"` // 融合材料
	}
)

type (
	GetPriorityBuyListReq struct {
		pagination.Pagination
		// 优先购ID
		PriorityBuyId *string `form:"priority_buy_id" json:"priority_buy_id"`
		// 优先购名称
		PriorityBuyName *string `form:"priority_buy_name" json:"priority_buy_name"`
		// 创建人
		Creator *string `form:"creator" json:"creator"`
	}
	GetPriorityBuyListData struct {
		ID          string              `json:"id"`            // 优先购ID
		Name        string              `json:"name"`          // 优先购名称
		ItemID      string              `json:"item_id"`       // 物品ID
		ItemName    string              `json:"item_name"`     // 物品名称
		ImageURL    string              `json:"item_url"`      // 物品图片
		Price       int32               `json:"price"`         // 发行价格
		IssueItemID string              `json:"issue_item_id"` // 一手物品ID
		PriorityBuy *GetPriorityBuyItem `json:"priority_buy"`  // 优先购配置
		Creator     string              `json:"creator"`
	}

	// GetPriorityBuyItem  优先购信息
	GetPriorityBuyItem struct {
		Status         int32  `json:"status"`          // 优先购状态
		Stock          *int32 `json:"stock"`           // 优先购库存
		UsedStock      int32  `json:"used_stock"`      // 优先购已用库存
		AdvanceMinutes *int64 `json:"advance_minutes"` // 优先购开始时间
	}
	GetPriorityBuyListResp struct {
		List  []*GetPriorityBuyListData `json:"list"`
		Total int64                     `json:"total"`
	}
)

type (
	SynthesisReleaseTime struct {
		ReleaseTime *time.Time `json:"release_time"` // 释放时间
		ReleaseDay  *int32     `json:"release_day"`  // 融合X天后释放
	}
)

type (
	GetItemSynthesisListReq struct {
		pagination.Pagination
		ItemID   *string `form:"item_id" json:"item_id"`     // 物品ID
		ItemName *string `form:"item_name" json:"item_name"` // 物品名称
	}

	GetItemSynthesisListData struct {
		ItemID      string `json:"item_id"`      // 物品ID
		ItemName    string `json:"item_name"`    // 物品名称
		ImageURL    string `json:"item_url"`     // 物品图片
		Price       int32  `json:"price"`        // 发行价格
		UsableStock int32  `json:"usable_stock"` // 物品可用库存
		Creator     string `json:"creator"`      // 创建人
	}

	GetItemSynthesisListResp struct {
		List  []*GetItemSynthesisListData `json:"list"`
		Total int64                       `json:"total"`
	}
)

type (
	GetSynthesisOrderDetailListReq struct {
		ID int64 `form:"id,string" json:"id,string"` // 订单ID
		pagination.Pagination
	}

	GetSynthesisOrderDetailListData struct {
		ID         string `json:"id"`           // 合成材料物品ID
		UserItemID string `json:"user_item_id"` // 用户物品ID
		Code       string `json:"code"`         // 编码
		ItemName   string `json:"item_name"`    // 物品名称
		CoverURL   string `json:"cover_url"`    // 物品封面图
		IP         string `json:"ip"`           // IP
		Issuer     string `json:"issuer"`       // 发行方
		IssuePrice int64  `json:"issue_price"`  // 发行价格
		CostPrice  int64  `json:"cost_price"`   // 成本价
	}

	GetSynthesisOrderDetailListResp struct {
		List  []*GetSynthesisOrderDetailListData `json:"list"`
		Total int64                              `json:"total"`
	}
)
