package define

type (
	GetPriorityBuyIsUpReq struct {
		PriorityBuyId string `form:"priority_buy_id" json:"priority_buy_id" binding:"required"` // 优先购id
	}

	GetPriorityBuyIsUpResp struct {
		HasUp int32 `json:"has_up"` // 0:没有上架 1:有上架
	}
)

type (
	GetItemIsUpReq struct {
		ItemId string `form:"item_id" json:"item_id" binding:"required"` // 物品id
	}

	GetItemIsUpResp struct {
		HasUp int32 `json:"has_up"` // 0:没有上架 1:有上架
	}
)

type (
	SynthesisFinishReq struct {
	}

	SynthesisFinishResp struct {
	}
)

type (
	SynthesisOrderUpChainReq struct {
		OrderId string `form:"order_id" json:"order_id"`
	}

	SynthesisOrderUpChainResp struct {
	}
)

type (
	SynthesisMaterialsReleaseReq struct {
		Id int64 `form:"id" json:"id"`
	}

	SynthesisMaterialsReleaseResp struct {
	}
)

type (
	SynthesisMaterialsGetReleaseTimeReq struct {
		UserItemIds []string `form:"user_item_id" json:"user_item_id"`
	}

	SynthesisMaterialsGetReleaseTimeResp struct {
		List []*SynthesisMaterialsGetReleaseTimeData `json:"list"`
	}

	SynthesisMaterialsGetReleaseTimeData struct {
		UserItemId  string `json:"user_item_id"`
		ReleaseTime int64  `json:"release_time"`
	}
)
