package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	SH100001Err  = response.NewError(100001, "核心材料有且最多存在一组")
	SH100002Err  = response.NewError(100002, "材料一组最多添加20件商品")
	SH100003Err  = response.NewError(100003, "关键材料集齐数量最多为10件")
	SH100004Err  = response.NewError(100004, "关键材料集齐数量不能大于等于商品数")
	SH100005Err  = response.NewError(100005, "关键材料最多为10组")
	SH100006Err  = response.NewError(100006, "活动名称不允许重复")
	SH100007Err  = response.NewError(100007, "不允许修改内容")
	SH100008Err  = response.NewError(100008, "优先购权益未上架")
	SH100009Err  = response.NewError(100009, "优先购库存不足")
	SH1000010Err = response.NewError(1000010, "活动未开始")
	SH1000011Err = response.NewError(1000011, "活动已结束")
	SH1000012Err = response.NewError(1000012, "非交易时间不可融合哦")
	SH1000013Err = response.NewError(1000013, "你已超限，每人限制合%d件")
	SH1000014Err = response.NewError(1000014, "材料不足")
	SH1000015Err = response.NewError(1000015, "库存不足")
	SH1000016Err = response.NewError(1000016, "开启二次流转后，释放时间必填")
	SH1000017Err = response.NewError(1000017, "释放时间必须在当前日期之前")
	SH1000018Err = response.NewError(1000018, "商品未通过审核")
	SH1000019Err = response.NewError(1000019, "商品库存不足")
	SH1000020Err = response.NewError(1000020, "开始时间不可小于商品发行时间")
	SH1000021Err = response.NewError(1000021, "结束时间不可大于商品发行时间")
	SH1000022Err = response.NewError(1000022, "商品未开启融合玩法")
)
