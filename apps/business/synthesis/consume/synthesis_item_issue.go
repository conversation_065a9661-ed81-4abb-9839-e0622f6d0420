package consume

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

type SynthesisItemIssueConsumer struct {
	middlewares.BaseConsumer
}

func NewSynthesisItemIssueConsumer() *SynthesisItemIssueConsumer {
	return &SynthesisItemIssueConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.SynthesisItemIssue, constant.CommonGroup),
	}
}

func (o *SynthesisItemIssueConsumer) GetTopic() string {
	return constant.SynthesisItemIssue
}

func (o *SynthesisItemIssueConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *SynthesisItemIssueConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "SynthesisItemIssueConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[synthesis_item_issue]SynthesisItemIssue kafka data:%s", util.Obj2JsonStr(event.Message()))
		// 解析消息
		data := &define.SynthesisItemIssue{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}

		log.Ctx(ctx).Infof("[synthesis_item_issue]SynthesisItemIssue data:%s", util.Obj2JsonStr(data))
		err = logic.SynthesisItemIssue(ctx, data.SynthesisOrderID)
		if err != nil {
			log.Ctx(ctx).Errorf("物品发放失败 err:%v", err)
		}
		return nil
	}
	return middlewares.SafeHandler(handler)
}
