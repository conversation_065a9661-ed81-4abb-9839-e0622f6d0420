package consume

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

type PriorityBuyIssueConsumer struct {
	middlewares.BaseConsumer
}

func NewPriorityBuyIssueConsumer() *PriorityBuyIssueConsumer {
	return &PriorityBuyIssueConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.PriorityBuyIssue, constant.CommonGroup),
	}
}

func (o *PriorityBuyIssueConsumer) GetTopic() string {
	return constant.PriorityBuyIssue
}

func (o *PriorityBuyIssueConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *PriorityBuyIssueConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "PriorityBuyIssueConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[priority_buy_issue]PriorityBuyIssue kafka data:%s", util.Obj2JsonStr(event.Message()))
		// 解析消息
		data := &define.PriorityBuyIssue{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}

		log.Ctx(ctx).Infof("[priority_buy_issue]PriorityBuyIssue data:%s", util.Obj2JsonStr(data))
		err = logic.PriorityBuyIssue(ctx, data.SynthesisOrderID)
		if err != nil {
			log.Ctx(ctx).Errorf("优先购权益发放失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}
		return nil
	}
	return middlewares.SafeHandler(handler)
}
