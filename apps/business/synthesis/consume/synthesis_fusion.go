package consume

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

type SynthesisFusionConsumer struct {
	middlewares.BaseConsumer
}

func NewSynthesisFusionConsumer() *SynthesisFusionConsumer {
	return &SynthesisFusionConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.SynthesisFusion, constant.CommonGroup),
	}
}

func (o *SynthesisFusionConsumer) GetTopic() string {
	return constant.SynthesisFusion
}

func (o *SynthesisFusionConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *SynthesisFusionConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "SynthesisFusionConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[synthesis_fusion]SynthesisFusion kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &define.SynthesisFusion{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}
		log.Ctx(ctx).Infof("[synthesis_fusion]SynthesisFusion data:%s", util.Obj2JsonStr(data))
		err = logic.SynthesisFusion(ctx, data.SynthesisOrderID)
		if err != nil {
			log.Ctx(ctx).Errorf("合成融合失败 err:%v", err)
			return commondefine.CommonWarnErr.Err(err)
		}
		return nil
	}
	return middlewares.SafeHandler(handler)
}
