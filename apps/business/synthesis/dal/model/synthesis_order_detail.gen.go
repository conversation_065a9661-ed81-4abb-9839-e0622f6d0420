// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameSynthesisOrderDetail = "synthesis_order_detail"

// SynthesisOrderDetail 合成订单详情表
type SynthesisOrderDetail struct {
	ID                int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                                    // id
	SynthesisOrderID  int64                 `gorm:"column:synthesis_order_id;type:bigint(20);not null;comment:合成订单id(synthesis_order.id)" json:"synthesis_order_id"` // 合成订单id(synthesis_order.id)
	MaterialsItemID   string                `gorm:"column:materials_item_id;type:varchar(24);not null;comment:物品id" json:"materials_item_id"`                        // 物品id
	MaterialsItemName string                `gorm:"column:materials_item_name;type:varchar(255);not null;comment:物品名称" json:"materials_item_name"`                   // 物品名称
	MaterialsItemURL  string                `gorm:"column:materials_item_url;type:varchar(1000);comment:物品图片" json:"materials_item_url"`                             // 物品图片
	MaterialsItemInfo *datatypes.JSON       `gorm:"column:materials_item_info;type:json;comment:物品信息" json:"materials_item_info"`                                    // 物品信息
	UserItemID        string                `gorm:"column:user_item_id;type:varchar(64);comment:用户背包物品id" json:"user_item_id"`                                       // 用户背包物品id
	IsDestroy         int32                 `gorm:"column:is_destroy;type:tinyint(4);default:2;comment:是否销毁【1->是; 2->否;】" json:"is_destroy"`                         // 是否销毁【1->是; 2->否;】
	CreatedBy         string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                                // 创建人
	CreatedAt         time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`         // 创建时间
	UpdatedBy         string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                                // 更新人
	UpdatedAt         time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`         // 更新时间
	IsDel             soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                // 是否删除【0->未删除; 1->删除】
	SynthesisOrder    SynthesisOrder        `gorm:"foreignKey:SynthesisOrderID" json:"synthesis_order"`
}

// TableName SynthesisOrderDetail's table name
func (*SynthesisOrderDetail) TableName() string {
	return TableNameSynthesisOrderDetail
}
