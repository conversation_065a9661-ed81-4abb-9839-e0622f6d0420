// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameSynthesisOrder = "synthesis_order"

// SynthesisOrder 合成订单表
type SynthesisOrder struct {
	ID                   int64                  `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                            // id
	OrderID              string                 `gorm:"column:order_id;type:varchar(64);not null;comment:订单号" json:"order_id"`                                   // 订单号
	ActivityType         int32                  `gorm:"column:activity_type;type:tinyint(4);not null;default:1;comment:合成类型【1-优先购权益；2-商品】" json:"activity_type"` // 合成类型【1-优先购权益；2-商品】
	SynthesisID          int64                  `gorm:"column:synthesis_id;type:bigint(20);not null;comment:合成活动id(synthesis.id)" json:"synthesis_id"`           // 合成活动id(synthesis.id)
	SynthesisTitle       string                 `gorm:"column:synthesis_title;type:varchar(64);not null;comment:合成活动名称(synthesis.title)" json:"synthesis_title"` // 合成活动名称(synthesis.title)
	ItemID               string                 `gorm:"column:item_id;type:varchar(64);not null;comment:合成物品id(商品/优先购)" json:"item_id"`                          // 合成物品id(商品/优先购)
	ItemTitle            string                 `gorm:"column:item_title;type:varchar(64);not null;comment:合成物品名称(商品/优先购)" json:"item_title"`                    // 合成物品名称(商品/优先购)
	ItemCoverURL         string                 `gorm:"column:item_cover_url;type:varchar(255);not null;comment:合成物品图片，没有则为空" json:"item_cover_url"`             // 合成物品图片，没有则为空
	ItemInfo             datatypes.JSON         `gorm:"column:item_info;type:json;not null;comment:合成物品信息" json:"item_info"`                                     // 合成物品信息
	UserID               string                 `gorm:"column:user_id;type:varchar(24);not null;comment:用户id" json:"user_id"`                                    // 用户id
	Status               int32                  `gorm:"column:status;type:tinyint(4);default:1;comment:状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】" json:"status"`   // 状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
	Qty                  int32                  `gorm:"column:qty;type:int(11);not null;comment:合成数量" json:"qty"`                                                // 合成数量
	AppChannel           string                 `gorm:"column:app_channel;type:varchar(20);comment:设备渠道" json:"app_channel"`                                     // 设备渠道
	AppVersion           string                 `gorm:"column:app_version;type:varchar(20);comment:设备版本号" json:"app_version"`                                    // 设备版本号
	IP                   string                 `gorm:"column:ip;type:varchar(255);comment:ip" json:"ip"`                                                        // ip
	SynthesisTime        *time.Time             `gorm:"column:synthesis_time;type:datetime(3);comment:合成时间" json:"synthesis_time"`                               // 合成时间
	ChainHash            string                 `gorm:"column:chain_hash;type:varchar(255);comment:链hash" json:"chain_hash"`                                     // 链hash
	ChainDataID          string                 `gorm:"column:chain_data_id;type:varchar(255);comment:链data_id" json:"chain_data_id"`                            // 链data_id
	ChainStatus          int32                  `gorm:"column:chain_status;type:tinyint(4);comment:链状态【0:未上链;1:已上链;2:上链中;3:上链失败】" json:"chain_status"`           // 链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	CreatedBy            string                 `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt            time.Time              `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy            string                 `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt            time.Time              `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel                soft_delete.DeletedAt  `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
	SynthesisOrderDetail []SynthesisOrderDetail `gorm:"foreignKey:SynthesisOrderID" json:"synthesis_order_detail"`
	Synthesis            Synthesis              `gorm:"foreignKey:SynthesisID" json:"synthesis"`
}

// TableName SynthesisOrder's table name
func (*SynthesisOrder) TableName() string {
	return TableNameSynthesisOrder
}
