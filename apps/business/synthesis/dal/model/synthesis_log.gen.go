// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameSynthesisLog = "synthesis_log"

// SynthesisLog 合成修改操作日志表
type SynthesisLog struct {
	ID          int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                            // id
	SynthesisID int64                 `gorm:"column:synthesis_id;type:bigint(20);not null;comment:合成活动id(synthesis.id)" json:"synthesis_id"`           // 合成活动id(synthesis.id)
	Action      int32                 `gorm:"column:action;type:tinyint(4);not null;comment:操作类型，1创建，2修改，3删除，4上架，5下架" json:"action"`                   // 操作类型，1创建，2修改，3删除，4上架，5下架
	Content     string                `gorm:"column:content;type:text;not null;comment:修改内容" json:"content"`                                           // 修改内容
	OldContent  string                `gorm:"column:old_content;type:text;not null;comment:旧内容" json:"old_content"`                                    // 旧内容
	CreatedBy   string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt   time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy   string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt   time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel       soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName SynthesisLog's table name
func (*SynthesisLog) TableName() string {
	return TableNameSynthesisLog
}
