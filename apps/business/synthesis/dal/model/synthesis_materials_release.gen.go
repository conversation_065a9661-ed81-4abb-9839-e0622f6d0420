// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/plugin/soft_delete"
)

const TableNameSynthesisMaterialsRelease = "synthesis_materials_release"

// SynthesisMaterialsRelease 合成材料释放表
type SynthesisMaterialsRelease struct {
	ID                     int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                                                           // id
	SynthesisOrderDetailID int64                 `gorm:"column:synthesis_order_detail_id;type:bigint(20);not null;comment:合成订单详情id(synthesis_order_detail.id)" json:"synthesis_order_detail_id"` // 合成订单详情id(synthesis_order_detail.id)
	UserID                 string                `gorm:"column:user_id;type:varchar(24);not null;comment:用户id" json:"user_id"`                                                                   // 用户id
	ItemID                 string                `gorm:"column:item_id;type:varchar(64);comment:物品id" json:"item_id"`                                                                            // 物品id
	UserItemID             string                `gorm:"column:user_item_id;type:varchar(64);comment:用户背包物品id" json:"user_item_id"`                                                              // 用户背包物品id
	Status                 int32                 `gorm:"column:status;type:tinyint(4);default:2;comment:状态【-1:释放失败;1:已释放;2:待释放;3:释放中】" json:"status"`                                            // 状态【-1:释放失败;1:已释放;2:待释放;3:释放中】
	ReleaseTime            *time.Time            `gorm:"column:release_time;type:datetime;comment:应释放时间" json:"release_time"`                                                                    // 应释放时间
	RealReleaseTime        *time.Time            `gorm:"column:real_release_time;type:datetime(3);comment:实际释放时间" json:"real_release_time"`                                                      // 实际释放时间
	CreatedBy              string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                                                       // 创建人
	CreatedAt              time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                                // 创建时间
	UpdatedBy              string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                                                       // 更新人
	UpdatedAt              time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                                // 更新时间
	IsDel                  soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`                                       // 是否删除【0->未删除; 1->删除】
}

// TableName SynthesisMaterialsRelease's table name
func (*SynthesisMaterialsRelease) TableName() string {
	return TableNameSynthesisMaterialsRelease
}
