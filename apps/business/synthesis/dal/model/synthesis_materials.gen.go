// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
	"gorm.io/plugin/soft_delete"
)

const TableNameSynthesisMaterials = "synthesis_materials"

// SynthesisMaterials 合成材料表
type SynthesisMaterials struct {
	ID            int64                 `gorm:"column:id;type:bigint(20);primaryKey;autoIncrement:true;comment:id" json:"id"`                            // id
	SynthesisID   int64                 `gorm:"column:synthesis_id;type:bigint(20);not null;comment:合成活动id(synthesis.id)" json:"synthesis_id"`           // 合成活动id(synthesis.id)
	MaterialsType int32                 `gorm:"column:materials_type;type:tinyint(4);default:1;comment:材料类型【1-核心材料；2-关键材料】" json:"materials_type"`       // 材料类型【1-核心材料；2-关键材料】
	MaterialsData *datatypes.JSON       `gorm:"column:materials_data;type:json;comment:材料内容" json:"materials_data"`                                      // 材料内容
	LimitQty      int32                 `gorm:"column:limit_qty;type:int(11);comment:限制数量(0全部集齐)" json:"limit_qty"`                                      // 限制数量(0全部集齐)
	CreatedBy     string                `gorm:"column:created_by;type:varchar(24);comment:创建人" json:"created_by"`                                        // 创建人
	CreatedAt     time.Time             `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"` // 创建时间
	UpdatedBy     string                `gorm:"column:updated_by;type:varchar(24);comment:更新人" json:"updated_by"`                                        // 更新人
	UpdatedAt     time.Time             `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"` // 更新时间
	IsDel         soft_delete.DeletedAt `gorm:"column:is_del;type:tinyint(1);not null;comment:是否删除【0->未删除; 1->删除】;softDelete:flag" json:"is_del"`        // 是否删除【0->未删除; 1->删除】
}

// TableName SynthesisMaterials's table name
func (*SynthesisMaterials) TableName() string {
	return TableNameSynthesisMaterials
}
