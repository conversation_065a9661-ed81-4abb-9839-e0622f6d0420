// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesisOrderDetail(db *gorm.DB, opts ...gen.DOOption) synthesisOrderDetail {
	_synthesisOrderDetail := synthesisOrderDetail{}

	_synthesisOrderDetail.synthesisOrderDetailDo.UseDB(db, opts...)
	_synthesisOrderDetail.synthesisOrderDetailDo.UseModel(&model.SynthesisOrderDetail{})

	tableName := _synthesisOrderDetail.synthesisOrderDetailDo.TableName()
	_synthesisOrderDetail.ALL = field.NewAsterisk(tableName)
	_synthesisOrderDetail.ID = field.NewInt64(tableName, "id")
	_synthesisOrderDetail.SynthesisOrderID = field.NewInt64(tableName, "synthesis_order_id")
	_synthesisOrderDetail.MaterialsItemID = field.NewString(tableName, "materials_item_id")
	_synthesisOrderDetail.MaterialsItemName = field.NewString(tableName, "materials_item_name")
	_synthesisOrderDetail.MaterialsItemURL = field.NewString(tableName, "materials_item_url")
	_synthesisOrderDetail.MaterialsItemInfo = field.NewField(tableName, "materials_item_info")
	_synthesisOrderDetail.UserItemID = field.NewString(tableName, "user_item_id")
	_synthesisOrderDetail.IsDestroy = field.NewInt32(tableName, "is_destroy")
	_synthesisOrderDetail.CreatedBy = field.NewString(tableName, "created_by")
	_synthesisOrderDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesisOrderDetail.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesisOrderDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesisOrderDetail.IsDel = field.NewField(tableName, "is_del")
	_synthesisOrderDetail.SynthesisOrder = synthesisOrderDetailBelongsToSynthesisOrder{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SynthesisOrder", "model.SynthesisOrder"),
		SynthesisOrderDetail: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("SynthesisOrder.SynthesisOrderDetail", "model.SynthesisOrderDetail"),
		},
		Synthesis: struct {
			field.RelationField
		}{
			RelationField: field.NewRelation("SynthesisOrder.Synthesis", "model.Synthesis"),
		},
	}

	_synthesisOrderDetail.fillFieldMap()

	return _synthesisOrderDetail
}

// synthesisOrderDetail 合成订单详情表
type synthesisOrderDetail struct {
	synthesisOrderDetailDo

	ALL               field.Asterisk
	ID                field.Int64  // id
	SynthesisOrderID  field.Int64  // 合成订单id(synthesis_order.id)
	MaterialsItemID   field.String // 物品id
	MaterialsItemName field.String // 物品名称
	MaterialsItemURL  field.String // 物品图片
	MaterialsItemInfo field.Field  // 物品信息
	UserItemID        field.String // 用户背包物品id
	IsDestroy         field.Int32  // 是否销毁【1->是; 2->否;】
	CreatedBy         field.String // 创建人
	CreatedAt         field.Time   // 创建时间
	UpdatedBy         field.String // 更新人
	UpdatedAt         field.Time   // 更新时间
	IsDel             field.Field  // 是否删除【0->未删除; 1->删除】
	SynthesisOrder    synthesisOrderDetailBelongsToSynthesisOrder

	fieldMap map[string]field.Expr
}

func (s synthesisOrderDetail) Table(newTableName string) *synthesisOrderDetail {
	s.synthesisOrderDetailDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesisOrderDetail) As(alias string) *synthesisOrderDetail {
	s.synthesisOrderDetailDo.DO = *(s.synthesisOrderDetailDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesisOrderDetail) updateTableName(table string) *synthesisOrderDetail {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SynthesisOrderID = field.NewInt64(table, "synthesis_order_id")
	s.MaterialsItemID = field.NewString(table, "materials_item_id")
	s.MaterialsItemName = field.NewString(table, "materials_item_name")
	s.MaterialsItemURL = field.NewString(table, "materials_item_url")
	s.MaterialsItemInfo = field.NewField(table, "materials_item_info")
	s.UserItemID = field.NewString(table, "user_item_id")
	s.IsDestroy = field.NewInt32(table, "is_destroy")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesisOrderDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesisOrderDetail) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 14)
	s.fieldMap["id"] = s.ID
	s.fieldMap["synthesis_order_id"] = s.SynthesisOrderID
	s.fieldMap["materials_item_id"] = s.MaterialsItemID
	s.fieldMap["materials_item_name"] = s.MaterialsItemName
	s.fieldMap["materials_item_url"] = s.MaterialsItemURL
	s.fieldMap["materials_item_info"] = s.MaterialsItemInfo
	s.fieldMap["user_item_id"] = s.UserItemID
	s.fieldMap["is_destroy"] = s.IsDestroy
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel

}

func (s synthesisOrderDetail) clone(db *gorm.DB) synthesisOrderDetail {
	s.synthesisOrderDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesisOrderDetail) replaceDB(db *gorm.DB) synthesisOrderDetail {
	s.synthesisOrderDetailDo.ReplaceDB(db)
	return s
}

type synthesisOrderDetailBelongsToSynthesisOrder struct {
	db *gorm.DB

	field.RelationField

	SynthesisOrderDetail struct {
		field.RelationField
	}
	Synthesis struct {
		field.RelationField
	}
}

func (a synthesisOrderDetailBelongsToSynthesisOrder) Where(conds ...field.Expr) *synthesisOrderDetailBelongsToSynthesisOrder {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a synthesisOrderDetailBelongsToSynthesisOrder) WithContext(ctx context.Context) *synthesisOrderDetailBelongsToSynthesisOrder {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a synthesisOrderDetailBelongsToSynthesisOrder) Session(session *gorm.Session) *synthesisOrderDetailBelongsToSynthesisOrder {
	a.db = a.db.Session(session)
	return &a
}

func (a synthesisOrderDetailBelongsToSynthesisOrder) Model(m *model.SynthesisOrderDetail) *synthesisOrderDetailBelongsToSynthesisOrderTx {
	return &synthesisOrderDetailBelongsToSynthesisOrderTx{a.db.Model(m).Association(a.Name())}
}

type synthesisOrderDetailBelongsToSynthesisOrderTx struct{ tx *gorm.Association }

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Find() (result *model.SynthesisOrder, err error) {
	return result, a.tx.Find(&result)
}

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Append(values ...*model.SynthesisOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Replace(values ...*model.SynthesisOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Delete(values ...*model.SynthesisOrder) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Clear() error {
	return a.tx.Clear()
}

func (a synthesisOrderDetailBelongsToSynthesisOrderTx) Count() int64 {
	return a.tx.Count()
}

type synthesisOrderDetailDo struct{ gen.DO }

type ISynthesisOrderDetailDo interface {
	gen.SubQuery
	Debug() ISynthesisOrderDetailDo
	WithContext(ctx context.Context) ISynthesisOrderDetailDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisOrderDetailDo
	WriteDB() ISynthesisOrderDetailDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisOrderDetailDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisOrderDetailDo
	Not(conds ...gen.Condition) ISynthesisOrderDetailDo
	Or(conds ...gen.Condition) ISynthesisOrderDetailDo
	Select(conds ...field.Expr) ISynthesisOrderDetailDo
	Where(conds ...gen.Condition) ISynthesisOrderDetailDo
	Order(conds ...field.Expr) ISynthesisOrderDetailDo
	Distinct(cols ...field.Expr) ISynthesisOrderDetailDo
	Omit(cols ...field.Expr) ISynthesisOrderDetailDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo
	Group(cols ...field.Expr) ISynthesisOrderDetailDo
	Having(conds ...gen.Condition) ISynthesisOrderDetailDo
	Limit(limit int) ISynthesisOrderDetailDo
	Offset(offset int) ISynthesisOrderDetailDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisOrderDetailDo
	Unscoped() ISynthesisOrderDetailDo
	Create(values ...*model.SynthesisOrderDetail) error
	CreateInBatches(values []*model.SynthesisOrderDetail, batchSize int) error
	Save(values ...*model.SynthesisOrderDetail) error
	First() (*model.SynthesisOrderDetail, error)
	Take() (*model.SynthesisOrderDetail, error)
	Last() (*model.SynthesisOrderDetail, error)
	Find() ([]*model.SynthesisOrderDetail, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisOrderDetail, err error)
	FindInBatches(result *[]*model.SynthesisOrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SynthesisOrderDetail) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisOrderDetailDo
	Assign(attrs ...field.AssignExpr) ISynthesisOrderDetailDo
	Joins(fields ...field.RelationField) ISynthesisOrderDetailDo
	Preload(fields ...field.RelationField) ISynthesisOrderDetailDo
	FirstOrInit() (*model.SynthesisOrderDetail, error)
	FirstOrCreate() (*model.SynthesisOrderDetail, error)
	FindByPage(offset int, limit int) (result []*model.SynthesisOrderDetail, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisOrderDetailDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisOrderDetailDo) Debug() ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisOrderDetailDo) WithContext(ctx context.Context) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisOrderDetailDo) ReadDB() ISynthesisOrderDetailDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisOrderDetailDo) WriteDB() ISynthesisOrderDetailDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisOrderDetailDo) Session(config *gorm.Session) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisOrderDetailDo) Clauses(conds ...clause.Expression) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisOrderDetailDo) Returning(value interface{}, columns ...string) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisOrderDetailDo) Not(conds ...gen.Condition) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisOrderDetailDo) Or(conds ...gen.Condition) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisOrderDetailDo) Select(conds ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisOrderDetailDo) Where(conds ...gen.Condition) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisOrderDetailDo) Order(conds ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisOrderDetailDo) Distinct(cols ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisOrderDetailDo) Omit(cols ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisOrderDetailDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisOrderDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisOrderDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisOrderDetailDo) Group(cols ...field.Expr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisOrderDetailDo) Having(conds ...gen.Condition) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisOrderDetailDo) Limit(limit int) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisOrderDetailDo) Offset(offset int) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisOrderDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisOrderDetailDo) Unscoped() ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisOrderDetailDo) Create(values ...*model.SynthesisOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisOrderDetailDo) CreateInBatches(values []*model.SynthesisOrderDetail, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisOrderDetailDo) Save(values ...*model.SynthesisOrderDetail) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisOrderDetailDo) First() (*model.SynthesisOrderDetail, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrderDetail), nil
	}
}

func (s synthesisOrderDetailDo) Take() (*model.SynthesisOrderDetail, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrderDetail), nil
	}
}

func (s synthesisOrderDetailDo) Last() (*model.SynthesisOrderDetail, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrderDetail), nil
	}
}

func (s synthesisOrderDetailDo) Find() ([]*model.SynthesisOrderDetail, error) {
	result, err := s.DO.Find()
	return result.([]*model.SynthesisOrderDetail), err
}

func (s synthesisOrderDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisOrderDetail, err error) {
	buf := make([]*model.SynthesisOrderDetail, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisOrderDetailDo) FindInBatches(result *[]*model.SynthesisOrderDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisOrderDetailDo) Attrs(attrs ...field.AssignExpr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisOrderDetailDo) Assign(attrs ...field.AssignExpr) ISynthesisOrderDetailDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisOrderDetailDo) Joins(fields ...field.RelationField) ISynthesisOrderDetailDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisOrderDetailDo) Preload(fields ...field.RelationField) ISynthesisOrderDetailDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisOrderDetailDo) FirstOrInit() (*model.SynthesisOrderDetail, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrderDetail), nil
	}
}

func (s synthesisOrderDetailDo) FirstOrCreate() (*model.SynthesisOrderDetail, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrderDetail), nil
	}
}

func (s synthesisOrderDetailDo) FindByPage(offset int, limit int) (result []*model.SynthesisOrderDetail, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisOrderDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisOrderDetailDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisOrderDetailDo) Delete(models ...*model.SynthesisOrderDetail) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisOrderDetailDo) withDO(do gen.Dao) *synthesisOrderDetailDo {
	s.DO = *do.(*gen.DO)
	return s
}
