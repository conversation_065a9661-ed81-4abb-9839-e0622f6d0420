// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                         = new(Query)
	Synthesis                 *synthesis
	SynthesisLog              *synthesisLog
	SynthesisMaterials        *synthesisMaterials
	SynthesisMaterialsRelease *synthesisMaterialsRelease
	SynthesisOrder            *synthesisOrder
	SynthesisOrderDetail      *synthesisOrderDetail
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	Synthesis = &Q.Synthesis
	SynthesisLog = &Q.SynthesisLog
	SynthesisMaterials = &Q.SynthesisMaterials
	SynthesisMaterialsRelease = &Q.SynthesisMaterialsRelease
	SynthesisOrder = &Q.SynthesisOrder
	SynthesisOrderDetail = &Q.SynthesisOrderDetail
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                        db,
		Synthesis:                 newSynthesis(db, opts...),
		SynthesisLog:              newSynthesisLog(db, opts...),
		SynthesisMaterials:        newSynthesisMaterials(db, opts...),
		SynthesisMaterialsRelease: newSynthesisMaterialsRelease(db, opts...),
		SynthesisOrder:            newSynthesisOrder(db, opts...),
		SynthesisOrderDetail:      newSynthesisOrderDetail(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	Synthesis                 synthesis
	SynthesisLog              synthesisLog
	SynthesisMaterials        synthesisMaterials
	SynthesisMaterialsRelease synthesisMaterialsRelease
	SynthesisOrder            synthesisOrder
	SynthesisOrderDetail      synthesisOrderDetail
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		Synthesis:                 q.Synthesis.clone(db),
		SynthesisLog:              q.SynthesisLog.clone(db),
		SynthesisMaterials:        q.SynthesisMaterials.clone(db),
		SynthesisMaterialsRelease: q.SynthesisMaterialsRelease.clone(db),
		SynthesisOrder:            q.SynthesisOrder.clone(db),
		SynthesisOrderDetail:      q.SynthesisOrderDetail.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                        db,
		Synthesis:                 q.Synthesis.replaceDB(db),
		SynthesisLog:              q.SynthesisLog.replaceDB(db),
		SynthesisMaterials:        q.SynthesisMaterials.replaceDB(db),
		SynthesisMaterialsRelease: q.SynthesisMaterialsRelease.replaceDB(db),
		SynthesisOrder:            q.SynthesisOrder.replaceDB(db),
		SynthesisOrderDetail:      q.SynthesisOrderDetail.replaceDB(db),
	}
}

type queryCtx struct {
	Synthesis                 ISynthesisDo
	SynthesisLog              ISynthesisLogDo
	SynthesisMaterials        ISynthesisMaterialsDo
	SynthesisMaterialsRelease ISynthesisMaterialsReleaseDo
	SynthesisOrder            ISynthesisOrderDo
	SynthesisOrderDetail      ISynthesisOrderDetailDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		Synthesis:                 q.Synthesis.WithContext(ctx),
		SynthesisLog:              q.SynthesisLog.WithContext(ctx),
		SynthesisMaterials:        q.SynthesisMaterials.WithContext(ctx),
		SynthesisMaterialsRelease: q.SynthesisMaterialsRelease.WithContext(ctx),
		SynthesisOrder:            q.SynthesisOrder.WithContext(ctx),
		SynthesisOrderDetail:      q.SynthesisOrderDetail.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
