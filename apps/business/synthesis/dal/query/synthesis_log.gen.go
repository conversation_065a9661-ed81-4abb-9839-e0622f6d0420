// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesisLog(db *gorm.DB, opts ...gen.DOOption) synthesisLog {
	_synthesisLog := synthesisLog{}

	_synthesisLog.synthesisLogDo.UseDB(db, opts...)
	_synthesisLog.synthesisLogDo.UseModel(&model.SynthesisLog{})

	tableName := _synthesisLog.synthesisLogDo.TableName()
	_synthesisLog.ALL = field.NewAsterisk(tableName)
	_synthesisLog.ID = field.NewInt64(tableName, "id")
	_synthesisLog.SynthesisID = field.NewInt64(tableName, "synthesis_id")
	_synthesisLog.Action = field.NewInt32(tableName, "action")
	_synthesisLog.Content = field.NewString(tableName, "content")
	_synthesisLog.OldContent = field.NewString(tableName, "old_content")
	_synthesisLog.CreatedBy = field.NewString(tableName, "created_by")
	_synthesisLog.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesisLog.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesisLog.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesisLog.IsDel = field.NewField(tableName, "is_del")

	_synthesisLog.fillFieldMap()

	return _synthesisLog
}

// synthesisLog 合成修改操作日志表
type synthesisLog struct {
	synthesisLogDo

	ALL         field.Asterisk
	ID          field.Int64  // id
	SynthesisID field.Int64  // 合成活动id(synthesis.id)
	Action      field.Int32  // 操作类型，1创建，2修改，3删除，4上架，5下架
	Content     field.String // 修改内容
	OldContent  field.String // 旧内容
	CreatedBy   field.String // 创建人
	CreatedAt   field.Time   // 创建时间
	UpdatedBy   field.String // 更新人
	UpdatedAt   field.Time   // 更新时间
	IsDel       field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s synthesisLog) Table(newTableName string) *synthesisLog {
	s.synthesisLogDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesisLog) As(alias string) *synthesisLog {
	s.synthesisLogDo.DO = *(s.synthesisLogDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesisLog) updateTableName(table string) *synthesisLog {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SynthesisID = field.NewInt64(table, "synthesis_id")
	s.Action = field.NewInt32(table, "action")
	s.Content = field.NewString(table, "content")
	s.OldContent = field.NewString(table, "old_content")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesisLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesisLog) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["synthesis_id"] = s.SynthesisID
	s.fieldMap["action"] = s.Action
	s.fieldMap["content"] = s.Content
	s.fieldMap["old_content"] = s.OldContent
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s synthesisLog) clone(db *gorm.DB) synthesisLog {
	s.synthesisLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesisLog) replaceDB(db *gorm.DB) synthesisLog {
	s.synthesisLogDo.ReplaceDB(db)
	return s
}

type synthesisLogDo struct{ gen.DO }

type ISynthesisLogDo interface {
	gen.SubQuery
	Debug() ISynthesisLogDo
	WithContext(ctx context.Context) ISynthesisLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisLogDo
	WriteDB() ISynthesisLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisLogDo
	Not(conds ...gen.Condition) ISynthesisLogDo
	Or(conds ...gen.Condition) ISynthesisLogDo
	Select(conds ...field.Expr) ISynthesisLogDo
	Where(conds ...gen.Condition) ISynthesisLogDo
	Order(conds ...field.Expr) ISynthesisLogDo
	Distinct(cols ...field.Expr) ISynthesisLogDo
	Omit(cols ...field.Expr) ISynthesisLogDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisLogDo
	Group(cols ...field.Expr) ISynthesisLogDo
	Having(conds ...gen.Condition) ISynthesisLogDo
	Limit(limit int) ISynthesisLogDo
	Offset(offset int) ISynthesisLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisLogDo
	Unscoped() ISynthesisLogDo
	Create(values ...*model.SynthesisLog) error
	CreateInBatches(values []*model.SynthesisLog, batchSize int) error
	Save(values ...*model.SynthesisLog) error
	First() (*model.SynthesisLog, error)
	Take() (*model.SynthesisLog, error)
	Last() (*model.SynthesisLog, error)
	Find() ([]*model.SynthesisLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisLog, err error)
	FindInBatches(result *[]*model.SynthesisLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SynthesisLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisLogDo
	Assign(attrs ...field.AssignExpr) ISynthesisLogDo
	Joins(fields ...field.RelationField) ISynthesisLogDo
	Preload(fields ...field.RelationField) ISynthesisLogDo
	FirstOrInit() (*model.SynthesisLog, error)
	FirstOrCreate() (*model.SynthesisLog, error)
	FindByPage(offset int, limit int) (result []*model.SynthesisLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisLogDo) Debug() ISynthesisLogDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisLogDo) WithContext(ctx context.Context) ISynthesisLogDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisLogDo) ReadDB() ISynthesisLogDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisLogDo) WriteDB() ISynthesisLogDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisLogDo) Session(config *gorm.Session) ISynthesisLogDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisLogDo) Clauses(conds ...clause.Expression) ISynthesisLogDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisLogDo) Returning(value interface{}, columns ...string) ISynthesisLogDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisLogDo) Not(conds ...gen.Condition) ISynthesisLogDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisLogDo) Or(conds ...gen.Condition) ISynthesisLogDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisLogDo) Select(conds ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisLogDo) Where(conds ...gen.Condition) ISynthesisLogDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisLogDo) Order(conds ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisLogDo) Distinct(cols ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisLogDo) Omit(cols ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisLogDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisLogDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisLogDo) Group(cols ...field.Expr) ISynthesisLogDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisLogDo) Having(conds ...gen.Condition) ISynthesisLogDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisLogDo) Limit(limit int) ISynthesisLogDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisLogDo) Offset(offset int) ISynthesisLogDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisLogDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisLogDo) Unscoped() ISynthesisLogDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisLogDo) Create(values ...*model.SynthesisLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisLogDo) CreateInBatches(values []*model.SynthesisLog, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisLogDo) Save(values ...*model.SynthesisLog) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisLogDo) First() (*model.SynthesisLog, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisLog), nil
	}
}

func (s synthesisLogDo) Take() (*model.SynthesisLog, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisLog), nil
	}
}

func (s synthesisLogDo) Last() (*model.SynthesisLog, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisLog), nil
	}
}

func (s synthesisLogDo) Find() ([]*model.SynthesisLog, error) {
	result, err := s.DO.Find()
	return result.([]*model.SynthesisLog), err
}

func (s synthesisLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisLog, err error) {
	buf := make([]*model.SynthesisLog, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisLogDo) FindInBatches(result *[]*model.SynthesisLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisLogDo) Attrs(attrs ...field.AssignExpr) ISynthesisLogDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisLogDo) Assign(attrs ...field.AssignExpr) ISynthesisLogDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisLogDo) Joins(fields ...field.RelationField) ISynthesisLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisLogDo) Preload(fields ...field.RelationField) ISynthesisLogDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisLogDo) FirstOrInit() (*model.SynthesisLog, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisLog), nil
	}
}

func (s synthesisLogDo) FirstOrCreate() (*model.SynthesisLog, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisLog), nil
	}
}

func (s synthesisLogDo) FindByPage(offset int, limit int) (result []*model.SynthesisLog, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisLogDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisLogDo) Delete(models ...*model.SynthesisLog) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisLogDo) withDO(do gen.Dao) *synthesisLogDo {
	s.DO = *do.(*gen.DO)
	return s
}
