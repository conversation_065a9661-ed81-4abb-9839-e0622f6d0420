// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesisOrder(db *gorm.DB, opts ...gen.DOOption) synthesisOrder {
	_synthesisOrder := synthesisOrder{}

	_synthesisOrder.synthesisOrderDo.UseDB(db, opts...)
	_synthesisOrder.synthesisOrderDo.UseModel(&model.SynthesisOrder{})

	tableName := _synthesisOrder.synthesisOrderDo.TableName()
	_synthesisOrder.ALL = field.NewAsterisk(tableName)
	_synthesisOrder.ID = field.NewInt64(tableName, "id")
	_synthesisOrder.OrderID = field.NewString(tableName, "order_id")
	_synthesisOrder.ActivityType = field.NewInt32(tableName, "activity_type")
	_synthesisOrder.SynthesisID = field.NewInt64(tableName, "synthesis_id")
	_synthesisOrder.SynthesisTitle = field.NewString(tableName, "synthesis_title")
	_synthesisOrder.ItemID = field.NewString(tableName, "item_id")
	_synthesisOrder.ItemTitle = field.NewString(tableName, "item_title")
	_synthesisOrder.ItemCoverURL = field.NewString(tableName, "item_cover_url")
	_synthesisOrder.ItemInfo = field.NewField(tableName, "item_info")
	_synthesisOrder.UserID = field.NewString(tableName, "user_id")
	_synthesisOrder.Status = field.NewInt32(tableName, "status")
	_synthesisOrder.Qty = field.NewInt32(tableName, "qty")
	_synthesisOrder.AppChannel = field.NewString(tableName, "app_channel")
	_synthesisOrder.AppVersion = field.NewString(tableName, "app_version")
	_synthesisOrder.IP = field.NewString(tableName, "ip")
	_synthesisOrder.SynthesisTime = field.NewTime(tableName, "synthesis_time")
	_synthesisOrder.ChainHash = field.NewString(tableName, "chain_hash")
	_synthesisOrder.ChainDataID = field.NewString(tableName, "chain_data_id")
	_synthesisOrder.ChainStatus = field.NewInt32(tableName, "chain_status")
	_synthesisOrder.CreatedBy = field.NewString(tableName, "created_by")
	_synthesisOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesisOrder.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesisOrder.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesisOrder.IsDel = field.NewField(tableName, "is_del")
	_synthesisOrder.SynthesisOrderDetail = synthesisOrderHasManySynthesisOrderDetail{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("SynthesisOrderDetail", "model.SynthesisOrderDetail"),
	}

	_synthesisOrder.Synthesis = synthesisOrderBelongsToSynthesis{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("Synthesis", "model.Synthesis"),
	}

	_synthesisOrder.fillFieldMap()

	return _synthesisOrder
}

// synthesisOrder 合成订单表
type synthesisOrder struct {
	synthesisOrderDo

	ALL                  field.Asterisk
	ID                   field.Int64  // id
	OrderID              field.String // 订单号
	ActivityType         field.Int32  // 合成类型【1-优先购权益；2-商品】
	SynthesisID          field.Int64  // 合成活动id(synthesis.id)
	SynthesisTitle       field.String // 合成活动名称(synthesis.title)
	ItemID               field.String // 合成物品id(商品/优先购)
	ItemTitle            field.String // 合成物品名称(商品/优先购)
	ItemCoverURL         field.String // 合成物品图片，没有则为空
	ItemInfo             field.Field  // 合成物品信息
	UserID               field.String // 用户id
	Status               field.Int32  // 状态【-1:失败;1:待融合;21:已融合(云仓材料消耗成功);91:已完成】
	Qty                  field.Int32  // 合成数量
	AppChannel           field.String // 设备渠道
	AppVersion           field.String // 设备版本号
	IP                   field.String // ip
	SynthesisTime        field.Time   // 合成时间
	ChainHash            field.String // 链hash
	ChainDataID          field.String // 链data_id
	ChainStatus          field.Int32  // 链状态【0:未上链;1:已上链;2:上链中;3:上链失败】
	CreatedBy            field.String // 创建人
	CreatedAt            field.Time   // 创建时间
	UpdatedBy            field.String // 更新人
	UpdatedAt            field.Time   // 更新时间
	IsDel                field.Field  // 是否删除【0->未删除; 1->删除】
	SynthesisOrderDetail synthesisOrderHasManySynthesisOrderDetail

	Synthesis synthesisOrderBelongsToSynthesis

	fieldMap map[string]field.Expr
}

func (s synthesisOrder) Table(newTableName string) *synthesisOrder {
	s.synthesisOrderDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesisOrder) As(alias string) *synthesisOrder {
	s.synthesisOrderDo.DO = *(s.synthesisOrderDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesisOrder) updateTableName(table string) *synthesisOrder {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.OrderID = field.NewString(table, "order_id")
	s.ActivityType = field.NewInt32(table, "activity_type")
	s.SynthesisID = field.NewInt64(table, "synthesis_id")
	s.SynthesisTitle = field.NewString(table, "synthesis_title")
	s.ItemID = field.NewString(table, "item_id")
	s.ItemTitle = field.NewString(table, "item_title")
	s.ItemCoverURL = field.NewString(table, "item_cover_url")
	s.ItemInfo = field.NewField(table, "item_info")
	s.UserID = field.NewString(table, "user_id")
	s.Status = field.NewInt32(table, "status")
	s.Qty = field.NewInt32(table, "qty")
	s.AppChannel = field.NewString(table, "app_channel")
	s.AppVersion = field.NewString(table, "app_version")
	s.IP = field.NewString(table, "ip")
	s.SynthesisTime = field.NewTime(table, "synthesis_time")
	s.ChainHash = field.NewString(table, "chain_hash")
	s.ChainDataID = field.NewString(table, "chain_data_id")
	s.ChainStatus = field.NewInt32(table, "chain_status")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesisOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesisOrder) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 26)
	s.fieldMap["id"] = s.ID
	s.fieldMap["order_id"] = s.OrderID
	s.fieldMap["activity_type"] = s.ActivityType
	s.fieldMap["synthesis_id"] = s.SynthesisID
	s.fieldMap["synthesis_title"] = s.SynthesisTitle
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["item_title"] = s.ItemTitle
	s.fieldMap["item_cover_url"] = s.ItemCoverURL
	s.fieldMap["item_info"] = s.ItemInfo
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["status"] = s.Status
	s.fieldMap["qty"] = s.Qty
	s.fieldMap["app_channel"] = s.AppChannel
	s.fieldMap["app_version"] = s.AppVersion
	s.fieldMap["ip"] = s.IP
	s.fieldMap["synthesis_time"] = s.SynthesisTime
	s.fieldMap["chain_hash"] = s.ChainHash
	s.fieldMap["chain_data_id"] = s.ChainDataID
	s.fieldMap["chain_status"] = s.ChainStatus
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel

}

func (s synthesisOrder) clone(db *gorm.DB) synthesisOrder {
	s.synthesisOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesisOrder) replaceDB(db *gorm.DB) synthesisOrder {
	s.synthesisOrderDo.ReplaceDB(db)
	return s
}

type synthesisOrderHasManySynthesisOrderDetail struct {
	db *gorm.DB

	field.RelationField
}

func (a synthesisOrderHasManySynthesisOrderDetail) Where(conds ...field.Expr) *synthesisOrderHasManySynthesisOrderDetail {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a synthesisOrderHasManySynthesisOrderDetail) WithContext(ctx context.Context) *synthesisOrderHasManySynthesisOrderDetail {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a synthesisOrderHasManySynthesisOrderDetail) Session(session *gorm.Session) *synthesisOrderHasManySynthesisOrderDetail {
	a.db = a.db.Session(session)
	return &a
}

func (a synthesisOrderHasManySynthesisOrderDetail) Model(m *model.SynthesisOrder) *synthesisOrderHasManySynthesisOrderDetailTx {
	return &synthesisOrderHasManySynthesisOrderDetailTx{a.db.Model(m).Association(a.Name())}
}

type synthesisOrderHasManySynthesisOrderDetailTx struct{ tx *gorm.Association }

func (a synthesisOrderHasManySynthesisOrderDetailTx) Find() (result []*model.SynthesisOrderDetail, err error) {
	return result, a.tx.Find(&result)
}

func (a synthesisOrderHasManySynthesisOrderDetailTx) Append(values ...*model.SynthesisOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a synthesisOrderHasManySynthesisOrderDetailTx) Replace(values ...*model.SynthesisOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a synthesisOrderHasManySynthesisOrderDetailTx) Delete(values ...*model.SynthesisOrderDetail) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a synthesisOrderHasManySynthesisOrderDetailTx) Clear() error {
	return a.tx.Clear()
}

func (a synthesisOrderHasManySynthesisOrderDetailTx) Count() int64 {
	return a.tx.Count()
}

type synthesisOrderBelongsToSynthesis struct {
	db *gorm.DB

	field.RelationField
}

func (a synthesisOrderBelongsToSynthesis) Where(conds ...field.Expr) *synthesisOrderBelongsToSynthesis {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a synthesisOrderBelongsToSynthesis) WithContext(ctx context.Context) *synthesisOrderBelongsToSynthesis {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a synthesisOrderBelongsToSynthesis) Session(session *gorm.Session) *synthesisOrderBelongsToSynthesis {
	a.db = a.db.Session(session)
	return &a
}

func (a synthesisOrderBelongsToSynthesis) Model(m *model.SynthesisOrder) *synthesisOrderBelongsToSynthesisTx {
	return &synthesisOrderBelongsToSynthesisTx{a.db.Model(m).Association(a.Name())}
}

type synthesisOrderBelongsToSynthesisTx struct{ tx *gorm.Association }

func (a synthesisOrderBelongsToSynthesisTx) Find() (result *model.Synthesis, err error) {
	return result, a.tx.Find(&result)
}

func (a synthesisOrderBelongsToSynthesisTx) Append(values ...*model.Synthesis) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a synthesisOrderBelongsToSynthesisTx) Replace(values ...*model.Synthesis) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a synthesisOrderBelongsToSynthesisTx) Delete(values ...*model.Synthesis) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a synthesisOrderBelongsToSynthesisTx) Clear() error {
	return a.tx.Clear()
}

func (a synthesisOrderBelongsToSynthesisTx) Count() int64 {
	return a.tx.Count()
}

type synthesisOrderDo struct{ gen.DO }

type ISynthesisOrderDo interface {
	gen.SubQuery
	Debug() ISynthesisOrderDo
	WithContext(ctx context.Context) ISynthesisOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisOrderDo
	WriteDB() ISynthesisOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisOrderDo
	Not(conds ...gen.Condition) ISynthesisOrderDo
	Or(conds ...gen.Condition) ISynthesisOrderDo
	Select(conds ...field.Expr) ISynthesisOrderDo
	Where(conds ...gen.Condition) ISynthesisOrderDo
	Order(conds ...field.Expr) ISynthesisOrderDo
	Distinct(cols ...field.Expr) ISynthesisOrderDo
	Omit(cols ...field.Expr) ISynthesisOrderDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo
	Group(cols ...field.Expr) ISynthesisOrderDo
	Having(conds ...gen.Condition) ISynthesisOrderDo
	Limit(limit int) ISynthesisOrderDo
	Offset(offset int) ISynthesisOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisOrderDo
	Unscoped() ISynthesisOrderDo
	Create(values ...*model.SynthesisOrder) error
	CreateInBatches(values []*model.SynthesisOrder, batchSize int) error
	Save(values ...*model.SynthesisOrder) error
	First() (*model.SynthesisOrder, error)
	Take() (*model.SynthesisOrder, error)
	Last() (*model.SynthesisOrder, error)
	Find() ([]*model.SynthesisOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisOrder, err error)
	FindInBatches(result *[]*model.SynthesisOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SynthesisOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisOrderDo
	Assign(attrs ...field.AssignExpr) ISynthesisOrderDo
	Joins(fields ...field.RelationField) ISynthesisOrderDo
	Preload(fields ...field.RelationField) ISynthesisOrderDo
	FirstOrInit() (*model.SynthesisOrder, error)
	FirstOrCreate() (*model.SynthesisOrder, error)
	FindByPage(offset int, limit int) (result []*model.SynthesisOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisOrderDo) Debug() ISynthesisOrderDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisOrderDo) WithContext(ctx context.Context) ISynthesisOrderDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisOrderDo) ReadDB() ISynthesisOrderDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisOrderDo) WriteDB() ISynthesisOrderDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisOrderDo) Session(config *gorm.Session) ISynthesisOrderDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisOrderDo) Clauses(conds ...clause.Expression) ISynthesisOrderDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisOrderDo) Returning(value interface{}, columns ...string) ISynthesisOrderDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisOrderDo) Not(conds ...gen.Condition) ISynthesisOrderDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisOrderDo) Or(conds ...gen.Condition) ISynthesisOrderDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisOrderDo) Select(conds ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisOrderDo) Where(conds ...gen.Condition) ISynthesisOrderDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisOrderDo) Order(conds ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisOrderDo) Distinct(cols ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisOrderDo) Omit(cols ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisOrderDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisOrderDo) Group(cols ...field.Expr) ISynthesisOrderDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisOrderDo) Having(conds ...gen.Condition) ISynthesisOrderDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisOrderDo) Limit(limit int) ISynthesisOrderDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisOrderDo) Offset(offset int) ISynthesisOrderDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisOrderDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisOrderDo) Unscoped() ISynthesisOrderDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisOrderDo) Create(values ...*model.SynthesisOrder) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisOrderDo) CreateInBatches(values []*model.SynthesisOrder, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisOrderDo) Save(values ...*model.SynthesisOrder) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisOrderDo) First() (*model.SynthesisOrder, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrder), nil
	}
}

func (s synthesisOrderDo) Take() (*model.SynthesisOrder, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrder), nil
	}
}

func (s synthesisOrderDo) Last() (*model.SynthesisOrder, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrder), nil
	}
}

func (s synthesisOrderDo) Find() ([]*model.SynthesisOrder, error) {
	result, err := s.DO.Find()
	return result.([]*model.SynthesisOrder), err
}

func (s synthesisOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisOrder, err error) {
	buf := make([]*model.SynthesisOrder, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisOrderDo) FindInBatches(result *[]*model.SynthesisOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisOrderDo) Attrs(attrs ...field.AssignExpr) ISynthesisOrderDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisOrderDo) Assign(attrs ...field.AssignExpr) ISynthesisOrderDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisOrderDo) Joins(fields ...field.RelationField) ISynthesisOrderDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisOrderDo) Preload(fields ...field.RelationField) ISynthesisOrderDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisOrderDo) FirstOrInit() (*model.SynthesisOrder, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrder), nil
	}
}

func (s synthesisOrderDo) FirstOrCreate() (*model.SynthesisOrder, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisOrder), nil
	}
}

func (s synthesisOrderDo) FindByPage(offset int, limit int) (result []*model.SynthesisOrder, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisOrderDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisOrderDo) Delete(models ...*model.SynthesisOrder) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisOrderDo) withDO(do gen.Dao) *synthesisOrderDo {
	s.DO = *do.(*gen.DO)
	return s
}
