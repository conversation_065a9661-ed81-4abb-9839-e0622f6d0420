// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesis(db *gorm.DB, opts ...gen.DOOption) synthesis {
	_synthesis := synthesis{}

	_synthesis.synthesisDo.UseDB(db, opts...)
	_synthesis.synthesisDo.UseModel(&model.Synthesis{})

	tableName := _synthesis.synthesisDo.TableName()
	_synthesis.ALL = field.NewAsterisk(tableName)
	_synthesis.ID = field.NewInt64(tableName, "id")
	_synthesis.ActivityCode = field.NewString(tableName, "activity_code")
	_synthesis.Title = field.NewString(tableName, "title")
	_synthesis.ActivityType = field.NewInt32(tableName, "activity_type")
	_synthesis.CoverURL = field.NewString(tableName, "cover_url")
	_synthesis.Status = field.NewInt32(tableName, "status")
	_synthesis.ItemID = field.NewString(tableName, "item_id")
	_synthesis.ItemTitle = field.NewString(tableName, "item_title")
	_synthesis.ItemImageURL = field.NewString(tableName, "item_image_url")
	_synthesis.UserLimit = field.NewInt32(tableName, "user_limit")
	_synthesis.CriticalMaterialLimit = field.NewInt32(tableName, "critical_material_limit")
	_synthesis.CompleteUserNum = field.NewInt32(tableName, "complete_user_num")
	_synthesis.Stock = field.NewInt32(tableName, "stock")
	_synthesis.TotalStock = field.NewInt32(tableName, "total_stock")
	_synthesis.StockDisplay = field.NewInt32(tableName, "stock_display")
	_synthesis.StartTime = field.NewTime(tableName, "start_time")
	_synthesis.EndTime = field.NewTime(tableName, "end_time")
	_synthesis.ActivityDesc = field.NewString(tableName, "activity_desc")
	_synthesis.ReleaseTime = field.NewField(tableName, "release_time")
	_synthesis.CreatedBy = field.NewString(tableName, "created_by")
	_synthesis.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesis.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesis.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesis.IsDel = field.NewField(tableName, "is_del")

	_synthesis.fillFieldMap()

	return _synthesis
}

// synthesis 合成活动表
type synthesis struct {
	synthesisDo

	ALL                   field.Asterisk
	ID                    field.Int64  // id
	ActivityCode          field.String // 活动编码
	Title                 field.String // 活动名称
	ActivityType          field.Int32  // 合成类型【1-优先购权益；2-商品】
	CoverURL              field.String // 封面图
	Status                field.Int32  // 状态【-1:已删除;1:待上架;2:已上架;3:已下架;4:已结束】
	ItemID                field.String // 合成物品id(商品/优先购)
	ItemTitle             field.String // 合成物品名称(商品/优先购)
	ItemImageURL          field.String // 合成物品图片
	UserLimit             field.Int32  // 每人限合(0不限制)
	CriticalMaterialLimit field.Int32  // 关键材料最小值
	CompleteUserNum       field.Int32  // 已合人数(按用户去重)
	Stock                 field.Int32  // 剩余库存
	TotalStock            field.Int32  // 总库存
	StockDisplay          field.Int32  // 剩余库存是否显示【1:显示;2:不显示】
	StartTime             field.Time   // 开始时间
	EndTime               field.Time   // 结束时间
	ActivityDesc          field.String // 说明
	ReleaseTime           field.Field  // 释放时间
	CreatedBy             field.String // 创建人
	CreatedAt             field.Time   // 创建时间
	UpdatedBy             field.String // 更新人
	UpdatedAt             field.Time   // 更新时间
	IsDel                 field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s synthesis) Table(newTableName string) *synthesis {
	s.synthesisDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesis) As(alias string) *synthesis {
	s.synthesisDo.DO = *(s.synthesisDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesis) updateTableName(table string) *synthesis {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.ActivityCode = field.NewString(table, "activity_code")
	s.Title = field.NewString(table, "title")
	s.ActivityType = field.NewInt32(table, "activity_type")
	s.CoverURL = field.NewString(table, "cover_url")
	s.Status = field.NewInt32(table, "status")
	s.ItemID = field.NewString(table, "item_id")
	s.ItemTitle = field.NewString(table, "item_title")
	s.ItemImageURL = field.NewString(table, "item_image_url")
	s.UserLimit = field.NewInt32(table, "user_limit")
	s.CriticalMaterialLimit = field.NewInt32(table, "critical_material_limit")
	s.CompleteUserNum = field.NewInt32(table, "complete_user_num")
	s.Stock = field.NewInt32(table, "stock")
	s.TotalStock = field.NewInt32(table, "total_stock")
	s.StockDisplay = field.NewInt32(table, "stock_display")
	s.StartTime = field.NewTime(table, "start_time")
	s.EndTime = field.NewTime(table, "end_time")
	s.ActivityDesc = field.NewString(table, "activity_desc")
	s.ReleaseTime = field.NewField(table, "release_time")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesis) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesis) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 24)
	s.fieldMap["id"] = s.ID
	s.fieldMap["activity_code"] = s.ActivityCode
	s.fieldMap["title"] = s.Title
	s.fieldMap["activity_type"] = s.ActivityType
	s.fieldMap["cover_url"] = s.CoverURL
	s.fieldMap["status"] = s.Status
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["item_title"] = s.ItemTitle
	s.fieldMap["item_image_url"] = s.ItemImageURL
	s.fieldMap["user_limit"] = s.UserLimit
	s.fieldMap["critical_material_limit"] = s.CriticalMaterialLimit
	s.fieldMap["complete_user_num"] = s.CompleteUserNum
	s.fieldMap["stock"] = s.Stock
	s.fieldMap["total_stock"] = s.TotalStock
	s.fieldMap["stock_display"] = s.StockDisplay
	s.fieldMap["start_time"] = s.StartTime
	s.fieldMap["end_time"] = s.EndTime
	s.fieldMap["activity_desc"] = s.ActivityDesc
	s.fieldMap["release_time"] = s.ReleaseTime
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s synthesis) clone(db *gorm.DB) synthesis {
	s.synthesisDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesis) replaceDB(db *gorm.DB) synthesis {
	s.synthesisDo.ReplaceDB(db)
	return s
}

type synthesisDo struct{ gen.DO }

type ISynthesisDo interface {
	gen.SubQuery
	Debug() ISynthesisDo
	WithContext(ctx context.Context) ISynthesisDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisDo
	WriteDB() ISynthesisDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisDo
	Not(conds ...gen.Condition) ISynthesisDo
	Or(conds ...gen.Condition) ISynthesisDo
	Select(conds ...field.Expr) ISynthesisDo
	Where(conds ...gen.Condition) ISynthesisDo
	Order(conds ...field.Expr) ISynthesisDo
	Distinct(cols ...field.Expr) ISynthesisDo
	Omit(cols ...field.Expr) ISynthesisDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisDo
	Group(cols ...field.Expr) ISynthesisDo
	Having(conds ...gen.Condition) ISynthesisDo
	Limit(limit int) ISynthesisDo
	Offset(offset int) ISynthesisDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisDo
	Unscoped() ISynthesisDo
	Create(values ...*model.Synthesis) error
	CreateInBatches(values []*model.Synthesis, batchSize int) error
	Save(values ...*model.Synthesis) error
	First() (*model.Synthesis, error)
	Take() (*model.Synthesis, error)
	Last() (*model.Synthesis, error)
	Find() ([]*model.Synthesis, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Synthesis, err error)
	FindInBatches(result *[]*model.Synthesis, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.Synthesis) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisDo
	Assign(attrs ...field.AssignExpr) ISynthesisDo
	Joins(fields ...field.RelationField) ISynthesisDo
	Preload(fields ...field.RelationField) ISynthesisDo
	FirstOrInit() (*model.Synthesis, error)
	FirstOrCreate() (*model.Synthesis, error)
	FindByPage(offset int, limit int) (result []*model.Synthesis, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisDo) Debug() ISynthesisDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisDo) WithContext(ctx context.Context) ISynthesisDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisDo) ReadDB() ISynthesisDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisDo) WriteDB() ISynthesisDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisDo) Session(config *gorm.Session) ISynthesisDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisDo) Clauses(conds ...clause.Expression) ISynthesisDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisDo) Returning(value interface{}, columns ...string) ISynthesisDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisDo) Not(conds ...gen.Condition) ISynthesisDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisDo) Or(conds ...gen.Condition) ISynthesisDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisDo) Select(conds ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisDo) Where(conds ...gen.Condition) ISynthesisDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisDo) Order(conds ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisDo) Distinct(cols ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisDo) Omit(cols ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisDo) Group(cols ...field.Expr) ISynthesisDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisDo) Having(conds ...gen.Condition) ISynthesisDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisDo) Limit(limit int) ISynthesisDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisDo) Offset(offset int) ISynthesisDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisDo) Unscoped() ISynthesisDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisDo) Create(values ...*model.Synthesis) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisDo) CreateInBatches(values []*model.Synthesis, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisDo) Save(values ...*model.Synthesis) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisDo) First() (*model.Synthesis, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Synthesis), nil
	}
}

func (s synthesisDo) Take() (*model.Synthesis, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Synthesis), nil
	}
}

func (s synthesisDo) Last() (*model.Synthesis, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Synthesis), nil
	}
}

func (s synthesisDo) Find() ([]*model.Synthesis, error) {
	result, err := s.DO.Find()
	return result.([]*model.Synthesis), err
}

func (s synthesisDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Synthesis, err error) {
	buf := make([]*model.Synthesis, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisDo) FindInBatches(result *[]*model.Synthesis, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisDo) Attrs(attrs ...field.AssignExpr) ISynthesisDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisDo) Assign(attrs ...field.AssignExpr) ISynthesisDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisDo) Joins(fields ...field.RelationField) ISynthesisDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisDo) Preload(fields ...field.RelationField) ISynthesisDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisDo) FirstOrInit() (*model.Synthesis, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Synthesis), nil
	}
}

func (s synthesisDo) FirstOrCreate() (*model.Synthesis, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Synthesis), nil
	}
}

func (s synthesisDo) FindByPage(offset int, limit int) (result []*model.Synthesis, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisDo) Delete(models ...*model.Synthesis) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisDo) withDO(do gen.Dao) *synthesisDo {
	s.DO = *do.(*gen.DO)
	return s
}
