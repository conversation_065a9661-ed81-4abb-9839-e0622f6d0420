// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesisMaterials(db *gorm.DB, opts ...gen.DOOption) synthesisMaterials {
	_synthesisMaterials := synthesisMaterials{}

	_synthesisMaterials.synthesisMaterialsDo.UseDB(db, opts...)
	_synthesisMaterials.synthesisMaterialsDo.UseModel(&model.SynthesisMaterials{})

	tableName := _synthesisMaterials.synthesisMaterialsDo.TableName()
	_synthesisMaterials.ALL = field.NewAsterisk(tableName)
	_synthesisMaterials.ID = field.NewInt64(tableName, "id")
	_synthesisMaterials.SynthesisID = field.NewInt64(tableName, "synthesis_id")
	_synthesisMaterials.MaterialsType = field.NewInt32(tableName, "materials_type")
	_synthesisMaterials.MaterialsData = field.NewField(tableName, "materials_data")
	_synthesisMaterials.LimitQty = field.NewInt32(tableName, "limit_qty")
	_synthesisMaterials.CreatedBy = field.NewString(tableName, "created_by")
	_synthesisMaterials.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesisMaterials.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesisMaterials.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesisMaterials.IsDel = field.NewField(tableName, "is_del")

	_synthesisMaterials.fillFieldMap()

	return _synthesisMaterials
}

// synthesisMaterials 合成材料表
type synthesisMaterials struct {
	synthesisMaterialsDo

	ALL           field.Asterisk
	ID            field.Int64  // id
	SynthesisID   field.Int64  // 合成活动id(synthesis.id)
	MaterialsType field.Int32  // 材料类型【1-核心材料；2-关键材料】
	MaterialsData field.Field  // 材料内容
	LimitQty      field.Int32  // 限制数量(0全部集齐)
	CreatedBy     field.String // 创建人
	CreatedAt     field.Time   // 创建时间
	UpdatedBy     field.String // 更新人
	UpdatedAt     field.Time   // 更新时间
	IsDel         field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s synthesisMaterials) Table(newTableName string) *synthesisMaterials {
	s.synthesisMaterialsDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesisMaterials) As(alias string) *synthesisMaterials {
	s.synthesisMaterialsDo.DO = *(s.synthesisMaterialsDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesisMaterials) updateTableName(table string) *synthesisMaterials {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SynthesisID = field.NewInt64(table, "synthesis_id")
	s.MaterialsType = field.NewInt32(table, "materials_type")
	s.MaterialsData = field.NewField(table, "materials_data")
	s.LimitQty = field.NewInt32(table, "limit_qty")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesisMaterials) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesisMaterials) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.ID
	s.fieldMap["synthesis_id"] = s.SynthesisID
	s.fieldMap["materials_type"] = s.MaterialsType
	s.fieldMap["materials_data"] = s.MaterialsData
	s.fieldMap["limit_qty"] = s.LimitQty
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s synthesisMaterials) clone(db *gorm.DB) synthesisMaterials {
	s.synthesisMaterialsDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesisMaterials) replaceDB(db *gorm.DB) synthesisMaterials {
	s.synthesisMaterialsDo.ReplaceDB(db)
	return s
}

type synthesisMaterialsDo struct{ gen.DO }

type ISynthesisMaterialsDo interface {
	gen.SubQuery
	Debug() ISynthesisMaterialsDo
	WithContext(ctx context.Context) ISynthesisMaterialsDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisMaterialsDo
	WriteDB() ISynthesisMaterialsDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisMaterialsDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisMaterialsDo
	Not(conds ...gen.Condition) ISynthesisMaterialsDo
	Or(conds ...gen.Condition) ISynthesisMaterialsDo
	Select(conds ...field.Expr) ISynthesisMaterialsDo
	Where(conds ...gen.Condition) ISynthesisMaterialsDo
	Order(conds ...field.Expr) ISynthesisMaterialsDo
	Distinct(cols ...field.Expr) ISynthesisMaterialsDo
	Omit(cols ...field.Expr) ISynthesisMaterialsDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo
	Group(cols ...field.Expr) ISynthesisMaterialsDo
	Having(conds ...gen.Condition) ISynthesisMaterialsDo
	Limit(limit int) ISynthesisMaterialsDo
	Offset(offset int) ISynthesisMaterialsDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisMaterialsDo
	Unscoped() ISynthesisMaterialsDo
	Create(values ...*model.SynthesisMaterials) error
	CreateInBatches(values []*model.SynthesisMaterials, batchSize int) error
	Save(values ...*model.SynthesisMaterials) error
	First() (*model.SynthesisMaterials, error)
	Take() (*model.SynthesisMaterials, error)
	Last() (*model.SynthesisMaterials, error)
	Find() ([]*model.SynthesisMaterials, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisMaterials, err error)
	FindInBatches(result *[]*model.SynthesisMaterials, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SynthesisMaterials) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisMaterialsDo
	Assign(attrs ...field.AssignExpr) ISynthesisMaterialsDo
	Joins(fields ...field.RelationField) ISynthesisMaterialsDo
	Preload(fields ...field.RelationField) ISynthesisMaterialsDo
	FirstOrInit() (*model.SynthesisMaterials, error)
	FirstOrCreate() (*model.SynthesisMaterials, error)
	FindByPage(offset int, limit int) (result []*model.SynthesisMaterials, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisMaterialsDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisMaterialsDo) Debug() ISynthesisMaterialsDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisMaterialsDo) WithContext(ctx context.Context) ISynthesisMaterialsDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisMaterialsDo) ReadDB() ISynthesisMaterialsDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisMaterialsDo) WriteDB() ISynthesisMaterialsDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisMaterialsDo) Session(config *gorm.Session) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisMaterialsDo) Clauses(conds ...clause.Expression) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisMaterialsDo) Returning(value interface{}, columns ...string) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisMaterialsDo) Not(conds ...gen.Condition) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisMaterialsDo) Or(conds ...gen.Condition) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisMaterialsDo) Select(conds ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisMaterialsDo) Where(conds ...gen.Condition) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisMaterialsDo) Order(conds ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisMaterialsDo) Distinct(cols ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisMaterialsDo) Omit(cols ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisMaterialsDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisMaterialsDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisMaterialsDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisMaterialsDo) Group(cols ...field.Expr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisMaterialsDo) Having(conds ...gen.Condition) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisMaterialsDo) Limit(limit int) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisMaterialsDo) Offset(offset int) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisMaterialsDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisMaterialsDo) Unscoped() ISynthesisMaterialsDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisMaterialsDo) Create(values ...*model.SynthesisMaterials) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisMaterialsDo) CreateInBatches(values []*model.SynthesisMaterials, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisMaterialsDo) Save(values ...*model.SynthesisMaterials) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisMaterialsDo) First() (*model.SynthesisMaterials, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterials), nil
	}
}

func (s synthesisMaterialsDo) Take() (*model.SynthesisMaterials, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterials), nil
	}
}

func (s synthesisMaterialsDo) Last() (*model.SynthesisMaterials, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterials), nil
	}
}

func (s synthesisMaterialsDo) Find() ([]*model.SynthesisMaterials, error) {
	result, err := s.DO.Find()
	return result.([]*model.SynthesisMaterials), err
}

func (s synthesisMaterialsDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisMaterials, err error) {
	buf := make([]*model.SynthesisMaterials, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisMaterialsDo) FindInBatches(result *[]*model.SynthesisMaterials, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisMaterialsDo) Attrs(attrs ...field.AssignExpr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisMaterialsDo) Assign(attrs ...field.AssignExpr) ISynthesisMaterialsDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisMaterialsDo) Joins(fields ...field.RelationField) ISynthesisMaterialsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisMaterialsDo) Preload(fields ...field.RelationField) ISynthesisMaterialsDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisMaterialsDo) FirstOrInit() (*model.SynthesisMaterials, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterials), nil
	}
}

func (s synthesisMaterialsDo) FirstOrCreate() (*model.SynthesisMaterials, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterials), nil
	}
}

func (s synthesisMaterialsDo) FindByPage(offset int, limit int) (result []*model.SynthesisMaterials, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisMaterialsDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisMaterialsDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisMaterialsDo) Delete(models ...*model.SynthesisMaterials) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisMaterialsDo) withDO(do gen.Dao) *synthesisMaterialsDo {
	s.DO = *do.(*gen.DO)
	return s
}
