// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/synthesis/dal/model"
)

func newSynthesisMaterialsRelease(db *gorm.DB, opts ...gen.DOOption) synthesisMaterialsRelease {
	_synthesisMaterialsRelease := synthesisMaterialsRelease{}

	_synthesisMaterialsRelease.synthesisMaterialsReleaseDo.UseDB(db, opts...)
	_synthesisMaterialsRelease.synthesisMaterialsReleaseDo.UseModel(&model.SynthesisMaterialsRelease{})

	tableName := _synthesisMaterialsRelease.synthesisMaterialsReleaseDo.TableName()
	_synthesisMaterialsRelease.ALL = field.NewAsterisk(tableName)
	_synthesisMaterialsRelease.ID = field.NewInt64(tableName, "id")
	_synthesisMaterialsRelease.SynthesisOrderDetailID = field.NewInt64(tableName, "synthesis_order_detail_id")
	_synthesisMaterialsRelease.UserID = field.NewString(tableName, "user_id")
	_synthesisMaterialsRelease.ItemID = field.NewString(tableName, "item_id")
	_synthesisMaterialsRelease.UserItemID = field.NewString(tableName, "user_item_id")
	_synthesisMaterialsRelease.Status = field.NewInt32(tableName, "status")
	_synthesisMaterialsRelease.ReleaseTime = field.NewTime(tableName, "release_time")
	_synthesisMaterialsRelease.RealReleaseTime = field.NewTime(tableName, "real_release_time")
	_synthesisMaterialsRelease.CreatedBy = field.NewString(tableName, "created_by")
	_synthesisMaterialsRelease.CreatedAt = field.NewTime(tableName, "created_at")
	_synthesisMaterialsRelease.UpdatedBy = field.NewString(tableName, "updated_by")
	_synthesisMaterialsRelease.UpdatedAt = field.NewTime(tableName, "updated_at")
	_synthesisMaterialsRelease.IsDel = field.NewField(tableName, "is_del")

	_synthesisMaterialsRelease.fillFieldMap()

	return _synthesisMaterialsRelease
}

// synthesisMaterialsRelease 合成材料释放表
type synthesisMaterialsRelease struct {
	synthesisMaterialsReleaseDo

	ALL                    field.Asterisk
	ID                     field.Int64  // id
	SynthesisOrderDetailID field.Int64  // 合成订单详情id(synthesis_order_detail.id)
	UserID                 field.String // 用户id
	ItemID                 field.String // 物品id
	UserItemID             field.String // 用户背包物品id
	Status                 field.Int32  // 状态【-1:释放失败;1:已释放;2:待释放;3:释放中】
	ReleaseTime            field.Time   // 应释放时间
	RealReleaseTime        field.Time   // 实际释放时间
	CreatedBy              field.String // 创建人
	CreatedAt              field.Time   // 创建时间
	UpdatedBy              field.String // 更新人
	UpdatedAt              field.Time   // 更新时间
	IsDel                  field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (s synthesisMaterialsRelease) Table(newTableName string) *synthesisMaterialsRelease {
	s.synthesisMaterialsReleaseDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s synthesisMaterialsRelease) As(alias string) *synthesisMaterialsRelease {
	s.synthesisMaterialsReleaseDo.DO = *(s.synthesisMaterialsReleaseDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *synthesisMaterialsRelease) updateTableName(table string) *synthesisMaterialsRelease {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.SynthesisOrderDetailID = field.NewInt64(table, "synthesis_order_detail_id")
	s.UserID = field.NewString(table, "user_id")
	s.ItemID = field.NewString(table, "item_id")
	s.UserItemID = field.NewString(table, "user_item_id")
	s.Status = field.NewInt32(table, "status")
	s.ReleaseTime = field.NewTime(table, "release_time")
	s.RealReleaseTime = field.NewTime(table, "real_release_time")
	s.CreatedBy = field.NewString(table, "created_by")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedBy = field.NewString(table, "updated_by")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.IsDel = field.NewField(table, "is_del")

	s.fillFieldMap()

	return s
}

func (s *synthesisMaterialsRelease) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *synthesisMaterialsRelease) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 13)
	s.fieldMap["id"] = s.ID
	s.fieldMap["synthesis_order_detail_id"] = s.SynthesisOrderDetailID
	s.fieldMap["user_id"] = s.UserID
	s.fieldMap["item_id"] = s.ItemID
	s.fieldMap["user_item_id"] = s.UserItemID
	s.fieldMap["status"] = s.Status
	s.fieldMap["release_time"] = s.ReleaseTime
	s.fieldMap["real_release_time"] = s.RealReleaseTime
	s.fieldMap["created_by"] = s.CreatedBy
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_by"] = s.UpdatedBy
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["is_del"] = s.IsDel
}

func (s synthesisMaterialsRelease) clone(db *gorm.DB) synthesisMaterialsRelease {
	s.synthesisMaterialsReleaseDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s synthesisMaterialsRelease) replaceDB(db *gorm.DB) synthesisMaterialsRelease {
	s.synthesisMaterialsReleaseDo.ReplaceDB(db)
	return s
}

type synthesisMaterialsReleaseDo struct{ gen.DO }

type ISynthesisMaterialsReleaseDo interface {
	gen.SubQuery
	Debug() ISynthesisMaterialsReleaseDo
	WithContext(ctx context.Context) ISynthesisMaterialsReleaseDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISynthesisMaterialsReleaseDo
	WriteDB() ISynthesisMaterialsReleaseDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISynthesisMaterialsReleaseDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISynthesisMaterialsReleaseDo
	Not(conds ...gen.Condition) ISynthesisMaterialsReleaseDo
	Or(conds ...gen.Condition) ISynthesisMaterialsReleaseDo
	Select(conds ...field.Expr) ISynthesisMaterialsReleaseDo
	Where(conds ...gen.Condition) ISynthesisMaterialsReleaseDo
	Order(conds ...field.Expr) ISynthesisMaterialsReleaseDo
	Distinct(cols ...field.Expr) ISynthesisMaterialsReleaseDo
	Omit(cols ...field.Expr) ISynthesisMaterialsReleaseDo
	Join(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo
	Group(cols ...field.Expr) ISynthesisMaterialsReleaseDo
	Having(conds ...gen.Condition) ISynthesisMaterialsReleaseDo
	Limit(limit int) ISynthesisMaterialsReleaseDo
	Offset(offset int) ISynthesisMaterialsReleaseDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisMaterialsReleaseDo
	Unscoped() ISynthesisMaterialsReleaseDo
	Create(values ...*model.SynthesisMaterialsRelease) error
	CreateInBatches(values []*model.SynthesisMaterialsRelease, batchSize int) error
	Save(values ...*model.SynthesisMaterialsRelease) error
	First() (*model.SynthesisMaterialsRelease, error)
	Take() (*model.SynthesisMaterialsRelease, error)
	Last() (*model.SynthesisMaterialsRelease, error)
	Find() ([]*model.SynthesisMaterialsRelease, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisMaterialsRelease, err error)
	FindInBatches(result *[]*model.SynthesisMaterialsRelease, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.SynthesisMaterialsRelease) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISynthesisMaterialsReleaseDo
	Assign(attrs ...field.AssignExpr) ISynthesisMaterialsReleaseDo
	Joins(fields ...field.RelationField) ISynthesisMaterialsReleaseDo
	Preload(fields ...field.RelationField) ISynthesisMaterialsReleaseDo
	FirstOrInit() (*model.SynthesisMaterialsRelease, error)
	FirstOrCreate() (*model.SynthesisMaterialsRelease, error)
	FindByPage(offset int, limit int) (result []*model.SynthesisMaterialsRelease, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISynthesisMaterialsReleaseDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s synthesisMaterialsReleaseDo) Debug() ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Debug())
}

func (s synthesisMaterialsReleaseDo) WithContext(ctx context.Context) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s synthesisMaterialsReleaseDo) ReadDB() ISynthesisMaterialsReleaseDo {
	return s.Clauses(dbresolver.Read)
}

func (s synthesisMaterialsReleaseDo) WriteDB() ISynthesisMaterialsReleaseDo {
	return s.Clauses(dbresolver.Write)
}

func (s synthesisMaterialsReleaseDo) Session(config *gorm.Session) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Session(config))
}

func (s synthesisMaterialsReleaseDo) Clauses(conds ...clause.Expression) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s synthesisMaterialsReleaseDo) Returning(value interface{}, columns ...string) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s synthesisMaterialsReleaseDo) Not(conds ...gen.Condition) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s synthesisMaterialsReleaseDo) Or(conds ...gen.Condition) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s synthesisMaterialsReleaseDo) Select(conds ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s synthesisMaterialsReleaseDo) Where(conds ...gen.Condition) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s synthesisMaterialsReleaseDo) Order(conds ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s synthesisMaterialsReleaseDo) Distinct(cols ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s synthesisMaterialsReleaseDo) Omit(cols ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s synthesisMaterialsReleaseDo) Join(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s synthesisMaterialsReleaseDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s synthesisMaterialsReleaseDo) RightJoin(table schema.Tabler, on ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s synthesisMaterialsReleaseDo) Group(cols ...field.Expr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s synthesisMaterialsReleaseDo) Having(conds ...gen.Condition) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s synthesisMaterialsReleaseDo) Limit(limit int) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s synthesisMaterialsReleaseDo) Offset(offset int) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s synthesisMaterialsReleaseDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s synthesisMaterialsReleaseDo) Unscoped() ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Unscoped())
}

func (s synthesisMaterialsReleaseDo) Create(values ...*model.SynthesisMaterialsRelease) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s synthesisMaterialsReleaseDo) CreateInBatches(values []*model.SynthesisMaterialsRelease, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s synthesisMaterialsReleaseDo) Save(values ...*model.SynthesisMaterialsRelease) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s synthesisMaterialsReleaseDo) First() (*model.SynthesisMaterialsRelease, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterialsRelease), nil
	}
}

func (s synthesisMaterialsReleaseDo) Take() (*model.SynthesisMaterialsRelease, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterialsRelease), nil
	}
}

func (s synthesisMaterialsReleaseDo) Last() (*model.SynthesisMaterialsRelease, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterialsRelease), nil
	}
}

func (s synthesisMaterialsReleaseDo) Find() ([]*model.SynthesisMaterialsRelease, error) {
	result, err := s.DO.Find()
	return result.([]*model.SynthesisMaterialsRelease), err
}

func (s synthesisMaterialsReleaseDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.SynthesisMaterialsRelease, err error) {
	buf := make([]*model.SynthesisMaterialsRelease, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s synthesisMaterialsReleaseDo) FindInBatches(result *[]*model.SynthesisMaterialsRelease, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s synthesisMaterialsReleaseDo) Attrs(attrs ...field.AssignExpr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s synthesisMaterialsReleaseDo) Assign(attrs ...field.AssignExpr) ISynthesisMaterialsReleaseDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s synthesisMaterialsReleaseDo) Joins(fields ...field.RelationField) ISynthesisMaterialsReleaseDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s synthesisMaterialsReleaseDo) Preload(fields ...field.RelationField) ISynthesisMaterialsReleaseDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s synthesisMaterialsReleaseDo) FirstOrInit() (*model.SynthesisMaterialsRelease, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterialsRelease), nil
	}
}

func (s synthesisMaterialsReleaseDo) FirstOrCreate() (*model.SynthesisMaterialsRelease, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.SynthesisMaterialsRelease), nil
	}
}

func (s synthesisMaterialsReleaseDo) FindByPage(offset int, limit int) (result []*model.SynthesisMaterialsRelease, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s synthesisMaterialsReleaseDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s synthesisMaterialsReleaseDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s synthesisMaterialsReleaseDo) Delete(models ...*model.SynthesisMaterialsRelease) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *synthesisMaterialsReleaseDo) withDO(do gen.Dao) *synthesisMaterialsReleaseDo {
	s.DO = *do.(*gen.DO)
	return s
}
