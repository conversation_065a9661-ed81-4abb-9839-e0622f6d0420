package admin

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
)

// GetSynthesisList
// @Summary 查询合成管理列表
// @Description 查询合成管理列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisAdminListResp}
// @Router  /admin/v1/synthesis/list [get]
// @Security Bearer
func GetSynthesisList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisAdminListReq{}, s.GetSynthesisList)
}

// GetSynthesisDetail
// @Summary 查询合成管理详情
// @Description 查询合成管理详情
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisAdminDetailResp}
// @Router  /admin/v1/synthesis/detail [get]
// @Security Bearer
func GetSynthesisDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisAdminDetailReq{}, s.GetSynthesisDetail)
}

// AddSynthesis
// @Summary 新增合成活动
// @Description 新增合成活动
// @Tags 管理端-合成活动管理
// @Param data body define.AddSynthesisReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddSynthesisResp}
// @Router  /admin/v1/synthesis/add [POST]
// @Security Bearer
func AddSynthesis(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddSynthesisReq{}, s.AddSynthesis)
}

// EditSynthesis
// @Summary 编辑合成活动
// @Description 编辑合成活动
// @Tags 管理端-合成活动管理
// @Param data body define.EditSynthesisReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditSynthesisResp}
// @Router  /admin/v1/synthesis/edit [POST]
// @Security Bearer
func EditSynthesis(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditSynthesisReq{}, s.EditSynthesis)
}

// EditSynthesisStatus
// @Summary 合成活动状态编辑
// @Description 合成活动状态编辑
// @Tags 管理端-合成活动管理
// @Param data body define.EditSynthesisStatusReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditSynthesisStatusResp}
// @Router  /admin/v1/synthesis/edit_status [POST]
// @Security Bearer
func EditSynthesisStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditSynthesisStatusReq{}, s.EditSynthesisStatus)
}

// DelSynthesis
// @Summary 合成活动删除
// @Description 合成活动删除
// @Tags 管理端-合成活动管理
// @Param data body define.DelSynthesisReq true "新增参数"
// @Success 200 {object} response.Data{data=define.DelSynthesisResp}
// @Router  /admin/v1/synthesis/del [POST]
// @Security Bearer
func DelSynthesis(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelSynthesisReq{}, s.DelSynthesis)
}

// GetSynthesisLogList
// @Summary 查询合成活动日志列表
// @Description 查询合成活动日志列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisLogListReq true "查询参数"
// @Success 200 {object} response.Data{data=[]define.GetSynthesisLogListResp}
// @Router  /admin/v1/synthesis/log_list [get]
// @Security Bearer
func GetSynthesisLogList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisLogListReq{}, s.GetSynthesisLogList)
}

// GetSynthesisRule
// @Summary 查询合成规则
// @Description 查询合成规则
// @Tags 管理端-合成活动管理
// @Success 200 {object} response.Data{data=define.GetSynthesisRuleAdminResp}
// @Router  /admin/v1/synthesis/rule [get]
// @Security Bearer
func GetSynthesisRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.GetSynthesisRuleAdmin)
}

// EditSynthesisRule
// @Summary 编辑合成规则
// @Description 编辑合成规则
// @Tags 管理端-合成活动管理
// @Param data body define.EditSynthesisRuleReq true "新增参数"
// @Success 200 {object} response.Data{data=define.EditSynthesisRuleResp}
// @Router  /admin/v1/synthesis/rule [post]
// @Security Bearer
func EditSynthesisRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditSynthesisRuleReq{}, s.EditSynthesisRule)
}

// GetSynthesisOrderList
// @Summary 查询合成订单列表
// @Description 查询合成订单列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisAdminOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisAdminOrderListResp}
// @Router  /admin/v1/synthesis/order/list [get]
// @Security Bearer
func GetSynthesisOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisAdminOrderListReq{}, s.GetSynthesisAdminOrderList)
}

// GetSynthesisOrderDetail
// @Summary 查询合成订单详情
// @Description 查询合成订单详情
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisAdminOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisAdminOrderDetailResp}
// @Router  /admin/v1/synthesis/order/detail [get]
// @Security Bearer
func GetSynthesisOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisAdminOrderDetailReq{}, s.GetSynthesisAdminOrderDetail)
}

// GetSynthesisOrderDetailList
// @Summary 获取合成订单详情列表
// @Description 获取合成订单详情列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisOrderDetailListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisOrderDetailListResp}
// @Router  /admin/v1/synthesis/order_detail/list [get]
// @Security Bearer
func GetSynthesisOrderDetailList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisOrderDetailListReq{}, s.GetSynthesisOrderDetailList)
}

// ExportSynthesisOrderList
// @Summary 导出合成订单列表
// @Description 导出合成订单列表，下载文件
// @Tags 管理端-合成活动管理
// @Param data query define.GetSynthesisAdminOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisAdminOrderListResp}
// @Router  /admin/v1/synthesis/order/export [get]
// @Security Bearer
func ExportSynthesisOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetSynthesisAdminOrderListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportSynthesisAdminOrderList(req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}

// GetPriorityBuyList
// @Summary 查询优先购列表
// @Description 查询优先购列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetPriorityBuyListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetPriorityBuyListResp}
// @Router  /admin/v1/synthesis/priority_buy/list [get]
// @Security Bearer
func GetPriorityBuyList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPriorityBuyListReq{}, s.GetPriorityBuyList)
}

// GetPrizeItemList
// @Summary 查询融合商品奖品列表
// @Description 查询融合商品奖品列表
// @Tags 管理端-合成活动管理
// @Param data query define.GetItemSynthesisListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetItemSynthesisListResp}
// @Router  /admin/v1/synthesis/prize_item/list [get]
// @Security Bearer
func GetPrizeItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetItemSynthesisListReq{}, s.GetItemSynthesisList)
}
