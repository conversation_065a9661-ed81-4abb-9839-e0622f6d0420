package open

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetPriorityBuyIsUp
// @Summary 获取优先购合成活动是否上架
// @Description 获取优先购合成活动是否上架
// @Tags open端-合成活动管理
// @Param data query define.GetPriorityBuyIsUpReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetPriorityBuyIsUpResp}
// @Router  /open/v1/synthesis/priority_buy_is_up [get]
// @Security Bearer
func GetPriorityBuyIsUp(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPriorityBuyIsUpReq{}, s.GetPriorityBuyIsUp)
}

// GetItemIsUp
// @Summary 获取合成活动是否上架
// @Description 获取合成活动是否上架
// @Tags open端-合成活动管理
// @Param data query define.GetItemIsUpReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetItemIsUpResp}
// @Router  /open/v1/synthesis/item_is_up [get]
// @Security Bearer
func GetItemIsUp(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetItemIsUpReq{}, s.GetItemIsUp)
}

// SynthesisFinish
// @Summary 合成活动超时下架
// @Description 合成活动超时下架
// @Tags open端-合成活动管理
// @Param data body define.SynthesisFinishReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SynthesisFinishResp}
// @Router  /open/v1/synthesis/finish [POST]
// @Security Bearer
func SynthesisFinish(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.SynthesisFinish)
}

// SynthesisOrderUpChain
// @Summary 合成订单上链
// @Description 合成订单上链
// @Tags open端-合成活动管理
// @Param data body define.SynthesisOrderUpChainReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SynthesisOrderUpChainResp}
// @Router  /open/v1/synthesis/order/up_chain [POST]
// @Security Bearer
func SynthesisOrderUpChain(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SynthesisOrderUpChainReq{}, s.AsyncSynthesisOrderUpChain)
}

// SynthesisMaterialsRelease
// @Summary 合成材料释放
// @Description 合成材料释放
// @Tags open端-合成活动管理
// @Param data body define.SynthesisMaterialsReleaseReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SynthesisMaterialsReleaseResp}
// @Router  /open/v1/synthesis/materials/release [POST]
// @Security Bearer
func SynthesisMaterialsRelease(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SynthesisMaterialsReleaseReq{}, s.AsyncSynthesisMaterialsRelease)
}

// SynthesisMaterialsGetReleaseTime
// @Summary 获取合成材料释放时间
// @Description 获取合成材料释放时间
// @Tags open端-合成活动管理
// @Param data body define.SynthesisMaterialsGetReleaseTimeReq true "新增参数"
// @Success 200 {object} response.Data{data=define.SynthesisMaterialsGetReleaseTimeResp}
// @Router  /open/v1/synthesis/materials/get_release_time [POST]
// @Security Bearer
func SynthesisMaterialsGetReleaseTime(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SynthesisMaterialsGetReleaseTimeReq{}, s.SynthesisMaterialsGetReleaseTime)
}
