package web

import (
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetSynthesisRule
// @Summary 查询合成规则
// @Description 查询合成规则
// @Tags 用户端-合成活动管理
// @Success 200 {object} response.Data{data=define.GetSynthesisRuleAdminResp}
// @Router  /web/v1/synthesis/rule [get]
// @Security Bearer
func GetSynthesisRule(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.GetSynthesisRuleAdmin)
}

// GetSynthesisWebList
// @Summary 查询合成活动列表
// @Description 查询合成活动列表
// @Tags 用户端-合成活动管理
// @Param data query define.GetSynthesisWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisWebListResp}
// @Router  /web/v1/synthesis/list [get]
// @Security Bearer
func GetSynthesisWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisWebListReq{}, s.GetSynthesisWebList)
}

// GetSynthesisWebDetail
// @Summary 查询合成活动详情
// @Description 查询合成活动详情
// @Tags 用户端-合成活动管理
// @Param data query define.GetSynthesisWebDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisWebDetailResp}
// @Router  /web/v1/synthesis/detail [get]
// @Security Bearer
func GetSynthesisWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisWebDetailReq{}, s.GetSynthesisWebDetail)
}

// LaunchSynthesis
// @Summary 发起合成
// @Description 发起合成
// @Tags 用户端-合成活动管理
// @Param data body define.LaunchSynthesisReq true "新增参数"
// @Success 200 {object} response.Data{data=define.LaunchSynthesisResp}
// @Router  /web/v1/synthesis/launch [POST]
// @Security Bearer
func LaunchSynthesis(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.LaunchSynthesisReq{}, s.LaunchSynthesis)
}

// GetSynthesisWebOrderList
// @Summary 查询合成活动订单列表
// @Description 查询合成活动订单列表
// @Tags 用户端-合成活动管理
// @Param data query define.GetSynthesisWebOrderListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisWebOrderListResp}
// @Router  /web/v1/synthesis/order/list [get]
// @Security Bearer
func GetSynthesisWebOrderList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisWebOrderListReq{}, s.GetSynthesisWebOrderList)
}

// GetSynthesisWebOrderDetail
// @Summary 查询合成活动订单详情
// @Description 查询合成活动订单详情
// @Tags 用户端-合成活动管理
// @Param data query define.GetSynthesisWebOrderDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisWebOrderDetailResp}
// @Router  /web/v1/synthesis/order/detail [get]
// @Security Bearer
func GetSynthesisWebOrderDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisWebOrderDetailReq{}, s.GetSynthesisWebOrderDetail)
}

// GetSynthesisWebOrderStatus
// @Summary 获取合成订单状态
// @Description 获取合成订单状态
// @Tags 用户端-合成活动管理
// @Param data query define.GetSynthesisWebOrderStatusReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetSynthesisWebOrderStatusResp}
// @Router  /web/v1/synthesis/order/status [get]
// @Security Bearer
func GetSynthesisWebOrderStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSynthesisWebOrderStatusReq{}, s.GetSynthesisWebOrderStatus)
}
