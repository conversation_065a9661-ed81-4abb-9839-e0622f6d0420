package repo

import (
	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/platform/user/repo"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

func (r *SynthesisRepository) SynthesisStockSelectForUpdate(synthesisId int64) (int32, error) {
	var currentStock int32
	synthesisSchema := GetQuery().Synthesis
	err := r.do.Select(synthesisSchema.Stock).Where(synthesisSchema.ID.Eq(synthesisId)).Clauses(clause.Locking{Strength: "UPDATE"}).Scan(&currentStock)
	if err != nil {
		return currentStock, err
	}
	return currentStock, nil
}

func (r *SynthesisRepository) IncrBySynthesisStock(synthesisId int64, qty int32) error {
	tx := repo.GetDB().Model(&model.Synthesis{})
	result := tx.Where("id = ? AND is_del = 0 AND stock < total_stock", synthesisId).UpdateColumns(map[string]interface{}{
		"stock": gorm.Expr("stock + ?", qty),
	})
	if result.Error != nil {
		return result.Error
	} else if result.RowsAffected == 0 {
		return UpdateFail
	}
	return nil
}
