package web

import (
	"app_service/apps/business/synthesis/api/web"
	"github.com/gin-gonic/gin"
)

// Synthesis 合成管理端相关
func Synthesis(router *gin.RouterGroup) {
	group := router.Group("/synthesis")
	{
		// 获取合成活动规则
		group.GET("/rule", web.GetSynthesisRule)
		// 查询合成活动列表
		group.GET("/list", web.GetSynthesisWebList)
		// 查询合成活动详情
		group.GET("/detail", web.GetSynthesisWebDetail)
		// 发起合成
		group.POST("/launch", web.LaunchSynthesis)
		// 获取合成订单列表
		group.GET("/order/list", web.GetSynthesisWebOrderList)
		// 获取合成订单详情
		group.GET("/order/detail", web.GetSynthesisWebOrderDetail)
		// 获取合成订单状态
		group.GET("/order/status", web.GetSynthesisWebOrderStatus)
	}
}
