package open

import (
	"app_service/apps/business/synthesis/api/open"
	"github.com/gin-gonic/gin"
)

// Synthesis 合成open相关
func Synthesis(router *gin.RouterGroup) {
	group := router.Group("/synthesis")
	{
		// 获取优先购合成活动是否上架
		group.GET("/priority_buy_is_up", open.GetPriorityBuyIsUp)
		// 获取合成活动是否上架
		group.GET("/item_is_up", open.GetItemIsUp)
		// 获取优先购合成活动是否上架
		group.POST("/finish", open.SynthesisFinish)
		// 合成订单上链
		group.POST("/order/up_chain", open.SynthesisOrderUpChain)
		// 合成材料释放
		group.POST("/materials/release", open.SynthesisMaterialsRelease)
		// 获取合成材料释放时间
		group.POST("/materials/get_release_time", open.SynthesisMaterialsGetReleaseTime)
	}
}
