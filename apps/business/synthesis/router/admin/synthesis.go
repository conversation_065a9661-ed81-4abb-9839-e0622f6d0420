package admin

import (
	"app_service/apps/business/synthesis/api/admin"
	"github.com/gin-gonic/gin"
)

// Synthesis 合成管理端相关
func Synthesis(router *gin.RouterGroup) {
	group := router.Group("/synthesis")
	{
		// 获取合成管理列表
		group.GET("/list", admin.GetSynthesisList)
		// 获取合成管理列表
		group.GET("/detail", admin.GetSynthesisDetail)
		// 新增合成活动
		group.POST("/add", admin.AddSynthesis)
		// 编辑合成活动
		group.POST("/edit", admin.EditSynthesis)
		// 合成活动状态编辑
		group.POST("/edit_status", admin.EditSynthesisStatus)
		// 合成活动删除
		group.POST("/del", admin.DelSynthesis)

		// 合成活动操作日志列表
		group.GET("/log_list", admin.GetSynthesisLogList)
		// 获取合成活动规则
		group.GET("/rule", admin.GetSynthesisRule)
		// 编辑合成活动规则
		group.POST("/rule", admin.EditSynthesisRule)
		// 获取合成订单列表
		group.GET("/order/list", admin.GetSynthesisOrderList)
		// 获取合成订单详情
		group.GET("/order/detail", admin.GetSynthesisOrderDetail)
		// 获取合成订单详情列表
		group.GET("/order_detail/list", admin.GetSynthesisOrderDetailList)
		// 导出合成订单列表
		group.GET("/order/export", admin.ExportSynthesisOrderList)

		// 查询优先购列表
		group.GET("/priority_buy/list", admin.GetPriorityBuyList)
		// 查询融合商品奖品列表
		group.GET("/prize_item/list", admin.GetPrizeItemList)
	}
}
