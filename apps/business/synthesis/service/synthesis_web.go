package service

import (
	"app_service/apps/business/synthesis/service/locker"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"

	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	log "e.coding.net/g-dtay0385/common/go-logger"
)

// GetSynthesisRule 查询合成规则
func (s *Service) GetSynthesisRule() (*define.GetSynthesisRuleResp, error) {
	resp := &define.GetSynthesisRuleResp{}
	err := facade.GetObj(s.ctx, enums.SynthesisRuleConfigKey, &resp.Content)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisRuleAdmin err:%v", err)
		return nil, err
	}
	return resp, nil
}

// GetSynthesisWebList 查询合成活动列表
func (s *Service) GetSynthesisWebList(req *define.GetSynthesisWebListReq) (*define.GetSynthesisWebListResp, error) {
	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")

	resp := &define.GetSynthesisWebListResp{
		CurrentTime: formatNow,
	}
	if req.Page*req.PageSize > 100 {
		return resp, nil
	}
	synthesisSchema := repo.GetQuery().Synthesis
	statusList := []int32{enums.SynthesisStatusUp.Val(), enums.SynthesisStatusDown.Val(), enums.SynthesisStatusExpires.Val()}
	builder := search.NewQueryBuilder().In(synthesisSchema.Status, statusList)
	count, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Count(builder.Build())
	if err != nil {
		return nil, err
	}
	list, err := logic.GetSynthesisWebList(s.ctx, req)
	if err != nil {
		return nil, err
	}
	if len(list) == 0 {
		return resp, nil
	}
	dataList := make([]*define.GetSynthesisWebListData, 0)
	goodsItemIds := make([]string, 0)
	for _, v := range list {
		if v.ActivityType == enums.Goods.Val() && util.IsValidObjectID(v.ItemID) {
			goodsItemIds = append(goodsItemIds, v.ItemID)
		}
	}
	goodsItemMap, err := issueFacade.GetIssueItemMap(s.ctx, goodsItemIds)
	if err != nil {
		return nil, err
	}
	for _, v := range list {
		data := &define.GetSynthesisWebListData{
			ActivityCode: v.ActivityCode,
			Title:        v.Title,
			ActivityType: v.ActivityType,
			CoverUrl:     v.CoverURL,
			TotalStock:   v.TotalStock,
			StartTime:    v.StartTime,
			EndTime:      v.EndTime,
		}
		data.Status = logic.SynthesisStatusHandler(v)
		if _, ok := goodsItemMap[v.ItemID]; ok {
			data.IssuerName = goodsItemMap[v.ItemID].IssuerName
			data.IssuerShortName = goodsItemMap[v.ItemID].IssuerShortName
			data.SellerName = goodsItemMap[v.ItemID].SellerName
			data.SellerShortName = goodsItemMap[v.ItemID].SellerShortName
		}
		dataList = append(dataList, data)
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetSynthesisWebDetail 查询合成活动详情
func (s *Service) GetSynthesisWebDetail(req *define.GetSynthesisWebDetailReq) (*define.GetSynthesisWebDetailResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ActivityCode, req.ActivityCode).In(synthesisSchema.Status,
		[]int32{enums.SynthesisStatusUp.Val(), enums.SynthesisStatusDown.Val(), enums.SynthesisStatusExpires.Val()})
	getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	// 获取当前时间（本地时区）
	now := time.Now()
	formatNow := now.Format("2006-01-02T15:04:05.000Z07:00")
	resp := &define.GetSynthesisWebDetailResp{
		ActivityCode: getSynthesis.ActivityCode,
		Title:        getSynthesis.Title,
		ActivityType: getSynthesis.ActivityType,
		CoverUrl:     getSynthesis.CoverURL,
		ItemId:       getSynthesis.ItemID,
		ItemTitle:    getSynthesis.ItemTitle,
		ItemImageUrl: getSynthesis.ItemImageURL,
		UserLimit:    getSynthesis.UserLimit,
		TotalStock:   getSynthesis.TotalStock,
		StartTime:    getSynthesis.StartTime,
		EndTime:      getSynthesis.EndTime,
		ActivityDesc: getSynthesis.ActivityDesc,
		CurrentTime:  formatNow,
	}
	if getSynthesis.ActivityType == enums.Goods.Val() {
		resp.CoverUrl = getSynthesis.ItemImageURL
	}
	resp.Status = logic.SynthesisStatusHandler(getSynthesis)
	if getSynthesis.StockDisplay == enums.SynthesisStockDisplayed.Val() {
		resp.Stock = &getSynthesis.Stock
	}
	synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, getSynthesis.ID)
	synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(s.ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	itemIds := make([]string, 0)
	userSynthesisMaterialList := make([]*define.UserSynthesisMaterials, 0)
	for _, material := range synthesisMaterialsList {
		materialsData, err := logic.UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		userSynthesisMaterials := &define.UserSynthesisMaterials{
			Id:            material.ID,
			MaterialsType: material.MaterialsType,
			Qty:           material.LimitQty,
		}
		userSynthesisMaterialsDatas := make([]*define.UserSynthesisMaterialsData, 0)
		for _, materials := range materialsData {
			itemIds = append(itemIds, materials.ItemId)
			userSynthesisMaterialsData := &define.UserSynthesisMaterialsData{
				ItemId:       materials.ItemId,
				ItemTitle:    materials.ItemTitle,
				ItemImageUrl: materials.ItemImageUrl,
				Qty:          materials.Qty,
			}
			userSynthesisMaterialsDatas = append(userSynthesisMaterialsDatas, userSynthesisMaterialsData)
		}
		userSynthesisMaterials.UserSynthesisMaterialsDatas = userSynthesisMaterialsDatas
		userSynthesisMaterialList = append(userSynthesisMaterialList, userSynthesisMaterials)
	}
	err = logic.UserSynthesisMaterialsHold(s.ctx, s.GetUserId(), itemIds, userSynthesisMaterialList)
	if err != nil {
		return nil, err
	}
	logic.CirculationStatusHold(s.ctx, itemIds, userSynthesisMaterialList)
	resp.UserSynthesisMaterials = userSynthesisMaterialList
	resp.MaxLimit = logic.GetMaxLimit(s.ctx, synthesisMaterialsList)
	return resp, nil
}

// LaunchSynthesis 发起合成
func (s *Service) LaunchSynthesis(req *define.LaunchSynthesisReq) (*define.LaunchSynthesisResp, error) {
	// 初始化锁 用户不能同时发起合成
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewSynthesisLock(s.GetUserId(), locker.Launch)))
	if !l.Lock(s.ctx) {
		return nil, response.TooManyRequestErr
	}
	defer l.UnLock(s.ctx)
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ActivityCode, req.ActivityCode)
	getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	// 前置校验
	err = logic.VerifyLaunchSynthesis(s.ctx, getSynthesis, req.Qty, s.GetUserId())
	if err != nil {
		return nil, err
	}
	// 合成材料
	synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, getSynthesis.ID)
	synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(s.ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	if req.Qty > logic.GetMaxLimit(s.ctx, synthesisMaterialsList) {
		return nil, common_define.CommonWarnErr
	}
	// 根据合成活动获取选中材料的用户背包物品id
	userItemIdMap, err := logic.GetSynthesisMaterialUserItemIdMap(s.ctx, s.GetUserId(), synthesisMaterialsList, req)
	if err != nil {
		log.Ctx(s.ctx).Errorf("LaunchSynthesis GetSynthesisMaterialUserItemIds err:%v", err)
		return nil, err
	}
	log.Ctx(s.ctx).Infof("LaunchSynthesis GetSynthesisMaterialUserItemIds userId:%v, req:%v,userItemIds:%+v", s.GetUserId(), util.Obj2JsonStr(req), util.Obj2JsonStr(userItemIdMap))
	// 创建订单发起合成
	order, err := logic.SaveSynthesisOrder(s.ctx, &logic.SaveSynthesisOrderData{UserId: s.GetUserId(), Qty: req.Qty, Synthesis: getSynthesis, UserItemIdMap: userItemIdMap})
	if err != nil {
		log.Ctx(s.ctx).Errorf("LaunchSynthesis SaveSynthesisOrder err:%v", err)
		return nil, err
	}
	//发送Kafka
	fusion := &define.SynthesisFusion{SynthesisOrderID: order.ID}
	_ = kafka_util.SendMsg(s.ctx, constant.SynthesisFusion, fusion)
	resp := &define.LaunchSynthesisResp{
		ItemTitle:    getSynthesis.ItemTitle,
		ItemId:       getSynthesis.ItemID,
		ItemCoverUrl: getSynthesis.CoverURL,
		Qty:          order.Qty,
		OrderId:      order.OrderID,
	}
	if getSynthesis.ActivityType == enums.Goods.Val() {
		resp.ItemCoverUrl = getSynthesis.ItemImageURL
	}
	return resp, nil
}

// GetSynthesisWebOrderList 查询合成订单列表
func (s *Service) GetSynthesisWebOrderList(req *define.GetSynthesisWebOrderListReq) (*define.GetSynthesisWebOrderListResp, error) {
	do := repo.GetQuery().SynthesisOrder
	list, count, err := repo.NewSynthesisOrderRepo(do.WithContext(s.ctx)).SelectPage(
		search.NewQueryBuilder().Eq(do.UserID, s.GetUserId()).OrderByDesc(do.ID).Build(),
		req.GetPage(),
		req.GetPageSize(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisWebOrderList err:%v", err)
		return nil, err
	}
	resp := &define.GetSynthesisWebOrderListResp{
		List:  make([]*define.GetSynthesisOrderListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}
	sids := make([]int64, 6)
	for _, v := range list {
		sids = append(sids, v.SynthesisID)
	}
	activityList, err := logic.GetSynthesisMap(s.ctx, sids)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisWebOrderList err:%v", err)
		return nil, err
	}
	for _, v := range list {
		aType := int32(0)
		if activityList[v.SynthesisID] != nil {
			aType = activityList[v.SynthesisID].ActivityType
		}
		data := &define.GetSynthesisOrderListData{
			ActivityType: aType,
			CreatedAt:    v.CreatedAt,
			ItemCoverURL: v.ItemCoverURL,
			ItemTitle:    v.ItemTitle,
			OrderID:      v.OrderID,
			Qty:          v.Qty,
		}
		if aType == enums.Goods.Val() && activityList[v.SynthesisID] != nil {
			data.ItemCoverURL = activityList[v.SynthesisID].ItemImageURL
		}
		resp.List = append(resp.List, data)
	}
	return resp, nil
}

// GetSynthesisWebOrderDetail 查询合成订单详情
func (s *Service) GetSynthesisWebOrderDetail(req *define.GetSynthesisWebOrderDetailReq) (*define.GetSynthesisWebOrderDetailResp, error) {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().
		Eq(synthesisOrderSchema.OrderID, req.OrderID).
		Eq(synthesisOrderSchema.UserID, s.GetUserId())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}

	om, err := logic.GetSynthesisOrderDetailUQ(s.ctx, order.ID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}
	err = repo.GetDB().Model(order).Association("Synthesis").Find(&order.Synthesis)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisAdminOrderDetail err:%v", err)
		return nil, err
	}
	materials := make([]*define.GetSynthesisOrderDetailMaterials, 0)
	for _, item := range om {
		materials = append(materials, &define.GetSynthesisOrderDetailMaterials{
			ItemName: item.MaterialsItemName,
			ItemUrl:  item.MaterialsItemURL,
			Qty:      item.Qty,
		})
	}
	resp := &define.GetSynthesisWebOrderDetailResp{
		ActivityType: order.Synthesis.ActivityType,
		ChainHash:    order.ChainHash,
		CreatedAt:    order.CreatedAt,
		ItemCoverURL: order.ItemCoverURL,
		ItemTitle:    order.ItemTitle,
		Materials:    materials,
		OrderID:      order.OrderID,
		Qty:          order.Qty,
	}

	return resp, nil
}

// GetSynthesisWebOrderStatus 获取合成订单状态
func (s *Service) GetSynthesisWebOrderStatus(req *define.GetSynthesisWebOrderStatusReq) (*define.GetSynthesisWebOrderStatusResp, error) {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().
		Eq(synthesisOrderSchema.OrderID, req.OrderID).
		Eq(synthesisOrderSchema.UserID, s.GetUserId())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetSynthesisWebOrderStatus err:%v", err)
		return nil, err
	}
	resp := &define.GetSynthesisWebOrderStatusResp{
		ItemTitle:    order.ItemTitle,
		ItemId:       order.ItemID,
		ItemCoverUrl: order.ItemCoverURL,
		Qty:          order.Qty,
		Status:       order.Status,
	}
	return resp, nil
}
