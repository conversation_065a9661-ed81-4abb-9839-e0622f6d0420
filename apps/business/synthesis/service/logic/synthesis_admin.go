package logic

import (
	"app_service/apps/business/synthesis/define/enums"
	"context"
	"encoding/json"

	"app_service/apps/business/synthesis/define"
	"gorm.io/datatypes"

	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/repo"
	issueFacade "app_service/apps/platform/issue/facade"
)

// GetSynthesisMap 根据id获取合成活动
func GetSynthesisMap(ctx context.Context, ids []int64) (map[int64]*model.Synthesis, error) {
	s := repo.GetQuery().Synthesis
	list, err := s.WithContext(ctx).
		Where(s.ID.In(ids...)).Find()
	if err != nil {
		return nil, err
	}
	res := make(map[int64]*model.Synthesis)
	for _, item := range list {
		res[item.ID] = item
	}
	return res, nil
}

// UnmarshalMaterialsData 解析材料数据
func UnmarshalMaterialsData(materialsData datatypes.JSON) ([]*define.SynthesisMaterialsData, error) {
	var data []*define.SynthesisMaterialsData
	if err := json.Unmarshal(materialsData, &data); err != nil {
		return nil, err
	}
	return data, nil
}

// GetSynthesisTotalStock 查询合成活动上架的物品总库存(加上已使用的库存)
func GetSynthesisTotalStock(ctx context.Context, itemId string) (int32, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum( total_stock ), 0) AS sum").
		Table("`synthesis`").
		Where("status = ? and item_id = ? and is_del = 0", enums.SynthesisStatusUp.Val(), itemId)
	totalStock := int32(0)
	err := exec.Find(&totalStock).Error
	if err != nil {
		return 0, err
	}

	useExec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum( total_stock - stock ), 0) AS sum").
		Table("`synthesis`").
		Where("status != ? and item_id = ? and is_del = 0", enums.SynthesisStatusUp.Val(), itemId)
	totalUseStock := int32(0)
	err = useExec.Find(&totalUseStock).Error
	if err != nil {
		return 0, err
	}
	return totalStock + totalUseStock, err
}

// GetPriorityBuyUsableStock 获取优先购可用库存（优先购总库存-合成活动上架的优先购总库存）
func GetPriorityBuyUsableStock(ctx context.Context, priorityBuyId string) (int32, error) {
	stock, err := issueFacade.GetPriorityBuyStock(ctx, priorityBuyId)
	if err != nil {
		return 0, err
	}
	totalStock, err := GetSynthesisTotalStock(ctx, priorityBuyId)
	if err != nil {
		return 0, err
	}
	return stock - totalStock, nil
}

// GetItemUsableStock 获取商品可用库存（商品融合总库存-合成活动上架的总库存）
func GetItemUsableStock(ctx context.Context, itemId string) (int32, error) {
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	stock := issueItem.Synthesis.Stock
	if err != nil {
		return 0, err
	}
	if stock == nil {
		stock = new(int32)
	}
	totalStock, err := GetSynthesisTotalStock(ctx, itemId)
	if err != nil {
		return 0, err
	}
	result := *stock - totalStock
	if result < 0 {
		result = 0
	}
	return result, nil
}

// GetSynthesisItemImageUrl 获取合成活动合成物品图片
func GetSynthesisItemImageUrl(ctx context.Context, activityType int32, itemId string) (string, error) {
	if activityType == enums.Rights.Val() {
		priorityBuy, err := issueFacade.GetPriorityBuy(ctx, itemId)
		if err != nil {
			return "", err
		}
		itemId = priorityBuy.ItemID.Hex()
	}
	item, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	if err != nil {
		return "", err
	}
	return item.ImageURL, nil
}
