package logic

import (
	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	commonFacade "app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"time"
)

// SynthesisOrderUpChain 合成订单上链
func SynthesisOrderUpChain(ctx context.Context, req *define.SynthesisOrderUpChainReq) (*define.SynthesisOrderUpChainResp, error) {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder()
	if req.OrderId != "" {
		builder.Eq(synthesisOrderSchema.OrderID, req.OrderId)
	}
	statusList := []int32{enums.SynthesisChainStatusDisable.Val(), enums.SynthesisChainStatusWaiting.Val()}
	builder = builder.In(synthesisOrderSchema.ChainStatus, statusList).Eq(synthesisOrderSchema.Status, enums.SynthesisOrderStatusDone.Val())
	pageSize := 50
	page := 1

	for {
		list, _, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectPage(builder.Build(), page, pageSize)
		if err != nil {
			log.Ctx(ctx).Errorf("Failed to get synthesis admin order list: %v", err)
			return nil, err
		}

		if len(list) == 0 {
			break
		}

		for _, item := range list {
			if err := processSynthesisOrder(ctx, item); err != nil {
				log.Ctx(ctx).Errorf("Failed to process synthesis order %v: %v", item.OrderID, err)
			}
		}

		if len(list) < pageSize {
			break
		}

		time.Sleep(1000 * time.Millisecond)
		page++
	}
	return &define.SynthesisOrderUpChainResp{}, nil
}

func processSynthesisOrder(ctx context.Context, order *model.SynthesisOrder) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	statusList := []int32{enums.SynthesisChainStatusDisable.Val(), enums.SynthesisChainStatusWaiting.Val()}
	// 更新订单状态为等待上链
	updateOrder := &model.SynthesisOrder{ChainStatus: enums.SynthesisChainStatusWaiting.Val()}
	if err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).Update(updateOrder,
		search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, order.ID).In(synthesisOrderSchema.ChainStatus, statusList).Build()); err != nil {
		if err != repo.UpdateFail {
			return fmt.Errorf("failed to update order status to waiting: %v", err)
		}
	}
	synthesisSchema := repo.GetQuery().Synthesis
	synthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).SelectOne(search.NewQueryBuilder().Eq(synthesisSchema.ID, order.SynthesisID).Build())
	if err != nil {
		return err
	}
	userInfo, err := userFacade.GetNodeUser(ctx, order.UserID)
	if err != nil {
		log.Ctx(ctx).Errorf("GetSynthesisAdminOrderDetail GetNodeUser err:%v", err)
	}
	// 上链请求
	req := &tmt.SynthesisOrderUpChainReq{
		OrderId:     order.OrderID,
		OrderType:   1,
		ChainDataId: order.ChainDataID,
	}
	if synthesis.ActivityType == enums.Rights.Val() {
		priorityBuy, err := issueFacade.GetPriorityBuy(ctx, synthesis.ItemID)
		if err != nil {
			return err
		}

		req.ItemName = ""
		req.ItemId = ""
		req.SkuNo = ""
		req.ItemChainHash = ""
		req.BenefitName = priorityBuy.Name
		req.BenefitID = synthesis.ItemID
		req.Price = 0
		req.Quantity = order.Qty
		req.TotalAmount = 0
	}
	req.PayAmount = 0
	req.TradeType = 4
	req.Fee = 0
	req.FromUserName = ""
	req.FromUserId = ""
	req.FromItemHash = ""
	req.ToUserName = userInfo.PatbgDetail.Nickname
	req.ToUserId = order.UserID
	req.CreatedAt = time.Unix(order.CreatedAt.Unix(), 0).UTC().Format("2006-01-02 15:04:05")
	req.PaidAt = ""
	if order.SynthesisTime != nil && !order.SynthesisTime.IsZero() {
		req.FinishedAt = time.Unix(order.SynthesisTime.Unix(), 0).UTC().Format("2006-01-02 15:04:05")
	} else {
		req.FinishedAt = time.Now().UTC().Format("2006-01-02 15:04:05")
	}
	req.CancelApplyAt = ""
	req.CancelDoneAt = ""

	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	builder := search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, order.ID)
	orderDetailList, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		return err
	}
	var itemIds []string
	itemIds2Qty := make(map[string]int32)
	for _, item := range orderDetailList {
		itemIds = append(itemIds, item.MaterialsItemID)
		itemIds2Qty[item.MaterialsItemID]++

	}
	issueItems, err := issueFacade.GetIssueItemMap(ctx, itemIds)
	if err != nil {
		log.Ctx(ctx).Errorf("processSynthesisOrder GetIssueItemMap err:%v", err)
		return nil
	}
	var fusionMaterials string
	for _, item := range orderDetailList {
		if issueItem, ok := issueItems[item.MaterialsItemID]; ok {
			fusionMaterials += fmt.Sprintf("%s-%s-%d;", issueItem.ID.Hex(), item.MaterialsItemName, itemIds2Qty[item.MaterialsItemID])
		}
	}
	req.FusionMaterial = "{" + fusionMaterials + "}"

	resp, err := tmt.SynthesisOrderUpChain(ctx, req)
	if err != nil {
		log.Ctx(ctx).Errorf("processSynthesisOrder SynthesisOrderUpChain req:%+v, err:%v", req, err)
	}

	switch resp.Status {
	case tmt.SynthesisOrderUpChainSuccess:
		log.Ctx(ctx).Infof("SynthesisOrderUpChain Success, OrderId:%v", order.OrderID)
		// 更新订单状态为完成
		updateOrder = &model.SynthesisOrder{
			ID:          order.ID,
			ChainHash:   resp.ChainHash,
			ChainDataID: resp.ChainDataId,
		}
		if resp.ChainHash != "" {
			updateOrder.ChainStatus = enums.SynthesisChainStatusDone.Val()
		}
		if err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateOrder); err != nil {
			return fmt.Errorf("failed to update order status to done: %v", err)
		}
	case tmt.SynthesisOrderUpChainFail:
		log.Ctx(ctx).Errorf("SynthesisOrderUpChain Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 更新订单状态为失败
		updateOrder = &model.SynthesisOrder{
			ID:          order.ID,
			ChainStatus: enums.SynthesisChainStatusFailed.Val(),
		}
		if err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateOrder); err != nil {
			return fmt.Errorf("failed to update order status to failed: %v", err)
		}
	default:
		log.Ctx(ctx).Errorf("SynthesisOrderUpChain Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
	}

	return nil
}

// SynthesisMaterialsRelease 合成材料释放
func SynthesisMaterialsRelease(ctx context.Context, req *define.SynthesisMaterialsReleaseReq) (*define.SynthesisMaterialsReleaseResp, error) {
	synthesisMaterialsReleaseSchema := repo.GetQuery().SynthesisMaterialsRelease

	builder := search.NewQueryBuilder()
	if req.Id != 0 {
		builder.Eq(synthesisMaterialsReleaseSchema.ID, req.Id)
	}
	statusList := []int32{enums.ReleaseCreated.Val(), enums.ReleaseIng.Val()}
	builder = builder.In(synthesisMaterialsReleaseSchema.Status, statusList)
	builder = builder.Lt(synthesisMaterialsReleaseSchema.ReleaseTime, time.Now())
	pageSize := 1000
	page := 1

	for {
		list, count, err := repo.NewSynthesisMaterialsReleaseRepo(synthesisMaterialsReleaseSchema.WithContext(ctx)).SelectPage(builder.Build(), page, pageSize)
		if err != nil {
			log.Ctx(ctx).Errorf("Failed to get synthesisMaterialsRelease list: %v", err)
			return nil, err
		}

		if count == 0 {
			break
		}

		if err = processSynthesisMaterialsRelease(ctx, list); err != nil {
			log.Ctx(ctx).Errorf("Failed to process synthesisMaterialsRelease page:%v, err:%v", page, err)
		}

		if len(list) < pageSize {
			break
		}

		time.Sleep(1000 * time.Millisecond)
	}
	return nil, nil
}

func processSynthesisMaterialsRelease(ctx context.Context, materialsReleases []*model.SynthesisMaterialsRelease) error {
	var userItemIds []string
	var ids []int64
	for _, item := range materialsReleases {
		userItemIds = append(userItemIds, item.UserItemID)
		ids = append(ids, item.ID)
	}
	fusionRes, err := yc_open.CancelFusion(ctx, "", "", userItemIds)
	if fusionRes == yc_open.CancelFusionUnknown {
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("processSynthesisMaterialsRelease Unknown Exception, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		commonFacade.SendDefaultWarnMsg(ctx, "【合成材料，释放失败，未知异常】", fmt.Sprintf("userItemIds: %+v, 错误信息: %v", util.Obj2JsonStr(userItemIds), err))
	} else {
		updateStatus := enums.ReleaseSuccess.Val()
		if fusionRes == yc_open.CancelFusionSuccess {
			// 明确成功
			log.Ctx(ctx).Infof("processSynthesisMaterialsRelease Success, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		} else if fusionRes == yc_open.CancelFusionFail {
			updateStatus = enums.ReleaseFail.Val()
			// 明确失败
			log.Ctx(ctx).Infof("processSynthesisMaterialsRelease Fail, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 发起告警【合成材料，释放失败，明确失败】
			commonFacade.SendDefaultWarnMsg(ctx, "【合成材料，释放失败，明确失败】", fmt.Sprintf("userItemIds: %+v, 错误信息: %v", util.Obj2JsonStr(userItemIds), err))
		}
		now := time.Now()
		updateSynthesisMaterialsRelease := &model.SynthesisMaterialsRelease{
			Status:          updateStatus,
			RealReleaseTime: &now,
		}
		statusList := []int32{enums.ReleaseCreated.Val(), enums.ReleaseIng.Val()}
		synthesisMaterialsReleaseSchema := repo.GetQuery().SynthesisMaterialsRelease
		err = repo.NewSynthesisMaterialsReleaseRepo(synthesisMaterialsReleaseSchema.WithContext(ctx)).Update(updateSynthesisMaterialsRelease,
			search.NewQueryBuilder().In(synthesisMaterialsReleaseSchema.ID, ids).In(synthesisMaterialsReleaseSchema.Status, statusList).Build())
		if err != nil && err != repo.UpdateFail {
			log.Ctx(ctx).Errorf("processSynthesisMaterialsRelease Success, Update Status err:%v, id:%v", err, util.Obj2JsonStr(ids))
			return err
		}
		return nil
	}
	return nil
}
