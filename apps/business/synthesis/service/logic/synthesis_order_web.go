package logic

import (
	"app_service/apps/business/story/service/logic"
	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/apps/platform/user/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/kafka_util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"gorm.io/datatypes"
	"gorm.io/gen/field"
	"gorm.io/gorm"
	"time"
)

type SynthesisOrderDetailUQData struct {
	MaterialsItemID   string
	MaterialsItemName string
	MaterialsItemURL  string
	Qty               int32
}

type SaveSynthesisOrderData struct {
	UserId        string
	Qty           int32
	Synthesis     *model.Synthesis
	UserItemIdMap map[string][]string
}

type MaterialsItemInfo struct {
	Ip         string `json:"ip"`
	Issuer     string `json:"issuer"`
	IssuePrice int32  `json:"issue_price"`
	Code       string `json:"code"`
}

// GetSynthesisOrderDetailUQ 根据合成订单ID获取订单合成材料列表，根据物品id去重
func GetSynthesisOrderDetailUQ(ctx context.Context, id int64) ([]SynthesisOrderDetailUQData, error) {
	data := make([]SynthesisOrderDetailUQData, 0)
	s := repo.GetQuery().SynthesisOrderDetail
	gq := s.WithContext(ctx).
		Select(s.ID.Count().As("qty"), s.ID.Min().As("id"), s.MaterialsItemID).
		Where(s.SynthesisOrderID.Eq(id)).
		Group(s.SynthesisOrderID, s.MaterialsItemID)
	err := s.WithContext(ctx).
		Select(s.ID, s.MaterialsItemName, s.MaterialsItemURL, field.NewString("gquery", "qty")).
		Join(gq.As("gquery"), s.ID.EqCol(field.NewString("gquery", "id"))).
		Scan(&data)
	if err != nil {
		return nil, err
	}

	return data, nil
}

// GetSynthesisOrderUserNum 查询合成数量
func GetSynthesisOrderUserNum(ctx context.Context, synthesisId int64, userId string) (int32, error) {
	statusList := []int32{enums.SynthesisOrderStatusDone.Val(), enums.SynthesisOrderStatusWaiting.Val(), enums.SynthesisOrderStatusSuccess.Val()}
	exec := repo.GetDB().WithContext(ctx).
		Select("IFNULL(sum(qty), 0) AS num").
		Table("`synthesis_order`").
		Where("synthesis_id = ? and user_id = ? and status in ? and is_del = 0", synthesisId, userId, statusList)
	totalNum := int32(0)
	err := exec.Find(&totalNum).Error
	if err != nil {
		return 0, err
	}
	return totalNum, err
}

// SaveSynthesisOrder 保存合成订单
func SaveSynthesisOrder(ctx context.Context, data *SaveSynthesisOrderData) (*model.SynthesisOrder, error) {
	userId := data.UserId
	qty := data.Qty
	synthesis := data.Synthesis
	userItemIdMap := data.UserItemIdMap
	order := &model.SynthesisOrder{
		OrderID:        util.StrVal(snowflakeutl.GenerateID()),
		ActivityType:   synthesis.ActivityType,
		SynthesisID:    synthesis.ID,
		SynthesisTitle: synthesis.Title,
		ItemID:         synthesis.ItemID,
		ItemTitle:      synthesis.ItemTitle,
		ItemCoverURL:   synthesis.ItemImageURL,
		UserID:         userId,
		Status:         enums.SynthesisOrderStatusWaiting.Val(),
		Qty:            qty,
		AppChannel:     fmt.Sprintf("%v", ctx.Value(constant.AppChannel)),
		AppVersion:     fmt.Sprintf("%v", ctx.Value(constant.AppVersion)),
		IP:             fmt.Sprintf("%v", ctx.Value(constant.Ip)),
	}
	json, err := GetItemInfoJson(ctx, synthesis)
	if err != nil {
		return nil, err
	}
	order.ItemInfo = json
	orderDetailList, err := buildSynthesisOrderDetailList(ctx, userItemIdMap, synthesis.ID)
	if err != nil {
		return nil, err
	}
	if synthesis.ActivityType == enums.Goods.Val() {
		// 商品，扣减总库存
		stockRes, _ := SynthesisStock(ctx, synthesis.ItemID, qty)
		if stockRes != tmt.SynthesisStockSuccess {
			return nil, define.SH1000019Err
		}
	}
	err = repo.ExecGenTx(ctx, func(tx context.Context) error {
		synthesisSchema := repo.GetQuery().Synthesis
		// 1. 查询库存并加锁（SELECT FOR UPDATE）
		currentStock, err := repo.NewSynthesisRepo(repo.Query(tx).Synthesis.WithContext(tx)).SynthesisStockSelectForUpdate(synthesis.ID)
		if err != nil {
			return err
		}

		// 2. 检查库存是否足够
		if currentStock < qty {
			return define.SH1000015Err // 库存不足
		}
		// 扣合成库存
		updateParams := map[string]interface{}{
			"stock": gorm.Expr("stock - ?", qty),
		}
		err = repo.NewSynthesisRepo(repo.Query(tx).Synthesis.WithContext(tx)).UpdateField(updateParams, search.NewQueryBuilder().Eq(synthesisSchema.ID, synthesis.ID).
			Gte(synthesisSchema.Stock, qty).Build())
		if err != nil {
			return define.SH1000015Err
		}
		err = repo.NewSynthesisOrderRepo(repo.Query(tx).SynthesisOrder.WithContext(tx)).Save(order)
		if err != nil {
			return err
		}
		for _, detail := range orderDetailList {
			detail.SynthesisOrderID = order.ID
		}
		err = repo.NewSynthesisOrderDetailRepo(repo.Query(tx).SynthesisOrderDetail.WithContext(tx)).BatchSave(orderDetailList, 100)
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		if synthesis.ActivityType == enums.Goods.Val() {
			// 扣减失败，恢复库存
			synthesisRes, _ := SynthesisStock(ctx, synthesis.ItemID, -qty)
			if synthesisRes != tmt.SynthesisStockSuccess {
				log.Ctx(ctx).Infof("恢复库存失败, itemId: %+v, qty: %+v", synthesis.ItemID, qty)
			}
		}
	}
	return order, err
}

func GetItemInfoJson(ctx context.Context, synthesis *model.Synthesis) (datatypes.JSON, error) {
	itemId := synthesis.ItemID
	if synthesis.ActivityType == enums.Rights.Val() {
		priorityBuy, err := issueFacade.GetPriorityBuy(ctx, synthesis.ItemID)
		if err != nil {
			return nil, err
		}
		itemId = priorityBuy.ItemID.Hex()
	}
	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, itemId)
	if err != nil {
		return nil, err
	}
	materialsItemInfo := &MaterialsItemInfo{
		Issuer:     issueItem.IssuerName,
		IssuePrice: issueItem.Price,
		Code:       issueItem.ID.Hex(),
	}
	var jsonData datatypes.JSON
	if len(issueItem.IPClassifyNames) > 0 {
		materialsItemInfo.Ip = issueItem.IPClassifyNames[0]
	}
	jsonData = datatypes.JSON(util.Obj2JsonStr(materialsItemInfo))
	return jsonData, err
}

func buildSynthesisOrderDetailList(ctx context.Context, userItemIdMap map[string][]string, synthesisId int64) ([]*model.SynthesisOrderDetail, error) {
	goodsItemIds := make([]string, 0)
	for itemId, _ := range userItemIdMap {
		goodsItemIds = append(goodsItemIds, itemId)
	}
	goodsItemMap, err := issueFacade.GetIssueItemMap(ctx, goodsItemIds)
	if err != nil {
		return nil, err
	}
	itemDestroyMap, err := GetItemMaterialsDestroyMap(ctx, synthesisId)
	if err != nil {
		return nil, err
	}
	orderDetailList := make([]*model.SynthesisOrderDetail, 0)
	for itemId, userItemIds := range userItemIdMap {
		// 根据itemId获取物品信息
		var materialsItemID string
		var materialsItemName string
		var materialsItemURL string
		var jsonData datatypes.JSON
		if _, ok := goodsItemMap[itemId]; ok {
			issueItem := goodsItemMap[itemId]
			materialsItemID = itemId
			materialsItemName = issueItem.ItemName
			materialsItemURL = issueItem.ImageURL
			materialsItemInfo := &MaterialsItemInfo{
				Issuer:     issueItem.IssuerName,
				IssuePrice: issueItem.Price,
				Code:       issueItem.ID.Hex(),
			}
			if len(issueItem.IPClassifyNames) > 0 {
				materialsItemInfo.Ip = issueItem.IPClassifyNames[0]
			}
			jsonData = datatypes.JSON(util.Obj2JsonStr(materialsItemInfo))
		} else {
			return nil, define.SH1000014Err
		}
		for _, userItemId := range userItemIds {
			orderDetail := &model.SynthesisOrderDetail{
				MaterialsItemID:   materialsItemID,
				MaterialsItemName: materialsItemName,
				MaterialsItemURL:  materialsItemURL,
				MaterialsItemInfo: &jsonData,
				UserItemID:        userItemId,
			}
			if itemDestroyMap != nil && itemDestroyMap[itemId] {
				orderDetail.IsDestroy = constant.Yes
			} else {
				orderDetail.IsDestroy = constant.No
			}
			orderDetailList = append(orderDetailList, orderDetail)
		}
	}
	return orderDetailList, nil
}

// SynthesisFusion 合成融合调用yc
func SynthesisFusion(ctx context.Context, synthesisOrderID int64) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, synthesisOrderID).Eq(synthesisOrderSchema.Status, enums.SynthesisOrderStatusWaiting.Val())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, order.ID)
	orderDetail, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	var userItemIds = make([]string, 0)
	var synthesisDestroyUserItemIds = make([]string, 0)
	for _, detail := range orderDetail {
		userItemIds = append(userItemIds, detail.UserItemID)
		if detail.IsDestroy == constant.Yes {
			synthesisDestroyUserItemIds = append(synthesisDestroyUserItemIds, detail.UserItemID)
		}
	}
	log.Ctx(ctx).Infof("SynthesisFusion openUserId:%+v, orderId:%+v, userItemIds:%+v", openUserId, order.OrderID, util.Obj2JsonStr(userItemIds))
	fusionRes, err := yc_open.Fusion(ctx, openUserId, order.OrderID, userItemIds, synthesisDestroyUserItemIds)
	var updateStatus int32
	switch fusionRes {
	case yc_open.FusionSuccess:
		log.Ctx(ctx).Infof("SynthesisFusion Fusion Success, OrderId:%v", order.OrderID)
		updateStatus = enums.SynthesisOrderStatusSuccess.Val()
	case yc_open.FusionFail:
		log.Ctx(ctx).Errorf("SynthesisFusion Fusion Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		updateStatus = enums.SynthesisOrderStatusFail.Val()
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("SynthesisFusion Fusion Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 告警 材料消耗，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【合成消耗材料未知异常】", fmt.Sprintf("合成订单Id: %+v, 错误信息: %v", synthesisOrderID, err))
		return common_define.CommonErr
	}
	// 材料消耗成功,订单状态变更
	updateSynthesisOrder := &model.SynthesisOrder{ID: synthesisOrderID, Status: updateStatus}
	err = repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateSynthesisOrder)
	if err != nil {
		log.Ctx(ctx).Errorf("SynthesisFusion Fusion Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
		return err
	}
	if updateStatus == enums.SynthesisOrderStatusSuccess.Val() {
		//材料消耗成功
		if order.ActivityType == enums.Rights.Val() {
			// 发送Kafka(发放优先购权益)
			fusion := &define.PriorityBuyIssue{SynthesisOrderID: order.ID}
			_ = kafka_util.SendMsg(ctx, constant.PriorityBuyIssue, fusion)
		} else {
			// 发送Kafka(发放实物)
			fusion := &define.SynthesisItemIssue{SynthesisOrderID: order.ID}
			_ = kafka_util.SendMsg(ctx, constant.SynthesisItemIssue, fusion)
		}
	} else if updateStatus == enums.SynthesisOrderStatusFail.Val() {
		// 退库存
		err = IncrBySynthesisStock(ctx, order)
		if err != nil {
			log.Ctx(ctx).Errorf("SynthesisFusion Fusion Fail, IncrBySynthesisStock err:%v, OrderId:%v", err, order.OrderID)
		}
	}
	return nil
}

// SynthesisCancelFusion 取消物品融合调用yc
func SynthesisCancelFusion(ctx context.Context, synthesisOrderID int64) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, synthesisOrderID).Eq(synthesisOrderSchema.Status, enums.SynthesisOrderStatusSuccess.Val())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, order.ID)
	orderDetail, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	var userItemIds = make([]string, 0)
	for _, detail := range orderDetail {
		userItemIds = append(userItemIds, detail.UserItemID)
	}
	fusionRes, err := yc_open.CancelFusion(ctx, openUserId, order.OrderID, userItemIds)
	if fusionRes == yc_open.CancelFusionUnknown {
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("SynthesisCancelFusion CancelFusion Unknown Exception, userItemIds:%v", util.Obj2JsonStr(userItemIds))
		commonFacade.SendDefaultWarnMsg(ctx, "【合成物品发放失败，材料已消耗，退回失败，未知异常】", fmt.Sprintf("合成订单Id: %+v, 错误信息: %v", synthesisOrderID, err))
		return common_define.CommonErr
	} else {
		if fusionRes == yc_open.CancelFusionSuccess {
			// 明确成功
			log.Ctx(ctx).Infof("SynthesisCancelFusion CancelFusion Success, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 退库存
			err = IncrBySynthesisStock(ctx, order)
			if err != nil {
				log.Ctx(ctx).Errorf("SynthesisCancelFusion Fail, IncrBySynthesisStock err:%v, OrderId:%v", err, order.OrderID)
			}
		} else if fusionRes == yc_open.CancelFusionFail {
			// 明确失败
			log.Ctx(ctx).Infof("SynthesisCancelFusion CancelFusion Fail, userItemIds:%v", util.Obj2JsonStr(userItemIds))
			// 发起告警【合成物品发放失败，材料已消耗，退回失败】
			commonFacade.SendDefaultWarnMsg(ctx, "【合成物品发放失败，材料已消耗，材料退回失败，明确失败】", fmt.Sprintf("合成订单Id: %+v, 错误信息: %v", synthesisOrderID, err))
		}
		// 不管材料退回成功还是失败,订单状态都变成融合失败
		updateSynthesisOrder := &model.SynthesisOrder{ID: synthesisOrderID, Status: enums.SynthesisOrderStatusFail.Val()}
		err = repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateSynthesisOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("SynthesisCancelFusion CancelFusion Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
			return err
		}
		return nil
	}
}

// PriorityBuyIssue 优先权益发放调用tmt
func PriorityBuyIssue(ctx context.Context, synthesisOrderID int64) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, synthesisOrderID).Eq(synthesisOrderSchema.Status, enums.SynthesisOrderStatusSuccess.Val())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	priorityBuyIssueRes, err := tmt.PriorityBuyIssue(ctx, &tmt.PriorityBuyIssueReq{
		PriorityBuyId: order.ItemID,
		UserId:        order.UserID,
		Quantity:      order.Qty,
		IssueType:     2,
		SourceOrderId: order.OrderID,
	})
	switch priorityBuyIssueRes.Status {
	case tmt.PriorityBuyIssueSuccess:
		log.Ctx(ctx).Infof("PriorityBuyIssue Success, OrderId:%v", order.OrderID)
		// 材料消耗成功,订单状态变更
		now := time.Now()
		updateSynthesisOrder := &model.SynthesisOrder{ID: synthesisOrderID, Status: enums.SynthesisOrderStatusDone.Val(), SynthesisTime: &now}
		err = repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateSynthesisOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("PriorityBuyIssue Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
			return err
		}
		// 新增已合人数
		err = SynthesisCompleteUserNum(ctx, order.SynthesisID)
		if err != nil {
			log.Ctx(ctx).Errorf("PriorityBuyIssue Success, SynthesisCompleteUserNum err:%v, OrderId:%v", err, order.OrderID)
		}
		// 新增合成材料退回
		err = SaveSynthesisMaterialsRelease(ctx, order.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("PriorityBuyIssue Success, SaveSynthesisMaterialsRelease err:%v, OrderId:%v", err, order.OrderID)
		}
		return nil
	case tmt.PriorityBuyIssueFail:
		log.Ctx(ctx).Errorf("PriorityBuyIssue Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 明确失败,材料退回 发送Kafka
		fusion := &define.SynthesisCancelFusion{SynthesisOrderID: order.ID}
		_ = kafka_util.SendMsg(ctx, constant.SynthesisCancelFusion, fusion)
		return nil
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("PriorityBuyIssue Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 告警 材料消耗，未发放成功，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【合成物品发放失败，材料已消耗，退回失败，未知异常】", fmt.Sprintf("合成订单Id: %+v, 错误信息: %v", synthesisOrderID, err))
		return common_define.CommonErr
	}
}

// SynthesisItemIssue 奖励发放调用yc
func SynthesisItemIssue(ctx context.Context, synthesisOrderID int64) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	builder := search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, synthesisOrderID).Eq(synthesisOrderSchema.Status, enums.SynthesisOrderStatusSuccess.Val())
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	openUserId, err := facade.GetOpenUserId(ctx, order.UserID)
	if openUserId == "" {
		return common_define.CommonErr
	}
	items, err := GetBatchInsertItems(ctx, order, openUserId)
	if err != nil {
		return err
	}
	form := &yc_open.BatchInsertUserItemReq{
		OpenUserId:    openUserId,
		ReceiveType:   126,
		Items:         items,
		SourceOrderId: order.OrderID,
	}

	res, _ := yc_open.BatchInsertUserItem(ctx, form)
	switch res {
	case yc_open.BatchInsertUserItemSuccess:
		log.Ctx(ctx).Infof("BatchInsertUserItem Success, OrderId:%v", order.OrderID)
		// 材料消耗成功,订单状态变更
		updateSynthesisOrder := &model.SynthesisOrder{ID: synthesisOrderID, Status: enums.SynthesisOrderStatusDone.Val()}
		err = repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).UpdateById(updateSynthesisOrder)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, Update Status err:%v, OrderId:%v", err, order.OrderID)
			return err
		}
		// 新增已合人数
		err = SynthesisCompleteUserNum(ctx, order.SynthesisID)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, SynthesisCompleteUserNum err:%v, OrderId:%v", err, order.OrderID)
		}
		// 新增故事玩法材料退回
		err = SaveSynthesisMaterialsRelease(ctx, order.ID)
		if err != nil {
			log.Ctx(ctx).Errorf("BatchInsertUserItem Success, SaveSynthesisMaterialsRelease err:%v, OrderId:%v", err, order.OrderID)
		}
		return nil
	case yc_open.BatchInsertUserItemFail:
		log.Ctx(ctx).Errorf("BatchInsertUserItem Fail, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 明确失败,材料退回 发送Kafka
		fusion := &define.SynthesisCancelFusion{SynthesisOrderID: order.ID}
		_ = kafka_util.SendMsg(ctx, constant.SynthesisCancelFusion, fusion)
		return nil
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("BatchInsertUserItem Unknown Exception, OrderId:%v, 错误信息: %v", order.OrderID, err)
		// 告警 材料消耗，未发放成功，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【融合玩法物品发放失败，材料已消耗，退回失败，未知异常】", fmt.Sprintf("融合玩法订单Id: %+v, 错误信息: %v", synthesisOrderID, err))
		return common_define.CommonErr
	}
}

func GetBatchInsertItems(ctx context.Context, order *model.SynthesisOrder, openUserId string) ([]*yc_open.BatchInsertItem, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	synthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).SelectOne(
		search.NewQueryBuilder().Eq(synthesisSchema.ID, order.SynthesisID).Build(),
	)
	if err != nil {
		return nil, err
	}

	issueItem, err := issueFacade.GetIssueItemByItemID(ctx, synthesis.ItemID)
	if err != nil {
		return nil, err
	}

	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	orderDetailList, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(ctx)).SelectList(
		search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, order.ID).Build(),
	)
	if err != nil {
		return nil, err
	}

	var needUserItemIds []string
	for _, detail := range orderDetailList {
		if detail.IsDestroy == constant.Yes {
			needUserItemIds = append(needUserItemIds, detail.UserItemID)
		}
	}

	var totalCost int64
	if len(needUserItemIds) > 0 {
		totalCost, err = yc_open.SumUserItemsCostPrice(ctx, openUserId, needUserItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("SumUserItemsCostPrice failed: %v", err)
			return nil, fmt.Errorf("failed to sum user item cost price: %w", err)
		}
	}

	qty := int(order.Qty)
	if qty <= 0 {
		return nil, common_define.CommonWarnErr
	}

	// 平均分配成本
	costPerItem := totalCost / int64(qty)
	remainder := totalCost % int64(qty)

	items := make([]*yc_open.BatchInsertItem, 0, qty)
	for i := 0; i < qty; i++ {
		item := &yc_open.BatchInsertItem{
			ItemId:     synthesis.ItemID,
			ItemNum:    1,
			SaleMode:   int32(issueItem.SaleMode),
			StoryTags:  int32(issueItem.Synthesis.Status),
			FusionTags: int32(issueItem.Synthesis.Status),
		}

		if issueItem.DeliveryTime != nil {
			item.DeliveryTime = issueItem.DeliveryTime
		}

		// 前 n-1 个使用平均值，最后一个加上余数
		if i < qty-1 {
			item.BuyPrice = costPerItem
		} else {
			item.BuyPrice = costPerItem + remainder
		}

		items = append(items, item)
	}

	return items, nil
}

// GetItemMaterialsDestroyMap 获取物品材料是否销毁
func GetItemMaterialsDestroyMap(ctx context.Context, synthesisId int64) (map[string]bool, error) {
	synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, synthesisId)
	synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	data := make(map[string]bool, 0)
	for _, material := range synthesisMaterialsList {
		materialsData, err := logic.UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, materials := range materialsData {
			data[materials.ItemId] = materials.Destroy == constant.Yes
		}
	}
	return data, nil
}

// SynthesisStock 物品库存扣减
func SynthesisStock(ctx context.Context, itemId string, quantity int32) (int32, error) {
	res, _ := tmt.SynthesisStock(ctx, &tmt.SynthesisStockReq{ItemId: itemId, Quantity: quantity})
	actionDesc := "【融合物品库存扣减"
	if quantity < 0 {
		actionDesc = "库存增加"
	} else {
		actionDesc = "库存扣减"
	}
	switch res.Status {
	case tmt.SynthesisStockSuccess:
		log.Ctx(ctx).Infof("SynthesisStock Success, itemId:%v, quantity:%v", itemId, quantity)
	case tmt.SynthesisStockFail:
		commonFacade.SendDefaultWarnMsg(ctx, fmt.Sprintf("【探索物品%s失败】", actionDesc), fmt.Sprintf("物品Id: %v, 数量: %v", itemId, quantity))
		log.Ctx(ctx).Errorf("SynthesisStock Fail, itemId:%v, quantity:%v", itemId, quantity)
	default:
		// 告警
		commonFacade.SendDefaultWarnMsg(ctx, fmt.Sprintf("【探索物品%s响应未知】", actionDesc), fmt.Sprintf("物品Id: %v, 数量: %v", itemId, quantity))
		log.Ctx(ctx).Errorf("SynthesisStock Unknown Exception, itemId:%v, quantity:%v", itemId, quantity)
	}
	return res.Status, nil
}
