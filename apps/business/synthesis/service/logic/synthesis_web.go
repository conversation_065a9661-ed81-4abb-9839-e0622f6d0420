package logic

import (
	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/pagination"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/third_party/tmt"
	"app_service/third_party/yc_open"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"fmt"
	"gorm.io/datatypes"
	"sort"
	"time"
)

// GetSynthesisWebList 查询合成活动列表
func GetSynthesisWebList(ctx context.Context, req *define.GetSynthesisWebListReq) ([]*model.Synthesis, error) {
	statusList := []int32{enums.SynthesisStatusUp.Val(), enums.SynthesisStatusDown.Val(), enums.SynthesisStatusExpires.Val()}
	listExec := repo.GetDB().WithContext(ctx).
		Select("activity_code as activity_code, title as title, activity_type as activity_type, cover_url as cover_url,status as status, "+
			"item_id as item_id, stock as stock, total_stock as total_stock,start_time as start_time,end_time as end_time").
		Table("`synthesis`").
		Where("status in (?)", statusList).
		Scopes(pagination.Paginate(req.Pagination, false)).
		Order(" CASE WHEN `status` = 2 THEN 1 ELSE 2 END, CASE WHEN `status` = 2 THEN UNIX_TIMESTAMP(start_time) ELSE -UNIX_TIMESTAMP(end_time) END ASC")
	var results []*model.Synthesis
	err := listExec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	return results, nil
}

// UserSynthesisMaterialsHold 持有量设置
func UserSynthesisMaterialsHold(ctx context.Context, userId string, itemIds []string, userSynthesisMaterials []*define.UserSynthesisMaterials) error {
	if userId == "" {
		return nil
	}
	openUserId, err := facade.GetOpenUserId(ctx, userId)
	if openUserId == "" {
		return nil
	}
	queryFusionItemIdsRes, err := yc_open.QueryFusionItemIds(ctx, openUserId, util.UniqueStringSlice(itemIds))
	if err != nil {
		log.Ctx(ctx).Errorf("CancelFusion QueryFusionItemIds err:%v", err)
		return err
	}
	log.Ctx(ctx).Infof("UserSynthesisMaterialsHold queryFusionItemIdsRes:%+v", util.Obj2JsonStr(queryFusionItemIdsRes))
	item2Count := make(map[string]int32, 0)
	for _, item := range queryFusionItemIdsRes {
		item2Count[item.ItemId] = item.Count
	}
	for _, material := range userSynthesisMaterials {
		for _, data := range material.UserSynthesisMaterialsDatas {
			if _, ok := item2Count[data.ItemId]; ok {
				data.UserQty = item2Count[data.ItemId]
			}
		}
	}
	return nil
}

// GetSynthesisMaterialItemIds 根据合成活动code获取合成材料物品id
func GetSynthesisMaterialItemIds(synthesisMaterials []*model.SynthesisMaterials) ([]string, error) {
	itemIds := make([]string, 0)
	for _, material := range synthesisMaterials {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, materials := range materialsData {
			itemIds = append(itemIds, materials.ItemId)

		}
	}
	return itemIds, nil
}

// GetSynthesisMaterialItemIdsAndQty 根据合成活动code获取合成材料物品id和数量
func GetSynthesisMaterialItemIdsAndQty(synthesisMaterials []*model.SynthesisMaterials, qty int32, launchSynthesisReq []*define.LaunchSynthesisMaterials) (map[string]int32, error) {
	// 初始化数据结构
	needItemIdQtyMap := make(map[string]int32)
	id2SynthesisMaterial := make(map[int64]*model.SynthesisMaterials, len(synthesisMaterials))
	selectedItemIdQtyMap := make(map[string]int32)
	synthesisMaterialsExist := make(map[int64]bool)
	for _, materials := range launchSynthesisReq {
		if !synthesisMaterialsExist[materials.Id] {
			synthesisMaterialsExist[materials.Id] = true
		} else {
			return nil, common_define.CommonWarnErr
		}
	}
	if len(synthesisMaterials) != len(launchSynthesisReq) {
		return nil, common_define.CommonWarnErr
	}

	// 预处理合成材料数据
	for _, material := range synthesisMaterials {
		id2SynthesisMaterial[material.ID] = material

		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}

		for _, materials := range materialsData {
			needItemIdQtyMap[util.StrVal(material.ID)+materials.ItemId] = materials.Qty * qty
		}
	}

	// 处理用户选择的材料
	for _, material := range launchSynthesisReq {
		synthesisMaterial, ok := id2SynthesisMaterial[material.Id]
		if !ok {
			return nil, define.SH1000014Err
		}

		materialsData, err := UnmarshalMaterialsData(*synthesisMaterial.MaterialsData)
		if err != nil {
			return nil, err
		}

		switch synthesisMaterial.MaterialsType {
		case enums.SynthesisMaterialsTypeCore.Val():
			// 核心材料 - 需要全部选中
			for _, materials := range materialsData {
				selectedItemIdQtyMap[materials.ItemId] += needItemIdQtyMap[util.StrVal(synthesisMaterial.ID)+materials.ItemId]
			}

		case enums.SynthesisMaterialsTypeCrux.Val():
			// 关键材料 - 需要选中指定数量
			needItemIds := make(map[string]struct{}, len(materialsData))
			for _, materials := range materialsData {
				needItemIds[materials.ItemId] = struct{}{}
			}

			var userSelectedQty int32
			for _, itemId := range material.ItemIds {
				// 用户选中的
				if _, ok := needItemIds[itemId]; ok {
					userSelectedQty++
					selectedItemIdQtyMap[itemId] += needItemIdQtyMap[util.StrVal(synthesisMaterial.ID)+itemId]
				} else {
					return nil, define.SH1000014Err
				}
			}

			if userSelectedQty != synthesisMaterial.LimitQty {
				return nil, define.SH1000014Err
			}
		}
	}

	return selectedItemIdQtyMap, nil
}

// GetSynthesisMaterialUserItemIdMap 根据合成活动材料的物品背包id和物品id
func GetSynthesisMaterialUserItemIdMap(ctx context.Context, userId string, synthesisMaterials []*model.SynthesisMaterials, launchSynthesisReq *define.LaunchSynthesisReq) (map[string][]string, error) {
	openUserId, err := facade.GetOpenUserId(ctx, userId)
	if openUserId == "" {
		return nil, common_define.CommonErr
	}
	itemIds, err := GetSynthesisMaterialItemIds(synthesisMaterials)
	if err != nil {
		log.Ctx(ctx).Errorf("GetSynthesisMaterialUserItemIds GetSynthesisMaterialItemIds err:%v", err)
		return nil, err
	}
	// 获取持仓用户物品id
	queryFusionItemIdsRes, err := yc_open.QueryFusionItemIds(ctx, openUserId, util.UniqueStringSlice(itemIds))
	if err != nil {
		log.Ctx(ctx).Errorf("GetSynthesisMaterialUserItemIds QueryFusionItemIds err:%v", err)
		return nil, err
	}
	log.Ctx(ctx).Infof("UserSynthesisMaterialsHold queryFusionItemIdsRes:%+v", util.Obj2JsonStr(queryFusionItemIdsRes))
	userItemIdsMap := make(map[string][]string)
	for _, item := range queryFusionItemIdsRes {
		userItemIdsMap[item.ItemId] = item.UserItemIds
	}
	itemIdsQtyMap, err := GetSynthesisMaterialItemIdsAndQty(synthesisMaterials, launchSynthesisReq.Qty, launchSynthesisReq.LaunchSynthesisMaterials)
	if err != nil {
		log.Ctx(ctx).Errorf("GetSynthesisMaterialUserItemIds GetSynthesisMaterialItemIdsAndQty err:%v", err)
		return nil, err
	}
	userItemIdMap := make(map[string][]string, 0)
	for itemId, qty := range itemIdsQtyMap {
		if _, ok := userItemIdsMap[itemId]; ok {
			if len(userItemIdsMap[itemId]) < int(qty) {
				return nil, define.SH1000014Err
			}
			userItemIds := make([]string, 0)
			for i := int32(0); i < qty; i++ {
				userItemIds = append(userItemIds, userItemIdsMap[itemId][i])
			}
			userItemIdMap[itemId] = userItemIds
		} else {
			return nil, define.SH1000014Err
		}
	}
	return userItemIdMap, err
}

func VerifyLaunchSynthesis(ctx context.Context, synthesis *model.Synthesis, qty int32, userId string) error {
	if synthesis.Status != enums.SynthesisStatusUp.Val() {
		return define.SH1000011Err
	}
	if time.Now().Before(synthesis.StartTime) {
		return define.SH1000010Err
	} else if time.Now().After(synthesis.EndTime) {
		return define.SH1000011Err
	}
	if synthesis.Stock < qty {
		return define.SH1000015Err
	}
	sale, err := tmt.AllowSale(ctx)
	if err != nil {
		return err
	}
	if !sale {
		return define.SH1000012Err
	}
	if synthesis.UserLimit != 0 {
		if qty > synthesis.UserLimit {
			return define.SH1000013Err.SetMsg(fmt.Sprintf(define.SH1000013Err.Msg, synthesis.UserLimit))
		}
		// 查询是否超过限合数量
		totalNum, err := GetSynthesisOrderUserNum(ctx, synthesis.ID, userId)
		if err != nil {
			return err
		}
		if totalNum+qty > synthesis.UserLimit {
			return define.SH1000013Err.SetMsg(fmt.Sprintf(define.SH1000013Err.Msg, synthesis.UserLimit))
		}
	}
	return nil
}

func SynthesisStatusHandler(synthesis *model.Synthesis) int32 {
	if synthesis.Status == enums.SynthesisStatusUp.Val() && synthesis.EndTime.Before(time.Now()) {
		return enums.SynthesisStatusExpires.Val()
	}
	if synthesis.Status == enums.SynthesisStatusDown.Val() {
		return enums.SynthesisStatusExpires.Val()
	}
	if synthesis.Status == enums.SynthesisStatusUp.Val() && synthesis.Stock == int32(0) {
		return enums.SynthesisStatusExpires.Val()
	}
	return synthesis.Status
}
func IncrBySynthesisStock(ctx context.Context, order *model.SynthesisOrder) error {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ID, order.SynthesisID)
	getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return err
	}
	// 增加合成库存
	err = repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).IncrBySynthesisStock(getSynthesis.ID, order.Qty)
	if err != nil {
		return err
	}

	if order.ActivityType == enums.Goods.Val() {
		// 恢复库存
		synthesisRes, _ := SynthesisStock(ctx, order.ItemID, -order.Qty)
		if synthesisRes != tmt.SynthesisStockSuccess {
			log.Ctx(ctx).Infof("融合消耗材料失败，恢复库存失败, itemId: %+v, qty: %+v", order.ItemID, order.Qty)
		}
	}
	return nil
}

func SynthesisCompleteUserNum(ctx context.Context, synthesisId int64) error {
	exec := repo.GetDB().WithContext(ctx).
		Select("COUNT(DISTINCT(user_id)) as num").
		Table("`synthesis_order`").
		Where("status = ? and synthesis_id = ? and is_del = 0", enums.SynthesisOrderStatusDone.Val(), synthesisId)
	userNum := int32(0)
	err := exec.Find(&userNum).Error
	if err != nil {
		return err
	}
	synthesisSchema := repo.GetQuery().Synthesis
	updateSynthesis := &model.Synthesis{ID: synthesisId, CompleteUserNum: userNum}
	err = repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).UpdateById(updateSynthesis)
	if err != nil {
		return err
	}
	return nil
}

func GetSynthesisItemsReleaseTime(ctx context.Context, synthesisId int64) (map[string]*time.Time, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ID, synthesisId)
	getSynthesis, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}
	releaseTimeData, err := GetSynthesisReleaseTime(getSynthesis.ReleaseTime)
	if err != nil {
		return nil, err
	}
	var releaseTime *time.Time
	if releaseTimeData != nil {
		// 二次流转开启
		if releaseTimeData.ReleaseTime != nil && !releaseTimeData.ReleaseTime.IsZero() {
			releaseTime = util.GetBeijingStartOfDay(releaseTimeData.ReleaseTime)
		} else if releaseTimeData.ReleaseDay != nil && *releaseTimeData.ReleaseDay != 0 {
			now := time.Now()
			nowDay := util.GetBeijingStartOfDay(&now)
			day := int(*releaseTimeData.ReleaseDay) + nowDay.Day()
			truncate := time.Date(nowDay.Year(), nowDay.Month(), day, 0, 0, 0, 0, nowDay.Location())
			releaseTime = &truncate
		}
	} else {
		releaseTime = nil
	}

	data := make(map[string]*time.Time)
	synthesisMaterialsSchema := repo.GetQuery().SynthesisMaterials
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisMaterialsSchema.SynthesisID, getSynthesis.ID)
	synthesisMaterialsList, err := repo.NewSynthesisMaterialsRepo(synthesisMaterialsSchema.WithContext(ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return nil, err
	}
	for _, material := range synthesisMaterialsList {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			return nil, err
		}
		for _, item := range materialsData {
			if item.Loop == constant.Yes && releaseTime != nil {
				data[item.ItemId] = releaseTime
			}
		}
	}
	return data, nil
}

func GetSynthesisReleaseTime(releaseTime *datatypes.JSON) (*define.SynthesisReleaseTime, error) {
	synthesisReleaseTime := &define.SynthesisReleaseTime{}
	if releaseTime != nil {
		err := json.Unmarshal([]byte(util.StrVal(releaseTime)), &synthesisReleaseTime)
		if err != nil {
			return nil, err
		}
		return synthesisReleaseTime, nil
	}
	return nil, nil
}

func CirculationStatusHold(ctx context.Context, itemIds []string, userSynthesisMaterials []*define.UserSynthesisMaterials) {
	loc, _ := time.LoadLocation("Asia/Shanghai")
	itemMap, err := issueFacade.GetIssueItemMap(ctx, util.UniqueStringSlice(itemIds))
	if err != nil {
		return
	}
	for _, material := range userSynthesisMaterials {
		for _, item := range material.UserSynthesisMaterialsDatas {
			issueItem, exists := itemMap[item.ItemId]
			if !exists {
				item.CirculationStatus = 2
				continue
			}

			var startTimeUTC8 time.Time
			var endTimeUTC8 time.Time
			if issueItem.CirculationStart != nil {
				startTimeUTC8 = issueItem.CirculationStart.In(loc)
			}
			if issueItem.CirculationEnd != nil {
				endTimeUTC8 = issueItem.CirculationEnd.In(loc)
			}

			if issueItem.CirculationStatus == 1 && !startTimeUTC8.IsZero() && time.Now().After(startTimeUTC8) && !endTimeUTC8.IsZero() && time.Now().Before(endTimeUTC8) {
				item.CirculationStatus = 1
			} else {
				item.CirculationStatus = 2
			}
		}
	}
}

func SaveSynthesisMaterialsRelease(ctx context.Context, synthesisOrderId int64) error {
	synthesisOrderSchema := repo.GetQuery().SynthesisOrder
	order, err := repo.NewSynthesisOrderRepo(synthesisOrderSchema.WithContext(ctx)).SelectOne(search.NewQueryBuilder().Eq(synthesisOrderSchema.ID, synthesisOrderId).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("SaveSynthesisMaterialsRelease err:%v", err)
		return err
	}
	synthesisOrderDetailSchema := repo.GetQuery().SynthesisOrderDetail
	synthesisMaterialsBuilder := search.NewQueryBuilder().Eq(synthesisOrderDetailSchema.SynthesisOrderID, order.ID)
	orderDetail, err := repo.NewSynthesisOrderDetailRepo(synthesisOrderDetailSchema.WithContext(ctx)).SelectList(synthesisMaterialsBuilder.Build())
	if err != nil {
		return err
	}
	releaseTimeMap, err := GetSynthesisItemsReleaseTime(ctx, order.SynthesisID)
	if err != nil {
		return err
	}
	// 保存合成释放记录
	materialsReleaseList := make([]*model.SynthesisMaterialsRelease, 0)
	userItemIdList := make([]string, 0)
	for _, detail := range orderDetail {
		if releaseTime, ok := releaseTimeMap[detail.MaterialsItemID]; ok {
			if releaseTime != nil {
				release := &model.SynthesisMaterialsRelease{
					SynthesisOrderDetailID: detail.ID,
					UserID:                 order.UserID,
					ItemID:                 detail.MaterialsItemID,
					UserItemID:             detail.UserItemID,
					Status:                 enums.ReleaseCreated.Val(),
				}
				release.ReleaseTime = util.GetBeijingStartOfDay(releaseTime)
				materialsReleaseList = append(materialsReleaseList, release)
				userItemIdList = append(userItemIdList, detail.UserItemID)
			}
		}
	}
	if len(materialsReleaseList) > 0 {
		err = repo.ExecGenTx(ctx, func(tx context.Context) error {
			synthesisMaterialsReleaseSchema := repo.GetQuery().SynthesisMaterialsRelease
			list, err := repo.NewSynthesisMaterialsReleaseRepo(repo.Query(tx).SynthesisMaterialsRelease.WithContext(tx)).
				SelectList(search.NewQueryBuilder().In(synthesisMaterialsReleaseSchema.UserItemID, userItemIdList).Build())
			if err != nil {
				log.Ctx(ctx).Errorf("SelectListSynthesisMaterialsRelease err:%v", err)
				return err
			}
			if len(list) > 0 {
				err = repo.NewSynthesisMaterialsReleaseRepo(repo.Query(tx).SynthesisMaterialsRelease.WithContext(tx)).RemoveByIds(list...)
				if err != nil {
					log.Ctx(ctx).Errorf("RemoveByIdsSynthesisMaterialsRelease err:%v", err)
					return err
				}
			}
			err = repo.NewSynthesisMaterialsReleaseRepo(repo.Query(tx).SynthesisMaterialsRelease.WithContext(tx)).BatchSave(materialsReleaseList, 100)
			if err != nil {
				log.Ctx(ctx).Errorf("SaveSynthesisMaterialsRelease err:%v", err)
				return err
			}
			return nil
		})
	}
	return nil
}

// GetMaxLimit 获取物品一次性融合次数
func GetMaxLimit(ctx context.Context, synthesisMaterialsList []*model.SynthesisMaterials) int32 {
	if len(synthesisMaterialsList) == 0 {
		return 0
	}

	var totalMaxLimit int32
	const maxUserItems = global.MaxStoryUserItemCount

	for _, material := range synthesisMaterialsList {
		materialsData, err := UnmarshalMaterialsData(*material.MaterialsData)
		if err != nil {
			log.Ctx(ctx).Errorf("UnmarshalMaterialsData failed for material %v: %v", material.ID, err)
			continue
		}

		if material.MaterialsType == enums.SynthesisMaterialsTypeCore.Val() {
			// 核心材料全部必选，直接累加数量
			for _, data := range materialsData {
				totalMaxLimit += data.Qty
			}
			continue
		}

		// 非核心材料处理
		limitQty := int(material.LimitQty)
		if limitQty <= 0 || len(materialsData) == 0 {
			continue
		}

		// 准备数量数组并排序
		qtyArray := make([]int, 0, len(materialsData))
		for _, data := range materialsData {
			qtyArray = append(qtyArray, int(data.Qty))
		}

		sort.Sort(sort.Reverse(sort.IntSlice(qtyArray)))

		// 计算前limitQty个最大值的和
		effectiveQty := util.Min(limitQty, len(qtyArray))
		sum := 0
		for _, qty := range qtyArray[:effectiveQty] {
			sum += qty
		}

		totalMaxLimit += int32(sum)
	}

	if totalMaxLimit == 0 {
		return 0
	}

	// 计算最终限制值
	return maxUserItems / totalMaxLimit
}
