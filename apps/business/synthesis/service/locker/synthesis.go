package locker

import (
	"fmt"
	"time"
)

// SynthesisAction 合成活动相关行为枚举
type SynthesisAction string

const (
	Launch SynthesisAction = "launch" // 发起合成
)

type SynthesisLock struct {
	ac  SynthesisAction // 行为
	tag string          // 唯一标识
}

func (p *SynthesisLock) GetCacheKey() string {
	return fmt.Sprintf("synthesis:launch:%s:%s", p.ac, p.tag)
}

func (p *SynthesisLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewSynthesisLock(tag string, ac SynthesisAction) *SynthesisLock {
	return &SynthesisLock{tag: tag, ac: ac}
}
