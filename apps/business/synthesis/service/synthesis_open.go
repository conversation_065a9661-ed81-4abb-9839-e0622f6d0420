package service

import (
	"app_service/apps/business/synthesis/dal/model"
	"app_service/apps/business/synthesis/define"
	"app_service/apps/business/synthesis/define/enums"
	"app_service/apps/business/synthesis/repo"
	"app_service/apps/business/synthesis/service/logic"
	"app_service/apps/platform/common/constant"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/govvii/go-hutool/async"
	"time"
)

// GetPriorityBuyIsUp 获取优先购合成活动是否上架
func (s *Service) GetPriorityBuyIsUp(req *define.GetPriorityBuyIsUpReq) (*define.GetPriorityBuyIsUpResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ItemID, req.PriorityBuyId).
		Eq(synthesisSchema.ActivityType, enums.Rights.Val()).Ne(synthesisSchema.Status, enums.SynthesisStatusWaiting.Val())
	count, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Count(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetPriorityBuyIsUpResp{}
	if count > 0 {
		resp.HasUp = constant.Yes
	}
	return resp, nil
}

// GetItemIsUp 获取合成活动是否上架
func (s *Service) GetItemIsUp(req *define.GetItemIsUpReq) (*define.GetItemIsUpResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	builder := search.NewQueryBuilder().Eq(synthesisSchema.ItemID, req.ItemId).
		Eq(synthesisSchema.ActivityType, enums.Goods.Val()).Ne(synthesisSchema.Status, enums.SynthesisStatusWaiting.Val())
	count, err := repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Count(builder.Build())
	if err != nil {
		return nil, err
	}
	resp := &define.GetItemIsUpResp{}
	if count > 0 {
		resp.HasUp = constant.Yes
	}
	return resp, nil
}

// SynthesisFinish 合成活动超时下架
func (s *Service) SynthesisFinish() (*define.SynthesisFinishResp, error) {
	synthesisSchema := repo.GetQuery().Synthesis
	updateSynthesis := &model.Synthesis{
		Status: enums.SynthesisStatusExpires.Val(),
	}
	_ = repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Update(updateSynthesis, search.NewQueryBuilder().Eq(synthesisSchema.Status,
		enums.SynthesisStatusUp.Val()).Lt(synthesisSchema.EndTime, time.Now()).Build())
	_ = repo.NewSynthesisRepo(synthesisSchema.WithContext(s.ctx)).Update(updateSynthesis, search.NewQueryBuilder().Eq(synthesisSchema.Status,
		enums.SynthesisStatusUp.Val()).Eq(synthesisSchema.Stock, int32(0)).Build())
	return &define.SynthesisFinishResp{}, nil
}

// AsyncSynthesisOrderUpChain 异步合成订单上链
func (s *Service) AsyncSynthesisOrderUpChain(req *define.SynthesisOrderUpChainReq) (*define.SynthesisOrderUpChainResp, error) {
	executor := asyncutil.NewAsyncExecutor(1, asyncutil.WithContext(util.NewCtx(s.ctx)))
	spanContext := s.NewContextWithSpanContext(s.ctx)
	executor.Execute(func(ctx context.Context) (interface{}, error) {
		_, err := logic.SynthesisOrderUpChain(spanContext, req)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	return &define.SynthesisOrderUpChainResp{}, nil
}

// AsyncSynthesisMaterialsRelease 异步合成材料释放
func (s *Service) AsyncSynthesisMaterialsRelease(req *define.SynthesisMaterialsReleaseReq) (*define.SynthesisMaterialsReleaseResp, error) {
	executor := asyncutil.NewAsyncExecutor(1, asyncutil.WithContext(util.NewCtx(s.ctx)))
	spanContext := s.NewContextWithSpanContext(s.ctx)
	executor.Execute(func(ctx context.Context) (interface{}, error) {
		_, err := logic.SynthesisMaterialsRelease(spanContext, req)
		if err != nil {
			return nil, err
		}
		return nil, nil
	})
	return &define.SynthesisMaterialsReleaseResp{}, nil
}

// SynthesisMaterialsGetReleaseTime 获取合成材料释放时间
func (s *Service) SynthesisMaterialsGetReleaseTime(req *define.SynthesisMaterialsGetReleaseTimeReq) (*define.SynthesisMaterialsGetReleaseTimeResp, error) {
	resp := &define.SynthesisMaterialsGetReleaseTimeResp{}
	if len(req.UserItemIds) == 0 {
		return resp, nil
	}
	synthesisMaterialsReleaseSchema := repo.GetQuery().SynthesisMaterialsRelease
	list, err := repo.NewSynthesisMaterialsReleaseRepo(synthesisMaterialsReleaseSchema.WithContext(s.ctx)).
		SelectList(search.NewQueryBuilder().In(synthesisMaterialsReleaseSchema.UserItemID, req.UserItemIds).
			Eq(synthesisMaterialsReleaseSchema.Status, enums.ReleaseCreated.Val()).Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("SynthesisMaterialsGetReleaseTime SelectList err:%v", err)
		return resp, nil
	}
	for _, item := range list {
		if item.ReleaseTime != nil {
			resp.List = append(resp.List, &define.SynthesisMaterialsGetReleaseTimeData{
				ReleaseTime: item.ReleaseTime.UnixMilli(),
				UserItemId:  item.UserItemID,
			})
		}
	}
	return resp, nil
}
