package consume

import (
	"app_service/apps/business/invite_reward/service/logic"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"time"
)

type UserBindConsumer struct {
	middlewares.BaseConsumer
}

func NewUserBindConsumer() *UserBindConsumer {
	return &UserBindConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.UserBind, constant.CommonGroup),
	}
}

func (o *UserBindConsumer) GetTopic() string {
	return constant.UserBind
}

func (o *UserBindConsumer) GetGroup() string {
	return constant.CommonGroup
}

func (o *UserBindConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "UserBindConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[user_bind]kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &userBind{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		err = logic.AddNewUserReward(ctx, data.UserID, data.TargetUserID)
		if err != nil {
			log.Ctx(ctx).Errorf("发送新客奖励失败 err:%v", err)
		}

		err = logic.AddInviteLockReward(ctx, data.UserID, data.TargetUserID)
		if err != nil {
			log.Ctx(ctx).Errorf("发送实名奖励（未解锁）失败 err:%v", err)
		}
		return nil
	}

	return middlewares.SafeHandler(handler)
}

type userBind struct {
	UserID       string     `json:"user_id"`        // 用户id
	TargetUserID string     `json:"target_user_id"` // 被邀请用户id
	BindTime     *time.Time `json:"bind_time"`
}
