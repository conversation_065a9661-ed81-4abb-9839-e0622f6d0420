package logic

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/platform/user/repo"
	"context"
	"time"
)

// GetPurchaseAmountMap 查询用户消费金额
func GetPurchaseAmountMap(ctx context.Context, userIdList []string, purchaseTimeStart time.Time, purchaseTimeEnd time.Time) (map[string]define.AmountResult, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("upl.user_id as user_id, SUM(order_amount) AS amount").
		Table("`user_purchase_log` upl").
		Group("user_id")

	if !purchaseTimeStart.IsZero() && !purchaseTimeEnd.IsZero() {
		exec = exec.Where("upl.user_id in (?) AND upl.purchase_time > ? AND upl.purchase_time < ? AND upl.is_del=0", userIdList, purchaseTimeStart, purchaseTimeEnd)
	} else {
		exec = exec.Where("upl.user_id in (?) AND upl.is_del=0", userIdList)
	}

	var results []*define.AmountResult
	err := exec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]define.AmountResult)
	for i := range results {
		resMap[results[i].UserId] = *results[i]
	}

	return resMap, nil
}

// GetTotalPurchaseAmountMap 查询用户总消费金额
func GetTotalPurchaseAmountMap(ctx context.Context, userIdList []string) (map[string]define.AmountResult, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("upl.user_id as user_id, SUM(order_amount) AS amount").
		Table("`user_purchase_log` upl").
		Where("upl.user_id in (?) AND upl.is_del=0", userIdList).
		Group("user_id")
	var results []*define.AmountResult
	err := exec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]define.AmountResult)
	for i := range results {
		resMap[results[i].UserId] = *results[i]
	}

	return resMap, nil
}

// GetRewardAmountMap 查询用户奖励金额
func GetRewardAmountMap(ctx context.Context, userIdList []string, receiveTimeStart time.Time, receiveTimeEnd time.Time) (map[string]define.AmountResult, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("ubl.user_id as user_id, SUM(amount) AS amount").
		Table("`user_bonus_log` ubl").
		Group("user_id")

	if !receiveTimeStart.IsZero() && !receiveTimeEnd.IsZero() {
		exec = exec.Where("ubl.user_id in (?) AND ubl.receive_status = 20 AND ubl.created_at > ? AND ubl.created_at < ? AND ubl.is_del=0", userIdList, receiveTimeStart, receiveTimeEnd)
	} else {
		exec = exec.Where("ubl.user_id in (?) AND ubl.receive_status = 20 AND ubl.is_del=0", userIdList)
	}

	var results []*define.AmountResult
	err := exec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]define.AmountResult)
	for i := range results {
		resMap[results[i].UserId] = *results[i]
	}

	return resMap, nil
}

// GetTotalRewardAmountMap 查询用户总奖励金额
func GetTotalRewardAmountMap(ctx context.Context, userIdList []string) (map[string]define.AmountResult, error) {
	exec := repo.GetDB().WithContext(ctx).
		Select("ubl.user_id as user_id, SUM(amount) AS amount").
		Table("`user_bonus_log` ubl").
		Where("ubl.user_id in (?) AND ubl.receive_status = 20 AND ubl.is_del=0", userIdList).
		Group("user_id")
	var results []*define.AmountResult
	err := exec.Find(&results).Error
	if err != nil {
		return nil, err
	}
	resMap := make(map[string]define.AmountResult)
	for i := range results {
		resMap[results[i].UserId] = *results[i]
	}

	return resMap, nil
}
