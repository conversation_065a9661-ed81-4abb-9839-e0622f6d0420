package logic

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/platform/asset/dal/model"
	asset_enum "app_service/apps/platform/asset/define/enums"
	asset_facade "app_service/apps/platform/asset/facade"
	asset_repo "app_service/apps/platform/asset/repo"
	"app_service/apps/platform/common/constant"
	"app_service/apps/platform/common/facade"
	enum "app_service/apps/platform/user/define/enums"
	user_facade "app_service/apps/platform/user/facade"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
	"gorm.io/gorm"
)

func GetInviteConfig(ctx context.Context) (*define.InviteRewardConfig, error) {
	inviteRewardConfig := &define.InviteRewardConfig{}
	err := facade.GetObj(ctx, "invite.reward.config", inviteRewardConfig)
	if err != nil {
		return nil, err
	}
	return inviteRewardConfig, nil
}

func AddRealAuthReward(ctx context.Context, targetUserId string) error {
	inviteRewardConfig, err := GetInviteConfig(ctx)
	if err != nil {
		return err
	}
	//开关校验
	enable := inviteRewardConfig.InviteRewardEnable
	if enable == constant.No {
		return nil
	}

	//查询UserBind
	bindSchema := repo.GetQuery().UserBind
	qwForBind := search.NewQueryBuilder().
		Eq(bindSchema.TargetUserID, targetUserId).
		Eq(bindSchema.Type, enum.Bind.Val()).Build()
	userBindTab, err := repo.NewUserBindRepo(bindSchema.WithContext(ctx)).SelectOne(qwForBind)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if userBindTab == nil {
		return nil
	}

	//查询User
	targetUser, err := user_facade.GetNewUser(ctx, targetUserId)
	if err != nil {
		return err
	}
	if targetUser == nil || len(targetUser.RealName) == 0 {
		return nil
	}

	//查询实名奖励
	bonusLogSchema := asset_repo.GetQuery().UserBonusLog
	qwForBl := search.NewQueryBuilder().
		Eq(bonusLogSchema.UserID, userBindTab.UserID).
		Eq(bonusLogSchema.RelateID, userBindTab.TargetUserID).
		Eq(bonusLogSchema.Source, "实名奖励").
		Eq(bonusLogSchema.ReceiveStatus, asset_enum.BonusStatusLocked.Val()).Build()
	userBonusLogList, err := asset_repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(ctx)).SelectList(qwForBl)
	if err != nil {
		return err
	}
	err = repo.ExecGenTx(ctx, func(ctx context.Context) error {
		bonusLogTxSchema := asset_repo.Query(ctx).UserBonusLog
		userWalletTxSchema := asset_repo.Query(ctx).UserWallet
		for _, bonusLog := range userBonusLogList {
			// 更改状态
			bonusLog.ReceiveStatus = asset_enum.BonusStatusWait.Val()
			err := asset_repo.NewUserBonusLogRepo(bonusLogTxSchema.WithContext(ctx)).UpdateById(bonusLog)
			if err != nil {
				return err
			}

			queryWrapper := search.NewQueryBuilder().Eq(userWalletTxSchema.UserID, bonusLog.UserID).Build()
			update := map[string]interface{}{
				"total_bonus": gorm.Expr("total_bonus + ?", bonusLog.Amount),
			}
			err = asset_repo.NewUserWalletRepo(userWalletTxSchema.WithContext(ctx)).UpdateField(update, queryWrapper)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return err
			}
		}
		return nil
	})
	if err != nil {
		log.Ctx(ctx).Errorf("下发实名奖励错误 %v", err.Error())
	}

	return nil
}

func AddNewUserReward(ctx context.Context, userId string, targetUserId string) error {
	//开关校验
	inviteRewardConfig, err := GetInviteConfig(ctx)
	if err != nil {
		return err
	}
	enable := inviteRewardConfig.NewUserRewardEnable
	if enable == constant.No {
		return nil
	}

	//查询用户
	targetUser, err := user_facade.GetNewUser(ctx, targetUserId)
	if err != nil {
		return err
	}
	if targetUser != nil {
		//新客奖励
		rewardAmount := inviteRewardConfig.NewUserRewardAmount
		err = asset_facade.AddUserBonus(ctx, targetUser.UserID,
			targetUser.UserID,
			targetUser.Nickname,
			targetUser.Avatar, "新客奖励", rewardAmount)
		if err != nil {
			log.Ctx(ctx).Error("发放奖励失败 err:%v", err)
			return err
		}
	}

	return nil
}

func AddInviteLockReward(ctx context.Context, userId string, targetUserId string) error {
	//发送未解锁奖励
	inviteRewardConfig, err := GetInviteConfig(ctx)
	if err != nil {
		return err
	}
	//开关校验
	enable := inviteRewardConfig.InviteRewardEnable
	if enable == constant.Yes {
		//查询TargetUser
		targetUser, err := user_facade.GetNewUser(ctx, targetUserId)
		if err != nil {
			return err
		}
		rewardAmount := inviteRewardConfig.InviteRewardAmount
		//写入BonusLog
		bonusLogSchema := asset_repo.GetQuery().UserBonusLog
		userBonusLog := &model.UserBonusLog{
			UserID:        userId,
			Name:          targetUser.Nickname,
			MainImg:       targetUser.Avatar,
			Source:        "实名奖励",
			Amount:        rewardAmount,
			RelateID:      targetUserId,
			ReceiveStatus: asset_enum.BonusStatusLocked.Val(),
			CreatedAt:     util.Now(),
		}
		err = asset_repo.NewUserBonusLogRepo(bonusLogSchema.WithContext(ctx)).Save(userBonusLog)
		if err != nil && !util.IsMySQLDuplicateError(err) {
			return err
		}
	}
	return nil
}

func AddPurchaseReward(ctx context.Context, userId string, targetUserId string) error {
	//开关校验
	inviteRewardConfig, err := GetInviteConfig(ctx)
	if err != nil {
		return err
	}
	enable := inviteRewardConfig.NewUserRewardEnable
	if enable == constant.No {
		return nil
	}

	//查询用户
	targetUser, err := user_facade.GetNewUser(ctx, targetUserId)
	if err != nil {
		return err
	}

	//新客奖励
	rewardAmount := inviteRewardConfig.NewUserRewardAmount
	err = asset_facade.AddUserBonus(ctx, targetUser.UserID,
		targetUser.UserID,
		targetUser.Nickname,
		targetUser.Avatar, "新客奖励", rewardAmount)
	if err != nil {
		log.Ctx(ctx).Error("发放奖励失败 err:%v", err)
		return err
	}
	return nil
}
