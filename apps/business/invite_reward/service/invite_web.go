package service

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/business/invite_reward/service/logic"
	asset_facade "app_service/apps/platform/asset/facade"
	asset_repo "app_service/apps/platform/asset/repo"
	common_define "app_service/apps/platform/common/define"
	usermodel "app_service/apps/platform/user/dal/model"
	enum "app_service/apps/platform/user/define/enums"
	user_facade "app_service/apps/platform/user/facade"
	userrepo "app_service/apps/platform/user/repo"
)

func (s *Service) GetInviteSummary(req *define.GetInviteSummaryReq) (*define.GetInviteSummaryResp, error) {
	userId := s.GetUserId()

	//查询用户信息
	newUser, err := user_facade.GetNewUser(s.ctx, userId)
	if err != nil {
		return nil, err
	}

	//查询用户钱包
	userWallet, err := asset_facade.GetUserWallet(s.ctx, userId)
	if err != nil {
		return nil, err
	}

	waitReceiveAmount, _ := asset_facade.GetWaitReceiveAmount(s.ctx, userId)

	//查询InviteCount
	count, err := user_facade.GetUserBindCount(s.ctx, userId)

	//查询邀友配置
	inviteConfig, err := logic.GetInviteConfig(s.ctx)
	if err != nil {
		return nil, common_define.CommonErr.Err(err)
	}

	//组装结果
	resp := &define.GetInviteSummaryResp{
		UserId:           userId,
		InviteCode:       newUser.InviteCode,
		InviteConfig:     inviteConfig,
		TotalInviteCount: count,
		WaitReceiveBonus: waitReceiveAmount,
		CurrentBonus:     userWallet.Bonus,
		TotalBonus:       userWallet.TotalBonus,
	}

	return resp, nil
}

func (s *Service) GetUserInviteList(req *define.GetUserInviteListReq) (*common_define.Resp, error) {
	userId := s.GetUserId()

	bonusLogSchema := asset_repo.GetQuery().UserBonusLog
	nuSchema := userrepo.GetQuery().NewUser
	baseQuery := bonusLogSchema.WithContext(s.ctx).
		Where(bonusLogSchema.UserID.Eq(userId)).
		Where(bonusLogSchema.Source.Eq("实名奖励")).
		RightJoin(nuSchema, bonusLogSchema.RelateID.EqCol(nuSchema.UserID))
	total, err := baseQuery.Count()
	if err != nil {
		return nil, err
	}
	type inviteUser struct {
		usermodel.NewUser
		Amount int32 `gorm:"column:amount;type:int;not null;comment:数额" json:"amount"` // 数额
	}
	userList := make([]*inviteUser, 0)
	pageQuery := baseQuery.
		Select(nuSchema.UserID, nuSchema.Nickname, nuSchema.Avatar, nuSchema.CreatedAt, nuSchema.RealName, bonusLogSchema.Amount).
		Order(nuSchema.CreatedAt.Desc()).
		Limit(req.GetPageSize()).
		Offset(req.GetOffset())
	err = pageQuery.Scan(&userList)
	if err != nil {
		return nil, err
	}
	userInviteList := make([]*define.GetUserInviteListResp, 0)
	for _, user := range userList {
		userInvite := &define.GetUserInviteListResp{
			UserId:   user.UserID,
			Nickname: user.Nickname,
			Avatar:   user.Avatar,
			RegTime:  user.CreatedAt,
		}
		if len(user.RealName) > 0 {
			userInvite.Status = enum.UserRealStatusVerified.Val()
		} else {
			userInvite.Status = enum.UserRealStatusUnverified.Val()
		}

		// 积分数额
		userInvite.Amount = user.Amount

		userInviteList = append(userInviteList, userInvite)
	}

	return &common_define.Resp{
		List:  userInviteList,
		Total: total,
	}, nil
}
