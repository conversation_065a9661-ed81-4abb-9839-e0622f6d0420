package service

import (
	"app_service/apps/business/invite_reward/define"
	enum "app_service/apps/business/invite_reward/define/enums"
	"app_service/apps/business/invite_reward/service/logic"
	common_define "app_service/apps/platform/common/define"
	"app_service/apps/platform/common/facade"
	user_facade "app_service/apps/platform/user/facade"
	"app_service/apps/platform/user/repo"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"github.com/gin-gonic/gin"
)

func (s *Service) EditInviteConfig(req *define.EditInviteConfigAdminReq) (*define.EditInviteConfigAdminResp, error) {
	err := facade.SaveOrUpdate(s.ctx, "invite.reward.config", req.InviteRewardConfig)
	if err != nil {
		return nil, err
	}
	return nil, nil
}

func (s *Service) GetInviteConfig() (*define.InviteRewardConfig, error) {
	inviteRewardConfig := &define.InviteRewardConfig{}
	err := facade.GetObj(s.ctx, "invite.reward.config", inviteRewardConfig)
	if err != nil {
		return nil, err
	}
	return inviteRewardConfig, nil
}

func (s *Service) GetInviteAdminList(req *define.GetInviteAdminListReq) (*common_define.Resp, error) {

	//查询UserBind
	userBindSchema := repo.GetQuery().UserBind
	builder := search.NewQueryBuilder().OrderByDesc(userBindSchema.BindTime)
	if !req.RegTimeStart.IsZero() && !req.RegTimeEnd.IsZero() {
		builder = builder.Gte(userBindSchema.BindTime, req.RegTimeStart)
		builder = builder.Lte(userBindSchema.BindTime, req.RegTimeEnd)
	}
	if req.Keyword != "" {
		userIdList, err := user_facade.GetUserIdList(s.ctx, req.Keyword)
		if err != nil {
			return nil, err
		}
		builder = builder.In(userBindSchema.UserID, userIdList)
	}
	if req.TargetKeyword != "" {
		userIdList, err := user_facade.GetUserIdList(s.ctx, req.TargetKeyword)
		if err != nil {
			return nil, err
		}
		builder = builder.In(userBindSchema.TargetUserID, userIdList)
	}
	userBindTabList, count, err := repo.NewUserBindRepo(userBindSchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}
	if len(userBindTabList) == 0 {
		return &common_define.Resp{}, nil
	}

	//查询用户信息
	userIdList := make([]string, 0)
	targetUserIdList := make([]string, 0)
	for _, userBind := range userBindTabList {
		userIdList = append(userIdList, userBind.UserID)
		targetUserIdList = append(targetUserIdList, userBind.TargetUserID)
	}
	allUserIdList := append(userIdList, targetUserIdList...)
	userMap, err := user_facade.GetNewUserMap(s.ctx, allUserIdList)
	if err != nil {
		return nil, err
	}

	//查询用户消费数据
	purchaseAmountMap, err := logic.GetPurchaseAmountMap(s.ctx, targetUserIdList, req.PurchaseTimeStart, req.PurchaseTimeEnd)
	if err != nil {
		return nil, err
	}
	totalPurchaseAmountMap, err := logic.GetTotalPurchaseAmountMap(s.ctx, targetUserIdList)
	if err != nil {
		return nil, err
	}

	//查询用户奖励数据
	rewardAmountMap, err := logic.GetRewardAmountMap(s.ctx, userIdList, req.PurchaseTimeStart, req.PurchaseTimeEnd)
	if err != nil {
		return nil, err
	}
	totalRewardAmountMap, err := logic.GetTotalRewardAmountMap(s.ctx, userIdList)
	if err != nil {
		return nil, err
	}

	//组装数据
	inviteRewardDataList := make([]*define.GetInviteAdminListResp, 0)
	for _, bind := range userBindTabList {
		user := userMap[bind.UserID]
		if user == nil {
			continue
		}
		targetUser := userMap[bind.TargetUserID]
		if targetUser == nil {
			continue
		}

		data := &define.GetInviteAdminListResp{
			UserId:            user.UserID,
			Nickname:          user.Nickname,
			Avatar:            user.Avatar,
			MobilePhone:       user.MobilePhone,
			TargetUserId:      targetUser.UserID,
			TargetNickname:    targetUser.Nickname,
			TargetAvatar:      targetUser.Avatar,
			TargetMobilePhone: targetUser.MobilePhone,
			TargetRealName:    targetUser.RealName,
			TargetRegTime:     targetUser.CreatedAt,
			Status:            1,
		}

		purchaseAmount, ok := purchaseAmountMap[bind.TargetUserID]
		if ok {
			data.TargetPurchaseAmount = purchaseAmount.Amount
		}
		totalPurchaseAmount, ok := totalPurchaseAmountMap[bind.TargetUserID]
		if ok {
			data.TargetTotalPurchaseAmount = totalPurchaseAmount.Amount
		}
		rewardAmount, ok := rewardAmountMap[bind.UserID]
		if ok {
			data.RewardAmount = rewardAmount.Amount
		}
		totalRewardAmount, ok := totalRewardAmountMap[bind.UserID]
		if ok {
			data.TotalReward = totalRewardAmount.Amount
		}

		//查询LoginLog
		userLoginLog, _ := user_facade.GetUserLoginLog(s.ctx, bind.UserID, 1)
		if len(userLoginLog) > 0 {
			data.UserLoginInfo = userLoginLog[0]
		}
		targetUserLoginLog, _ := user_facade.GetUserLoginLog(s.ctx, bind.TargetUserID, 1)
		if len(targetUserLoginLog) > 0 {
			data.TargetUserLoginInfo = targetUserLoginLog[0]
		}
		if len(userLoginLog) > 0 && len(targetUserLoginLog) > 0 {
			if data.UserLoginInfo.LoginIP == data.TargetUserLoginInfo.LoginIP {
				data.Status = enum.Suspicious.Val()
				data.RickReason = "注册IP异常"
				data.RickTime = data.TargetUserLoginInfo.LoginTime
			} else if data.UserLoginInfo.Type == data.TargetUserLoginInfo.Type && data.UserLoginInfo.LoginIP == data.TargetUserLoginInfo.LoginIP {
				data.Status = enum.Abnormal.Val()
				data.RickReason = "注册IP异常"
				data.RickTime = data.TargetUserLoginInfo.LoginTime
			} else {
				data.Status = enum.Normal.Val()
			}
		}

		inviteRewardDataList = append(inviteRewardDataList, data)
	}

	return &common_define.Resp{
		List:  inviteRewardDataList,
		Total: count,
	}, nil
}

func (s *Service) ExportInviteAdminList(ctx *gin.Context, req *define.GetInviteAdminListReq) error {
	dataList := make([]*define.GetInviteAdminListResp, 0)
	for i := 1; i < 10000; i++ {
		req.PageSize = 100
		req.Page = i
		page, err := s.GetInviteAdminList(req)
		if err != nil {
			return err
		}
		if page.List == nil {
			break
		}
		inviteAdminList := page.List.([]*define.GetInviteAdminListResp)
		dataList = append(dataList, inviteAdminList...)
	}
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	dataKey = append(dataKey, map[string]string{"key": "target_reg_time", "title": "注册时间", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_user_id", "title": "受邀人ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_nickname", "title": "受邀人昵称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_mobile_phone", "title": "受邀人手机", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_real_status", "title": "受邀人实名状态", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_purchase_amount", "title": "受邀人阶段交易金额", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "target_total_purchase_amount", "title": "受邀人总交易金额", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "userId", "title": "邀请人ID", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "nickname", "title": "邀请人昵称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "mobile_phone", "title": "邀请人手机", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "reward_amount", "title": "邀请人阶段奖励", "width": "20", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "total_reward", "title": "邀请人总奖励", "width": "20", "is_num": "1"})

	data := make([]map[string]interface{}, 0)
	for _, idata := range dataList {
		targetRealStatus := "未实名"
		if len(idata.TargetRealName) > 0 {
			targetRealStatus = "已实名"
		}

		data = append(data, map[string]interface{}{
			"target_reg_time":              util.GetDateTimeFormatStr(idata.TargetRegTime),
			"target_user_id":               idata.TargetUserId,
			"target_nickname":              idata.TargetNickname,
			"target_mobile_phone":          idata.TargetMobilePhone,
			"target_real_status":           targetRealStatus,
			"target_purchase_amount":       "¥" + util.FenToYuanString(idata.TargetPurchaseAmount),
			"target_total_purchase_amount": "¥" + util.FenToYuanString(idata.TargetTotalPurchaseAmount),
			"userId":                       idata.UserId,
			"nickname":                     idata.Nickname,
			"mobile_phone":                 idata.MobilePhone,
			"reward_amount":                idata.RewardAmount,
			"total_reward":                 idata.TotalReward,
		})
	}

	err := excel.ExportToStream(dataKey, data, ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("[Service.ExportReviveRecordList] ExportToWeb err:%v", err)
		return response.SystemErr
	}
	return nil
}
