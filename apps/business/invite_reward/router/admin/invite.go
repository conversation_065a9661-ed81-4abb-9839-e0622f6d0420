package admin

import (
	"app_service/apps/business/invite_reward/api/admin"
	"github.com/gin-gonic/gin"
)

// Invite 管理端端相关
func Invite(router *gin.RouterGroup) {
	group := router.Group("/invite_reward")
	{
		// 编辑配置信息
		group.POST("/edit_invite_config", admin.EditInviteConfig)
		// 获取配置信息
		group.GET("/get_invite_config", admin.GetInviteConfig)
		// 获取邀友管理列表
		group.GET("/invite_admin_list", admin.GetInviteAdminList)
		// 导出邀友管理列表
		group.GET("/invite_admin_list_export", admin.ExportInviteAdminList)
	}
}
