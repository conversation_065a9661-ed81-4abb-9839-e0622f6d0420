package define

import (
	"app_service/apps/platform/user/dal/model"
	"app_service/pkg/pagination"
	"time"
)

type (
	GetInviteSummaryReq struct {
	}
	GetInviteSummaryResp struct {
		UserId           string              `json:"userId"`
		InviteCode       string              `json:"invite_code"`        //用户邀请码
		WaitReceiveBonus int32               `json:"wait_receive_bonus"` //待领取奖励余额
		CurrentBonus     int32               `json:"current_bonus"`      //当前奖励余额
		TotalBonus       int32               `json:"total_bonus"`        //累计奖励
		TotalInviteCount int32               `json:"total_invite_count"` //累计邀请数量
		InviteConfig     *InviteRewardConfig `json:"invite_config"`
	}
	Banner struct {
		ImgUrl string `json:"img_url"`
		Link   string `json:"link"`
	}
	GetInviteRewardListReq struct {
		Status int32 `form:"status"` //状态 0-全部 1-待领取 10-未解锁 20-已领取 99-过期未领取
		pagination.Pagination
	}
	GetInviteRewardListResp struct {
		UserId   string    `json:"userId"`
		Nickname string    `json:"nickname"`
		Avatar   string    `json:"avatar"`
		Type     int32     `json:"type"`
		Amount   int32     `json:"amount"`
		Status   int32     `json:"status"`
		UpdateAt time.Time `json:"update_at"`
	}
	GetUserInviteListReq struct {
		pagination.Pagination
	}
	GetUserInviteListResp struct {
		UserId   string    `json:"userId"`
		Nickname string    `json:"nickname"`
		Avatar   string    `json:"avatar"`
		Amount   int32     `json:"amount"`
		Status   int32     `json:"status"` //1-已邀请 10-已实名
		RegTime  time.Time `json:"reg_time"`
	}
)

type (
	GetInviteAdminListReq struct {
		RegTimeStart      time.Time `form:"reg_time_start"`
		RegTimeEnd        time.Time `form:"reg_time_end"`
		PurchaseTimeStart time.Time `form:"purchase_time_start"`
		PurchaseTimeEnd   time.Time `form:"purchase_time_end"`
		Keyword           string    `form:"keyword"`
		TargetKeyword     string    `form:"target_keyword"`
		pagination.Pagination
	}
	GetInviteAdminListResp struct {
		UserId        string              `json:"userId"`
		Nickname      string              `json:"nickname"`
		Avatar        string              `json:"avatar"`
		MobilePhone   string              `json:"mobile_phone"`
		RewardAmount  int32               `json:"reward_amount"`
		TotalReward   int32               `json:"total_reward"`
		UserLoginInfo *model.UserLoginLog `json:"user_login_info"`

		TargetUserId              string              `json:"target_user_id"`
		TargetNickname            string              `json:"target_nickname"`
		TargetAvatar              string              `json:"target_avatar"`
		TargetMobilePhone         string              `json:"target_mobile_phone"`
		TargetRealName            string              `json:"target_real_name"`
		TargetPurchaseAmount      int32               `json:"target_purchase_amount"`
		TargetTotalPurchaseAmount int32               `json:"target_total_purchase_amount"`
		TargetRegTime             time.Time           `json:"target_reg_time"`
		TargetUserLoginInfo       *model.UserLoginLog `json:"target_user_login_info"`

		Status     int32     `json:"status"`      //1-正常 99-异常 100-可疑
		RickReason string    `json:"rick_reason"` //风控原因
		RickTime   time.Time `json:"rick_time"`   //分控时间
	}

	EditInviteConfigAdminReq struct {
		InviteRewardConfig InviteRewardConfig `json:"invite_reward_config"`
	}
	EditInviteConfigAdminResp struct{}

	InviteRewardConfig struct {
		InviteRewardEnable   int32     `json:"invite_reward_enable"`   //邀友返利开关 1-Yes 2-No
		InviteRewardAmount   int32     `json:"invite_reward_amount"`   //邀友返利金额
		NewUserRewardEnable  int32     `json:"new_user_reward_enable"` //新客奖励开关 1-Yes 2-No
		NewUserRewardAmount  int32     `json:"new_user_reward_amount"` //新客奖励金额
		PurchaseRewardEnable int32     `json:"purchase_reward_enable"` //消费奖励开关 1-Yes 2-No
		PurchaseRewardAmount int32     `json:"purchase_reward_amount"` //消费奖励比例 0～100
		ContentTextList      []string  `json:"content_text_list"`      //分享文案列表
		BannerList           []*Banner `json:"banner_list"`            //广告位配置
		RuleDesc             string    `json:"rule_desc"`              //规则说明
		ExpireTime           int32     `json:"expire_time"`            //过期时间，单位：天
	}
)

type AmountResult struct {
	UserId string `gorm:"column:user_id" json:"user_id"`
	Amount int32  `gorm:"column:amount" json:"amount"`
}
