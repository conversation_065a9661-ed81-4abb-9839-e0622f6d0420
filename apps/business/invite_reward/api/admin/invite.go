package admin

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/business/invite_reward/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
)

// EditInviteConfig
// @Summary 编辑邀友配置
// @Description 编辑邀友配置
// @Tags 管理端
// @Param data body define.EditInviteConfigAdminReq true "请求参数"
// @Success 200 {object} response.Data{data=define.EditInviteConfigAdminResp}
// @Router  /admin/v1/invite_reward/edit_invite_config [post]
// @Security Bearer
func EditInviteConfig(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditInviteConfigAdminReq{}, s.EditInviteConfig)
}

// GetInviteConfig
// @Summary 查询邀友奖励配置
// @Description 查询邀友奖励配置
// @Tags 管理端
// @Success 200 {object} response.Data{data=define.InviteRewardConfig}
// @Router  /admin/v1/invite_reward/get_invite_config [get]
// @Security Bearer
func GetInviteConfig(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandleSimple(ctx, s.GetInviteConfig)
}

// GetInviteAdminList
// @Summary 获取用户邀友管理列表
// @Description 获取用户邀友管理列表
// @Tags 管理端
// @Param data query define.GetInviteAdminListReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetInviteAdminListResp}
// @Router  /admin/v1/invite_reward/invite_admin_list [get]
// @Security Bearer
func GetInviteAdminList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetInviteAdminListReq{}, s.GetInviteAdminList)
}

// ExportInviteAdminList
// @Summary 导出用户邀友管理列表
// @Description 导出用户邀友管理列表
// @Tags 管理端
// @Param data query define.GetInviteAdminListReq true "查询参数"
// @Success 200 {object} response.Data{}
// @Router  /admin/v1/invite_reward/invite_admin_list_export [get]
// @Security Bearer
func ExportInviteAdminList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetInviteAdminListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportInviteAdminList(ctx, req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}
