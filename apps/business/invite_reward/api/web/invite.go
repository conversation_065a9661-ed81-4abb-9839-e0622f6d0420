package web

import (
	"app_service/apps/business/invite_reward/define"
	"app_service/apps/business/invite_reward/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetUserInviteSummary
// @Summary 获取用户邀请总览
// @Description 获取用户邀请总览
// @Tags 用户端
// @Param data query define.GetInviteSummaryReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetInviteSummaryResp}
// @Router  /web/v1/invite_reward/invite_summary [get]
// @Security Bearer
func GetUserInviteSummary(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetInviteSummaryReq{}, s.GetInviteSummary)
}

// GetUserInviteList
// @Summary 获取用户奖励列表
// @Description 获取用户奖励列表
// @Tags 用户端
// @Param data query define.GetUserInviteListReq true "请求参数"
// @Success 200 {object} response.Data{data=define.GetUserInviteListResp}
// @Router  /web/v1/invite_reward/invite_list [get]
// @Security Bearer
func GetUserInviteList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetUserInviteListReq{}, s.GetUserInviteList)
}
