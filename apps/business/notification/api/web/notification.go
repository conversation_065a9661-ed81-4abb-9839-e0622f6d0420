package web

import (
	"app_service/apps/business/notification/define"
	"app_service/apps/business/notification/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// RegisterDevice
// @Summary 注册设备
// @Description 注册设备
// @Tags 用户端-消息通知
// @Param data body define.RegisterDeviceReq true "注册参数"
// @Success 200 {object} response.Data{data=define.RegisterDeviceResp}
// @Router  /web/v1/notification/device/register [POST]
// @Security Bearer
func RegisterDevice(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.RegisterDeviceReq{}, s.RegisterDevice)
}

// UpdateDeviceStatus
// @Summary 更新设备状态
// @Description 更新设备状态
// @Tags 用户端-消息通知
// @Param data body define.UpdateDeviceStatusReq true "更新参数"
// @Success 200 {object} response.Data{data=define.UpdateDeviceStatusResp}
// @Router  /web/v1/notification/device/update_status [POST]
// @Security Bearer
func UpdateDeviceStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateDeviceStatusReq{}, s.UpdateDeviceStatus)
}

// TestPush
// @Summary 测试推送（仅测试环境可用）
// @Description 测试推送（仅测试环境可用）
// @Tags 用户端-消息通知
// @Param data body define.TestPushReq true "推送参数"
// @Success 200 {object} response.Data{data=define.TestPushResp}
// @Router  /web/v1/notification/push/test [POST]
// @Security Bearer
func TestPush(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.TestPushReq{}, s.TestPush)
}
