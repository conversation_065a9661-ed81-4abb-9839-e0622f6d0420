// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNamePushRecord = "push_record"

// PushRecord 推送记录表
type PushRecord struct {
	PushRecordID  int64           `gorm:"column:push_record_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 id" json:"push_record_id"` // 主键 id
	Title         string          `gorm:"column:title;type:varchar(128);not null;comment:推送标题" json:"title"`                                            // 推送标题
	Content       string          `gorm:"column:content;type:text;not null;comment:推送内容" json:"content"`                                                // 推送内容
	RelateType    string          `gorm:"column:relate_type;type:varchar(64);comment:关联类型，行情异动：market_changes" json:"relate_type"`                      // 关联类型，行情异动：market_changes
	RelateID      string          `gorm:"column:relate_id;type:varchar(64);comment:关联资源 id" json:"relate_id"`                                           // 关联资源 id
	RelateScene   string          `gorm:"column:relate_scene;type:varchar(64);comment:关联场景" json:"relate_scene"`                                        // 关联场景
	BatchID       int32           `gorm:"column:batch_id;type:int;default:1;comment:批次 id" json:"batch_id"`                                             // 批次 id
	MessageID     string          `gorm:"column:message_id;type:varchar(256);not null;comment:推送服务商返回的消息 id" json:"message_id"`                         // 推送服务商返回的消息 id
	Provider      string          `gorm:"column:provider;type:varchar(32);not null;comment:推送服务商，极光：jpush" json:"provider"`                             // 推送服务商，极光：jpush
	AudienceType  string          `gorm:"column:audience_type;type:varchar(16);not null;comment:受众类型(user/device/all)" json:"audience_type"`            // 受众类型(user/device/all)
	AudienceValue *datatypes.JSON `gorm:"column:audience_value;type:json;comment:受众值" json:"audience_value"`                                            // 受众值
	Payload       *datatypes.JSON `gorm:"column:payload;type:json;comment:附加信息" json:"payload"`                                                         // 附加信息
	SendCount     int32           `gorm:"column:send_count;type:int;not null;comment:发送设备数" json:"send_count"`                                          // 发送设备数
	Status        int32           `gorm:"column:status;type:tinyint;default:1;comment:状态【1：已提交，2：成功，3：失败】" json:"status"`                               // 状态【1：已提交，2：成功，3：失败】
	CreatedAt     time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`            // 创建时间
	UpdatedAt     time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`            // 更新时间
}

// TableName PushRecord's table name
func (*PushRecord) TableName() string {
	return TableNamePushRecord
}
