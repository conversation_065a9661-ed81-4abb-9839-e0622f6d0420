// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNamePushDevice = "push_device"

// PushDevice 推送设备表
type PushDevice struct {
	PushDeviceID int64      `gorm:"column:push_device_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 id" json:"push_device_id"` // 主键 id
	DeviceID     string     `gorm:"column:device_id;type:varchar(256);not null;comment:设备唯一标识符" json:"device_id"`                                 // 设备唯一标识符
	Platform     string     `gorm:"column:platform;type:varchar(16);not null;comment:平台，android/ios" json:"platform"`                             // 平台，android/ios
	Provider     string     `gorm:"column:provider;type:varchar(32);not null;comment:推送服务商，极光：jpush" json:"provider"`                             // 推送服务商，极光：jpush
	PushID       string     `gorm:"column:push_id;type:varchar(256);not null;comment:推送id" json:"push_id"`                                        // 推送id
	UserID       string     `gorm:"column:user_id;type:varchar(32);comment:用户 id" json:"user_id"`                                                 // 用户 id
	ChannelID    string     `gorm:"column:channel_id;type:varchar(256);not null;comment:终端 id" json:"channel_id"`                                 // 终端 id
	DeviceModel  string     `gorm:"column:device_model;type:varchar(64);comment:设备型号" json:"device_model"`                                        // 设备型号
	OsVersion    string     `gorm:"column:os_version;type:varchar(32);comment:设备系统版本" json:"os_version"`                                          // 设备系统版本
	Status       int32      `gorm:"column:status;type:tinyint;default:1;comment:状态【1:在线;2:离线】" json:"status"`                                     // 状态【1:在线;2:离线】
	CreatedAt    time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`            // 创建时间
	UpdatedAt    time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`            // 更新时间
	LastActive   *time.Time `gorm:"column:last_active;type:datetime;comment:最后活跃时间" json:"last_active"`                                           // 最后活跃时间
}

// TableName PushDevice's table name
func (*PushDevice) TableName() string {
	return TableNamePushDevice
}
