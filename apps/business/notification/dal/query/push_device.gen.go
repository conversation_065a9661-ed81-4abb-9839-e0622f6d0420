// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/notification/dal/model"
)

func newPushDevice(db *gorm.DB, opts ...gen.DOOption) pushDevice {
	_pushDevice := pushDevice{}

	_pushDevice.pushDeviceDo.UseDB(db, opts...)
	_pushDevice.pushDeviceDo.UseModel(&model.PushDevice{})

	tableName := _pushDevice.pushDeviceDo.TableName()
	_pushDevice.ALL = field.NewAsterisk(tableName)
	_pushDevice.PushDeviceID = field.NewInt64(tableName, "push_device_id")
	_pushDevice.DeviceID = field.NewString(tableName, "device_id")
	_pushDevice.Platform = field.NewString(tableName, "platform")
	_pushDevice.Provider = field.NewString(tableName, "provider")
	_pushDevice.PushID = field.NewString(tableName, "push_id")
	_pushDevice.UserID = field.NewString(tableName, "user_id")
	_pushDevice.ChannelID = field.NewString(tableName, "channel_id")
	_pushDevice.DeviceModel = field.NewString(tableName, "device_model")
	_pushDevice.OsVersion = field.NewString(tableName, "os_version")
	_pushDevice.Status = field.NewInt32(tableName, "status")
	_pushDevice.CreatedAt = field.NewTime(tableName, "created_at")
	_pushDevice.UpdatedAt = field.NewTime(tableName, "updated_at")
	_pushDevice.LastActive = field.NewTime(tableName, "last_active")

	_pushDevice.fillFieldMap()

	return _pushDevice
}

// pushDevice 推送设备表
type pushDevice struct {
	pushDeviceDo

	ALL          field.Asterisk
	PushDeviceID field.Int64  // 主键 id
	DeviceID     field.String // 设备唯一标识符
	Platform     field.String // 平台，android/ios
	Provider     field.String // 推送服务商，极光：jpush
	PushID       field.String // 推送id
	UserID       field.String // 用户 id
	ChannelID    field.String // 终端 id
	DeviceModel  field.String // 设备型号
	OsVersion    field.String // 设备系统版本
	Status       field.Int32  // 状态【1:在线;2:离线】
	CreatedAt    field.Time   // 创建时间
	UpdatedAt    field.Time   // 更新时间
	LastActive   field.Time   // 最后活跃时间

	fieldMap map[string]field.Expr
}

func (p pushDevice) Table(newTableName string) *pushDevice {
	p.pushDeviceDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pushDevice) As(alias string) *pushDevice {
	p.pushDeviceDo.DO = *(p.pushDeviceDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pushDevice) updateTableName(table string) *pushDevice {
	p.ALL = field.NewAsterisk(table)
	p.PushDeviceID = field.NewInt64(table, "push_device_id")
	p.DeviceID = field.NewString(table, "device_id")
	p.Platform = field.NewString(table, "platform")
	p.Provider = field.NewString(table, "provider")
	p.PushID = field.NewString(table, "push_id")
	p.UserID = field.NewString(table, "user_id")
	p.ChannelID = field.NewString(table, "channel_id")
	p.DeviceModel = field.NewString(table, "device_model")
	p.OsVersion = field.NewString(table, "os_version")
	p.Status = field.NewInt32(table, "status")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")
	p.LastActive = field.NewTime(table, "last_active")

	p.fillFieldMap()

	return p
}

func (p *pushDevice) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pushDevice) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 13)
	p.fieldMap["push_device_id"] = p.PushDeviceID
	p.fieldMap["device_id"] = p.DeviceID
	p.fieldMap["platform"] = p.Platform
	p.fieldMap["provider"] = p.Provider
	p.fieldMap["push_id"] = p.PushID
	p.fieldMap["user_id"] = p.UserID
	p.fieldMap["channel_id"] = p.ChannelID
	p.fieldMap["device_model"] = p.DeviceModel
	p.fieldMap["os_version"] = p.OsVersion
	p.fieldMap["status"] = p.Status
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
	p.fieldMap["last_active"] = p.LastActive
}

func (p pushDevice) clone(db *gorm.DB) pushDevice {
	p.pushDeviceDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pushDevice) replaceDB(db *gorm.DB) pushDevice {
	p.pushDeviceDo.ReplaceDB(db)
	return p
}

type pushDeviceDo struct{ gen.DO }

type IPushDeviceDo interface {
	gen.SubQuery
	Debug() IPushDeviceDo
	WithContext(ctx context.Context) IPushDeviceDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPushDeviceDo
	WriteDB() IPushDeviceDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPushDeviceDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPushDeviceDo
	Not(conds ...gen.Condition) IPushDeviceDo
	Or(conds ...gen.Condition) IPushDeviceDo
	Select(conds ...field.Expr) IPushDeviceDo
	Where(conds ...gen.Condition) IPushDeviceDo
	Order(conds ...field.Expr) IPushDeviceDo
	Distinct(cols ...field.Expr) IPushDeviceDo
	Omit(cols ...field.Expr) IPushDeviceDo
	Join(table schema.Tabler, on ...field.Expr) IPushDeviceDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPushDeviceDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPushDeviceDo
	Group(cols ...field.Expr) IPushDeviceDo
	Having(conds ...gen.Condition) IPushDeviceDo
	Limit(limit int) IPushDeviceDo
	Offset(offset int) IPushDeviceDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPushDeviceDo
	Unscoped() IPushDeviceDo
	Create(values ...*model.PushDevice) error
	CreateInBatches(values []*model.PushDevice, batchSize int) error
	Save(values ...*model.PushDevice) error
	First() (*model.PushDevice, error)
	Take() (*model.PushDevice, error)
	Last() (*model.PushDevice, error)
	Find() ([]*model.PushDevice, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PushDevice, err error)
	FindInBatches(result *[]*model.PushDevice, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PushDevice) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPushDeviceDo
	Assign(attrs ...field.AssignExpr) IPushDeviceDo
	Joins(fields ...field.RelationField) IPushDeviceDo
	Preload(fields ...field.RelationField) IPushDeviceDo
	FirstOrInit() (*model.PushDevice, error)
	FirstOrCreate() (*model.PushDevice, error)
	FindByPage(offset int, limit int) (result []*model.PushDevice, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPushDeviceDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pushDeviceDo) Debug() IPushDeviceDo {
	return p.withDO(p.DO.Debug())
}

func (p pushDeviceDo) WithContext(ctx context.Context) IPushDeviceDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pushDeviceDo) ReadDB() IPushDeviceDo {
	return p.Clauses(dbresolver.Read)
}

func (p pushDeviceDo) WriteDB() IPushDeviceDo {
	return p.Clauses(dbresolver.Write)
}

func (p pushDeviceDo) Session(config *gorm.Session) IPushDeviceDo {
	return p.withDO(p.DO.Session(config))
}

func (p pushDeviceDo) Clauses(conds ...clause.Expression) IPushDeviceDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pushDeviceDo) Returning(value interface{}, columns ...string) IPushDeviceDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pushDeviceDo) Not(conds ...gen.Condition) IPushDeviceDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pushDeviceDo) Or(conds ...gen.Condition) IPushDeviceDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pushDeviceDo) Select(conds ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pushDeviceDo) Where(conds ...gen.Condition) IPushDeviceDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pushDeviceDo) Order(conds ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pushDeviceDo) Distinct(cols ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pushDeviceDo) Omit(cols ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pushDeviceDo) Join(table schema.Tabler, on ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pushDeviceDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pushDeviceDo) RightJoin(table schema.Tabler, on ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pushDeviceDo) Group(cols ...field.Expr) IPushDeviceDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pushDeviceDo) Having(conds ...gen.Condition) IPushDeviceDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pushDeviceDo) Limit(limit int) IPushDeviceDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pushDeviceDo) Offset(offset int) IPushDeviceDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pushDeviceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPushDeviceDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pushDeviceDo) Unscoped() IPushDeviceDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pushDeviceDo) Create(values ...*model.PushDevice) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pushDeviceDo) CreateInBatches(values []*model.PushDevice, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pushDeviceDo) Save(values ...*model.PushDevice) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pushDeviceDo) First() (*model.PushDevice, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushDevice), nil
	}
}

func (p pushDeviceDo) Take() (*model.PushDevice, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushDevice), nil
	}
}

func (p pushDeviceDo) Last() (*model.PushDevice, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushDevice), nil
	}
}

func (p pushDeviceDo) Find() ([]*model.PushDevice, error) {
	result, err := p.DO.Find()
	return result.([]*model.PushDevice), err
}

func (p pushDeviceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PushDevice, err error) {
	buf := make([]*model.PushDevice, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pushDeviceDo) FindInBatches(result *[]*model.PushDevice, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pushDeviceDo) Attrs(attrs ...field.AssignExpr) IPushDeviceDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pushDeviceDo) Assign(attrs ...field.AssignExpr) IPushDeviceDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pushDeviceDo) Joins(fields ...field.RelationField) IPushDeviceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pushDeviceDo) Preload(fields ...field.RelationField) IPushDeviceDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pushDeviceDo) FirstOrInit() (*model.PushDevice, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushDevice), nil
	}
}

func (p pushDeviceDo) FirstOrCreate() (*model.PushDevice, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushDevice), nil
	}
}

func (p pushDeviceDo) FindByPage(offset int, limit int) (result []*model.PushDevice, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pushDeviceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pushDeviceDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pushDeviceDo) Delete(models ...*model.PushDevice) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pushDeviceDo) withDO(do gen.Dao) *pushDeviceDo {
	p.DO = *do.(*gen.DO)
	return p
}
