// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/notification/dal/model"
)

func newPushRecord(db *gorm.DB, opts ...gen.DOOption) pushRecord {
	_pushRecord := pushRecord{}

	_pushRecord.pushRecordDo.UseDB(db, opts...)
	_pushRecord.pushRecordDo.UseModel(&model.PushRecord{})

	tableName := _pushRecord.pushRecordDo.TableName()
	_pushRecord.ALL = field.NewAsterisk(tableName)
	_pushRecord.PushRecordID = field.NewInt64(tableName, "push_record_id")
	_pushRecord.Title = field.NewString(tableName, "title")
	_pushRecord.Content = field.NewString(tableName, "content")
	_pushRecord.RelateType = field.NewString(tableName, "relate_type")
	_pushRecord.RelateID = field.NewString(tableName, "relate_id")
	_pushRecord.RelateScene = field.NewString(tableName, "relate_scene")
	_pushRecord.BatchID = field.NewInt32(tableName, "batch_id")
	_pushRecord.MessageID = field.NewString(tableName, "message_id")
	_pushRecord.Provider = field.NewString(tableName, "provider")
	_pushRecord.AudienceType = field.NewString(tableName, "audience_type")
	_pushRecord.AudienceValue = field.NewField(tableName, "audience_value")
	_pushRecord.Payload = field.NewField(tableName, "payload")
	_pushRecord.SendCount = field.NewInt32(tableName, "send_count")
	_pushRecord.Status = field.NewInt32(tableName, "status")
	_pushRecord.CreatedAt = field.NewTime(tableName, "created_at")
	_pushRecord.UpdatedAt = field.NewTime(tableName, "updated_at")

	_pushRecord.fillFieldMap()

	return _pushRecord
}

// pushRecord 推送记录表
type pushRecord struct {
	pushRecordDo

	ALL           field.Asterisk
	PushRecordID  field.Int64  // 主键 id
	Title         field.String // 推送标题
	Content       field.String // 推送内容
	RelateType    field.String // 关联类型，行情异动：market_changes
	RelateID      field.String // 关联资源 id
	RelateScene   field.String // 关联场景
	BatchID       field.Int32  // 批次 id
	MessageID     field.String // 推送服务商返回的消息 id
	Provider      field.String // 推送服务商，极光：jpush
	AudienceType  field.String // 受众类型(user/device/all)
	AudienceValue field.Field  // 受众值
	Payload       field.Field  // 附加信息
	SendCount     field.Int32  // 发送设备数
	Status        field.Int32  // 状态【1：已提交，2：成功，3：失败】
	CreatedAt     field.Time   // 创建时间
	UpdatedAt     field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (p pushRecord) Table(newTableName string) *pushRecord {
	p.pushRecordDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p pushRecord) As(alias string) *pushRecord {
	p.pushRecordDo.DO = *(p.pushRecordDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *pushRecord) updateTableName(table string) *pushRecord {
	p.ALL = field.NewAsterisk(table)
	p.PushRecordID = field.NewInt64(table, "push_record_id")
	p.Title = field.NewString(table, "title")
	p.Content = field.NewString(table, "content")
	p.RelateType = field.NewString(table, "relate_type")
	p.RelateID = field.NewString(table, "relate_id")
	p.RelateScene = field.NewString(table, "relate_scene")
	p.BatchID = field.NewInt32(table, "batch_id")
	p.MessageID = field.NewString(table, "message_id")
	p.Provider = field.NewString(table, "provider")
	p.AudienceType = field.NewString(table, "audience_type")
	p.AudienceValue = field.NewField(table, "audience_value")
	p.Payload = field.NewField(table, "payload")
	p.SendCount = field.NewInt32(table, "send_count")
	p.Status = field.NewInt32(table, "status")
	p.CreatedAt = field.NewTime(table, "created_at")
	p.UpdatedAt = field.NewTime(table, "updated_at")

	p.fillFieldMap()

	return p
}

func (p *pushRecord) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *pushRecord) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 16)
	p.fieldMap["push_record_id"] = p.PushRecordID
	p.fieldMap["title"] = p.Title
	p.fieldMap["content"] = p.Content
	p.fieldMap["relate_type"] = p.RelateType
	p.fieldMap["relate_id"] = p.RelateID
	p.fieldMap["relate_scene"] = p.RelateScene
	p.fieldMap["batch_id"] = p.BatchID
	p.fieldMap["message_id"] = p.MessageID
	p.fieldMap["provider"] = p.Provider
	p.fieldMap["audience_type"] = p.AudienceType
	p.fieldMap["audience_value"] = p.AudienceValue
	p.fieldMap["payload"] = p.Payload
	p.fieldMap["send_count"] = p.SendCount
	p.fieldMap["status"] = p.Status
	p.fieldMap["created_at"] = p.CreatedAt
	p.fieldMap["updated_at"] = p.UpdatedAt
}

func (p pushRecord) clone(db *gorm.DB) pushRecord {
	p.pushRecordDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p pushRecord) replaceDB(db *gorm.DB) pushRecord {
	p.pushRecordDo.ReplaceDB(db)
	return p
}

type pushRecordDo struct{ gen.DO }

type IPushRecordDo interface {
	gen.SubQuery
	Debug() IPushRecordDo
	WithContext(ctx context.Context) IPushRecordDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IPushRecordDo
	WriteDB() IPushRecordDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IPushRecordDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IPushRecordDo
	Not(conds ...gen.Condition) IPushRecordDo
	Or(conds ...gen.Condition) IPushRecordDo
	Select(conds ...field.Expr) IPushRecordDo
	Where(conds ...gen.Condition) IPushRecordDo
	Order(conds ...field.Expr) IPushRecordDo
	Distinct(cols ...field.Expr) IPushRecordDo
	Omit(cols ...field.Expr) IPushRecordDo
	Join(table schema.Tabler, on ...field.Expr) IPushRecordDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IPushRecordDo
	RightJoin(table schema.Tabler, on ...field.Expr) IPushRecordDo
	Group(cols ...field.Expr) IPushRecordDo
	Having(conds ...gen.Condition) IPushRecordDo
	Limit(limit int) IPushRecordDo
	Offset(offset int) IPushRecordDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IPushRecordDo
	Unscoped() IPushRecordDo
	Create(values ...*model.PushRecord) error
	CreateInBatches(values []*model.PushRecord, batchSize int) error
	Save(values ...*model.PushRecord) error
	First() (*model.PushRecord, error)
	Take() (*model.PushRecord, error)
	Last() (*model.PushRecord, error)
	Find() ([]*model.PushRecord, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PushRecord, err error)
	FindInBatches(result *[]*model.PushRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.PushRecord) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IPushRecordDo
	Assign(attrs ...field.AssignExpr) IPushRecordDo
	Joins(fields ...field.RelationField) IPushRecordDo
	Preload(fields ...field.RelationField) IPushRecordDo
	FirstOrInit() (*model.PushRecord, error)
	FirstOrCreate() (*model.PushRecord, error)
	FindByPage(offset int, limit int) (result []*model.PushRecord, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IPushRecordDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (p pushRecordDo) Debug() IPushRecordDo {
	return p.withDO(p.DO.Debug())
}

func (p pushRecordDo) WithContext(ctx context.Context) IPushRecordDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p pushRecordDo) ReadDB() IPushRecordDo {
	return p.Clauses(dbresolver.Read)
}

func (p pushRecordDo) WriteDB() IPushRecordDo {
	return p.Clauses(dbresolver.Write)
}

func (p pushRecordDo) Session(config *gorm.Session) IPushRecordDo {
	return p.withDO(p.DO.Session(config))
}

func (p pushRecordDo) Clauses(conds ...clause.Expression) IPushRecordDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p pushRecordDo) Returning(value interface{}, columns ...string) IPushRecordDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p pushRecordDo) Not(conds ...gen.Condition) IPushRecordDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p pushRecordDo) Or(conds ...gen.Condition) IPushRecordDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p pushRecordDo) Select(conds ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p pushRecordDo) Where(conds ...gen.Condition) IPushRecordDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p pushRecordDo) Order(conds ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p pushRecordDo) Distinct(cols ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p pushRecordDo) Omit(cols ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p pushRecordDo) Join(table schema.Tabler, on ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p pushRecordDo) LeftJoin(table schema.Tabler, on ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p pushRecordDo) RightJoin(table schema.Tabler, on ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p pushRecordDo) Group(cols ...field.Expr) IPushRecordDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p pushRecordDo) Having(conds ...gen.Condition) IPushRecordDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p pushRecordDo) Limit(limit int) IPushRecordDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p pushRecordDo) Offset(offset int) IPushRecordDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p pushRecordDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IPushRecordDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p pushRecordDo) Unscoped() IPushRecordDo {
	return p.withDO(p.DO.Unscoped())
}

func (p pushRecordDo) Create(values ...*model.PushRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p pushRecordDo) CreateInBatches(values []*model.PushRecord, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p pushRecordDo) Save(values ...*model.PushRecord) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p pushRecordDo) First() (*model.PushRecord, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushRecord), nil
	}
}

func (p pushRecordDo) Take() (*model.PushRecord, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushRecord), nil
	}
}

func (p pushRecordDo) Last() (*model.PushRecord, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushRecord), nil
	}
}

func (p pushRecordDo) Find() ([]*model.PushRecord, error) {
	result, err := p.DO.Find()
	return result.([]*model.PushRecord), err
}

func (p pushRecordDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.PushRecord, err error) {
	buf := make([]*model.PushRecord, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p pushRecordDo) FindInBatches(result *[]*model.PushRecord, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p pushRecordDo) Attrs(attrs ...field.AssignExpr) IPushRecordDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p pushRecordDo) Assign(attrs ...field.AssignExpr) IPushRecordDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p pushRecordDo) Joins(fields ...field.RelationField) IPushRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p pushRecordDo) Preload(fields ...field.RelationField) IPushRecordDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p pushRecordDo) FirstOrInit() (*model.PushRecord, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushRecord), nil
	}
}

func (p pushRecordDo) FirstOrCreate() (*model.PushRecord, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.PushRecord), nil
	}
}

func (p pushRecordDo) FindByPage(offset int, limit int) (result []*model.PushRecord, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p pushRecordDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p pushRecordDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p pushRecordDo) Delete(models ...*model.PushRecord) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *pushRecordDo) withDO(do gen.Dao) *pushRecordDo {
	p.DO = *do.(*gen.DO)
	return p
}
