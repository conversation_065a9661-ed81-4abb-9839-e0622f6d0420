package logic

import (
	"app_service/apps/business/notification/define"
	"app_service/apps/business/notification/define/enums"
	"app_service/apps/business/notification/repo"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"context"
	"errors"
	"fmt"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

func CheckPushMessage(ctx context.Context, message define.PushMessage, relateInfo define.PushRelateInfo) error {
	// 检查是否已经发送了
	pushRecordSchema := repo.GetQuery().PushRecord
	recordQb := search.NewQueryBuilder().
		Eq(pushRecordSchema.RelateType, relateInfo.RelateType.Val()).
		Eq(pushRecordSchema.RelateScene, relateInfo.RelateScene.Val()).
		Eq(pushRecordSchema.RelateID, relateInfo.RelateID).
		Eq(pushRecordSchema.AudienceType, message.AudienceType.Val()).
		Build()
	record, err := repo.NewPushRecordRepo(pushRecordSchema.WithContext(ctx)).SelectOne(recordQb)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return err
	}
	if record != nil {
		return define.NT310004Err
	}

	if message.AudienceType == enums.PushAudienceTypeAll {
		// 检查是否超限，每天最多发 10 次
		if !allowPushAll(ctx) {
			return define.NT310003Err
		}
	}

	switch message.AudienceType {
	case enums.PushAudienceTypeChannel:
		if len(message.ChannelIDs) == 0 {
			return define.NT310006Err.SetMsg(fmt.Sprintf("推送听众类型为 %s，message.ChannelIDs 不能为空！", message.AudienceType.Val()))
		}
	case enums.PushAudienceTypeUser:
		if len(message.UserIDs) == 0 {
			return define.NT310006Err.SetMsg(fmt.Sprintf("推送听众类型为 %s，message.UserIDs 不能为空！", message.AudienceType.Val()))
		}
	case enums.PushAudienceTypeDevice:
		if len(message.DeviceIDs) == 0 {
			return define.NT310006Err.SetMsg(fmt.Sprintf("推送听众类型为 %s，message.DeviceIDs 不能为空！", message.AudienceType.Val()))
		}
	case enums.PushAudienceTypePushID:
		if len(message.PushIDs) == 0 {
			return define.NT310006Err.SetMsg(fmt.Sprintf("推送听众类型为 %s，message.PushIDs 不能为空！", message.AudienceType.Val()))
		}
	}

	return nil
}

func getPushAllLimitKey() string {
	dateStr := util.Now().Format("2006-01-02")
	return fmt.Sprintf("app_service:notification:push_all_count:%s", dateStr)
}
func allowPushAll(ctx context.Context) bool {
	cacheKey := getPushAllLimitKey()
	luaScript := `
	local current = redis.call("INCR", KEYS[1])
	if current == 1 then
		redis.call("EXPIRE", KEYS[1], tonumber(ARGV[1]))
	end
	return current
	`
	expireIn := 60 * 60 * 24
	count, err := global.REDIS.Eval(ctx, luaScript, []string{cacheKey}, expireIn).Int()
	if err != nil {
		log.Ctx(ctx).Errorf("allowPushAll exec lua script error, %v", err)
		return false
	}

	// 每天最多广播 10 次
	return count <= 10
}

func RollbackPushAllLimit(ctx context.Context) {
	cacheKey := getPushAllLimitKey()
	_, err := global.REDIS.Decr(ctx, cacheKey).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("RollbackPushAllLimit exec redis cache error, %v", err)
	}
}
