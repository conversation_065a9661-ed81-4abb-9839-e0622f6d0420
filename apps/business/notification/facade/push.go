package facade

import (
	"app_service/apps/business/notification/define"
	"app_service/apps/business/notification/define/enums"
	"app_service/apps/business/notification/facade/logic"
	"app_service/apps/business/notification/facade/providers"
	"app_service/global"
	"context"
)

// PushMessage 推送消息通用方法
func PushMessage(ctx context.Context, message define.PushMessage, relateInfo define.PushRelateInfo) (*define.PushResult, error) {
	// 校验参数
	err := logic.CheckPushMessage(ctx, message, relateInfo)
	if err != nil {
		return nil, err
	}

	// 极光推送
	pushProvider := providers.NewJPushProvider(global.JPushAPIv3)

	result, err := pushProvider.Send(ctx, message, relateInfo)
	if err != nil {
		if message.AudienceType == enums.PushAudienceTypeAll {
			// 回滚广播推送限制
			logic.RollbackPushAllLimit(ctx)
		}
		return nil, err
	}

	return result, nil
}
