package web

import (
	"app_service/apps/business/notification/api/web"
	"github.com/gin-gonic/gin"
)

func Notification(router *gin.RouterGroup) {
	group := router.Group("/notification")
	{
		deviceGroup := group.Group("/device")
		// 注册设备
		deviceGroup.POST("/register", web.RegisterDevice)
		// 更新设备状态
		deviceGroup.POST("/update_status", web.UpdateDeviceStatus)
	}

	{
		pushGroup := group.Group("/push")
		// 测试推送
		pushGroup.POST("/test", web.TestPush)
	}
}
