package define

import (
	"app_service/apps/business/notification/define/enums"
	"time"
)

type (
	PushMessage struct {
		Title          string                     `json:"title"`
		Content        string                     `json:"content"`
		Platforms      []enums.PushPlatformEnum   `json:"platform"` // 指定平台，默认全部平台
		AudienceType   enums.PushAudienceTypeEnum `json:"audience_type"`
		ChannelIDs     []string                   `json:"channel_ids"`
		UserIDs        []string                   `json:"user_ids"`
		DeviceIDs      []string                   `json:"device_ids"`
		PushIDs        []string                   `json:"push_ids"`
		AndroidExtras  map[string]interface{}     `json:"android_extras"`
		IOSExtras      map[string]interface{}     `json:"ios_extras"`
		ApnsProduction *bool                      `json:"apns_production"`
	}
	PushRelateInfo struct {
		RelateType  enums.PushRelateTypeEnum  `json:"relate_type"`
		RelateID    string                    `json:"relate_id"`
		RelateScene enums.PushRelateSceneEnum `json:"relate_scene"`
		BatchID     int32                     `json:"batch_id"`
	}
	PushResult struct {
		SendTotal    int32         `json:"send_total"`
		SuccessCount int32         `json:"success_count"`
		FailCount    int32         `json:"fail_count"`
		Duration     time.Duration `json:"duration"`
		MessageID    string        `json:"message_id"`
	}
)
