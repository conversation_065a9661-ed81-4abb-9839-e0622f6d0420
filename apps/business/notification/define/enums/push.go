package enums

type PushAudienceTypeEnum string

func (r PushAudienceTypeEnum) Val() string {
	return string(r)
}

const (
	PushAudienceTypeAll     PushAudienceTypeEnum = "all"     // 推送给所有用户（广播推送）
	PushAudienceTypeChannel PushAudienceTypeEnum = "channel" // 按照渠道/终端类型推送
	PushAudienceTypeUser    PushAudienceTypeEnum = "user"    // 指定用户 id 推送
	PushAudienceTypeDevice  PushAudienceTypeEnum = "device"  // 指定设备 id 推送
	PushAudienceTypePushID  PushAudienceTypeEnum = "push_id" // 指定推送 id 推送
)

type PushPlatformEnum string

func (r PushPlatformEnum) Val() string {
	return string(r)
}

const (
	PushPlatformAndroid   PushPlatformEnum = "android"
	PushPlatformIOS       PushPlatformEnum = "ios"
	PushPlatformQuickApp  PushPlatformEnum = "quickapp" // 快应用
	PushPlatformHarmonyOS PushPlatformEnum = "hmos"     // 鸿蒙
)

// PushRelateTypeEnum 推送关联类型枚举
type PushRelateTypeEnum string

func (r PushRelateTypeEnum) Val() string {
	return string(r)
}

const (
	PushRelateTypeCardCommunityOrder   PushRelateTypeEnum = "card_community_order"   // 卡牌社区订单
	PushRelateTypeCardCommunityMessage PushRelateTypeEnum = "card_community_message" // 卡牌社区消息
)

// PushRelateSceneEnum 推送关联场景枚举
type PushRelateSceneEnum string

func (r PushRelateSceneEnum) Val() string {
	return string(r)
}

const (
	PushRelateSceneUnPaid      PushRelateSceneEnum = "un_paid"      // 待支付
	PushRelateSceneUnDelivered PushRelateSceneEnum = "un_delivered" // 待发货
	PushRelateSceneUnReceived  PushRelateSceneEnum = "un_received"  // 待收货
	PushRelateSceneCompleted   PushRelateSceneEnum = "completed"    // 已完成
	PushRelateSceneNewMessage  PushRelateSceneEnum = "new_message"  // 新消息
)
