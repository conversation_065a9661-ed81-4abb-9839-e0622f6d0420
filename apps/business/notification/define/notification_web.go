package define

type (
	RegisterDeviceReq struct {
		DeviceID    string `json:"device_id" binding:"required"`                  // 设备唯一标识符
		Provider    string `json:"provider" binding:"required,oneof=jpush"`       // 推送服务提供商
		Platform    string `json:"platform" binding:"required,oneof=ios android"` // 设备平台
		PushID      string `json:"push_id" binding:"required"`                    // 推送 id，极光传 registration_id
		DeviceModel string `json:"device_model"`                                  // 设备型号
		OSVersion   string `json:"os_version"`                                    // 设备系统版本
	}
	RegisterDeviceResp struct {
		ID        int64  `json:"id,string"`
		CreatedAt string `json:"created_at"`
	}
)

type (
	UpdateDeviceStatusReq struct {
		DeviceID string `json:"device_id" binding:"required"`        // 设备唯一标识符
		Status   int32  `json:"status" binding:"required,oneof=1 2"` // 设备状态，1：在线，2：离线
	}
	UpdateDeviceStatusResp struct {
		ID        int64  `json:"id,string"`
		UpdatedAt string `json:"updated_at"`
	}
)

type (
	TestPushReq struct {
		PushIDs        []string               `json:"push_ids" bind:"required"`   // 推送 id 列表（极光推送填注册 id）
		Title          string                 `json:"title" binding:"required"`   // 推送标题
		Content        string                 `json:"content" binding:"required"` // 推送内容
		Extras         map[string]interface{} `json:"extras"`                     // 额外参数，如：{"url": "ojb://message_list?type=2"}
		ApnsProduction *bool                  `json:"apns_production"`            // 是否为生产环境，iOS 专用
	}
	TestPushResp struct {
		MessageID string `json:"message_id"` // 消息 id
	}
)
