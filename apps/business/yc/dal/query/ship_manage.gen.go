// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/yc/dal/model"
)

func newShipManage(db *gorm.DB, opts ...gen.DOOption) shipManage {
	_shipManage := shipManage{}

	_shipManage.shipManageDo.UseDB(db, opts...)
	_shipManage.shipManageDo.UseModel(&model.ShipManage{})

	tableName := _shipManage.shipManageDo.TableName()
	_shipManage.ALL = field.NewAsterisk(tableName)
	_shipManage.ShipManageID = field.NewInt64(tableName, "ship_manage_id")
	_shipManage.YcID = field.NewString(tableName, "yc_id")
	_shipManage.WithdrawType = field.NewInt32(tableName, "withdraw_type")
	_shipManage.OrderType = field.NewInt32(tableName, "order_type")
	_shipManage.CreatedAt = field.NewTime(tableName, "created_at")
	_shipManage.UpdatedAt = field.NewTime(tableName, "updated_at")

	_shipManage.fillFieldMap()

	return _shipManage
}

// shipManage 云仓发货管理表
type shipManage struct {
	shipManageDo

	ALL          field.Asterisk
	ShipManageID field.Int64  // 主键ID
	YcID         field.String // 云仓订单 id
	WithdrawType field.Int32  // 提货方式：1：手动提货 2：强制发货
	OrderType    field.Int32  // 订单来源：1：云仓提货 2：文潮提货
	CreatedAt    field.Time   // 创建时间
	UpdatedAt    field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (s shipManage) Table(newTableName string) *shipManage {
	s.shipManageDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s shipManage) As(alias string) *shipManage {
	s.shipManageDo.DO = *(s.shipManageDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *shipManage) updateTableName(table string) *shipManage {
	s.ALL = field.NewAsterisk(table)
	s.ShipManageID = field.NewInt64(table, "ship_manage_id")
	s.YcID = field.NewString(table, "yc_id")
	s.WithdrawType = field.NewInt32(table, "withdraw_type")
	s.OrderType = field.NewInt32(table, "order_type")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")

	s.fillFieldMap()

	return s
}

func (s *shipManage) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *shipManage) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 6)
	s.fieldMap["ship_manage_id"] = s.ShipManageID
	s.fieldMap["yc_id"] = s.YcID
	s.fieldMap["withdraw_type"] = s.WithdrawType
	s.fieldMap["order_type"] = s.OrderType
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
}

func (s shipManage) clone(db *gorm.DB) shipManage {
	s.shipManageDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s shipManage) replaceDB(db *gorm.DB) shipManage {
	s.shipManageDo.ReplaceDB(db)
	return s
}

type shipManageDo struct{ gen.DO }

type IShipManageDo interface {
	gen.SubQuery
	Debug() IShipManageDo
	WithContext(ctx context.Context) IShipManageDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IShipManageDo
	WriteDB() IShipManageDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IShipManageDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IShipManageDo
	Not(conds ...gen.Condition) IShipManageDo
	Or(conds ...gen.Condition) IShipManageDo
	Select(conds ...field.Expr) IShipManageDo
	Where(conds ...gen.Condition) IShipManageDo
	Order(conds ...field.Expr) IShipManageDo
	Distinct(cols ...field.Expr) IShipManageDo
	Omit(cols ...field.Expr) IShipManageDo
	Join(table schema.Tabler, on ...field.Expr) IShipManageDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IShipManageDo
	RightJoin(table schema.Tabler, on ...field.Expr) IShipManageDo
	Group(cols ...field.Expr) IShipManageDo
	Having(conds ...gen.Condition) IShipManageDo
	Limit(limit int) IShipManageDo
	Offset(offset int) IShipManageDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IShipManageDo
	Unscoped() IShipManageDo
	Create(values ...*model.ShipManage) error
	CreateInBatches(values []*model.ShipManage, batchSize int) error
	Save(values ...*model.ShipManage) error
	First() (*model.ShipManage, error)
	Take() (*model.ShipManage, error)
	Last() (*model.ShipManage, error)
	Find() ([]*model.ShipManage, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ShipManage, err error)
	FindInBatches(result *[]*model.ShipManage, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ShipManage) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IShipManageDo
	Assign(attrs ...field.AssignExpr) IShipManageDo
	Joins(fields ...field.RelationField) IShipManageDo
	Preload(fields ...field.RelationField) IShipManageDo
	FirstOrInit() (*model.ShipManage, error)
	FirstOrCreate() (*model.ShipManage, error)
	FindByPage(offset int, limit int) (result []*model.ShipManage, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IShipManageDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (s shipManageDo) Debug() IShipManageDo {
	return s.withDO(s.DO.Debug())
}

func (s shipManageDo) WithContext(ctx context.Context) IShipManageDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s shipManageDo) ReadDB() IShipManageDo {
	return s.Clauses(dbresolver.Read)
}

func (s shipManageDo) WriteDB() IShipManageDo {
	return s.Clauses(dbresolver.Write)
}

func (s shipManageDo) Session(config *gorm.Session) IShipManageDo {
	return s.withDO(s.DO.Session(config))
}

func (s shipManageDo) Clauses(conds ...clause.Expression) IShipManageDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s shipManageDo) Returning(value interface{}, columns ...string) IShipManageDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s shipManageDo) Not(conds ...gen.Condition) IShipManageDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s shipManageDo) Or(conds ...gen.Condition) IShipManageDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s shipManageDo) Select(conds ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s shipManageDo) Where(conds ...gen.Condition) IShipManageDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s shipManageDo) Order(conds ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s shipManageDo) Distinct(cols ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s shipManageDo) Omit(cols ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s shipManageDo) Join(table schema.Tabler, on ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s shipManageDo) LeftJoin(table schema.Tabler, on ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s shipManageDo) RightJoin(table schema.Tabler, on ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s shipManageDo) Group(cols ...field.Expr) IShipManageDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s shipManageDo) Having(conds ...gen.Condition) IShipManageDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s shipManageDo) Limit(limit int) IShipManageDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s shipManageDo) Offset(offset int) IShipManageDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s shipManageDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IShipManageDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s shipManageDo) Unscoped() IShipManageDo {
	return s.withDO(s.DO.Unscoped())
}

func (s shipManageDo) Create(values ...*model.ShipManage) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s shipManageDo) CreateInBatches(values []*model.ShipManage, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s shipManageDo) Save(values ...*model.ShipManage) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s shipManageDo) First() (*model.ShipManage, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShipManage), nil
	}
}

func (s shipManageDo) Take() (*model.ShipManage, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShipManage), nil
	}
}

func (s shipManageDo) Last() (*model.ShipManage, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShipManage), nil
	}
}

func (s shipManageDo) Find() ([]*model.ShipManage, error) {
	result, err := s.DO.Find()
	return result.([]*model.ShipManage), err
}

func (s shipManageDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ShipManage, err error) {
	buf := make([]*model.ShipManage, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s shipManageDo) FindInBatches(result *[]*model.ShipManage, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s shipManageDo) Attrs(attrs ...field.AssignExpr) IShipManageDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s shipManageDo) Assign(attrs ...field.AssignExpr) IShipManageDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s shipManageDo) Joins(fields ...field.RelationField) IShipManageDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s shipManageDo) Preload(fields ...field.RelationField) IShipManageDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s shipManageDo) FirstOrInit() (*model.ShipManage, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShipManage), nil
	}
}

func (s shipManageDo) FirstOrCreate() (*model.ShipManage, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ShipManage), nil
	}
}

func (s shipManageDo) FindByPage(offset int, limit int) (result []*model.ShipManage, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s shipManageDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s shipManageDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s shipManageDo) Delete(models ...*model.ShipManage) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *shipManageDo) withDO(do gen.Dao) *shipManageDo {
	s.DO = *do.(*gen.DO)
	return s
}
