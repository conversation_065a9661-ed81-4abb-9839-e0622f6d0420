// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameShipManage = "ship_manage"

// ShipManage 云仓发货管理表
type ShipManage struct {
	ShipManageID int64     `gorm:"column:ship_manage_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键ID" json:"ship_manage_id"` // 主键ID
	YcID         string    `gorm:"column:yc_id;type:varchar(255);not null;comment:云仓订单 id" json:"yc_id"`                                        // 云仓订单 id
	WithdrawType int32     `gorm:"column:withdraw_type;type:tinyint;not null;comment:提货方式：1：手动提货 2：强制发货" json:"withdraw_type"`                  // 提货方式：1：手动提货 2：强制发货
	OrderType    int32     `gorm:"column:order_type;type:tinyint;not null;comment:订单来源：1：云仓提货 2：文潮提货" json:"order_type"`                        // 订单来源：1：云仓提货 2：文潮提货
	CreatedAt    time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`           // 创建时间
	UpdatedAt    time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`           // 更新时间
}

// TableName ShipManage's table name
func (*ShipManage) TableName() string {
	return TableNameShipManage
}
