package admin

import (
	"app_service/apps/business/yc/api/admin"

	"github.com/gin-gonic/gin"
)

// ShipManage 发货管理相关路由
func ShipManage(router *gin.RouterGroup) {
	group := router.Group("/ship_manage")
	{
		// 发货管理列表
		group.GET("/list", admin.GetShipManageList)
		// 发货管理列表导出
		group.GET("/list_export", admin.GetShipManageListExport)
		// 发货管理详情
		group.GET("/detail", admin.GetShipManageDetail)
		// 查看手机号
		group.GET("/mobile_phone", admin.GetShipManageMobilePhone)
		// 发货管理部分更新
		group.PUT("/edit", admin.EditShipManage)
		// 订单操作日志
		group.GET("/orderLogs/:id", admin.GetShipManageOrderLogs)
		// 同步物流
		group.POST("/freight_sync", admin.FreightSync)
		// 批量强制发货
		group.POST("/forced_shipment_batch", admin.ForcedShipmentBatch)
	}
}
