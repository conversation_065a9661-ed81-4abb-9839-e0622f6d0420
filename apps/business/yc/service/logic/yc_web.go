package logic

import (
	"app_service/apps/business/story/define/enums"
	storyRepo "app_service/apps/business/story/repo"
	synthesisRepo "app_service/apps/business/synthesis/repo"
	"app_service/apps/business/yc/define"
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/search"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"time"
)

var shanghaiLoc *time.Location // 统一时区处理（如 UTC+8）
func init() {
	loc, err := time.LoadLocation("Asia/Shanghai")
	if err != nil {
		panic(err)
	}
	shanghaiLoc = loc
}

// GetStartOfDay 获取当天的零点时间（上海时区）
func GetStartOfDay(t time.Time) time.Time {
	// 转换为上海时区
	locTime := t.In(shanghaiLoc)
	// 获取当天零点
	return time.Date(locTime.Year(), locTime.Month(), locTime.Day(), 0, 0, 0, 0, shanghaiLoc)
}

// GetReleaseTimeByUserItemIds 根据 userItemIDs 获取释放时间
func GetReleaseTimeByUserItemIds(ctx context.Context, userItemList []*define.UserItem) (map[string]*time.Time, error) {
	var storyUserItemIds []string
	var synthesisUserItemIds []string
	for _, userItem := range userItemList {
		if userItem.FlowStatus == define.FlowStatusStory {
			storyUserItemIds = append(storyUserItemIds, userItem.ID)
		} else if userItem.FlowStatus == define.FlowStatusFused {
			synthesisUserItemIds = append(synthesisUserItemIds, userItem.ID)
		}

	}
	storyMaterialsReleaseSchema := storyRepo.GetQuery().StoryMaterialsRelease
	storyUserItemList, err := storyRepo.NewStoryMaterialsReleaseRepo(storyMaterialsReleaseSchema.WithContext(ctx)).
		SelectList(search.NewQueryBuilder().In(storyMaterialsReleaseSchema.UserItemID, storyUserItemIds).
			Eq(storyMaterialsReleaseSchema.Status, enums.ReleaseCreated.Val()).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("GetReleaseTimeByUserItemIds SelectListStoryUserItem err:%v", err)
		return nil, err
	}

	releaseTimeMap := make(map[string]*time.Time)
	if len(storyUserItemList) > 0 {
		for _, item := range storyUserItemList {
			releaseTimeMap[item.UserItemID] = item.ReleaseTime
		}
	}

	synthesisMaterialsReleaseSchema := synthesisRepo.GetQuery().SynthesisMaterialsRelease
	synthesisUserItemList, err := synthesisRepo.NewSynthesisMaterialsReleaseRepo(synthesisMaterialsReleaseSchema.WithContext(ctx)).
		SelectList(search.NewQueryBuilder().In(synthesisMaterialsReleaseSchema.UserItemID, synthesisUserItemIds).
			Eq(synthesisMaterialsReleaseSchema.Status, enums.ReleaseCreated.Val()).Build())
	if err != nil {
		log.Ctx(ctx).Errorf("GetReleaseTimeByUserItemIds SelectListSynthesisUserItem err:%v", err)
		return nil, err
	}
	if len(synthesisUserItemList) > 0 {
		for _, item := range synthesisUserItemList {
			releaseTimeMap[item.UserItemID] = item.ReleaseTime
		}
	}

	return releaseTimeMap, nil
}

func CalculateReleaseTime(ctx context.Context, circulationStart, circulationEnd, releaseTime time.Time) int32 {
	// 强制转换为上海时区
	convertToShanghai := func(t time.Time) time.Time {
		return t.In(shanghaiLoc)
	}

	// 获取当前时间（上海时区）
	now := convertToShanghai(time.Now())

	// 标准化时间变量（全部转为上海时区日期）
	saleStartDay := GetStartOfDay(convertToShanghai(circulationStart))
	saleEndDay := GetStartOfDay(convertToShanghai(circulationEnd))
	releaseDay := GetStartOfDay(convertToShanghai(releaseTime))
	currentDay := GetStartOfDay(now)

	log.Ctx(ctx).Infof("saleStartDay: %v, saleEndDay: %v, releaseDay: %v, currentDay: %v",
		saleStartDay.Format("2006-01-02"),
		saleEndDay.Format("2006-01-02"),
		releaseDay.Format("2006-01-02"),
		currentDay.Format("2006-01-02"))

	// 1. 检查释放时间是否在销售期外
	if releaseDay.Before(saleStartDay) || releaseDay.After(saleEndDay) {
		// 如果早于销售开始时间，计算到销售开始的天数差
		if releaseDay.Before(saleStartDay) {
			days := int32(saleStartDay.Sub(currentDay).Hours() / 24)
			return days
		}
		return -1 // 晚于销售结束时间
	}

	// 2. 检查是否已经过了 releaseDay
	if currentDay.After(releaseDay) {
		return 0 // 已过释放时间
	}

	// 3. 检查是否是同一天
	if currentDay.Equal(releaseDay) {
		return 0 // 今天释放
	}

	daysDiff := int32(releaseDay.Sub(currentDay).Hours() / 24)
	return daysDiff
}

func SetUserItemsFlowStatus(ctx context.Context, userItemList []*define.UserItem, userItemId2ReleaseTime map[string]*time.Time, issueItem *mongdb.IssueItem) {
	if issueItem.CirculationStatus == mongdb.CirculationStatusProhibit {
		// 不允许流通
		for _, userItem := range userItemList {
			userItem.FlowStatus = define.FlowStatusForbidden
		}
	} else if issueItem.CirculationStatus == mongdb.CirculationStatusAllow && issueItem.CirculationStart != nil && issueItem.CirculationEnd != nil {
		// 允许流通
		for _, userItem := range userItemList {
			if !(userItem.FlowStatus == define.FlowStatusStory || userItem.FlowStatus == define.FlowStatusFused) {
				continue
			}

			releaseTime, exists := userItemId2ReleaseTime[userItem.ID]
			if !exists || releaseTime == nil {
				userItem.FlowStatus = define.FlowStatusForbidden
				continue
			}

			daysLeft := CalculateReleaseTime(ctx, *issueItem.CirculationStart, *issueItem.CirculationEnd, *releaseTime)
			switch daysLeft {
			case 0:
				userItem.FlowStatus = define.FlowStatusAvailable
			case -1:
				userItem.FlowStatus = define.FlowStatusForbidden
			default:
				userItem.FlowStatus = define.FlowStatusCountdown
				userItem.DaysUntilSellable = daysLeft
			}
		}
	}
}
