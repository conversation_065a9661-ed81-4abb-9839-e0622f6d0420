package logic

import (
	"app_service/apps/business/yc/dal/model"
	"app_service/apps/business/yc/define"
	"app_service/apps/business/yc/define/enums"
	"app_service/apps/business/yc/repo"
	"app_service/apps/business/yc/service/locker"
	common_define "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	issueMongodb "app_service/apps/platform/issue/dal/model/mongdb"
	issueFacade "app_service/apps/platform/issue/facade"
	userMongodb "app_service/apps/platform/user/dal/model/mongdb"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"app_service/third_party/tmt"
	yc_open "app_service/third_party/yc_open"
	yc_open_define "app_service/third_party/yc_open/define"
	"context"
	"errors"
	"fmt"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"github.com/samber/lo"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetOrderStatusText 获取订单状态文本
func GetOrderStatusText(status yc_open_define.ItemWithdrawOrderStatus) string {
	switch status {
	case yc_open_define.WithdrawOrderStatusAbandoned:
		return "已废弃"
	case yc_open_define.WithdrawOrderStatusDeleted:
		return "已删除"
	case yc_open_define.WithdrawOrderStatusExpired:
		return "已失效"
	case yc_open_define.WithdrawOrderStatusDone:
		return "已完成"
	case yc_open_define.WithdrawOrderStatusAftersaleing:
		return "售后中"
	case yc_open_define.WithdrawOrderStatusAfterSaleRefunding:
		return "退款中"
	case yc_open_define.WithdrawOrderStatusAfterSaleDone:
		return "已退款"
	case yc_open_define.WithdrawOrderStatusCreated:
		return "已下单"
	case yc_open_define.WithdrawOrderStatusLocked:
		return "物品已锁定"
	case yc_open_define.WithdrawOrderStatusChecked:
		return "物品已检查"
	case yc_open_define.WithdrawOrderStatusWaitForPay:
		return "待付款"
	case yc_open_define.WithdrawOrderStatusWaitForShip:
		return "待发货"
	case yc_open_define.WithdrawOrderStatusPartShiped:
		return "待发货"
	case yc_open_define.WithdrawOrderStatusShiped:
		return "已发货"
	case yc_open_define.WithdrawOrderStatusReceived:
		return "已收货"
	case yc_open_define.WithdrawOrderStatusWaitForMake:
		return "商品制作中"
	case yc_open_define.WithdrawOrderStatusWaitForDue:
		return "待付尾款"
	default:
		return "未知状态"
	}
}

// getShipManageByYcID 根据云仓ID获取发货管理信息
func getShipManageByYcID(ctx context.Context,
	ycID string) (*model.ShipManage, error) {
	shipManageSchema := repo.GetQuery().ShipManage
	builder := search.NewQueryBuilder().Eq(shipManageSchema.YcID, ycID)

	shipManage, err := repo.NewShipManageRepo(shipManageSchema.WithContext(ctx)).SelectOne(builder.Build())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.Ctx(ctx).Errorf("getShipManageByYcID failed, ycID: %s, error: %v", ycID, err)
		return nil, err
	}

	return shipManage, nil
}

func getShipManageByYcIDMap(ctx context.Context,
	ycIDs []string) (map[string]*model.ShipManage, error) {
	shipManageSchema := repo.GetQuery().ShipManage
	builder := search.NewQueryBuilder().In(shipManageSchema.YcID, ycIDs)

	shipManageList, err := repo.NewShipManageRepo(shipManageSchema.WithContext(ctx)).SelectList(builder.Build())
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		log.Ctx(ctx).Errorf("getShipManageByYcIDMap failed, ycIDs: %v, error: %v", ycIDs, err)
		return nil, err
	}

	shipManageMap := make(map[string]*model.ShipManage, len(shipManageList))
	for _, shipManage := range shipManageList {
		shipManageMap[shipManage.YcID] = shipManage
	}
	return shipManageMap, nil
}

// 过滤文潮商品
// 说明：
//   - 文潮商品：
//   - OrderType == OrderTypeWcBonusOrder  文潮金币商城
//   - ReceiveType == WCUserItemReceiveType 文潮
func filterWcItems(extend yc_open.OrderExtends) yc_open.OrderExtends {
	items := make([]yc_open.OrderExtendsItems, 0)
	for _, item := range extend.Items {
		if extend.OrderType == yc_open_define.OrderTypeWcBonusOrder ||
			lo.Contains(yc_open_define.WCUserItemReceiveType, item.ReceiveType) {
			items = append(items, item)
		}
	}
	extend.Items = items
	return extend
}

// EnrichShipManageInfoResult 封装 EnrichShipManageInfo 的返回结果
type EnrichShipManageInfoResult struct {
	Detail       *yc_open.WithdrawOrderDetail
	WithdrawType enums.WithdrawType
	OrderType    enums.OrderType
	ShipManageID int64
	OrderUser    define.OrderUser
	SumPayAmount int64
	Items        []define.OrderItem // 转换后的商品列表
}

// EnrichShipManageInfo 丰富发货管理信息
// 包括：计算商品成本、获取提货方式、订单来源、用户信息等
// 参数:
//   - ctx: 上下文
//   - ycID: 云仓订单ID
//
// 返回:
//   - *EnrichShipManageInfoResult: 包含订单详情、提货方式、订单来源、用户信息等
//   - error: 错误信息
func EnrichShipManageInfo(
	ctx context.Context,
	detail *yc_open.WithdrawOrderDetail,
	ycID string,
) (*EnrichShipManageInfoResult, error) {
	// 过滤文潮的商品
	detail.Extends = filterWcItems(detail.Extends)

	// 获取云仓背包实付金额
	userItemIds := make([]string, 0)
	for _, item := range detail.Extends.Items {
		userItemIds = append(userItemIds, item.UserItemIDs...)
	}

	// 计算商品成本
	costMap := make(map[string]int64)
	if len(userItemIds) > 0 {
		// 成本价
		costResList, err := yc_open.QueryUserItemsCostPrice(ctx, userItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("EnrichShipManageInfo QueryUserItemsCostPrice err:%v", err)
		} else {
			for _, item := range costResList {
				costMap[item.ID] = item.Cost
			}
		}
	}

	// 计算每个商品的PayAmount和总金额
	sumPayAmount := int64(0)
	for i, item := range detail.Extends.Items {
		cost := int64(0)
		for _, userItemID := range item.UserItemIDs {
			cost += costMap[userItemID]
		}
		detail.Extends.Items[i].PayAmount = cost
		sumPayAmount += cost
	}

	// 查询本地数据库获取提货方式和订单来源
	withdrawType := enums.WithdrawTypeManual
	orderType := enums.OrderTypeYcOpen
	shipManageID := int64(0)

	shipManage, err := getShipManageByYcID(ctx, ycID)
	if err != nil {
		log.Ctx(ctx).Errorf("GetShipManageByYcID failed: %v", err)
		return nil, err
	}

	// 如果本地数据库有记录，使用本地数据
	if shipManage != nil {
		withdrawType = enums.WithdrawType(shipManage.WithdrawType)
		orderType = enums.OrderType(shipManage.OrderType)
		shipManageID = shipManage.ShipManageID
	}

	// 转换用户信息
	var orderUser define.OrderUser
	user, err := userFacade.GetNodeUserByOpenUserId(ctx, detail.OpenUserID)
	if err != nil {
		log.Ctx(ctx).Errorf("GetNodeUserByOpenUserId failed: %v, openUserID: %s", err, detail.OpenUserID)
		// 测试环境脏数据，所以不中断继续执行
		//return orderUser, nil
	}

	// 手机号脱敏
	if user != nil {
		mobilePhone := ""
		if user.PatbgDetail.MobilePhone != "" {
			mobilePhone = util.PhoneMix(user.PatbgDetail.MobilePhone)
		}
		orderUser = define.OrderUser{
			Type: 1, // 默认类型为1，表示普通用户
			ID:   user.ID.Hex(),
			PatbgDetail: define.OrderPatbgDetail{
				Nickname:    user.PatbgDetail.Nickname,
				MobilePhone: mobilePhone,
				Avatar:      user.PatbgDetail.Avatar,
			},
		}
	}
	// 收货信息手机号脱敏
	if detail.Extends.Address.MobilePhone != "" {
		detail.Extends.Address.MobilePhone = util.PhoneMix(detail.Extends.Address.MobilePhone)
	}

	// 转换商品列表
	uniqueIssueItemIDs := make([]string, 0, len(detail.Extends.Items))
	uniqueSteamItemIDs := make([]string, 0, len(detail.Extends.Items))
	for _, item := range detail.Extends.Items {
		if detail.Extends.OrderType == yc_open_define.OrderTypeWcBonusOrder {
			uniqueSteamItemIDs = append(uniqueSteamItemIDs, item.ItemID)
		} else {
			uniqueIssueItemIDs = append(uniqueIssueItemIDs, item.ItemID)
		}
	}
	issueItemMap, err := issueFacade.GetIssueItemMap(ctx, uniqueIssueItemIDs)
	if err != nil {
		log.Ctx(ctx).Warnf("GetIssueItemMap failed: %v", err)
		issueItemMap = make(map[string]*issueMongodb.IssueItem)
	}
	steamItemMap, err := issueFacade.GetSteamItemMap(ctx, uniqueSteamItemIDs)
	if err != nil {
		log.Ctx(ctx).Warnf("GetSteamItemMap failed: %v", err)
		steamItemMap = make(map[string]*issueMongodb.SteamItem)
	}
	items := make([]define.OrderItem, 0, len(detail.Extends.Items))
	for _, item := range detail.Extends.Items {
		if detail.Extends.OrderType == yc_open_define.OrderTypeWcBonusOrder {
			steamItem, ok := steamItemMap[item.ItemID]
			if !ok {
				log.Ctx(ctx).Errorf("GetIssueItemMap failed: %v", err)
				continue
			}
			ipNames := make([]string, 0, len(steamItem.IPInfo))
			for _, ipInfo := range steamItem.IPInfo {
				ipNames = append(ipNames, ipInfo.Name)
			}
			items = append(items, define.OrderItem{
				ID:       steamItem.ID.Hex(),
				IconURL:  steamItem.IconURL,
				ItemName: steamItem.ItemName,
				ItemID:   steamItem.ID.Hex(),
				// Price:           steamItem.Price,
				SkuNo:           steamItem.SkuNo,
				IPClassifyNames: ipNames,
				ItemSpecs:       steamItem.Specs,
				//IssuerName:      steamItem.IssuerName,
				//IssuerShortName: steamItem.IssuerShortName,
			})
		} else {
			issueItem, ok := issueItemMap[item.ItemID]
			if !ok {
				log.Ctx(ctx).Errorf("GetIssueItemMap failed: %v", err)
				continue
			}
			items = append(items, define.OrderItem{
				ID:              issueItem.ID.Hex(),
				IconURL:         issueItem.ImageURL,
				ItemName:        issueItem.ItemName,
				ItemID:          issueItem.ItemID.Hex(),
				Price:           issueItem.Price,
				SkuNo:           issueItem.SkuNo,
				IPClassifyNames: issueItem.IPClassifyNames,
				ItemSpecs:       issueItem.ItemSpecs,
				IssuerName:      issueItem.IssuerName,
				IssuerShortName: issueItem.IssuerShortName,
			})
		}
		// 云仓那边商品状态是待发货和部分发货的数据，文潮这边都是待发货
		if item.Status == yc_open_define.WithdrawOrderStatusWaitForShip || item.Status == yc_open_define.WithdrawOrderStatusPartShiped {
			item.Status = yc_open_define.WithdrawOrderStatusWaitForShip
		}
	}

	return &EnrichShipManageInfoResult{
		Detail:       detail,
		WithdrawType: withdrawType,
		OrderType:    orderType,
		ShipManageID: shipManageID,
		OrderUser:    orderUser,
		SumPayAmount: sumPayAmount,
		Items:        items,
	}, nil
}

// EnrichShipManageInfoList 批量丰富发货管理信息
// 参数:
//   - ctx: 上下文
//   - items: 订单列表项
//
// 返回:
//   - map[string]*EnrichShipManageInfoResult: 包含所有订单的丰富信息，key为ycID
//   - error: 错误信息
func EnrichShipManageInfoList(
	ctx context.Context,
	withdrawOrderListItem []yc_open.WithdrawOrderListItem,
) (map[string]*EnrichShipManageInfoResult, error) {
	if len(withdrawOrderListItem) == 0 {
		return make(map[string]*EnrichShipManageInfoResult), nil
	}

	// 过滤文潮商品
	for i, item := range withdrawOrderListItem {
		withdrawOrderListItem[i].Extends = filterWcItems(item.Extends)
	}

	// 1. 收集所有需要批量查询的数据
	ycIDs := make([]string, 0, len(withdrawOrderListItem))
	openUserIDs := make([]string, 0, len(withdrawOrderListItem))
	uniqueIssueItemIDs := make([]string, 0, len(withdrawOrderListItem))
	uniqueSteamItemIDs := make([]string, 0, len(withdrawOrderListItem))
	userItemIds := make([]string, 0, len(withdrawOrderListItem))

	// 用于去重
	ycIDMap := make(map[string]bool)
	openUserIDMap := make(map[string]bool)
	issueItemIDMap := make(map[string]bool)
	steamItemIDMap := make(map[string]bool)
	// userItemIDMap := make(map[string]bool)

	for _, withdrawOrderItem := range withdrawOrderListItem {
		//if withdrawOrderItem == nil {
		//	continue
		//}

		// 收集云仓ID
		if !ycIDMap[withdrawOrderItem.ID] {
			ycIDs = append(ycIDs, withdrawOrderItem.ID)
			ycIDMap[withdrawOrderItem.ID] = true
		}

		// 收集用户ID
		if !openUserIDMap[withdrawOrderItem.OpenUserID] {
			openUserIDs = append(openUserIDs, withdrawOrderItem.OpenUserID)
			openUserIDMap[withdrawOrderItem.OpenUserID] = true
		}

		// 收集商品ID
		for _, item := range withdrawOrderItem.Extends.Items {
			if withdrawOrderItem.Extends.OrderType == yc_open_define.OrderTypeWcBonusOrder {
				if !steamItemIDMap[item.ItemID] {
					uniqueSteamItemIDs = append(uniqueSteamItemIDs, item.ItemID)
					steamItemIDMap[item.ItemID] = true
				}
			} else {
				if !issueItemIDMap[item.ItemID] {
					uniqueIssueItemIDs = append(uniqueIssueItemIDs, item.ItemID)
					issueItemIDMap[item.ItemID] = true
				}
			}
			userItemIds = append(userItemIds, item.UserItemIDs...)
		}
	}

	// 2. 批量查询用户信息
	openUserMap := make(map[string]*userMongodb.User)
	if len(openUserIDs) > 0 {
		var err error
		openUserMap, err = userFacade.GetNodeUserByOpenUserIdMap(ctx, openUserIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("GetNodeUserByOpenUserIdMap failed: %v", err)
			// 不中断流程，继续处理
		}
	}

	// 3. 批量查询发货管理信息
	shipManageMap := make(map[string]*model.ShipManage)
	if len(ycIDs) > 0 {
		var err error
		shipManageMap, err = getShipManageByYcIDMap(ctx, ycIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("getShipManageByYcIDMap failed: %v", err)
			// 不中断流程，继续处理
		}
	}

	// 4. 批量查询商品信息
	issueItemInfoMap := make(map[string]*issueMongodb.IssueItem)
	steamItemInfoMap := make(map[string]*issueMongodb.SteamItem)
	if len(uniqueIssueItemIDs) > 0 {
		var err error
		issueItemInfoMap, err = issueFacade.GetIssueItemMap(ctx, uniqueIssueItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("GetIssueItemMap failed: %v", err)
			issueItemInfoMap = make(map[string]*issueMongodb.IssueItem)
		}
	}
	if len(uniqueSteamItemIDs) > 0 {
		var err error
		steamItemInfoMap, err = issueFacade.GetSteamItemMap(ctx, uniqueSteamItemIDs)
		if err != nil {
			log.Ctx(ctx).Errorf("GetSteamItemMap failed: %v", err)
			steamItemInfoMap = make(map[string]*issueMongodb.SteamItem)
		}
	}

	// 计算商品成本
	costMap := make(map[string]int64)
	if len(userItemIds) > 0 {
		// 成本价
		costResList, err := yc_open.QueryUserItemsCostPrice(ctx, userItemIds)
		if err != nil {
			log.Ctx(ctx).Errorf("EnrichShipManageInfo QueryUserItemsCostPrice err:%v", err)
		} else {
			for _, item := range costResList {
				costMap[item.ID] = item.Cost
			}
		}
	}

	// 5. 处理每个订单
	result := make(map[string]*EnrichShipManageInfoResult, len(withdrawOrderListItem))

	for _, withdrawOrderItem := range withdrawOrderListItem {
		//if withdrawOrderItem == nil {
		//	continue
		//}

		// 获取用户信息
		var orderUser define.OrderUser
		if u, exists := openUserMap[withdrawOrderItem.OpenUserID]; exists && u != nil {
			mobilePhone := ""
			if u.PatbgDetail.MobilePhone != "" {
				mobilePhone = util.PhoneMix(u.PatbgDetail.MobilePhone)
			}
			orderUser = define.OrderUser{
				Type: 1, // 默认类型为1，表示普通用户
				ID:   u.ID.Hex(),
				PatbgDetail: define.OrderPatbgDetail{
					Nickname:    u.PatbgDetail.Nickname,
					MobilePhone: mobilePhone,
					Avatar:      u.PatbgDetail.Avatar,
				},
			}
		}

		// 获取发货管理信息
		withdrawType := enums.WithdrawTypeManual
		orderType := enums.OrderTypeYcOpen
		var shipManageID int64

		if sm, exists := shipManageMap[withdrawOrderItem.ID]; exists && sm != nil {
			withdrawType = enums.WithdrawType(sm.WithdrawType)
			orderType = enums.OrderType(sm.OrderType)
			shipManageID = sm.ShipManageID
		}

		// 计算每个商品的PayAmount和总金额
		sumPayAmount := int64(0)
		for i, itemDetail := range withdrawOrderItem.Extends.Items {
			cost := int64(0)
			for _, userItemID := range itemDetail.UserItemIDs {
				cost += costMap[userItemID]
			}
			withdrawOrderItem.Extends.Items[i].PayAmount = cost
			sumPayAmount += cost
		}

		// 处理商品列表
		itemsList := make([]define.OrderItem, 0, len(withdrawOrderItem.Extends.Items))
		for _, itemDetail := range withdrawOrderItem.Extends.Items {
			if withdrawOrderItem.Extends.OrderType == yc_open_define.OrderTypeWcBonusOrder {
				if steamItem, exists := steamItemInfoMap[itemDetail.ItemID]; exists && steamItem != nil {
					ipNames := make([]string, 0, len(steamItem.IPInfo))
					for _, ipInfo := range steamItem.IPInfo {
						ipNames = append(ipNames, ipInfo.Name)
					}
					itemsList = append(itemsList, define.OrderItem{
						ID:       steamItem.ID.Hex(),
						IconURL:  steamItem.IconURL,
						ItemName: steamItem.ItemName,
						ItemID:   steamItem.ID.Hex(),
						//Price:           steamItem.Price,
						SkuNo:           steamItem.SkuNo,
						IPClassifyNames: ipNames,
						ItemSpecs:       steamItem.Specs,
						//IssuerName:      steamItem.IssuerName,
						//IssuerShortName: steamItem.IssuerShortName,
					})
				}
			} else {
				if issueItem, exists := issueItemInfoMap[itemDetail.ItemID]; exists && issueItem != nil {
					itemsList = append(itemsList, define.OrderItem{
						ID:              issueItem.ID.Hex(),
						IconURL:         issueItem.ImageURL,
						ItemName:        issueItem.ItemName,
						ItemID:          issueItem.ItemID.Hex(),
						Price:           issueItem.Price,
						SkuNo:           issueItem.SkuNo,
						IPClassifyNames: issueItem.IPClassifyNames,
						ItemSpecs:       issueItem.ItemSpecs,
						IssuerName:      issueItem.IssuerName,
						IssuerShortName: issueItem.IssuerShortName,
					})
				}
			}
			// 云仓那边商品状态是待发货和部分发货的数据，文潮这边都是待发货
			if itemDetail.Status == yc_open_define.WithdrawOrderStatusWaitForShip || itemDetail.Status == yc_open_define.WithdrawOrderStatusPartShiped {
				itemDetail.Status = yc_open_define.WithdrawOrderStatusWaitForShip
			}
		}

		// 构建结果
		result[withdrawOrderItem.ID] = &EnrichShipManageInfoResult{
			Detail: &yc_open.WithdrawOrderDetail{
				ID:         withdrawOrderItem.ID,
				OpenUserID: withdrawOrderItem.OpenUserID,
				Status:     withdrawOrderItem.Status,
				CreatedAt:  withdrawOrderItem.CreatedAt,
				UpdatedAt:  withdrawOrderItem.UpdatedAt,
				// 复制其他必要字段
				Extends: withdrawOrderItem.Extends,
			},
			WithdrawType: withdrawType,
			OrderType:    orderType,
			ShipManageID: shipManageID,
			OrderUser:    orderUser,
			SumPayAmount: sumPayAmount,
			Items:        itemsList,
		}

	}
	return result, nil
}

// ProcessUserShipment 处理单个用户的发货逻辑
func ProcessUserShipment(ctx context.Context, openUserID string, operator *pat.CheckAdmJwtUserInfo, items []*yc_open.GetUserItemsByItemIdListResItem, itemID string) error {
	var userItemIDs []string
	for _, item := range items {
		userItemIDs = append(userItemIDs, item.ID)
	}

	if len(userItemIDs) == 0 {
		return nil
	}

	user, err := userFacade.GetNodeUserByOpenUserId(ctx, openUserID)
	if err != nil {
		log.Ctx(ctx).Errorf("ProcessUserShipment: GetNodeUserByOpenUserId 获取用户失败: %v, itemID: %s, OpenUserID: %s", err, itemID, openUserID)
		return fmt.Errorf("获取用户失败: %v", err)
	}
	userID := user.ID.Hex()

	// 首先尝试获取默认地址
	defaultAddress, err := tmt.GetDefaultAddressDetail(ctx, userID)
	if err != nil || defaultAddress == nil {
		log.Ctx(ctx).Warnf("ProcessUserShipment: 获取默认地址失败或为空，尝试获取第一个地址: %v, itemID: %s, UserID: %s", err, itemID, userID)

		// 获取用户地址列表，取第一个可用地址
		addressList, listErr := tmt.GetUserAddressList(ctx, userID)
		if listErr != nil {
			log.Ctx(ctx).Errorf("ProcessUserShipment: GetUserAddressList 获取用户地址列表失败: %v, itemID: %s, UserID: %s", listErr, itemID, userID)
			return fmt.Errorf("获取用户地址失败: %v", listErr)
		}

		// 检查地址列表是否为空
		if len(addressList) == 0 {
			log.Ctx(ctx).Errorf("ProcessUserShipment: 用户没有任何可用地址, itemID: %s, UserID: %s", itemID, userID)
			return fmt.Errorf("用户没有任何可用地址，无法发货")
		}

		// 使用第一个地址作为发货地址
		defaultAddress = addressList[0]
		log.Ctx(ctx).Infof("ProcessUserShipment: 使用第一个地址作为发货地址, UserID: %s, AddressID: %s", userID, defaultAddress.ID)
	}

	// 创建发货请求
	withdrawReq := &yc_open.BatchWithdrawOrderReq{
		UserItemIDs: userItemIDs,
		AddressInfo: yc_open_define.WithdrawOrderAddressInfo{
			Name:        defaultAddress.Name,
			MobilePhone: defaultAddress.MobilePhone,
			Code:        defaultAddress.Code,
			Area:        defaultAddress.Area,
			Place:       defaultAddress.Place,
		},
		OpenUserID: openUserID,
		Operator:   operator,
	}

	log.Ctx(ctx).Infof("ProcessUserShipment: 准备创建发货单 UserID: %s, withdrawReq: %v", userID, util.Obj2JsonStr(withdrawReq))
	ycResp, status, err := yc_open.BatchWithdrawOrder(ctx, withdrawReq)
	switch status {
	case yc_open.BatchWithdrawOrderSuccess:
		log.Ctx(ctx).Infof("ProcessUserShipment BatchWithdrawOrder Success, itemID:%v, userID:%v， ycResp: %v", itemID, userID, util.Obj2JsonStr(ycResp))
	case yc_open.BatchWithdrawOrderFailed:
		log.Ctx(ctx).Errorf("ProcessUserShipment BatchWithdrawOrder Fail, itemID:%v, userID:%v, 错误信息: %v", itemID, userID, err)
		return common_define.CommonErr
	case yc_open.BatchWithdrawOrderLowStocks:
		log.Ctx(ctx).Errorf("ProcessUserShipment BatchWithdrawOrder LowStocks, itemID:%v, userID:%v, 错误信息: %v", itemID, userID, err)
		// 限制告警频率
		l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewShipManageLock(util.StrVal(itemID), locker.ForcedShipLowStocks)))
		if !l.Lock(ctx) {
			return common_define.CommonErr
		}
		// defer l.UnLock(ctx)
		// 告警，库存不足
		commonFacade.SendDefaultWarnMsg(ctx, "【强制发货库存不足】", fmt.Sprintf("发货商品ID: %+v, 错误信息: %v", itemID, err))
		return common_define.CommonErr
	default:
		// yc响应超时，未知异常
		log.Ctx(ctx).Errorf("ProcessUserShipment BatchWithdrawOrder Unknown Exception, itemID:%v, userID:%v, 错误信息: %v", itemID, userID, err)
		// 告警，未知异常
		commonFacade.SendDefaultWarnMsg(ctx, "【强制发货未知异常】", fmt.Sprintf("发货商品ID: %+v, 用户ID: %+v, 错误信息: %v", itemID, userID, err))
		return common_define.CommonErr
	}

	for _, orderID := range ycResp.OrderIds {
		// 创建ship_manage 数据
		shipManageSchema := repo.GetQuery().ShipManage
		shipManage := &model.ShipManage{
			ShipManageID: snowflakeutl.GenerateID(),
			YcID:         orderID,
			WithdrawType: enums.WithdrawTypeForced.Val(),
			OrderType:    enums.OrderTypeWc.Val(),
		}

		err = repo.NewShipManageRepo(shipManageSchema.WithContext(ctx)).Save(shipManage)
		if err != nil {
			log.Ctx(ctx).Errorf("ProcessUserShipment: Create shipManage err:%v, ycID: %s", err, orderID)
			return fmt.Errorf("保存发货记录失败: %v", err)
		}
		log.Ctx(ctx).Infof("ProcessUserShipment: Create shipManage success, ycID: %s, shipManage: %v", orderID, util.Obj2JsonStr(shipManage))
	}

	return nil
}
