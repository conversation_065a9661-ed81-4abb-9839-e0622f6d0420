package locker

import (
	"fmt"
	"time"
)

// ShipManageAction 发货管理
type ShipManageAction string

const (
	ForcedShip          ShipManageAction = "forced_ship"            // 强制发货
	ForcedShipLowStocks ShipManageAction = "forced_ship_low_stocks" // 强制发货库存不足
)

type ShipManageLock struct {
	ac  ShipManageAction // 行为
	tag string           // 唯一标识
}

func (p *ShipManageLock) GetCacheKey() string {
	return fmt.Sprintf("app_service:ship_manage:locker:%s:%s", p.ac, p.tag)
}

func (p *ShipManageLock) LockTime() time.Duration {
	return time.Second * 10
}

func NewShipManageLock(tag string, ac ShipManageAction) *ShipManageLock {
	return &ShipManageLock{tag: tag, ac: ac}
}
