package service

import (
	"app_service/apps/business/yc/consume"
	"app_service/apps/business/yc/define"
	"app_service/apps/business/yc/define/enums"
	"app_service/apps/business/yc/service/locker"
	"app_service/apps/business/yc/service/logic"
	"app_service/apps/platform/common/constant"
	userFacade "app_service/apps/platform/user/facade"
	"app_service/global"
	"app_service/pkg/util"
	"app_service/pkg/util/excelize_lib"
	"app_service/pkg/util/kafka_util"
	"app_service/third_party/yc_open"
	yc_open_define "app_service/third_party/yc_open/define"
	"fmt"
	"strings"
	"time"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/redis_locker"
	"github.com/gin-gonic/gin"
)

// GetShipManageList 获取发货管理列表
func (s *Service) GetShipManageList(req *define.GetShipManageAdminListReq) (*define.GetShipManageAdminListResp, error) {
	// 构建查询参数
	params := map[string]interface{}{}

	// 添加过滤条件
	//if req.ShipManageID != 0 {
	//	params["id"] = req.ShipManageID
	//}
	if req.YcId != "" {
		params["id"] = req.YcId
	}
	if req.ItemId != "" {
		params["item_id"] = req.ItemId
	}
	if req.ItemName != "" {
		params["item_name"] = req.ItemName
	}
	if req.UserId != "" {
		// -> open_user_id
		user, err := userFacade.GetNodeUser(s.ctx, req.UserId)
		if err != nil {
			log.Ctx(s.ctx).Errorf("GetShipManageList GetNodeUser failed: %v", err)
			return nil, err
		}
		params["open_user_id"] = user.OpenInfo.OpenUserID.Hex()
	}
	if req.MobilePhone != "" {
		params["mobile_phone"] = req.MobilePhone
	}
	if req.FreightNo != "" {
		params["freight_no"] = req.FreightNo
	}
	if req.Status != nil {
		params["status"] = *req.Status
	}
	if req.ItemsStatus != nil {
		params["items_status"] = *req.ItemsStatus
	}
	//if req.WithdrawType > 0 {
	//	params["withdraw_type"] = req.WithdrawType
	//}
	//if req.OrderType > 0 {
	//	params["order_type"] = req.OrderType
	//}
	if !req.CreatedAtStart.IsZero() {
		params["created_at_start"] = req.CreatedAtStart.Format(time.RFC3339)
	}
	if !req.CreatedAtEnd.IsZero() {
		params["created_at_end"] = req.CreatedAtEnd.Format(time.RFC3339)
	}
	if !req.FreightTimeStart.IsZero() {
		params["freight_time_start"] = req.FreightTimeStart.Format(time.RFC3339)
	}
	if !req.FreightTimeEnd.IsZero() {
		params["freight_time_end"] = req.FreightTimeEnd.Format(time.RFC3339)
	}
	// 调用云仓接口获取订单列表
	data, err := yc_open.GetWithdrawOrderList(
		s.ctx,
		req.Page,
		req.PageSize,
		params,
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetWithdrawOrderList failed: %v", err)
		return nil, err
	}

	// 转换响应数据
	resp := &define.GetShipManageAdminListResp{
		List:  make([]*define.GetShipManageAdminListData, 0, len(data.List)),
		Total: data.Total,
	}

	// 批量获取订单的提货方式、订单来源和用户信息
	enrichResults, err := logic.EnrichShipManageInfoList(s.ctx, data.List)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EnrichShipManageInfoList failed: %v", err)
		// 即使出错也继续处理，返回部分结果
	}

	// 构建响应数据
	for _, item := range data.List {
		// 从批量处理结果中获取当前订单的丰富信息
		result, exists := enrichResults[item.ID]
		if !exists || result == nil {
			log.Ctx(s.ctx).Errorf("No enrich result for order %s", item.ID)
			continue
		}

		// 构建列表项
		listItem := define.GetShipManageAdminListData{
			WithdrawOrderListItem: item, // 使用原始列表项数据
			Items:                 result.Items,
			UserID:                result.OrderUser.ID,
			User:                  result.OrderUser,
			ShipManageID:          result.ShipManageID,
			YcID:                  item.ID,
			WithdrawType:          result.WithdrawType,
			OrderType:             result.OrderType,
			PayAmount:             result.SumPayAmount,
		}

		resp.List = append(resp.List, &listItem)
	}

	return resp, nil
}

// GetShipManageDetail 获取发货管理详情
func (s *Service) GetShipManageDetail(req *define.GetShipManageAdminDetailReq) (*define.GetShipManageAdminDetailResp, error) {
	// 调用云仓接口获取订单详情
	detail, err := yc_open.GetWithdrawOrderV2(s.ctx, req.YcId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetWithdrawOrderV2 failed: %v", err)
		return nil, err
	}

	// 使用 EnrichShipManageInfo 获取订单详情、提货方式、订单来源和用户信息
	result, err := logic.EnrichShipManageInfo(
		s.ctx,
		detail,
		req.YcId,
	)
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp := &define.GetShipManageAdminDetailResp{
		WithdrawOrderDetail: *result.Detail,
		UserID:              result.OrderUser.ID,
		User:                result.OrderUser,
		Items:               result.Items,
		ShipManageID:        result.ShipManageID,
		YcId:                req.YcId,
		WithdrawType:        result.WithdrawType,
		OrderType:           result.OrderType,
		PayAmount:           result.SumPayAmount,
	}

	return resp, nil
}

// GetShipManageMobilePhone 查看手机号
func (s *Service) GetShipManageMobilePhone(req *define.GetShipManageMobilePhoneReq) (*define.GetShipManageMobilePhoneResp, error) {

	// 调用云仓接口获取订单详情
	detail, err := yc_open.GetWithdrawOrderV2(s.ctx, req.YcId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetWithdrawOrderV2 failed: %v", err)
		return nil, err
	}

	return &define.GetShipManageMobilePhoneResp{
		MobilePhone: detail.Extends.Address.MobilePhone,
	}, nil
}

// EditShipManage 编辑发货管理
func (s *Service) EditShipManage(
	req *define.EditShipManageReq,
) (*define.EditShipManageResp, error) {
	// 取消时需校验订单中的物品是否全都未发货，仅全未发货的情况才予取消。
	// 检查是否是取消操作（状态变更为无效）
	if req.Status != nil && *req.Status == yc_open_define.WithdrawOrderStatusExpired {
		// 获取订单详情
		detail, err := yc_open.GetWithdrawOrderV2(s.ctx, req.YcId)
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取订单详情失败: %v", err)
			return nil, define.SM400004Err
		}

		// 检查是否有已发货的商品
		for _, item := range detail.Extends.Items {
			// 如果商品状态 != 待发货，则不允许取消
			if item.Status != yc_open_define.WithdrawOrderStatusWaitForShip {
				return nil, define.SM400005Err
			}
		}
	}

	// 构建更新参数
	updateReq := &yc_open.UpdateOrderReq{
		Status:      req.Status,
		AdminRemark: req.AdminRemark,
		Extends: yc_open.UpdateExtends{
			FreightAmount: req.Extends.FreightAmount,
			//AddressInfo:   util.Obj2JsonStr(*req.Address),
			Address: req.Extends.Address,
		},
		Operator: s.userService.GetAdmin(),
	}
	// 调用云仓接口更新订单
	if err := yc_open.UpdateOrder(s.ctx, req.YcId, updateReq); err != nil {
		log.Ctx(s.ctx).Errorf("UpdateOrder failed: %v", err)
		return nil, err
	}

	return &define.EditShipManageResp{
		YcId: req.YcId,
	}, nil
}

// FreightSync 同步物流信息
func (s *Service) FreightSync(req *define.FreightSyncReq) (*define.FreightSyncResp, error) {
	// 调用云仓接口同步物流
	err := yc_open.FreightSync(s.ctx, &yc_open.FreightSyncReq{
		FreightNos: req.FreightNos,
	})
	if err != nil {
		log.Ctx(s.ctx).Errorf("FreightSync failed: %v", err)
		return nil, err
	}

	return &define.FreightSyncResp{}, nil
}

// GetShipManageListExport 导出发货管理列表
func (s *Service) GetShipManageListExport(ctx *gin.Context, req *define.GetShipManageAdminListReq) error {
	// 检查是否至少有一组完整的时间范围
	hasCreatedAtRange := !req.CreatedAtStart.IsZero() && !req.CreatedAtEnd.IsZero()
	hasFreightTimeRange := !req.FreightTimeStart.IsZero() && !req.FreightTimeEnd.IsZero()

	if !hasCreatedAtRange && !hasFreightTimeRange {
		return define.SM400006Err // 请至少选择一组完整的时间范围（提货时间或发货时间）
	}

	// 验证提货时间范围（如果提供了完整的提货时间）
	if hasCreatedAtRange {
		// 结束时间必须在开始时间之后
		if req.CreatedAtEnd.Before(req.CreatedAtStart) {
			return define.SM400002Err
		}
		// 最多限制导出 31 天范围的数据
		if req.CreatedAtStart.AddDate(0, 0, 31).Before(req.CreatedAtEnd) {
			return define.SM400001Err
		}
	}

	// 验证发货时间范围（如果提供了完整的发货时间）
	if hasFreightTimeRange {
		// 结束时间必须在开始时间之后
		if req.FreightTimeEnd.Before(req.FreightTimeStart) {
			return define.SM400002Err
		}
		// 最多限制导出 31 天范围的数据
		if req.FreightTimeStart.AddDate(0, 0, 31).Before(req.FreightTimeEnd) {
			return define.SM400001Err
		}
	}

	// 1. 初始化数据列表
	dataList := make([]*define.GetShipManageAdminListData, 0)

	// 2. 分页获取所有数据
	for page := 1; page < 10000; page++ {
		req.Page = page
		req.PageSize = 100 // 每页100条

		// 调用现有的获取列表方法
		resp, err := s.GetShipManageList(req)
		if err != nil {
			return err
		}

		// 如果没有数据或数据为空，则结束循环
		if resp == nil || len(resp.List) == 0 {
			break
		}

		// 将当前页数据添加到总列表
		dataList = append(dataList, resp.List...)

		// 如果当前页数据小于分页大小，说明是最后一页
		if len(resp.List) < 100 {
			break
		}
	}

	// 3. 准备导出数据
	excel := excelize_lib.NewExcel()
	dataKey := make([]map[string]string, 0)
	// 定义导出列
	dataKey = append(dataKey, map[string]string{"key": "ship_manage_id", "title": "订单ID", "width": "24", "is_num": "1"})
	dataKey = append(dataKey, map[string]string{"key": "yc_id", "title": "云仓订单ID", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "withdraw_type", "title": "提货方式", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "status", "title": "订单状态", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_id", "title": "用户ID", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_name", "title": "用户昵称", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "user_phone", "title": "用户手机号", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_name", "title": "收货人", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_phone", "title": "收货电话", "width": "15", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "receiver_address", "title": "收货地址", "width": "40", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_id", "title": "挂牌编码", "width": "24", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "item_name", "title": "商品名称", "width": "30", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "quantity", "title": "数量", "width": "10", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "withdraw_time", "title": "提货时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "shipping_time", "title": "发货时间", "width": "20", "is_num": "0"})
	dataKey = append(dataKey, map[string]string{"key": "pay_amount", "title": "成交金额", "width": "20", "is_num": "0"})
	//dataKey = append(dataKey, map[string]string{"key": "remark", "title": "备注", "width": "30", "is_num": "0"})

	// 4. 准备数据
	data := make([]map[string]interface{}, 0, len(dataList))
	for _, item := range dataList {
		// 获取用户信息
		userName := item.User.PatbgDetail.Nickname
		userPhone := item.User.PatbgDetail.MobilePhone
		userID := item.User.ID

		// 获取地址信息
		addressInfo := item.Extends.Address

		// 映射提货方式
		withdrawType := "手动提货"
		if item.WithdrawType == enums.WithdrawTypeForced {
			withdrawType = "强制发货"
		}

		// 准备商品详情
		var (
			itemNames     []string
			quantities    []string
			payAmounts    []string
			statuses      []string
			shippingTimes []string
			itemIDs       []string
		)

		// 获取商品信息
		for _, itemDetail := range item.Extends.Items {
			// 从item.Items中查找对应的商品名称
			itemName := ""
			for _, itemInfo := range item.Items {
				if itemInfo.ItemID == itemDetail.ItemID {
					itemName = itemInfo.ItemName
					break
				}
			}
			itemNames = append(itemNames, itemName)
			quantities = append(quantities, util.StrVal(itemDetail.Quantity))

			// 计算实付金额（分转元）
			payAmount := ""
			if itemDetail.PayAmount > 0 {
				payAmount = "¥" + util.FenToYuanString(int32(itemDetail.PayAmount))
			}
			payAmounts = append(payAmounts, payAmount)

			// 获取商品状态
			statuses = append(statuses, logic.GetOrderStatusText(item.Status)) // 使用订单状态

			// 格式化发货时间
			shippingTime := ""
			if itemDetail.FreightTime != nil && !itemDetail.FreightTime.IsZero() {
				shippingTime = util.GetDateTimeFormatStr(*itemDetail.FreightTime)
			}
			shippingTimes = append(shippingTimes, shippingTime)
			itemIDs = append(itemIDs, itemDetail.ItemID)
		}

		// 构建数据行
		row := map[string]interface{}{
			"ship_manage_id":   util.StrVal(item.ShipManageID),            // 订单ID
			"yc_id":            util.StrVal(item.YcID),                    // 云仓订单ID
			"withdraw_type":    withdrawType,                              // 提货方式
			"status":           logic.GetOrderStatusText(item.Status),     // 订单状态
			"user_name":        userName,                                  // 用户昵称
			"user_phone":       util.PhoneMix(userPhone),                  // 用户手机号
			"receiver_name":    addressInfo.Name,                          // 收货人
			"receiver_phone":   util.PhoneMix(addressInfo.MobilePhone),    // 收货电话
			"receiver_address": addressInfo.Area + addressInfo.Place,      // 收货地址
			"withdraw_time":    util.GetDateTimeFormatStr(item.CreatedAt), // 提货时间（使用创建时间）
			"user_id":          userID,                                    // 用户ID
		}

		// 添加商品相关字段，确保即使没有商品也不会报错
		if len(itemNames) > 0 {
			row["item_name"] = strings.Join(itemNames, "\n")
			row["quantity"] = strings.Join(quantities, "\n")
			row["pay_amount"] = strings.Join(payAmounts, "\n")
			row["item_status"] = strings.Join(statuses, "\n")
			row["shipping_time"] = strings.Join(shippingTimes, "\n")
			row["item_id"] = strings.Join(itemIDs, "\n")
		} else {
			// 如果没有商品，添加空值
			row["item_name"] = ""
			row["quantity"] = ""
			row["pay_amount"] = ""
			row["item_status"] = ""
			row["shipping_time"] = ""
		}

		data = append(data, row)
	}
	log.Ctx(s.ctx).Debugf("导出发货管理列表, 数据条数: %d, 数据: %v", len(data), data)
	// 5. 导出Excel
	err := excel.ExportToStream(dataKey, data, ctx)
	if err != nil {
		log.Ctx(s.ctx).Errorf("failed to export ship manage list: %v", err)
		return err
	}

	return nil
}

// GetShipManageOrderLogs 获取订单操作日志
func (s *Service) GetShipManageOrderLogs(req *define.GetShipManageOrderLogsReq) (interface{}, error) {

	// 调用云仓接口获取订单日志
	logs, err := yc_open.GetOrderLogs(s.ctx, req.YcId)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetOrderLogs failed: %v", err)
		return nil, err
	}

	return logs, nil
}

// ForcedShipmentBatch 批量强制发货
func (s *Service) ForcedShipmentBatch(req *define.ForcedShipmentBatchAdminReq) (*define.ForcedShipmentBatchAdminResp, error) {
	// 上锁
	l := redis_locker.New(global.REDIS.Client, redis_locker.WithLocker(locker.NewShipManageLock(util.StrVal(req.ItemID), locker.ForcedShip)))
	if !l.Lock(s.ctx) {
		return nil, define.SM400003Err
	}
	//defer l.UnLock(s.ctx)

	// 查询用户物品
	userItems, err := yc_open.QueryUserItemsByUserAndItemId(s.ctx, "", req.ItemID,
		[]any{yc_open_define.UserItemStatusOwned},
		yc_open.GetUserItemListReqOptions{})
	if err != nil {
		log.Ctx(s.ctx).Errorf("ForcedShipmentBatch: GetUserItemList 失败: %v, ItemID: %s", err, req.ItemID)
		return nil, fmt.Errorf("查询用户商品失败: %w", err)
	}

	if len(userItems) == 0 {
		log.Ctx(s.ctx).Infof("ForcedShipmentBatch: 没有找到用户商品, ItemID: %s", req.ItemID)
		return &define.ForcedShipmentBatchAdminResp{
			List: make(map[string][]*yc_open.GetUserItemsByItemIdListResItem),
		}, nil
	}

	// 发送批量发货任务到Kafka
	operator := s.userService.GetAdmin()

	// 按OpenUserID对userItems进行分组
	userItemGroup := make(map[string][]*yc_open.GetUserItemsByItemIdListResItem)
	for _, item := range userItems {
		userItemGroup[item.OpenUserID] = append(userItemGroup[item.OpenUserID], item)
	}
	log.Ctx(s.ctx).Infof("ForcedShipmentBatch, ItemID: %s, 总用户数: %d", req.ItemID, len(userItemGroup))

	// 构建任务消息
	for openUserID, items := range userItemGroup {
		// 构建消息体
		message := &consume.ShipmentTask{
			ItemID:     req.ItemID,
			OpenUserID: openUserID,
			Items:      items,
			Operator:   operator,
		}

		log.Ctx(s.ctx).Infof("ForcedShipmentBatch SendMsg, ItemID: %s, OpenUserID: %s", req.ItemID, openUserID)
		//发送Kafka
		_ = kafka_util.SendMsg(s.ctx, constant.ShipmentForce, message)
	}

	log.Ctx(s.ctx).Infof("ForcedShipmentBatch 批量发货任务已全部发送到Kafka, ItemID: %s", req.ItemID)

	return &define.ForcedShipmentBatchAdminResp{
		List:  userItemGroup,
		Total: len(userItemGroup),
	}, nil
}
