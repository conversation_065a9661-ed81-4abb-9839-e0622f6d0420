package web

import (
	"app_service/apps/business/yc/define"
	"app_service/apps/business/yc/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetYcWebAllItems
// @Summary 查询云仓商品列表
// @Description 查询云仓商品列表
// @Tags 用户端-云仓商品管理
// @Param data query define.GetWebAllItemsReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebAllItemsResp}
// @Router  /web/v1/yc/all_items [get]
// @Security Bearer
func GetYcWebAllItems(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebAllItemsReq{}, s.GetWebAllItems)
}

// GetYcWebAllStatisticOverview
// @Summary 查询云仓数据面板
// @Description 查询云仓数据面板
// @Tags 用户端-云仓商品管理
// @Param data query define.GetWebStatisticOverviewReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebStatisticOverviewResp}
// @Router  /web/v1/yc/statistic_overview [get]
// @Security Bearer
func GetYcWebAllStatisticOverview(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebStatisticOverviewReq{}, s.GetWebStatisticOverview)
}

// GetYcWebUserItemList
// @Summary 查询云仓每个商品的持仓列表
// @Description 查询云仓每个商品的持仓列表
// @Tags 用户端-云仓商品管理
// @Param data query define.GetWebUserItemListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebUserItemListResp}
// @Router  /web/v1/yc/items/{item_id}/user_item_list [get]
// @Security Bearer
func GetYcWebUserItemList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebUserItemListReq{}, s.GetWebUserItemList)
}
