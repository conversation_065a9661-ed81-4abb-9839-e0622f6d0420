package admin

import (
	"app_service/apps/business/yc/define"
	"app_service/apps/business/yc/service"

	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetShipManageList
// @Summary 查询发货管理列表
// @Description 查询发货管理列表
// @Tags 管理端-发货管理
// @Param data query define.GetShipManageAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetShipManageAdminListResp}
// @Router /admin/v1/ship_manage/list [get]
// @Security Bearer
func GetShipManageList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetShipManageAdminListReq{}, s.GetShipManageList)
}

// GetShipManageDetail
// @Summary 查询发货管理详情
// @Description 查询发货管理详情
// @Tags 管理端-发货管理
// @Param data query define.GetShipManageAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetShipManageAdminDetailResp}
// @Router /admin/v1/ship_manage/detail [get]
// @Security Bearer
func GetShipManageDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetShipManageAdminDetailReq{}, s.GetShipManageDetail)
}

// GetShipManageMobilePhone
// @Summary 查看手机号
// @Description 查看手机号
// @Tags 管理端-发货管理
// @Param data query define.GetShipManageMobilePhoneReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetShipManageMobilePhoneResp}
// @Router /admin/v1/ship_manage/mobile_phone [get]
// @Security Bearer
func GetShipManageMobilePhone(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetShipManageMobilePhoneReq{}, s.GetShipManageMobilePhone)
}

// EditShipManage
// @Summary 编辑发货管理
// @Description 编辑发货管理
// @Tags 管理端-发货管理
// @Param data body define.EditShipManageReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditShipManageResp}
// @Router /admin/v1/ship_manage/edit [put]
// @Security Bearer
func EditShipManage(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditShipManageReq{}, s.EditShipManage)
}

// FreightSync
// @Summary 同步物流信息
// @Description 同步物流信息
// @Tags 管理端-发货管理
// @Param data body define.FreightSyncReq true "同步参数"
// @Success 200 {object} response.Data{data=define.FreightSyncResp}
// @Router /admin/v1/ship_manage/freight_sync [post]
// @Security Bearer
func FreightSync(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.FreightSyncReq{}, s.FreightSync)
}

// GetShipManageListExport
// @Summary 导出发货管理列表
// @Description 导出发货管理列表
// @Tags 管理端-发货管理
// @Param data query define.GetShipManageAdminListReq true "查询参数"
// @Success 200 {object} response.Data
// @Router /admin/v1/ship_manage/list_export [get]
// @Security Bearer
func GetShipManageListExport(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetShipManageAdminListReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.GetShipManageListExport(ctx, req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}

// GetShipManageOrderLogs
// @Summary 获取订单操作日志
// @Description 获取订单操作日志
// @Tags 管理端-发货管理
// @Param data query define.GetShipManageOrderLogsReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetShipManageOrderLogsResp}
// @Router /admin/v1/ship_manage/orderLogs/{id} [get]
// @Security Bearer
func GetShipManageOrderLogs(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetShipManageOrderLogsReq{}, s.GetShipManageOrderLogs)
}

// ForcedShipmentBatch
// @Summary 批量强制发货
// @Description 批量强制发货
// @Tags 管理端-发货管理
// @Param data body define.ForcedShipmentBatchAdminReq true "请求参数"
// @Success 200 {object} response.Data{data=define.ForcedShipmentBatchAdminResp}
// @Router  /admin/v1/ship_manage/forced_shipment_batch [post]
// @Security Bearer
func ForcedShipmentBatch(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ForcedShipmentBatchAdminReq{}, s.ForcedShipmentBatch)
}
