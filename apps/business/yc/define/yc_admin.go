package define

import "app_service/third_party/yc_open"

type (
	GetAdminStatisticOverviewDetailReq struct {
		UserID string `form:"user_id" json:"user_id" binding:"required"`
	}

	GetAdminStatisticOverviewDetailResp struct {
		GetWebStatisticOverviewResp
		YcCountItemList                     []*yc_open.CountUserItemsRes `json:"yc_count_item_list"`
		LastClosePriceTotal                 int64                        `json:"last_close_price_total"`
		TodayTotalAmount                    int32                        `json:"today_total_amount"`
		TodayTotalAmountWithLastClosePrice  int32                        `json:"today_total_amount_with_last_close_price"`
		HoldAndTodayTotalWithLastClosePrice int32                        `json:"hold_and_today_total_with_last_close_price"`
		LastMarketPriceMap                  map[string]int32             `json:"last_market_price_map"`
		LastClosePriceMap                   map[string]int32             `json:"last_close_price_map"`
		TodaySaleOrderMap                   string                       `json:"today_sale_order_map"`
	}
)
