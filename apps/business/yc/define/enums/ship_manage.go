package enums

type WithdrawType int32

func (s WithdrawType) Val() int32 {
	return int32(s)
}

const (
	// WithdrawTypeManual 手动提货
	WithdrawTypeManual WithdrawType = 1
	// WithdrawTypeForced 强制发货
	WithdrawTypeForced WithdrawType = 2
)

type OrderType int32

func (s OrderType) Val() int32 {
	return int32(s)
}

const (
	// OrderTypeYcOpen 云仓提货
	OrderTypeYcOpen OrderType = 1
	// OrderTypeWc 文潮提货
	OrderTypeWc OrderType = 2
)
