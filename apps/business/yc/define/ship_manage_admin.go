package define

import (
	"app_service/apps/business/yc/define/enums"
	"app_service/pkg/pagination"
	"app_service/third_party/yc_open"
	"app_service/third_party/yc_open/define"

	"time"
)

// 发货管理列表相关结构体
type (
	GetShipManageAdminListReq struct {
		pagination.Pagination
		ShipManageID     int64                           `form:"ship_manage_id" json:"ship_manage_id,string,omitempty"` // 发货管理ID
		YcId             string                          `form:"yc_id" json:"yc_id"`                                    // 云仓id
		ItemId           string                          `form:"item_id" json:"item_id"`                                // 商品ID
		ItemName         string                          `form:"item_name" json:"item_name"`                            // 商品名称
		UserId           string                          `form:"user_id" json:"user_id"`                                // 用户id
		MobilePhone      string                          `form:"mobile_phone" json:"mobile_phone"`                      // 手机号
		FreightNo        string                          `form:"freight_no" json:"freight_no"`                          // 快递单号
		Status           *define.ItemWithdrawOrderStatus `form:"status" json:"status"`                                  // 订单状态
		ItemsStatus      *define.ItemWithdrawOrderStatus `form:"items_status" json:"items_status"`                      // 商品状态
		WithdrawType     enums.WithdrawType              `form:"withdraw_type" json:"withdraw_type"`                    // 提货方式：1：手动提货: 2：强制发货。
		OrderType        enums.OrderType                 `form:"order_type" json:"order_type"`                          // 订单来源：1：云仓提货:2：文潮提货。
		CreatedAtStart   time.Time                       `form:"created_at_start" json:"created_at_start"`              // 提货时间
		CreatedAtEnd     time.Time                       `form:"created_at_end" json:"created_at_end"`                  // 提货时间
		FreightTimeStart time.Time                       `form:"freight_time_start" json:"freight_time_start"`          // 发货时间
		FreightTimeEnd   time.Time                       `form:"freight_time_end" json:"freight_time_end"`              // 发货时间
	}

	GetShipManageAdminListData struct {
		yc_open.WithdrawOrderListItem
		ShipManageID int64              `json:"ship_manage_id,string,omitempty"` // 发货管理ID
		YcID         string             `json:"yc_id"`                           // 云仓ID
		WithdrawType enums.WithdrawType `json:"withdraw_type"`                   // 提货方式：1：手动提货: 2：强制发货。
		OrderType    enums.OrderType    `json:"order_type"`                      // 订单来源：1：云仓提货:2：文潮提货。
		User         OrderUser          `json:"user"`                            // 用户信息
		UserID       string             `json:"user_id"`                         // 用户ID
		PayAmount    int64              `json:"pay_amount"`                      // 实付金额
		Items        []OrderItem        `json:"items"`                           // 商品列表
	}

	GetShipManageAdminListResp struct {
		List  []*GetShipManageAdminListData `json:"list"`
		Total int64                         `json:"total"`
	}
)

// 发货管理详情相关结构体
type (
	GetShipManageAdminDetailReq struct {
		YcId string `form:"yc_id" json:"yc_id" binding:"required"` // 发货管理ID
	}

	GetShipManageAdminDetailResp struct {
		yc_open.WithdrawOrderDetail
		Items        []OrderItem        `json:"items"`                           // 商品列表
		ShipManageID int64              `json:"ship_manage_id,string,omitempty"` // 发货管理ID
		YcId         string             `json:"yc_id"`                           // 云仓ID
		WithdrawType enums.WithdrawType `json:"withdraw_type"`                   // 提货方式：1：手动提货: 2：强制发货。
		OrderType    enums.OrderType    `json:"order_type"`                      // 订单来源：1：云仓提货:2：文潮提货。
		User         OrderUser          `json:"user"`                            // 用户信息
		UserID       string             `json:"user_id"`                         // 用户ID
		PayAmount    int64              `json:"pay_amount"`                      // 总实付金额
	}

	// OrderItem 订单商品信息
	OrderItem struct {
		ID              string   `json:"id"`                // 商品ID
		IconURL         string   `json:"icon_url"`          // 商品主图
		ItemName        string   `json:"item_name"`         // 商品名称
		ItemID          string   `json:"item_id"`           // 挂牌编码
		Price           int32    `json:"price"`             // 发行价格
		SkuNo           string   `json:"sku_no"`            // sku_no
		IPClassifyNames []string `json:"ip_classify_names"` // 商品IP
		ItemSpecs       string   `json:"item_specs"`        // 商品规格
		IssuerName      string   `json:"issuer_name"`       // 发行方全称
		IssuerShortName string   `json:"issuer_short_name"` // 发行方简称
	}

	// OrderUser 订单用户信息
	OrderUser struct {
		Type        int32            `json:"type"`         // 类型
		ID          string           `json:"_id"`          // 用户ID
		PatbgDetail OrderPatbgDetail `json:"patbg_detail"` // 用户详情
	}

	// OrderPatbgDetail 用户详情
	OrderPatbgDetail struct {
		Nickname    string `json:"nickname"`     // 用户昵称
		MobilePhone string `json:"mobile_phone"` // 手机号
		Avatar      string `json:"avatar"`       // 头像URL
	}
)

// 查看手机号
type (
	GetShipManageMobilePhoneReq struct {
		YcId string `form:"yc_id" json:"yc_id" binding:"required"` // 云仓ID
	}
	GetShipManageMobilePhoneResp struct {
		MobilePhone string `json:"mobile_phone"` // 手机号
	}
)

// 编辑发货管理相关结构体
type (
	EditShipManageReq struct {
		yc_open.UpdateOrderReq
		YcId string `json:"yc_id" binding:"required"` // 云仓ID
	}

	EditShipManageResp struct {
		YcId string `json:"yc_id"` // 云仓ID
	}
)

// 同步物流
type (
	FreightSyncReq struct {
		yc_open.FreightSyncReq
	}
	FreightSyncResp struct{}
)

// GetShipManageOrderLogsReq 获取订单操作日志请求
type (
	GetShipManageOrderLogsReq struct {
		YcId string `form:"yc_id" json:"yc_id" binding:"required"` // 云仓ID
	}
	GetShipManageOrderLogsResp struct {
		List  []*yc_open.OrderLogsData `json:"list"`
		Total int64                    `json:"total"`
	}
)

type (
	ForcedShipmentBatchAdminReq struct {
		ItemID string `json:"item_id" binding:"required"`
	}
	ForcedShipmentBatchAdminResp struct {
		List  map[string][]*yc_open.GetUserItemsByItemIdListResItem `json:"list"`
		Total int                                                   `json:"total"`
	}
)
