package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	// 发货管理模块错误码
	SM400001Err = response.NewError(400001, "最多限导31天的数据")
	SM400002Err = response.NewError(400002, "结束时间必须在开始时间之后")
	SM400003Err = response.NewError(400003, "频繁操作，请10秒后再试")
	SM400004Err = response.NewError(400004, "获取订单详情失败")
	SM400005Err = response.NewError(400005, "仅全部未发货的情况才可取消")
	SM400006Err = response.NewError(400006, "请至少选择一组完整的时间范围（提货时间或发货时间）")
)
