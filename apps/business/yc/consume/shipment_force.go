package consume

import (
	"app_service/apps/business/yc/service/logic"
	"app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"app_service/third_party/yc_open"
	"context"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
)

// ShipmentForceConsumer 强制发货消费者
type ShipmentForceConsumer struct {
	middlewares.BaseConsumer
}

// NewShipmentForceConsumer 创建强制发货消费者
func NewShipmentForceConsumer() *ShipmentForceConsumer {
	return &ShipmentForceConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(constant.ShipmentForce, constant.CommonGroup),
	}
}

// GetTopic 获取主题名称
func (c *ShipmentForceConsumer) GetTopic() string {
	return constant.ShipmentForce
}

// GetGroup 获取消费者组
func (c *ShipmentForceConsumer) GetGroup() string {
	return constant.CommonGroup
}

// HandleFun 处理消息
func (c *ShipmentForceConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(
			context.Background(),
			"ShipmentForceConsumer",
			trace.WithSpanKind(trace.SpanKindConsumer),
		)
		defer span.End()

		log.Ctx(ctx).Infof("[shipment_force] shipment_force kafka data:%s", util.Obj2JsonStr(event.Message()))

		// 解析消息
		task := &ShipmentTask{}
		if err := util.JsonStr2Struct(string(event.Message().Body), &task); err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		log.Ctx(ctx).Infof("[shipment_force]shipment_force data:%s", util.Obj2JsonStr(task))

		// 处理单个用户发货
		if err := logic.ProcessUserShipment(ctx, task.OpenUserID, task.Operator, task.Items, task.ItemID); err != nil {
			log.Ctx(ctx).Errorf("处理用户 %s 发货失败，商品ID:%s: %v", task.OpenUserID, task.ItemID, err)
			//return fmt.Errorf("处理用户 %s 发货失败: %w", task.OpenUserID, err)
		}

		log.Ctx(ctx).Infof("用户 %s 发货处理完成，商品ID:%s", task.OpenUserID, task.ItemID)

		// 控制消费速度，避免云仓那边处理不过来（每秒最多处理10个订单）
		time.Sleep(time.Second / 10)

		return nil
	}

	// 使用安全处理器包装，防止panic
	return middlewares.SafeHandler(handler)
}

// ShipmentTask 强制发货任务消息
// 用于Kafka消息传递
// @Description 强制发货任务消息结构
// @Description 用于Kafka消息传递的任务信息
type ShipmentTask struct {
	// OpenUserID 用户OpenID
	OpenUserID string `json:"open_user_id"`
	// Operator 操作人信息
	Operator *pat.CheckAdmJwtUserInfo `json:"operator"`
	// Items 待发货商品列表
	Items []*yc_open.GetUserItemsByItemIdListResItem `json:"items"`
	// ItemID 商品ID
	ItemID string `json:"item_id"`
}
