package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	BM300001Err = response.NewError(300001, "库存不足")
	BM300002Err = response.NewError(300002, "同一商品在同一时段只能上架一个")
	BM300003Err = response.NewError(300003, "实物商品不存在")
	BM300004Err = response.NewError(300004, "活动已开始，不能修改开始时间")
	BM300005Err = response.NewError(300005, "只能修改待上架和已上架的活动")
	BM300006Err = response.NewError(300006, "当前无法修改积分商品状态")
	BM300007Err = response.NewError(300007, "兑换超限")
	BM300008Err = response.NewError(300008, "您的积分不足")
	BM300009Err = response.NewError(300009, "活动还未开始")
	BM300010Err = response.NewError(300010, "商品不可兑换")
	BM300011Err = response.NewError(300011, "请先绑定云仓")
	BM300012Err = response.NewError(300012, "仅在交易时间内可兑换")
	BM300013Err = response.NewError(300013, "商品补货中，请稍后再试")
)
