package enums

// PerUserLimitRefreshRateEnum 每人限兑刷新频率枚举
type PerUserLimitRefreshRateEnum string

func (r PerUserLimitRefreshRateEnum) Val() string {
	return string(r)
}

const (
	PerUserLimitRefreshRateNotRefresh PerUserLimitRefreshRateEnum = "not_refresh"
	PerUserLimitRefreshRateEveryDay   PerUserLimitRefreshRateEnum = "1d"
)

// ExchangeUserTypeEnum 可兑人群枚举
type ExchangeUserTypeEnum int32

func (r ExchangeUserTypeEnum) Val() int32 {
	return int32(r)
}

const (
	ExchangeUserTypeAll ExchangeUserTypeEnum = 0 // 不限人群
)

type BonusItemStatus int32

func (r BonusItemStatus) Val() int32 {
	return int32(r)
}

const (
	BonusItemStatusPending   BonusItemStatus = 0 // 待上架
	BonusItemStatusAvailable BonusItemStatus = 1 // 已上架
	BonusItemStatusDisable   BonusItemStatus = 2 // 已下架
	BonusItemStatusExpired   BonusItemStatus = 3 // 已结束
)
