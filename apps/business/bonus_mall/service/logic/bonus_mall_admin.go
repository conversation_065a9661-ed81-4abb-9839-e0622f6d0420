package logic

import (
	"app_service/apps/business/bonus_mall/dal/model"
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/define/enums"
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/util"
	"time"
)

func CheckAddBonusItem(steamItem *mongdb.SteamItem, req *define.AddBonusItemReq, existsBonusItems []*model.BonusItem) error {
	// 检查库存
	if req.StockQty > steamItem.MarketPrices.Unx.SellListings {
		return define.BM300001Err
	}

	// 同一个实物商品在某一时间段内只能有一个积分商品
	if err := checkExchangeStartTime(req.ExchangeStartTime, existsBonusItems); err != nil {
		return err
	}

	return nil
}

func checkExchangeStartTime(exchangeStartTime time.Time, bonusItems []*model.BonusItem) error {
	for _, bonusItem := range bonusItems {
		if exchangeStartTime.Before(bonusItem.ExchangeEndTime) || exchangeStartTime.Equal(bonusItem.ExchangeEndTime) {
			return define.BM300002Err
		}
	}

	return nil
}

func CheckEditBonusItem(steamItem *mongdb.SteamItem, currentBonusItem *model.BonusItem, req *define.EditBonusItemReq,
	otherBonusItems []*model.BonusItem) error {
	// 只能修改待上架和已上架的积分商品
	statusList := []int32{enums.BonusItemStatusPending.Val(), enums.BonusItemStatusAvailable.Val()}
	if !util.FindIntInSlice(currentBonusItem.Status, statusList) {
		return define.BM300005Err
	}

	// 检查库存
	if req.StockQty > steamItem.MarketPrices.Unx.SellListings {
		return define.BM300001Err
	}

	// 同一个实物商品在某一时间段内只能有一个积分商品
	if err := checkExchangeStartTime(req.ExchangeStartTime, otherBonusItems); err != nil {
		return err
	}

	// 如果活动已经开始，不能修改开始时间
	if currentBonusItem.ExchangeStartTime.After(util.Now()) &&
		!req.ExchangeStartTime.Equal(currentBonusItem.ExchangeStartTime) {
		return define.BM300004Err
	}

	return nil
}

func CheckEditBonusItemStatus(currentBonusItem *model.BonusItem, req *define.EditBonusItemStatusReq) error {
	// 只能上架/下架
	statusList := []int32{enums.BonusItemStatusAvailable.Val(), enums.BonusItemStatusDisable.Val()}
	if !util.FindIntInSlice(req.Status, statusList) {
		return define.BM300006Err
	}

	// 上架
	if req.Status == enums.BonusItemStatusAvailable.Val() &&
		currentBonusItem.Status != enums.BonusItemStatusPending.Val() {
		return define.BM300006Err
	}

	// 下架
	if req.Status == enums.BonusItemStatusDisable.Val() &&
		currentBonusItem.Status != enums.BonusItemStatusAvailable.Val() {
		return define.BM300006Err
	}

	return nil
}
