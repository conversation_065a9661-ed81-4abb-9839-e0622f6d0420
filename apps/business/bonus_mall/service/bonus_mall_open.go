package service

import (
	"app_service/apps/business/bonus_mall/dal/model"
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/define/enums"
	"app_service/apps/business/bonus_mall/repo"
	commondefine "app_service/apps/platform/common/define"
	commonFacade "app_service/apps/platform/common/facade"
	"app_service/global"
	"app_service/pkg/search"
	"app_service/pkg/util"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"encoding/json"
	"fmt"
	"time"
)

// FinishBonusItem 检查并结束到期的积分商品活动
func (s *Service) FinishBonusItem(req *define.FinishBonusItemReq) (*define.FinishBonusItemResp, error) {
	bonusItemSchema := repo.GetQuery().BonusItem
	updateBonusItem := &model.BonusItem{
		Status: enums.BonusItemStatusExpired.Val(),
	}
	q := search.NewQueryBuilder().
		Eq(bonusItemSchema.Status, enums.BonusItemStatusAvailable.Val()).
		Lt(bonusItemSchema.ExchangeEndTime, util.Now()).
		Build()

	_ = repo.NewBonusItemRepo(bonusItemSchema.WithContext(s.ctx)).Update(updateBonusItem, q)

	return &define.FinishBonusItemResp{}, nil
}

// CheckAndAlarmAbnormalExchangeLog 检查并告警积分兑换异常情况
func (s *Service) CheckAndAlarmAbnormalExchangeLog(req *define.CheckAndAlarmAbnormalExchangeLogReq) (*define.CheckAndAlarmAbnormalExchangeLogResp, error) {
	// 最近三天，处于 pending 状态超过 5 分钟的兑换记录
	exLogSchema := repo.GetQuery().BonusItemExchangeLog
	fiveMinutesAgo := util.Now().Add(-5 * time.Minute)
	threeDaysAgo := util.Now().Add(-3 * 24 * time.Hour)
	qb := search.NewQueryBuilder().
		Select(exLogSchema.BonusItemExchangeLogID).
		Eq(exLogSchema.Status, enums.BonusItemStatusPending.Val()).
		Gt(exLogSchema.CreatedAt, threeDaysAgo).
		Lt(exLogSchema.CreatedAt, fiveMinutesAgo).
		Build()
	total, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(s.ctx)).Count(qb)
	if err != nil {
		log.Ctx(s.ctx).Errorf("checkAndAlarmPendingExchangeLog.count error: %s", err.Error())
		return nil, err
	}
	if total == 0 {
		return &define.CheckAndAlarmAbnormalExchangeLogResp{}, nil
	}
	exLogs, err := repo.NewBonusItemExchangeLogRepo(exLogSchema.WithContext(s.ctx)).SelectList(qb)
	if err != nil {
		log.Ctx(s.ctx).Errorf("checkAndAlarmPendingExchangeLog.selectlist error: %s", err.Error())
		return nil, err
	}
	exLogIDs := make([]int64, len(exLogs))
	for idx, exLog := range exLogs {
		exLogIDs[idx] = exLog.BonusItemExchangeLogID
	}

	ids, _ := json.Marshal(exLogIDs)
	logURL := "https://console.huaweicloud.com/lts/?region=cn-east-3#/cts/logEventsLeftMenu/events?groupId=6f4c6d6d-13ad-46d2-a1cb-3cfd96106309&topicId=f04897e6-f750-434a-9551-1dfc574e8d02&epsId=0"
	if global.GlobalConfig.Service.Env == global.EnvProd {
		logURL = "https://console.huaweicloud.com/lts/?region=cn-east-3&locale=zh-cn#/cts/logEventsLeftMenu/events?groupId=177ef6d2-2d3f-416b-9141-9c41f1138b8f&topicId=7c972005-9e86-4852-8940-f9f38a29cf18&epsId=0"
	}
	msg := fmt.Sprintf("有 %d 条积分兑换异常记录，请及时处理！\n兑换记录 ID: %s\n日志链接：%s", total, ids, logURL)
	warnOption := &commondefine.WarnMsgMentionOption{
		MentionedMobileList: []string{"***********"},
	}
	commonFacade.SendDefaultWarnMsgWithMentionOption(s.ctx, "积分商城兑换异常", msg, warnOption)

	return &define.CheckAndAlarmAbnormalExchangeLogResp{
		AbnormalExchangeLogIDs: exLogIDs,
		Total:                  total,
	}, nil
}

// ClearExpiredExchangeLimitCache 清除过期的积分兑换限制缓存
func (s *Service) ClearExpiredExchangeLimitCache(req *define.ClearExpiredExchangeLimitCacheReq) (*define.ClearExpiredExchangeLimitCacheResp, error) {
	logPrefix := "[ClearExpiredExchangeLimitCache]"
	ctx := s.ctx
	// 删除过期积分商品的 Redis 缓存
	// 3天内且已结束一天的数据
	bonusItemSchema := repo.GetQuery().BonusItem
	startTime := util.Now().Add(-3 * 24 * time.Hour)
	endTime := util.Now().Add(-24 * time.Hour)
	statusList := []int32{enums.BonusItemStatusDisable.Val(), enums.BonusItemStatusExpired.Val()}
	expiredQb := search.NewQueryBuilder().
		Select(bonusItemSchema.BonusItemID).
		In(bonusItemSchema.Status, statusList).
		Gte(bonusItemSchema.ExchangeEndTime, startTime).
		Lt(bonusItemSchema.ExchangeEndTime, endTime).
		Build()
	expiredBonusItemList, err := repo.NewBonusItemRepo(bonusItemSchema.WithContext(ctx)).SelectList(expiredQb)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" get bonus item list err: %v", err)
		return nil, err
	}

	log.Ctx(ctx).Debugf(logPrefix+" expiredBonusItemList total: %d", len(expiredBonusItemList))
	for _, item := range expiredBonusItemList {
		keys, err := global.REDIS.Keys(ctx, fmt.Sprintf("app_service:bonus_item:%d:user_total:*", item.BonusItemID)).Result()
		if err != nil {
			log.Ctx(ctx).Debugf(logPrefix+"redis keys err: %v", err)
			continue
		}
		log.Ctx(ctx).Debugf(logPrefix+" keys for bonus item(%d): %v", item.BonusItemID, keys)
		for _, key := range keys {
			d, err := global.REDIS.TTL(ctx, key).Result()
			if err != nil {
				log.Ctx(ctx).Debugf(logPrefix+" key: %s, redis ttl err: %v", key, err)
				continue
			}
			if d.Nanoseconds() == -1 {
				log.Ctx(ctx).Debugf(logPrefix+" deleting key: %s", key)
				_, err := global.REDIS.Del(ctx, key).Result()
				if err != nil {
					log.Ctx(ctx).Debugf(logPrefix+" key: %s, redis del err: %v", key, err)
					continue
				}
			}
		}
	}

	return &define.ClearExpiredExchangeLimitCacheResp{}, nil
}
