package open

import (
	"app_service/apps/business/bonus_mall/api/open"
	"github.com/gin-gonic/gin"
)

func BonusMall(router *gin.RouterGroup) {
	group := router.Group("/bonus_mall")
	{
		bonusItemGroup := group.Group("/bonus_item")
		// 结束到期的积分商品活动
		bonusItemGroup.POST("/finish", open.FinishBonusItem)
	}
	{
		exLogGroup := group.Group("/exchange_log")
		// 检查并告警积分兑换异常情况
		exLogGroup.POST("/check_and_alarm", open.CheckAndAlarmAbnormalExchangeLog)
		// 清除过期的积分兑换限制缓存
		exLogGroup.POST("/clear_expired_cache", open.ClearExpiredExchangeLimitCache)
	}
}
