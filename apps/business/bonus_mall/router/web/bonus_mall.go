package web

import (
	"app_service/apps/business/bonus_mall/api/web"
	"github.com/gin-gonic/gin"
)

// BonusMall 积分商城用户端相关路由
func BonusMall(router *gin.RouterGroup) {
	group := router.Group("/bonus_mall")
	{
		exchangeLogGroup := group.Group("/exchange_log")
		// 获取最新兑换记录列表
		exchangeLogGroup.GET("/top_list", web.GetExchangeLogWebTopList)
		// 获取用户兑换记录列表
		exchangeLogGroup.GET("/list", web.GetUserExchangeLogWebList)
	}
	{
		bonusItemGroup := group.Group("/bonus_item")
		// 获取积分商品列表
		bonusItemGroup.GET("/list", web.GetBonusItemWebList)
		// 获取积分商品详情
		bonusItemGroup.GET("/detail", web.GetBonusItemWebDetail)
		// 兑换积分商品
		bonusItemGroup.POST("/exchange", web.UserExchangeBonusItem)
	}
}
