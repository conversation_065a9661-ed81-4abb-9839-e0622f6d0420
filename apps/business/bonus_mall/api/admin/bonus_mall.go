package admin

import (
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
)

// AddBonusItem
// @Summary 新增积分商品
// @Description 新增积分商品
// @Tags 管理端-积分商城管理
// @Param data body define.AddBonusItemReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddBonusItemResp}
// @Router  /admin/v1/bonus_mall/bonus_item/add [POST]
// @Security Bearer
func AddBonusItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddBonusItemReq{}, s.AddBonusItem)
}

// EditBonusItem
// @Summary 修改积分商品
// @Description 修改积分商品
// @Tags 管理端-积分商城管理
// @Param data body define.EditBonusItemReq true "修改参数"
// @Success 200 {object} response.Data{data=define.EditBonusItemResp}
// @Router  /admin/v1/bonus_mall/bonus_item/edit [POST]
// @Security Bearer
func EditBonusItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditBonusItemReq{}, s.EditBonusItem)
}

// EditBonusItemStatus
// @Summary 修改积分商品状态
// @Description 修改积分商品状态
// @Tags 管理端-积分商城管理
// @Param data body define.EditBonusItemStatusReq true "修改参数"
// @Success 200 {object} response.Data{data=define.EditBonusItemStatusResp}
// @Router  /admin/v1/bonus_mall/bonus_item/edit_status [POST]
// @Security Bearer
func EditBonusItemStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditBonusItemStatusReq{}, s.EditBonusItemStatus)
}

// GetBonusItemAdminDetail
// @Summary 获取积分商品详情
// @Description 获取积分商品详情
// @Tags 管理端-积分商城管理
// @Param data body define.GetBonusItemDetailAdminReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetBonusItemDetailAdminResp}
// @Router  /admin/v1/bonus_mall/bonus_item/detail [GET]
// @Security Bearer
func GetBonusItemAdminDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetBonusItemDetailAdminReq{}, s.GetBonusItemAdminDetail)
}

// GetBonusItemAdminList
// @Summary 获取积分商品列表
// @Description 获取积分商品列表
// @Tags 管理端-积分商城管理
// @Param data body define.GetBonusItemListAdminReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetBonusItemListAdminResp}
// @Router  /admin/v1/bonus_mall/bonus_item/list [GET]
// @Security Bearer
func GetBonusItemAdminList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetBonusItemListAdminReq{}, s.GetBonusItemAdminList)
}

// GetExchangeLogAdminList
// @Summary 获取积分商品兑换记录列表
// @Description 获取积分商品兑换记录列表
// @Tags 管理端-积分商城管理
// @Param data body define.GetExchangeLogListAdminReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetExchangeLogListAdminResp}
// @Router  /admin/v1/bonus_mall/exchange_log/list [GET]
// @Security Bearer
func GetExchangeLogAdminList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetExchangeLogListAdminReq{}, s.GetExchangeLogAdminList)
}

// ExportExchangeLogList
// @Summary 导出积分商品兑换记录列表
// @Description 导出积分商品兑换记录列表
// @Tags 管理端-积分商城管理
// @Param data query define.GetExchangeLogListAdminReq true "查询参数"
// @Success 200 {object} response.Data{}
// @Router  /admin/v1/bonus_mall/exchange_log/list_export [GET]
// @Security Bearer
func ExportExchangeLogList(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.GetExchangeLogListAdminReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportExchangeLogList(ctx, req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}
