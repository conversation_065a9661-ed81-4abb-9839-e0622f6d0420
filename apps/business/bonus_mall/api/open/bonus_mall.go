package open

import (
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// FinishBonusItem
// @Summary 自动结束到期的积分商品活动
// @Description 自动结束到期的积分商品活动
// @Tags Open端-积分商城
// @Param data body define.FinishBonusItemReq true "参数"
// @Success 200 {object} response.Data{data=define.FinishBonusItemResp}
// @Router /open/v1/bonus_mall/bonus_item/finish [POST]
// @Security Bearer
func FinishBonusItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.FinishBonusItemReq{}, s.FinishBonusItem)
}

// CheckAndAlarmAbnormalExchangeLog
// @Summary 检查并告警积分兑换异常情况
// @Description 检查并告警积分兑换异常情况
// @Tags Open端-积分商城
// @Param data body define.CheckAndAlarmAbnormalExchangeLogReq true "参数"
// @Success 200 {object} response.Data{data=define.CheckAndAlarmAbnormalExchangeLogResp}
// @Router /open/v1/bonus_mall/exchange_log/check_and_alarm [POST]
// @Security Bearer
func CheckAndAlarmAbnormalExchangeLog(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.CheckAndAlarmAbnormalExchangeLogReq{}, s.CheckAndAlarmAbnormalExchangeLog)
}

// ClearExpiredExchangeLimitCache
// @Summary 清除过期的积分兑换限制缓存
// @Description 清除过期的积分兑换限制缓存
// @Tags Open端-积分商城
// @Param data body define.ClearExpiredExchangeLimitCacheReq true "参数"
// @Success 200 {object} response.Data{data=define.ClearExpiredExchangeLimitCacheResp}
// @Router /open/v1/bonus_mall/exchange_log/clear_expired_cache [POST]
// @Security Bearer
func ClearExpiredExchangeLimitCache(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ClearExpiredExchangeLimitCacheReq{}, s.ClearExpiredExchangeLimitCache)
}
