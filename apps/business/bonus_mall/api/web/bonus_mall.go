package web

import (
	"app_service/apps/business/bonus_mall/define"
	"app_service/apps/business/bonus_mall/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetExchangeLogWebTopList
// @Summary 获取最新兑换记录列表
// @Description 获取最新兑换记录列表
// @Tags 用户端-积分商城
// @Param data body define.GetExchangeLogWebTopListReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetExchangeLogWebTopListResp}
// @Router  /web/v1/bonus_mall/exchange_log/top_list [GET]
// @Security Bearer
func GetExchangeLogWebTopList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetExchangeLogWebTopListReq{}, s.GetExchangeLogWebTopList)
}

// GetUserExchangeLogWebList
// @Summary 获取用户兑换记录列表
// @Description 获取用户兑换记录列表
// @Tags 用户端-积分商城
// @Param data body define.GetUserExchangeLogWebListReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetUserExchangeLogWebListResp}
// @Router  /web/v1/bonus_mall/exchange_log/list [GET]
// @Security Bearer
func GetUserExchangeLogWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetUserExchangeLogWebListReq{}, s.GetUserExchangeLogWebList)
}

// GetBonusItemWebList
// @Summary 获取积分商品列表
// @Description 获取积分商品列表
// @Tags 用户端-积分商城
// @Param data body define.GetBonusItemWebListReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetBonusItemWebListResp}
// @Router  /web/v1/bonus_mall/bonus_item/list [GET]
// @Security Bearer
func GetBonusItemWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetBonusItemWebListReq{}, s.GetBonusItemWebList)
}

// GetBonusItemWebDetail
// @Summary 获取积分商品详情
// @Description 获取积分商品详情
// @Tags 用户端-积分商城
// @Param data body define.GetBonusItemWebDetailReq true "获取参数"
// @Success 200 {object} response.Data{data=define.GetBonusItemWebDetailResp}
// @Router  /web/v1/bonus_mall/bonus_item/detail [GET]
// @Security Bearer
func GetBonusItemWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetBonusItemWebDetailReq{}, s.GetBonusItemWebDetail)
}

// UserExchangeBonusItem
// @Summary 兑换积分商品
// @Description 兑换积分商品
// @Tags 用户端-积分商城
// @Param data body define.UserExchangeBonusItemReq true "获取参数"
// @Success 200 {object} response.Data{data=define.UserExchangeBonusItemResp}
// @Router  /web/v1/bonus_mall/bonus_item/exchange [POST]
// @Security Bearer
func UserExchangeBonusItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UserExchangeBonusItemReq{}, s.UserExchangeBonusItem)
}
