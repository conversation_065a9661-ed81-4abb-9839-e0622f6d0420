// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameBonusItem = "bonus_item"

// BonusItem 积分商品表
type BonusItem struct {
	BonusItemID             int64     `gorm:"column:bonus_item_id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键id" json:"bonus_item_id"`                                                      // 主键id
	SteamItemID             string    `gorm:"column:steam_item_id;type:char(24);not null;comment:实物商品id(tmt.steam_items._id)" json:"steam_item_id"`                                                           // 实物商品id(tmt.steam_items._id)
	SkuNo                   string    `gorm:"column:sku_no;type:varchar(64);not null;comment:商品sku" json:"sku_no"`                                                                                            // 商品sku
	ItemName                string    `gorm:"column:item_name;type:varchar(256);comment:商品名称" json:"item_name"`                                                                                               // 商品名称
	IconURL                 string    `gorm:"column:icon_url;type:varchar(256);comment:商品图片（tmt.steam_item.icon_url）" json:"icon_url"`                                                                        // 商品图片（tmt.steam_item.icon_url）
	Status                  int32     `gorm:"column:status;type:tinyint;not null;comment:状态，0：待上架，1：已上架，2：已下架，3：已结束" json:"status"`                                                                           // 状态，0：待上架，1：已上架，2：已下架，3：已结束
	ExchangePrice           int32     `gorm:"column:exchange_price;type:int;not null;comment:兑换价格（积分）" json:"exchange_price"`                                                                                 // 兑换价格（积分）
	StockQty                int32     `gorm:"column:stock_qty;type:int;not null;comment:可兑换数量/库存" json:"stock_qty"`                                                                                           // 可兑换数量/库存
	ExchangedQty            int32     `gorm:"column:exchanged_qty;type:int;not null;comment:已兑数量" json:"exchanged_qty"`                                                                                       // 已兑数量
	ExchangeStartTime       time.Time `gorm:"column:exchange_start_time;type:datetime;not null;comment:可兑开始时间" json:"exchange_start_time"`                                                                    // 可兑开始时间
	ExchangeEndTime         time.Time `gorm:"column:exchange_end_time;type:datetime;not null;comment:可兑结束时间" json:"exchange_end_time"`                                                                        // 可兑结束时间
	PerUserLimitQty         int32     `gorm:"column:per_user_limit_qty;type:int;not null;comment:每人限兑数量，-1 表示不限" json:"per_user_limit_qty"`                                                                   // 每人限兑数量，-1 表示不限
	PerUserLimitRefreshRate string    `gorm:"column:per_user_limit_refresh_rate;type:varchar(64);not null;default:not_refresh;comment:每人限兑刷新频率，not_refresh: 不刷新，1d: 每天刷新" json:"per_user_limit_refresh_rate"` // 每人限兑刷新频率，not_refresh: 不刷新，1d: 每天刷新
	ExchangeUserType        int32     `gorm:"column:exchange_user_type;type:tinyint;not null;comment:可兑人群，-1：不限" json:"exchange_user_type"`                                                                   // 可兑人群，-1：不限
	Priority                int32     `gorm:"column:priority;type:int unsigned;not null;comment:优先级" json:"priority"`                                                                                         // 优先级
	CreatedBy               string    `gorm:"column:created_by;type:char(24);comment:创建人id" json:"created_by"`                                                                                                // 创建人id
	CreatedAt               time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                                              // 创建时间
	UpdatedAt               time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                                              // 更新时间
}

// TableName BonusItem's table name
func (*BonusItem) TableName() string {
	return TableNameBonusItem
}
