// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/bonus_mall/dal/model"
)

func newBonusItemExchangeLog(db *gorm.DB, opts ...gen.DOOption) bonusItemExchangeLog {
	_bonusItemExchangeLog := bonusItemExchangeLog{}

	_bonusItemExchangeLog.bonusItemExchangeLogDo.UseDB(db, opts...)
	_bonusItemExchangeLog.bonusItemExchangeLogDo.UseModel(&model.BonusItemExchangeLog{})

	tableName := _bonusItemExchangeLog.bonusItemExchangeLogDo.TableName()
	_bonusItemExchangeLog.ALL = field.NewAsterisk(tableName)
	_bonusItemExchangeLog.BonusItemExchangeLogID = field.NewInt64(tableName, "bonus_item_exchange_log_id")
	_bonusItemExchangeLog.BonusItemID = field.NewInt64(tableName, "bonus_item_id")
	_bonusItemExchangeLog.SteamItemID = field.NewString(tableName, "steam_item_id")
	_bonusItemExchangeLog.SkuNo = field.NewString(tableName, "sku_no")
	_bonusItemExchangeLog.ItemName = field.NewString(tableName, "item_name")
	_bonusItemExchangeLog.IconURL = field.NewString(tableName, "icon_url")
	_bonusItemExchangeLog.UserID = field.NewString(tableName, "user_id")
	_bonusItemExchangeLog.MobilePhone = field.NewString(tableName, "mobile_phone")
	_bonusItemExchangeLog.Nickname = field.NewString(tableName, "nickname")
	_bonusItemExchangeLog.UserAvatar = field.NewString(tableName, "user_avatar")
	_bonusItemExchangeLog.ExchangeTime = field.NewTime(tableName, "exchange_time")
	_bonusItemExchangeLog.ExchangePrice = field.NewInt32(tableName, "exchange_price")
	_bonusItemExchangeLog.ExchangeQty = field.NewInt32(tableName, "exchange_qty")
	_bonusItemExchangeLog.BonusTotal = field.NewInt32(tableName, "bonus_total")
	_bonusItemExchangeLog.CostTotal = field.NewInt32(tableName, "cost_total")
	_bonusItemExchangeLog.Status = field.NewInt32(tableName, "status")
	_bonusItemExchangeLog.FailedReason = field.NewString(tableName, "failed_reason")
	_bonusItemExchangeLog.YcWithdrawOrderID = field.NewString(tableName, "yc_withdraw_order_id")
	_bonusItemExchangeLog.Extra = field.NewField(tableName, "extra")
	_bonusItemExchangeLog.CreatedAt = field.NewTime(tableName, "created_at")
	_bonusItemExchangeLog.UpdatedAt = field.NewTime(tableName, "updated_at")

	_bonusItemExchangeLog.fillFieldMap()

	return _bonusItemExchangeLog
}

// bonusItemExchangeLog 积分商品兑换记录表
type bonusItemExchangeLog struct {
	bonusItemExchangeLogDo

	ALL                    field.Asterisk
	BonusItemExchangeLogID field.Int64  // 主健id
	BonusItemID            field.Int64  // 积分商品id
	SteamItemID            field.String // 实物商品id(tmt.steam_items._id)
	SkuNo                  field.String // 商品sku
	ItemName               field.String // 商品名称
	IconURL                field.String // 商品图片（tmt.steam_item.icon_url）
	UserID                 field.String // 用户id(pat.users._id)
	MobilePhone            field.String // 用户手机号
	Nickname               field.String // 用户昵称
	UserAvatar             field.String // 用户头像
	ExchangeTime           field.Time   // 兑换时间
	ExchangePrice          field.Int32  // 兑换价格（积分）
	ExchangeQty            field.Int32  // 兑换数量
	BonusTotal             field.Int32  // 消耗积分
	CostTotal              field.Int32  // 合计成本（单位：分）
	Status                 field.Int32  // 状态，0：处理中，1：成功，2：失败
	FailedReason           field.String // 失败原因
	YcWithdrawOrderID      field.String // 云仓订单 id（item_withdraw_orders._id）
	Extra                  field.Field  // 额外信息
	CreatedAt              field.Time   // 创建时间
	UpdatedAt              field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (b bonusItemExchangeLog) Table(newTableName string) *bonusItemExchangeLog {
	b.bonusItemExchangeLogDo.UseTable(newTableName)
	return b.updateTableName(newTableName)
}

func (b bonusItemExchangeLog) As(alias string) *bonusItemExchangeLog {
	b.bonusItemExchangeLogDo.DO = *(b.bonusItemExchangeLogDo.As(alias).(*gen.DO))
	return b.updateTableName(alias)
}

func (b *bonusItemExchangeLog) updateTableName(table string) *bonusItemExchangeLog {
	b.ALL = field.NewAsterisk(table)
	b.BonusItemExchangeLogID = field.NewInt64(table, "bonus_item_exchange_log_id")
	b.BonusItemID = field.NewInt64(table, "bonus_item_id")
	b.SteamItemID = field.NewString(table, "steam_item_id")
	b.SkuNo = field.NewString(table, "sku_no")
	b.ItemName = field.NewString(table, "item_name")
	b.IconURL = field.NewString(table, "icon_url")
	b.UserID = field.NewString(table, "user_id")
	b.MobilePhone = field.NewString(table, "mobile_phone")
	b.Nickname = field.NewString(table, "nickname")
	b.UserAvatar = field.NewString(table, "user_avatar")
	b.ExchangeTime = field.NewTime(table, "exchange_time")
	b.ExchangePrice = field.NewInt32(table, "exchange_price")
	b.ExchangeQty = field.NewInt32(table, "exchange_qty")
	b.BonusTotal = field.NewInt32(table, "bonus_total")
	b.CostTotal = field.NewInt32(table, "cost_total")
	b.Status = field.NewInt32(table, "status")
	b.FailedReason = field.NewString(table, "failed_reason")
	b.YcWithdrawOrderID = field.NewString(table, "yc_withdraw_order_id")
	b.Extra = field.NewField(table, "extra")
	b.CreatedAt = field.NewTime(table, "created_at")
	b.UpdatedAt = field.NewTime(table, "updated_at")

	b.fillFieldMap()

	return b
}

func (b *bonusItemExchangeLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := b.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (b *bonusItemExchangeLog) fillFieldMap() {
	b.fieldMap = make(map[string]field.Expr, 21)
	b.fieldMap["bonus_item_exchange_log_id"] = b.BonusItemExchangeLogID
	b.fieldMap["bonus_item_id"] = b.BonusItemID
	b.fieldMap["steam_item_id"] = b.SteamItemID
	b.fieldMap["sku_no"] = b.SkuNo
	b.fieldMap["item_name"] = b.ItemName
	b.fieldMap["icon_url"] = b.IconURL
	b.fieldMap["user_id"] = b.UserID
	b.fieldMap["mobile_phone"] = b.MobilePhone
	b.fieldMap["nickname"] = b.Nickname
	b.fieldMap["user_avatar"] = b.UserAvatar
	b.fieldMap["exchange_time"] = b.ExchangeTime
	b.fieldMap["exchange_price"] = b.ExchangePrice
	b.fieldMap["exchange_qty"] = b.ExchangeQty
	b.fieldMap["bonus_total"] = b.BonusTotal
	b.fieldMap["cost_total"] = b.CostTotal
	b.fieldMap["status"] = b.Status
	b.fieldMap["failed_reason"] = b.FailedReason
	b.fieldMap["yc_withdraw_order_id"] = b.YcWithdrawOrderID
	b.fieldMap["extra"] = b.Extra
	b.fieldMap["created_at"] = b.CreatedAt
	b.fieldMap["updated_at"] = b.UpdatedAt
}

func (b bonusItemExchangeLog) clone(db *gorm.DB) bonusItemExchangeLog {
	b.bonusItemExchangeLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return b
}

func (b bonusItemExchangeLog) replaceDB(db *gorm.DB) bonusItemExchangeLog {
	b.bonusItemExchangeLogDo.ReplaceDB(db)
	return b
}

type bonusItemExchangeLogDo struct{ gen.DO }

type IBonusItemExchangeLogDo interface {
	gen.SubQuery
	Debug() IBonusItemExchangeLogDo
	WithContext(ctx context.Context) IBonusItemExchangeLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IBonusItemExchangeLogDo
	WriteDB() IBonusItemExchangeLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IBonusItemExchangeLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IBonusItemExchangeLogDo
	Not(conds ...gen.Condition) IBonusItemExchangeLogDo
	Or(conds ...gen.Condition) IBonusItemExchangeLogDo
	Select(conds ...field.Expr) IBonusItemExchangeLogDo
	Where(conds ...gen.Condition) IBonusItemExchangeLogDo
	Order(conds ...field.Expr) IBonusItemExchangeLogDo
	Distinct(cols ...field.Expr) IBonusItemExchangeLogDo
	Omit(cols ...field.Expr) IBonusItemExchangeLogDo
	Join(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo
	Group(cols ...field.Expr) IBonusItemExchangeLogDo
	Having(conds ...gen.Condition) IBonusItemExchangeLogDo
	Limit(limit int) IBonusItemExchangeLogDo
	Offset(offset int) IBonusItemExchangeLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IBonusItemExchangeLogDo
	Unscoped() IBonusItemExchangeLogDo
	Create(values ...*model.BonusItemExchangeLog) error
	CreateInBatches(values []*model.BonusItemExchangeLog, batchSize int) error
	Save(values ...*model.BonusItemExchangeLog) error
	First() (*model.BonusItemExchangeLog, error)
	Take() (*model.BonusItemExchangeLog, error)
	Last() (*model.BonusItemExchangeLog, error)
	Find() ([]*model.BonusItemExchangeLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BonusItemExchangeLog, err error)
	FindInBatches(result *[]*model.BonusItemExchangeLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.BonusItemExchangeLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IBonusItemExchangeLogDo
	Assign(attrs ...field.AssignExpr) IBonusItemExchangeLogDo
	Joins(fields ...field.RelationField) IBonusItemExchangeLogDo
	Preload(fields ...field.RelationField) IBonusItemExchangeLogDo
	FirstOrInit() (*model.BonusItemExchangeLog, error)
	FirstOrCreate() (*model.BonusItemExchangeLog, error)
	FindByPage(offset int, limit int) (result []*model.BonusItemExchangeLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IBonusItemExchangeLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (b bonusItemExchangeLogDo) Debug() IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Debug())
}

func (b bonusItemExchangeLogDo) WithContext(ctx context.Context) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.WithContext(ctx))
}

func (b bonusItemExchangeLogDo) ReadDB() IBonusItemExchangeLogDo {
	return b.Clauses(dbresolver.Read)
}

func (b bonusItemExchangeLogDo) WriteDB() IBonusItemExchangeLogDo {
	return b.Clauses(dbresolver.Write)
}

func (b bonusItemExchangeLogDo) Session(config *gorm.Session) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Session(config))
}

func (b bonusItemExchangeLogDo) Clauses(conds ...clause.Expression) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Clauses(conds...))
}

func (b bonusItemExchangeLogDo) Returning(value interface{}, columns ...string) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Returning(value, columns...))
}

func (b bonusItemExchangeLogDo) Not(conds ...gen.Condition) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Not(conds...))
}

func (b bonusItemExchangeLogDo) Or(conds ...gen.Condition) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Or(conds...))
}

func (b bonusItemExchangeLogDo) Select(conds ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Select(conds...))
}

func (b bonusItemExchangeLogDo) Where(conds ...gen.Condition) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Where(conds...))
}

func (b bonusItemExchangeLogDo) Order(conds ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Order(conds...))
}

func (b bonusItemExchangeLogDo) Distinct(cols ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Distinct(cols...))
}

func (b bonusItemExchangeLogDo) Omit(cols ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Omit(cols...))
}

func (b bonusItemExchangeLogDo) Join(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Join(table, on...))
}

func (b bonusItemExchangeLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.LeftJoin(table, on...))
}

func (b bonusItemExchangeLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.RightJoin(table, on...))
}

func (b bonusItemExchangeLogDo) Group(cols ...field.Expr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Group(cols...))
}

func (b bonusItemExchangeLogDo) Having(conds ...gen.Condition) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Having(conds...))
}

func (b bonusItemExchangeLogDo) Limit(limit int) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Limit(limit))
}

func (b bonusItemExchangeLogDo) Offset(offset int) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Offset(offset))
}

func (b bonusItemExchangeLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Scopes(funcs...))
}

func (b bonusItemExchangeLogDo) Unscoped() IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Unscoped())
}

func (b bonusItemExchangeLogDo) Create(values ...*model.BonusItemExchangeLog) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Create(values)
}

func (b bonusItemExchangeLogDo) CreateInBatches(values []*model.BonusItemExchangeLog, batchSize int) error {
	return b.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (b bonusItemExchangeLogDo) Save(values ...*model.BonusItemExchangeLog) error {
	if len(values) == 0 {
		return nil
	}
	return b.DO.Save(values)
}

func (b bonusItemExchangeLogDo) First() (*model.BonusItemExchangeLog, error) {
	if result, err := b.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItemExchangeLog), nil
	}
}

func (b bonusItemExchangeLogDo) Take() (*model.BonusItemExchangeLog, error) {
	if result, err := b.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItemExchangeLog), nil
	}
}

func (b bonusItemExchangeLogDo) Last() (*model.BonusItemExchangeLog, error) {
	if result, err := b.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItemExchangeLog), nil
	}
}

func (b bonusItemExchangeLogDo) Find() ([]*model.BonusItemExchangeLog, error) {
	result, err := b.DO.Find()
	return result.([]*model.BonusItemExchangeLog), err
}

func (b bonusItemExchangeLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.BonusItemExchangeLog, err error) {
	buf := make([]*model.BonusItemExchangeLog, 0, batchSize)
	err = b.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (b bonusItemExchangeLogDo) FindInBatches(result *[]*model.BonusItemExchangeLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return b.DO.FindInBatches(result, batchSize, fc)
}

func (b bonusItemExchangeLogDo) Attrs(attrs ...field.AssignExpr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Attrs(attrs...))
}

func (b bonusItemExchangeLogDo) Assign(attrs ...field.AssignExpr) IBonusItemExchangeLogDo {
	return b.withDO(b.DO.Assign(attrs...))
}

func (b bonusItemExchangeLogDo) Joins(fields ...field.RelationField) IBonusItemExchangeLogDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Joins(_f))
	}
	return &b
}

func (b bonusItemExchangeLogDo) Preload(fields ...field.RelationField) IBonusItemExchangeLogDo {
	for _, _f := range fields {
		b = *b.withDO(b.DO.Preload(_f))
	}
	return &b
}

func (b bonusItemExchangeLogDo) FirstOrInit() (*model.BonusItemExchangeLog, error) {
	if result, err := b.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItemExchangeLog), nil
	}
}

func (b bonusItemExchangeLogDo) FirstOrCreate() (*model.BonusItemExchangeLog, error) {
	if result, err := b.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.BonusItemExchangeLog), nil
	}
}

func (b bonusItemExchangeLogDo) FindByPage(offset int, limit int) (result []*model.BonusItemExchangeLog, count int64, err error) {
	result, err = b.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = b.Offset(-1).Limit(-1).Count()
	return
}

func (b bonusItemExchangeLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = b.Count()
	if err != nil {
		return
	}

	err = b.Offset(offset).Limit(limit).Scan(result)
	return
}

func (b bonusItemExchangeLogDo) Scan(result interface{}) (err error) {
	return b.DO.Scan(result)
}

func (b bonusItemExchangeLogDo) Delete(models ...*model.BonusItemExchangeLog) (result gen.ResultInfo, err error) {
	return b.DO.Delete(models)
}

func (b *bonusItemExchangeLogDo) withDO(do gen.Dao) *bonusItemExchangeLogDo {
	b.DO = *do.(*gen.DO)
	return b
}
