package consume

import (
	"app_service/apps/business/trade/service/logic"
	common_constant "app_service/apps/platform/common/constant"
	common_define "app_service/apps/platform/common/define"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"time"
)

type CirculationControlConsumer struct {
	middlewares.BaseConsumer
}

func NewCirculationControlConsumer() *CirculationControlConsumer {
	return &CirculationControlConsumer{
		BaseConsumer: middlewares.NewBaseConsumer(common_constant.CirculationControl, ConsumerGroup),
	}
}

func (o *CirculationControlConsumer) GetTopic() string {
	return common_constant.CirculationControl
}

func (o *CirculationControlConsumer) GetGroup() string {
	return ConsumerGroup
}

func (o *CirculationControlConsumer) HandleFun() broker.Handler {
	handler := func(event broker.Event) error {
		// 初始化上下文及链路信息
		ctx, span := otel.Tracer(global.GlobalConfig.Service.Name).Start(context.Background(), "CirculationControlConsumer", trace.WithSpanKind(trace.SpanKindConsumer))
		defer span.End()
		log.Ctx(ctx).Infof("[%s:%s]kafka data:%s", common_constant.CirculationControl, ConsumerGroup, util.Obj2JsonStr(event.Message()))

		// 解析消息
		data := &issueItemCirculationControl{}
		err := util.JsonStr2Struct(string(event.Message().Body), data)
		if err != nil {
			log.Ctx(ctx).Errorf("消息解析失败 err:%v", err)
			return common_define.CommonWarnErr.Err(err)
		}

		return logic.UpsertCirculationItem(ctx, data.ItemID)
	}

	return middlewares.SafeHandler(handler)
}

type issueItemCirculationControl struct {
	ItemID                 string     `json:"item_id"`
	CirculationStatus      int32      `json:"circulation_status"`
	CirculationStart       *time.Time `json:"circulation_start"`
	CirculationEnd         *time.Time `json:"circulation_end"`
	CirculationEndShowType int32      `json:"circulation_end_show_type"`
}
