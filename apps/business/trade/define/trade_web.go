package define

import (
	"app_service/apps/platform/issue/dal/model/mongdb"
	"app_service/pkg/pagination"
	"time"
)

type (
	GetCirculationItemWebListReq struct {
		pagination.Pagination
		Type      string `form:"type" json:"type" binding:"required,oneof=all hot transaction_amount market_amount"` // 类型，all: 全部（二手默认列表页），hot: 热榜，transaction_amount: 成交额榜，market_amount: 市值榜
		OrderBy   string `form:"order_by" json:"order_by"`                                                           // 排序字段，transaction_amount(成交额) | market_amount(市值) | price_change_rate(涨跌幅)
		SortOrder string `form:"sort_order" json:"sort_order"`                                                       // 排序方式，asc(升序) | desc(降序)
	}
	CirculationItemWebListInfo struct {
		ID                     int64   `json:"id,string"`
		ItemID                 string  `json:"item_id"`
		ItemName               string  `json:"item_name"`
		ImageURL               string  `json:"image_url"`
		TotalCirculation       int64   `json:"total_circulation"`        // 流通数量
		MarketAmount           int64   `json:"market_amount"`            // 市值（单位：分）
		LatestTransactionPrice int64   `json:"latest_transaction_price"` // 最新成交价（单位：分）
		TransactionAmount      int64   `json:"transaction_amount"`       // 成交额（单位：分）
		PriceChangeRate        float32 `json:"price_change_rate"`        // 涨跌幅
		IsDelisted             bool    `json:"is_delisted"`              // 是否退市，true：已退市
	}
	GetCirculationItemWebListResp struct {
		List    []CirculationItemWebListInfo `json:"list"`
		HasMore bool                         `json:"has_more"`
	}
)

type (
	GetIssueItemWebTopListReq struct{}
	IssueItemWebTopListInfo   struct {
		ID       string `json:"id"`
		ItemID   string `json:"item_id"`
		ItemName string `json:"item_name"`
		ImageURL string `json:"image_url"`
		Quantity int32  `json:"quantity"`
		TopTag   string `json:"top_tag"` // 标签，new: 新，rush: 抢
	}
	GetIssueItemWebTopListResp struct {
		List []IssueItemWebTopListInfo `json:"list"`
	}
)

type (
	GetCirculationItemWebTopListReq struct{}
	CirculationItemWebTopListInfo   struct {
		ID              int64   `json:"id,string"`
		ItemID          string  `json:"item_id"`
		ItemName        string  `json:"item_name"`
		ImageURL        string  `json:"image_url"`
		PriceChangeRate float64 `json:"price_change_rate"` // 涨跌幅
	}
	GetCirculationItemWebTopListResp struct {
		List []CirculationItemWebTopListInfo `json:"list"`
	}
)

type (
	GetMarketWebOverviewReq  struct{}
	GetMarketWebOverviewResp struct {
		TransactionAmount int64  `json:"transaction_amount"` // 成交额（单位：分）
		DownCount         int64  `json:"down_count"`         // 跌
		UpCount           int64  `json:"up_count"`           // 涨
		FlatCount         int64  `json:"flat_count"`         // 平
		UpdateTime        string `json:"update_time"`        // 更新时间
	}
)

type (
	GetWebActivitiesReq struct{}
	ActivityWebItem     struct {
		ItemId            string                       `json:"item_id"`            // 商品ID
		ItemName          string                       `json:"item_name"`          // 商品名称
		PriceChanges      float64                      `json:"price_changes"`      // 价格变动
		CirculationStatus mongdb.CirculationStatusEnum `json:"circulation_status"` // 流通状态
		Status            mongdb.IssueItemStatusEnum   `json:"status"`             // 状态
	}
	ActivityWebInfo struct {
		ID          int64             `json:"id,string"`
		Type        string            `json:"type"`         // 类型，platform_announcement: 平台方公告，operation_announcement: 运营方公告，market_changes: 行情异动
		Title       string            `json:"title"`        // 标题
		PublishTime time.Time         `json:"publish_time"` // 发布时间
		Items       []ActivityWebItem `json:"items"`        // 商品列表（仅行情异动）
		IsCanJump   bool              `json:"is_can_jump"`  // 是否可跳转
	}
	GetWebActivitiesResp struct {
		List []ActivityWebInfo `json:"list"`
	}
)

type (
	GetCirculationItemWebOverviewReq struct{}
	HotWebOverviewInfo               struct {
		ItemID   string  `json:"item_id"`   // 商品 id
		ItemName string  `json:"item_name"` // 商品名称
		Rate     float32 `json:"rate"`      // 涨跌幅
	}
	TransactionAmountWebOverviewInfo struct {
		Amount int64   `json:"amount"` // 历史累计成交额（单位：分）
		Rate   float32 `json:"rate"`   // 成交额百分比
	}
	MarketAmountWebOverviewInfo struct {
		Amount int64   `json:"amount"` // 历史累计市值（单位：分）
		Rate   float32 `json:"rate"`   // 市值百分比
	}
	GetCirculationItemWebOverviewResp struct {
		Hot               HotWebOverviewInfo               `json:"hot"`                // 热榜概览
		TransactionAmount TransactionAmountWebOverviewInfo `json:"transaction_amount"` // 成交额概览
		MarketAmount      MarketAmountWebOverviewInfo      `json:"market_amount"`      // 市值概览
	}
)
