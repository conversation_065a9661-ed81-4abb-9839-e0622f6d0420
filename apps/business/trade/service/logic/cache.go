package logic

import (
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/yc_open"
	"context"
	"encoding/json"
	"fmt"
	"github.com/go-redis/redis/v8"
	"github.com/shopspring/decimal"
	"time"
)

// GetCirculationItemLatestPriceCacheKey 获取二手商品最新成交价缓存 key
func GetCirculationItemLatestPriceCacheKey(itemID string) string {
	return fmt.Sprintf("app_service:circulation_item:latest_price:%s", itemID)
}

// getCirculationItemPriceChangeRateCacheKey 获取二手商品当天涨跌幅缓存 key
func getCirculationItemPriceChangeRateCacheKey() string {
	now := util.Now()
	return fmt.Sprintf("app_service:circulation_item:price_change_rate:%s", now.Format("2006-01-02"))
}

func CachePriceChangeRateForCirculationItem(ctx context.Context, itemID string, priceChangeRate float64) error {
	cacheKey := getCirculationItemPriceChangeRateCacheKey()
	// 使用 decimal 处理精度
	scaledValue := decimal.NewFromFloat(priceChangeRate).
		Mul(decimal.NewFromInt(100)).
		Round(0). // 四舍五入到整数
		BigInt()

	err := global.REDIS.ZAdd(ctx, cacheKey, &redis.Z{
		Score:  float64(scaledValue.Int64()),
		Member: itemID,
	}).Err()
	if err != nil {
		return err
	}
	// 处理缓存有效期
	d, err := global.REDIS.TTL(ctx, cacheKey).Result()
	if err != nil {
		return err
	}
	if d.Nanoseconds() == -1 {
		now := util.Now()
		todayEnd := util.GetEndOfDay(now)
		_, err = global.REDIS.Expire(ctx, cacheKey, todayEnd.Sub(now)).Result()
		if err != nil {
			return err
		}
	}

	return nil
}

func GetPriceChangeRateFromCache(ctx context.Context, itemID string) (float64, error) {
	cacheKey := getCirculationItemPriceChangeRateCacheKey()
	score, err := global.REDIS.ZScore(ctx, cacheKey, itemID).Result()
	if err != nil {
		return 0, err
	}

	// 使用 decimal 转换回原始值
	value, _ := decimal.NewFromFloat(score).
		Div(decimal.NewFromInt(100)).
		Round(2). // 四舍五入保留2位小数
		Float64()

	return value, nil
}

const CirculationItemUpdateQueueCacheKey = "app_service:circulation_item:update:queue"

func AddToCirculationItemUpdateQueue(ctx context.Context, itemID string) error {
	_, err := global.REDIS.SAdd(ctx, CirculationItemUpdateQueueCacheKey, itemID).Result()
	if d, err := global.REDIS.TTL(ctx, CirculationItemUpdateQueueCacheKey).Result(); err == nil {
		if d.Nanoseconds() == -1 {
			global.REDIS.Expire(ctx, CirculationItemUpdateQueueCacheKey, time.Hour*24)
		}
	}
	return err
}

func GetFromCirculationItemUpdateQueue(ctx context.Context, batchSize int64) ([]string, error) {
	itemIDs, err := global.REDIS.SPopN(ctx, CirculationItemUpdateQueueCacheKey, batchSize).Result()
	if err != nil {
		return nil, err
	}

	return itemIDs, nil
}

func GetTotalCirculationMapFromTmtCache(ctx context.Context, itemIDs []string) map[string]int64 {
	itemCountMap := map[string]int64{}
	countCacheKeys := make([]string, 0)
	for _, itemID := range itemIDs {
		cacheKey := fmt.Sprintf("XIONGMAOMART-TMTBG:userItemCount:%s", itemID)
		countCacheKeys = append(countCacheKeys, cacheKey)
	}
	countCacheVals, err := global.REDIS.MGet(ctx, countCacheKeys...).Result()
	type countMapData struct {
		Count  int64  `json:"count"`
		ItemID string `json:"item_id"`
	}
	if err == nil && len(countCacheVals) > 0 {
		for _, val := range countCacheVals {
			if val == nil {
				continue
			}
			if strVal, ok := val.(string); ok {
				countData := countMapData{}
				if err := json.Unmarshal([]byte(strVal), &countData); err == nil {
					itemCountMap[countData.ItemID] = countData.Count
				}
			}
		}
	}

	return itemCountMap
}

func GetTotalCirculationMapFromYc(ctx context.Context, itemIDs []string) (map[string]int64, error) {
	// 先从 tmt 的缓存获取，保持一致性
	itemCountMap := GetTotalCirculationMapFromTmtCache(ctx, itemIDs)
	// 无缓存的 item id
	noCacheItemIDs := make([]string, 0)
	for _, itemID := range itemIDs {
		if _, ok := itemCountMap[itemID]; !ok {
			noCacheItemIDs = append(noCacheItemIDs, itemID)
		}
	}
	if len(noCacheItemIDs) > 0 {
		countList, err := yc_open.CountValidUserItems(ctx, noCacheItemIDs)
		if err != nil {
			return nil, err
		}

		for _, item := range countList {
			itemCountMap[item.ItemID] = item.Count
		}
	}

	return itemCountMap, nil
}
