package service

import (
	"app_service/pkg/middlewares/g/auth"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go.opentelemetry.io/otel/trace"
)

// Service 业务服务
// 传递上下文
type Service struct {
	ctx context.Context
}

func New(ctx context.Context) *Service {
	return &Service{
		ctx: ctx,
	}
}

func (s *Service) NewContextWithSpanContext(parent context.Context) context.Context {
	spanContext := trace.SpanContextFromContext(parent)
	return trace.ContextWithSpanContext(context.Background(), spanContext)
}

// GetUserId 获取当前登录用户id
func (s *Service) GetUserId() string {
	if s.ctx.Value("Authorization").(string) == "" {
		return ""
	}
	info, ok := auth.GetUserFromCtx(s.ctx)
	if !ok {
		log.Ctx(s.ctx).Error("获取用户信息失败")
		return ""
	}
	return info.Id
}
