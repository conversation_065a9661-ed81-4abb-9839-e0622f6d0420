// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameMarketOverview = "market_overview"

// MarketOverview 市场总览表
type MarketOverview struct {
	ID                     int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true" json:"id"`
	TransactionAmount      int64     `gorm:"column:transaction_amount;type:bigint unsigned;not null;comment:当天成交额，一手+二手（单位：分）" json:"transaction_amount"`           // 当天成交额，一手+二手（单位：分）
	DownCount              int64     `gorm:"column:down_count;type:bigint unsigned;not null;comment:当天跌的商品数量" json:"down_count"`                                    // 当天跌的商品数量
	UpCount                int64     `gorm:"column:up_count;type:bigint unsigned;not null;comment:当天涨的商品数量" json:"up_count"`                                        // 当天涨的商品数量
	FlatCount              int64     `gorm:"column:flat_count;type:bigint unsigned;not null;comment:当天平的商品数量" json:"flat_count"`                                    // 当天平的商品数量
	TradingDay             time.Time `gorm:"column:trading_day;type:date;not null;comment:交易日" json:"trading_day"`                                                  // 交易日
	TotalTransactionAmount int64     `gorm:"column:total_transaction_amount;type:bigint unsigned;not null;comment:二手历史累计成交额（单位：分）" json:"total_transaction_amount"` // 二手历史累计成交额（单位：分）
	TransactionAmountRate  float32   `gorm:"column:transaction_amount_rate;type:float;not null;comment:二手成交额百分比" json:"transaction_amount_rate"`                    // 二手成交额百分比
	TotalMarketAmount      int64     `gorm:"column:total_market_amount;type:bigint unsigned;not null;comment:历史累计市值（单位：分）" json:"total_market_amount"`              // 历史累计市值（单位：分）
	MarketAmountRate       float32   `gorm:"column:market_amount_rate;type:float;not null;comment:市值百分比" json:"market_amount_rate"`                                 // 市值百分比
	CreatedAt              time.Time `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`               // 创建时间
	UpdatedAt              time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`               // 更新时间
}

// TableName MarketOverview's table name
func (*MarketOverview) TableName() string {
	return TableNameMarketOverview
}
