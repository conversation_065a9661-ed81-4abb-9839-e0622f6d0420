// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameCirculationItem = "circulation_item"

// CirculationItem 流通商品表
type CirculationItem struct {
	ID                     int64     `gorm:"column:id;type:bigint unsigned;primaryKey;autoIncrement:true;comment:主键 id" json:"id"`                              // 主键 id
	ItemID                 string    `gorm:"column:item_id;type:varchar(32);not null;comment:商品 id (issue_item.item_id)" json:"item_id"`                        // 商品 id (issue_item.item_id)
	IssueItemID            string    `gorm:"column:issue_item_id;type:varchar(32);not null;comment:一手商品 id (issue_item._id)" json:"issue_item_id"`              // 一手商品 id (issue_item._id)
	ItemName               string    `gorm:"column:item_name;type:varchar(256);not null;comment:商品名称(issue_item.item_name)" json:"item_name"`                   // 商品名称(issue_item.item_name)
	ImageURL               string    `gorm:"column:image_url;type:varchar(256);not null;comment:商品图片(issue_item.image_url)" json:"image_url"`                   // 商品图片(issue_item.image_url)
	Price                  int64     `gorm:"column:price;type:bigint unsigned;not null;comment:发行价格(issue_item.price)" json:"price"`                            // 发行价格(issue_item.price)
	PreTransactionQty      int64     `gorm:"column:pre_transaction_qty;type:bigint unsigned;not null;comment:上一次有交易的日成交数量" json:"pre_transaction_qty"`          // 上一次有交易的日成交数量
	CurTransactionQty      int64     `gorm:"column:cur_transaction_qty;type:bigint unsigned;not null;comment:当前交易日的成交数量" json:"cur_transaction_qty"`            // 当前交易日的成交数量
	LatestTransactionPrice int64     `gorm:"column:latest_transaction_price;type:bigint unsigned;not null;comment:最新成交价(单位：分)" json:"latest_transaction_price"` // 最新成交价(单位：分)
	PriceChangeRate        float32   `gorm:"column:price_change_rate;type:float;not null;comment:涨跌幅" json:"price_change_rate"`                                 // 涨跌幅
	TotalTransactionAmount int64     `gorm:"column:total_transaction_amount;type:bigint unsigned;not null;comment:历史成交额(单位：分)" json:"total_transaction_amount"` // 历史成交额(单位：分)
	TotalCirculation       int64     `gorm:"column:total_circulation;type:bigint unsigned;not null;comment:流通数量" json:"total_circulation"`                      // 流通数量
	MarketAmount           int64     `gorm:"column:market_amount;type:bigint unsigned;not null;comment:流通市值(单位：分)" json:"market_amount"`                        // 流通市值(单位：分)
	IsDisplay              int32     `gorm:"column:is_display;type:tinyint;not null;default:1;comment:是否展示(1:展示，2:不展示)" json:"is_display"`                      // 是否展示(1:展示，2:不展示)
	IsDelisted             int32     `gorm:"column:is_delisted;type:tinyint;not null;comment:是否退市(1:已退市)" json:"is_delisted"`                                   // 是否退市(1:已退市)
	CreatedAt              time.Time `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`           // 创建时间
	UpdatedAt              time.Time `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`           // 更新时间
}

// TableName CirculationItem's table name
func (*CirculationItem) TableName() string {
	return TableNameCirculationItem
}
