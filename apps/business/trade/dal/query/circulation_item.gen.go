// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trade/dal/model"
)

func newCirculationItem(db *gorm.DB, opts ...gen.DOOption) circulationItem {
	_circulationItem := circulationItem{}

	_circulationItem.circulationItemDo.UseDB(db, opts...)
	_circulationItem.circulationItemDo.UseModel(&model.CirculationItem{})

	tableName := _circulationItem.circulationItemDo.TableName()
	_circulationItem.ALL = field.NewAsterisk(tableName)
	_circulationItem.ID = field.NewInt64(tableName, "id")
	_circulationItem.ItemID = field.NewString(tableName, "item_id")
	_circulationItem.IssueItemID = field.NewString(tableName, "issue_item_id")
	_circulationItem.ItemName = field.NewString(tableName, "item_name")
	_circulationItem.ImageURL = field.NewString(tableName, "image_url")
	_circulationItem.Price = field.NewInt64(tableName, "price")
	_circulationItem.PreTransactionQty = field.NewInt64(tableName, "pre_transaction_qty")
	_circulationItem.CurTransactionQty = field.NewInt64(tableName, "cur_transaction_qty")
	_circulationItem.LatestTransactionPrice = field.NewInt64(tableName, "latest_transaction_price")
	_circulationItem.PriceChangeRate = field.NewFloat32(tableName, "price_change_rate")
	_circulationItem.TotalTransactionAmount = field.NewInt64(tableName, "total_transaction_amount")
	_circulationItem.TotalCirculation = field.NewInt64(tableName, "total_circulation")
	_circulationItem.MarketAmount = field.NewInt64(tableName, "market_amount")
	_circulationItem.IsDisplay = field.NewInt32(tableName, "is_display")
	_circulationItem.IsDelisted = field.NewInt32(tableName, "is_delisted")
	_circulationItem.CreatedAt = field.NewTime(tableName, "created_at")
	_circulationItem.UpdatedAt = field.NewTime(tableName, "updated_at")

	_circulationItem.fillFieldMap()

	return _circulationItem
}

// circulationItem 流通商品表
type circulationItem struct {
	circulationItemDo

	ALL                    field.Asterisk
	ID                     field.Int64   // 主键 id
	ItemID                 field.String  // 商品 id (issue_item.item_id)
	IssueItemID            field.String  // 一手商品 id (issue_item._id)
	ItemName               field.String  // 商品名称(issue_item.item_name)
	ImageURL               field.String  // 商品图片(issue_item.image_url)
	Price                  field.Int64   // 发行价格(issue_item.price)
	PreTransactionQty      field.Int64   // 上一次有交易的日成交数量
	CurTransactionQty      field.Int64   // 当前交易日的成交数量
	LatestTransactionPrice field.Int64   // 最新成交价(单位：分)
	PriceChangeRate        field.Float32 // 涨跌幅
	TotalTransactionAmount field.Int64   // 历史成交额(单位：分)
	TotalCirculation       field.Int64   // 流通数量
	MarketAmount           field.Int64   // 流通市值(单位：分)
	IsDisplay              field.Int32   // 是否展示(1:展示，2:不展示)
	IsDelisted             field.Int32   // 是否退市(1:已退市)
	CreatedAt              field.Time    // 创建时间
	UpdatedAt              field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (c circulationItem) Table(newTableName string) *circulationItem {
	c.circulationItemDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c circulationItem) As(alias string) *circulationItem {
	c.circulationItemDo.DO = *(c.circulationItemDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *circulationItem) updateTableName(table string) *circulationItem {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewInt64(table, "id")
	c.ItemID = field.NewString(table, "item_id")
	c.IssueItemID = field.NewString(table, "issue_item_id")
	c.ItemName = field.NewString(table, "item_name")
	c.ImageURL = field.NewString(table, "image_url")
	c.Price = field.NewInt64(table, "price")
	c.PreTransactionQty = field.NewInt64(table, "pre_transaction_qty")
	c.CurTransactionQty = field.NewInt64(table, "cur_transaction_qty")
	c.LatestTransactionPrice = field.NewInt64(table, "latest_transaction_price")
	c.PriceChangeRate = field.NewFloat32(table, "price_change_rate")
	c.TotalTransactionAmount = field.NewInt64(table, "total_transaction_amount")
	c.TotalCirculation = field.NewInt64(table, "total_circulation")
	c.MarketAmount = field.NewInt64(table, "market_amount")
	c.IsDisplay = field.NewInt32(table, "is_display")
	c.IsDelisted = field.NewInt32(table, "is_delisted")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *circulationItem) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *circulationItem) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 17)
	c.fieldMap["id"] = c.ID
	c.fieldMap["item_id"] = c.ItemID
	c.fieldMap["issue_item_id"] = c.IssueItemID
	c.fieldMap["item_name"] = c.ItemName
	c.fieldMap["image_url"] = c.ImageURL
	c.fieldMap["price"] = c.Price
	c.fieldMap["pre_transaction_qty"] = c.PreTransactionQty
	c.fieldMap["cur_transaction_qty"] = c.CurTransactionQty
	c.fieldMap["latest_transaction_price"] = c.LatestTransactionPrice
	c.fieldMap["price_change_rate"] = c.PriceChangeRate
	c.fieldMap["total_transaction_amount"] = c.TotalTransactionAmount
	c.fieldMap["total_circulation"] = c.TotalCirculation
	c.fieldMap["market_amount"] = c.MarketAmount
	c.fieldMap["is_display"] = c.IsDisplay
	c.fieldMap["is_delisted"] = c.IsDelisted
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c circulationItem) clone(db *gorm.DB) circulationItem {
	c.circulationItemDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c circulationItem) replaceDB(db *gorm.DB) circulationItem {
	c.circulationItemDo.ReplaceDB(db)
	return c
}

type circulationItemDo struct{ gen.DO }

type ICirculationItemDo interface {
	gen.SubQuery
	Debug() ICirculationItemDo
	WithContext(ctx context.Context) ICirculationItemDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICirculationItemDo
	WriteDB() ICirculationItemDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICirculationItemDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICirculationItemDo
	Not(conds ...gen.Condition) ICirculationItemDo
	Or(conds ...gen.Condition) ICirculationItemDo
	Select(conds ...field.Expr) ICirculationItemDo
	Where(conds ...gen.Condition) ICirculationItemDo
	Order(conds ...field.Expr) ICirculationItemDo
	Distinct(cols ...field.Expr) ICirculationItemDo
	Omit(cols ...field.Expr) ICirculationItemDo
	Join(table schema.Tabler, on ...field.Expr) ICirculationItemDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICirculationItemDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICirculationItemDo
	Group(cols ...field.Expr) ICirculationItemDo
	Having(conds ...gen.Condition) ICirculationItemDo
	Limit(limit int) ICirculationItemDo
	Offset(offset int) ICirculationItemDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICirculationItemDo
	Unscoped() ICirculationItemDo
	Create(values ...*model.CirculationItem) error
	CreateInBatches(values []*model.CirculationItem, batchSize int) error
	Save(values ...*model.CirculationItem) error
	First() (*model.CirculationItem, error)
	Take() (*model.CirculationItem, error)
	Last() (*model.CirculationItem, error)
	Find() ([]*model.CirculationItem, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CirculationItem, err error)
	FindInBatches(result *[]*model.CirculationItem, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CirculationItem) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICirculationItemDo
	Assign(attrs ...field.AssignExpr) ICirculationItemDo
	Joins(fields ...field.RelationField) ICirculationItemDo
	Preload(fields ...field.RelationField) ICirculationItemDo
	FirstOrInit() (*model.CirculationItem, error)
	FirstOrCreate() (*model.CirculationItem, error)
	FindByPage(offset int, limit int) (result []*model.CirculationItem, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICirculationItemDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c circulationItemDo) Debug() ICirculationItemDo {
	return c.withDO(c.DO.Debug())
}

func (c circulationItemDo) WithContext(ctx context.Context) ICirculationItemDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c circulationItemDo) ReadDB() ICirculationItemDo {
	return c.Clauses(dbresolver.Read)
}

func (c circulationItemDo) WriteDB() ICirculationItemDo {
	return c.Clauses(dbresolver.Write)
}

func (c circulationItemDo) Session(config *gorm.Session) ICirculationItemDo {
	return c.withDO(c.DO.Session(config))
}

func (c circulationItemDo) Clauses(conds ...clause.Expression) ICirculationItemDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c circulationItemDo) Returning(value interface{}, columns ...string) ICirculationItemDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c circulationItemDo) Not(conds ...gen.Condition) ICirculationItemDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c circulationItemDo) Or(conds ...gen.Condition) ICirculationItemDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c circulationItemDo) Select(conds ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c circulationItemDo) Where(conds ...gen.Condition) ICirculationItemDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c circulationItemDo) Order(conds ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c circulationItemDo) Distinct(cols ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c circulationItemDo) Omit(cols ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c circulationItemDo) Join(table schema.Tabler, on ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c circulationItemDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c circulationItemDo) RightJoin(table schema.Tabler, on ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c circulationItemDo) Group(cols ...field.Expr) ICirculationItemDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c circulationItemDo) Having(conds ...gen.Condition) ICirculationItemDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c circulationItemDo) Limit(limit int) ICirculationItemDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c circulationItemDo) Offset(offset int) ICirculationItemDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c circulationItemDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICirculationItemDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c circulationItemDo) Unscoped() ICirculationItemDo {
	return c.withDO(c.DO.Unscoped())
}

func (c circulationItemDo) Create(values ...*model.CirculationItem) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c circulationItemDo) CreateInBatches(values []*model.CirculationItem, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c circulationItemDo) Save(values ...*model.CirculationItem) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c circulationItemDo) First() (*model.CirculationItem, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CirculationItem), nil
	}
}

func (c circulationItemDo) Take() (*model.CirculationItem, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CirculationItem), nil
	}
}

func (c circulationItemDo) Last() (*model.CirculationItem, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CirculationItem), nil
	}
}

func (c circulationItemDo) Find() ([]*model.CirculationItem, error) {
	result, err := c.DO.Find()
	return result.([]*model.CirculationItem), err
}

func (c circulationItemDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CirculationItem, err error) {
	buf := make([]*model.CirculationItem, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c circulationItemDo) FindInBatches(result *[]*model.CirculationItem, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c circulationItemDo) Attrs(attrs ...field.AssignExpr) ICirculationItemDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c circulationItemDo) Assign(attrs ...field.AssignExpr) ICirculationItemDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c circulationItemDo) Joins(fields ...field.RelationField) ICirculationItemDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c circulationItemDo) Preload(fields ...field.RelationField) ICirculationItemDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c circulationItemDo) FirstOrInit() (*model.CirculationItem, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CirculationItem), nil
	}
}

func (c circulationItemDo) FirstOrCreate() (*model.CirculationItem, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CirculationItem), nil
	}
}

func (c circulationItemDo) FindByPage(offset int, limit int) (result []*model.CirculationItem, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c circulationItemDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c circulationItemDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c circulationItemDo) Delete(models ...*model.CirculationItem) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *circulationItemDo) withDO(do gen.Dao) *circulationItemDo {
	c.DO = *do.(*gen.DO)
	return c
}
