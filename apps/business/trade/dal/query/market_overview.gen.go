// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/trade/dal/model"
)

func newMarketOverview(db *gorm.DB, opts ...gen.DOOption) marketOverview {
	_marketOverview := marketOverview{}

	_marketOverview.marketOverviewDo.UseDB(db, opts...)
	_marketOverview.marketOverviewDo.UseModel(&model.MarketOverview{})

	tableName := _marketOverview.marketOverviewDo.TableName()
	_marketOverview.ALL = field.NewAsterisk(tableName)
	_marketOverview.ID = field.NewInt64(tableName, "id")
	_marketOverview.TransactionAmount = field.NewInt64(tableName, "transaction_amount")
	_marketOverview.DownCount = field.NewInt64(tableName, "down_count")
	_marketOverview.UpCount = field.NewInt64(tableName, "up_count")
	_marketOverview.FlatCount = field.NewInt64(tableName, "flat_count")
	_marketOverview.TradingDay = field.NewTime(tableName, "trading_day")
	_marketOverview.TotalTransactionAmount = field.NewInt64(tableName, "total_transaction_amount")
	_marketOverview.TransactionAmountRate = field.NewFloat32(tableName, "transaction_amount_rate")
	_marketOverview.TotalMarketAmount = field.NewInt64(tableName, "total_market_amount")
	_marketOverview.MarketAmountRate = field.NewFloat32(tableName, "market_amount_rate")
	_marketOverview.CreatedAt = field.NewTime(tableName, "created_at")
	_marketOverview.UpdatedAt = field.NewTime(tableName, "updated_at")

	_marketOverview.fillFieldMap()

	return _marketOverview
}

// marketOverview 市场总览表
type marketOverview struct {
	marketOverviewDo

	ALL                    field.Asterisk
	ID                     field.Int64
	TransactionAmount      field.Int64   // 当天成交额，一手+二手（单位：分）
	DownCount              field.Int64   // 当天跌的商品数量
	UpCount                field.Int64   // 当天涨的商品数量
	FlatCount              field.Int64   // 当天平的商品数量
	TradingDay             field.Time    // 交易日
	TotalTransactionAmount field.Int64   // 二手历史累计成交额（单位：分）
	TransactionAmountRate  field.Float32 // 二手成交额百分比
	TotalMarketAmount      field.Int64   // 历史累计市值（单位：分）
	MarketAmountRate       field.Float32 // 市值百分比
	CreatedAt              field.Time    // 创建时间
	UpdatedAt              field.Time    // 更新时间

	fieldMap map[string]field.Expr
}

func (m marketOverview) Table(newTableName string) *marketOverview {
	m.marketOverviewDo.UseTable(newTableName)
	return m.updateTableName(newTableName)
}

func (m marketOverview) As(alias string) *marketOverview {
	m.marketOverviewDo.DO = *(m.marketOverviewDo.As(alias).(*gen.DO))
	return m.updateTableName(alias)
}

func (m *marketOverview) updateTableName(table string) *marketOverview {
	m.ALL = field.NewAsterisk(table)
	m.ID = field.NewInt64(table, "id")
	m.TransactionAmount = field.NewInt64(table, "transaction_amount")
	m.DownCount = field.NewInt64(table, "down_count")
	m.UpCount = field.NewInt64(table, "up_count")
	m.FlatCount = field.NewInt64(table, "flat_count")
	m.TradingDay = field.NewTime(table, "trading_day")
	m.TotalTransactionAmount = field.NewInt64(table, "total_transaction_amount")
	m.TransactionAmountRate = field.NewFloat32(table, "transaction_amount_rate")
	m.TotalMarketAmount = field.NewInt64(table, "total_market_amount")
	m.MarketAmountRate = field.NewFloat32(table, "market_amount_rate")
	m.CreatedAt = field.NewTime(table, "created_at")
	m.UpdatedAt = field.NewTime(table, "updated_at")

	m.fillFieldMap()

	return m
}

func (m *marketOverview) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := m.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (m *marketOverview) fillFieldMap() {
	m.fieldMap = make(map[string]field.Expr, 12)
	m.fieldMap["id"] = m.ID
	m.fieldMap["transaction_amount"] = m.TransactionAmount
	m.fieldMap["down_count"] = m.DownCount
	m.fieldMap["up_count"] = m.UpCount
	m.fieldMap["flat_count"] = m.FlatCount
	m.fieldMap["trading_day"] = m.TradingDay
	m.fieldMap["total_transaction_amount"] = m.TotalTransactionAmount
	m.fieldMap["transaction_amount_rate"] = m.TransactionAmountRate
	m.fieldMap["total_market_amount"] = m.TotalMarketAmount
	m.fieldMap["market_amount_rate"] = m.MarketAmountRate
	m.fieldMap["created_at"] = m.CreatedAt
	m.fieldMap["updated_at"] = m.UpdatedAt
}

func (m marketOverview) clone(db *gorm.DB) marketOverview {
	m.marketOverviewDo.ReplaceConnPool(db.Statement.ConnPool)
	return m
}

func (m marketOverview) replaceDB(db *gorm.DB) marketOverview {
	m.marketOverviewDo.ReplaceDB(db)
	return m
}

type marketOverviewDo struct{ gen.DO }

type IMarketOverviewDo interface {
	gen.SubQuery
	Debug() IMarketOverviewDo
	WithContext(ctx context.Context) IMarketOverviewDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IMarketOverviewDo
	WriteDB() IMarketOverviewDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IMarketOverviewDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IMarketOverviewDo
	Not(conds ...gen.Condition) IMarketOverviewDo
	Or(conds ...gen.Condition) IMarketOverviewDo
	Select(conds ...field.Expr) IMarketOverviewDo
	Where(conds ...gen.Condition) IMarketOverviewDo
	Order(conds ...field.Expr) IMarketOverviewDo
	Distinct(cols ...field.Expr) IMarketOverviewDo
	Omit(cols ...field.Expr) IMarketOverviewDo
	Join(table schema.Tabler, on ...field.Expr) IMarketOverviewDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IMarketOverviewDo
	RightJoin(table schema.Tabler, on ...field.Expr) IMarketOverviewDo
	Group(cols ...field.Expr) IMarketOverviewDo
	Having(conds ...gen.Condition) IMarketOverviewDo
	Limit(limit int) IMarketOverviewDo
	Offset(offset int) IMarketOverviewDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketOverviewDo
	Unscoped() IMarketOverviewDo
	Create(values ...*model.MarketOverview) error
	CreateInBatches(values []*model.MarketOverview, batchSize int) error
	Save(values ...*model.MarketOverview) error
	First() (*model.MarketOverview, error)
	Take() (*model.MarketOverview, error)
	Last() (*model.MarketOverview, error)
	Find() ([]*model.MarketOverview, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketOverview, err error)
	FindInBatches(result *[]*model.MarketOverview, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.MarketOverview) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IMarketOverviewDo
	Assign(attrs ...field.AssignExpr) IMarketOverviewDo
	Joins(fields ...field.RelationField) IMarketOverviewDo
	Preload(fields ...field.RelationField) IMarketOverviewDo
	FirstOrInit() (*model.MarketOverview, error)
	FirstOrCreate() (*model.MarketOverview, error)
	FindByPage(offset int, limit int) (result []*model.MarketOverview, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IMarketOverviewDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (m marketOverviewDo) Debug() IMarketOverviewDo {
	return m.withDO(m.DO.Debug())
}

func (m marketOverviewDo) WithContext(ctx context.Context) IMarketOverviewDo {
	return m.withDO(m.DO.WithContext(ctx))
}

func (m marketOverviewDo) ReadDB() IMarketOverviewDo {
	return m.Clauses(dbresolver.Read)
}

func (m marketOverviewDo) WriteDB() IMarketOverviewDo {
	return m.Clauses(dbresolver.Write)
}

func (m marketOverviewDo) Session(config *gorm.Session) IMarketOverviewDo {
	return m.withDO(m.DO.Session(config))
}

func (m marketOverviewDo) Clauses(conds ...clause.Expression) IMarketOverviewDo {
	return m.withDO(m.DO.Clauses(conds...))
}

func (m marketOverviewDo) Returning(value interface{}, columns ...string) IMarketOverviewDo {
	return m.withDO(m.DO.Returning(value, columns...))
}

func (m marketOverviewDo) Not(conds ...gen.Condition) IMarketOverviewDo {
	return m.withDO(m.DO.Not(conds...))
}

func (m marketOverviewDo) Or(conds ...gen.Condition) IMarketOverviewDo {
	return m.withDO(m.DO.Or(conds...))
}

func (m marketOverviewDo) Select(conds ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Select(conds...))
}

func (m marketOverviewDo) Where(conds ...gen.Condition) IMarketOverviewDo {
	return m.withDO(m.DO.Where(conds...))
}

func (m marketOverviewDo) Order(conds ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Order(conds...))
}

func (m marketOverviewDo) Distinct(cols ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Distinct(cols...))
}

func (m marketOverviewDo) Omit(cols ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Omit(cols...))
}

func (m marketOverviewDo) Join(table schema.Tabler, on ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Join(table, on...))
}

func (m marketOverviewDo) LeftJoin(table schema.Tabler, on ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.LeftJoin(table, on...))
}

func (m marketOverviewDo) RightJoin(table schema.Tabler, on ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.RightJoin(table, on...))
}

func (m marketOverviewDo) Group(cols ...field.Expr) IMarketOverviewDo {
	return m.withDO(m.DO.Group(cols...))
}

func (m marketOverviewDo) Having(conds ...gen.Condition) IMarketOverviewDo {
	return m.withDO(m.DO.Having(conds...))
}

func (m marketOverviewDo) Limit(limit int) IMarketOverviewDo {
	return m.withDO(m.DO.Limit(limit))
}

func (m marketOverviewDo) Offset(offset int) IMarketOverviewDo {
	return m.withDO(m.DO.Offset(offset))
}

func (m marketOverviewDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IMarketOverviewDo {
	return m.withDO(m.DO.Scopes(funcs...))
}

func (m marketOverviewDo) Unscoped() IMarketOverviewDo {
	return m.withDO(m.DO.Unscoped())
}

func (m marketOverviewDo) Create(values ...*model.MarketOverview) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Create(values)
}

func (m marketOverviewDo) CreateInBatches(values []*model.MarketOverview, batchSize int) error {
	return m.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (m marketOverviewDo) Save(values ...*model.MarketOverview) error {
	if len(values) == 0 {
		return nil
	}
	return m.DO.Save(values)
}

func (m marketOverviewDo) First() (*model.MarketOverview, error) {
	if result, err := m.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketOverview), nil
	}
}

func (m marketOverviewDo) Take() (*model.MarketOverview, error) {
	if result, err := m.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketOverview), nil
	}
}

func (m marketOverviewDo) Last() (*model.MarketOverview, error) {
	if result, err := m.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketOverview), nil
	}
}

func (m marketOverviewDo) Find() ([]*model.MarketOverview, error) {
	result, err := m.DO.Find()
	return result.([]*model.MarketOverview), err
}

func (m marketOverviewDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.MarketOverview, err error) {
	buf := make([]*model.MarketOverview, 0, batchSize)
	err = m.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (m marketOverviewDo) FindInBatches(result *[]*model.MarketOverview, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return m.DO.FindInBatches(result, batchSize, fc)
}

func (m marketOverviewDo) Attrs(attrs ...field.AssignExpr) IMarketOverviewDo {
	return m.withDO(m.DO.Attrs(attrs...))
}

func (m marketOverviewDo) Assign(attrs ...field.AssignExpr) IMarketOverviewDo {
	return m.withDO(m.DO.Assign(attrs...))
}

func (m marketOverviewDo) Joins(fields ...field.RelationField) IMarketOverviewDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Joins(_f))
	}
	return &m
}

func (m marketOverviewDo) Preload(fields ...field.RelationField) IMarketOverviewDo {
	for _, _f := range fields {
		m = *m.withDO(m.DO.Preload(_f))
	}
	return &m
}

func (m marketOverviewDo) FirstOrInit() (*model.MarketOverview, error) {
	if result, err := m.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketOverview), nil
	}
}

func (m marketOverviewDo) FirstOrCreate() (*model.MarketOverview, error) {
	if result, err := m.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.MarketOverview), nil
	}
}

func (m marketOverviewDo) FindByPage(offset int, limit int) (result []*model.MarketOverview, count int64, err error) {
	result, err = m.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = m.Offset(-1).Limit(-1).Count()
	return
}

func (m marketOverviewDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = m.Count()
	if err != nil {
		return
	}

	err = m.Offset(offset).Limit(limit).Scan(result)
	return
}

func (m marketOverviewDo) Scan(result interface{}) (err error) {
	return m.DO.Scan(result)
}

func (m marketOverviewDo) Delete(models ...*model.MarketOverview) (result gen.ResultInfo, err error) {
	return m.DO.Delete(models)
}

func (m *marketOverviewDo) withDO(do gen.Dao) *marketOverviewDo {
	m.DO = *do.(*gen.DO)
	return m
}
