package web

import (
	"app_service/apps/business/trade/define"
	"app_service/apps/business/trade/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetCirculationItemWebList
// @Summary 查询二手列表（含榜单）
// @Description 查询二手列表（含榜单）
// @Tags 用户端-交易所
// @Param data query define.GetCirculationItemWebListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCirculationItemWebListResp}
// @Router  /web/v1/trade/circulation_item/list [get]
// @Security Bearer
func GetCirculationItemWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCirculationItemWebListReq{}, s.GetCirculationItemWebList)
}

// GetIssueItemWebTopList
// @Summary 查询一手入口商品列表
// @Description 查询一手入口商品列表
// @Tags 用户端-交易所
// @Param data query define.GetIssueItemWebTopListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetIssueItemWebTopListResp}
// @Router  /web/v1/trade/issue_item/top_list [get]
// @Security Bearer
func GetIssueItemWebTopList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetIssueItemWebTopListReq{}, s.GetIssueItemWebTopList)
}

// GetCirculationItemWebTopList
// @Summary 查询二手入口商品列表
// @Description 查询二手入口商品列表
// @Tags 用户端-交易所
// @Param data query define.GetCirculationItemWebTopListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCirculationItemWebTopListResp}
// @Router  /web/v1/trade/circulation_item/top_list [get]
// @Security Bearer
func GetCirculationItemWebTopList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCirculationItemWebTopListReq{}, s.GetCirculationItemWebTopList)
}

// GetMarketWebOverview
// @Summary 查询市场总览数据
// @Description 查询市场总览数据
// @Tags 用户端-交易所
// @Param data query define.GetMarketWebOverviewReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMarketWebOverviewResp}
// @Router  /web/v1/trade/market/overview [get]
// @Security Bearer
func GetMarketWebOverview(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMarketWebOverviewReq{}, s.GetMarketWebOverview)
}

// GetCirculationItemWebOverview
// @Summary 查询榜单总览数据
// @Description 查询榜单总览数据
// @Tags 用户端-交易所
// @Param data query define.GetCirculationItemWebOverviewReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetCirculationItemWebOverviewResp}
// @Router  /web/v1/trade/circulation_item/overview [get]
// @Security Bearer
func GetCirculationItemWebOverview(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetCirculationItemWebOverviewReq{}, s.GetCirculationItemWebOverview)
}

// GetWebActivities
// @Summary 查询动态（公告、行情异动等）
// @Description 查询动态（公告、行情异动等）
// @Tags 用户端-交易所
// @Param data query define.GetWebActivitiesReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetWebActivitiesResp}
// @Router  /web/v1/trade/activities [get]
// @Security Bearer
func GetWebActivities(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetWebActivitiesReq{}, s.GetWebActivities)
}
