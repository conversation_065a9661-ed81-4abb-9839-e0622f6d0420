package open

import (
	"app_service/apps/business/trade/define"
	"app_service/apps/business/trade/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// SyncCirculationItem
// @Summary 同步二手商品数据
// @Description 同步二手商品数据
// @Tags open端-交易所
// @Param data query define.SyncCirculationItemReq true "查询参数"
// @Success 200 {object} response.Data{data=define.SyncCirculationItemResp}
// @Router  /open/v1/trade/circulation_item/sync [post]
// @Security Bearer
func SyncCirculationItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SyncCirculationItemReq{}, s.SyncCirculationItem)
}

// UpdateMarketOverview
// @Summary 更新概览数据
// @Description 更新概览数据
// @Tags open端-交易所
// @Param data query define.UpdateMarketOverviewReq true "查询参数"
// @Success 200 {object} response.Data{data=define.UpdateMarketOverviewResp}
// @Router  /open/v1/trade/market/update_overview [post]
// @Security Bearer
func UpdateMarketOverview(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateMarketOverviewReq{}, s.UpdateMarketOverview)
}

// BatchUpdateCirculationItem
// @Summary 批量更新二手商品
// @Description 批量更新二手商品
// @Tags open端-交易所
// @Param data query define.BatchUpdateCirculationItemReq true "查询参数"
// @Success 200 {object} response.Data{data=define.BatchUpdateCirculationItemResp}
// @Router  /open/v1/trade/circulation_item/batch_update [post]
// @Security Bearer
func BatchUpdateCirculationItem(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.BatchUpdateCirculationItemReq{}, s.BatchUpdateCirculationItem)
}
