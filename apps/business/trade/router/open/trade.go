package open

import (
	"app_service/apps/business/trade/api/open"
	"github.com/gin-gonic/gin"
)

// Trade 交易所开放路由
func Trade(router *gin.RouterGroup) {
	group := router.Group("/trade")
	{
		marketGroup := group.Group("/market")
		marketGroup.POST("/update_overview", open.UpdateMarketOverview)
	}
	{
		ciGroup := group.Group("/circulation_item")
		ciGroup.POST("/sync", open.SyncCirculationItem)
		ciGroup.POST("/batch_update", open.BatchUpdateCirculationItem)
	}
}
