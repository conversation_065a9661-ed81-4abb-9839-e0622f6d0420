package web

import (
	"app_service/apps/business/trade/api/web"
	"github.com/gin-gonic/gin"
)

// Trade 交易所用户端路由
func Trade(router *gin.RouterGroup) {
	group := router.Group("/trade")

	group.GET("/activities", web.GetWebActivities)
	{
		ciGroup := group.Group("/circulation_item")
		ciGroup.GET("/overview", web.GetCirculationItemWebOverview)
		ciGroup.GET("/list", web.GetCirculationItemWebList)
		ciGroup.GET("/top_list", web.GetCirculationItemWebTopList)
	}
	{
		issueItemGroup := group.Group("/issue_item")
		issueItemGroup.GET("/top_list", web.GetIssueItemWebTopList)
	}
	{
		marketGroup := group.Group("/market")
		marketGroup.GET("/overview", web.GetMarketWebOverview)
	}
}
