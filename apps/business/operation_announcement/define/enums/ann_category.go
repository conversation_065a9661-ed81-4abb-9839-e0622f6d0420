package enums

// OperationAnnCategoryStatus 运营公告分类状态 -1 // 已删除 1 // 启用
type OperationAnnCategoryStatus int32

func (s OperationAnnCategoryStatus) Val() int32 {
	return int32(s)
}

const (
	// OperationAnnCategoryStatusDeleted 已删除
	OperationAnnCategoryStatusDeleted OperationAnnCategoryStatus = -1 // 已删除
	// OperationAnnCategoryStatusEnable 启用
	OperationAnnCategoryStatusEnable OperationAnnCategoryStatus = 1 // 启用
)
