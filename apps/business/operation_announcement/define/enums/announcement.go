package enums

// OperationAnnouncementStatus 运营公告状态 1待发布 2定时中 3已发布 4已下架
type OperationAnnouncementStatus int32

func (s OperationAnnouncementStatus) Val() int32 {
	return int32(s)
}

const (
	// OperationAnnouncementStatusDraft 待发布
	OperationAnnouncementStatusDraft OperationAnnouncementStatus = 1
	// OperationAnnouncementStatusScheduled 定时中
	OperationAnnouncementStatusScheduled OperationAnnouncementStatus = 2
	// OperationAnnouncementStatusPublished 已发布
	OperationAnnouncementStatusPublished OperationAnnouncementStatus = 3
	// OperationAnnouncementStatusOffline 已下架
	OperationAnnouncementStatusOffline OperationAnnouncementStatus = 4
)

// OperationAnnouncementPublishType 运营公告发布方式 1立即发布 2定时发布
type OperationAnnouncementPublishType int32

func (s OperationAnnouncementPublishType) Val() int32 {
	return int32(s)
}

const (
	// OperationAnnouncementPublishTypeImmediate 立即发布
	OperationAnnouncementPublishTypeImmediate OperationAnnouncementPublishType = 1
	// OperationAnnouncementPublishTypeTiming 定时发布
	OperationAnnouncementPublishTypeTiming OperationAnnouncementPublishType = 2
)

// OperationAnnouncementTimeType 时间类型 1发布时间 2创建时间
type OperationAnnouncementTimeType int32

func (s OperationAnnouncementTimeType) Val() int32 {
	return int32(s)
}

const (
	// OperationAnnouncementTimeTypePublish 发布时间
	OperationAnnouncementTimeTypePublish OperationAnnouncementTimeType = 1
	// OperationAnnouncementTimeTypeCreate 创建时间
	OperationAnnouncementTimeTypeCreate OperationAnnouncementTimeType = 2
)

type OperationAnnouncementMessagePushEnum int32

func (s OperationAnnouncementMessagePushEnum) Val() int32 {
	return int32(s)
}

const (
	OperationAnnouncementMessagePushYes OperationAnnouncementMessagePushEnum = 1 // 推送
	OperationAnnouncementMessagePushNo  OperationAnnouncementMessagePushEnum = 2 // 不推送
)
