package define

import "time"

type (
	SchedulePublishReq  struct{}
	SchedulePublishResp struct{}
)

type NoticeAggregateReq struct {
	AdID string `json:"ad_id" form:"ad_id" binding:"required"`
}

type UnifiedNotice struct {
	ID       string    `json:"id"`
	Title    string    `json:"title"`
	Link     string    `json:"link"`
	SortTime time.Time `json:"sort_time"`
}

type NoticeAggregateResp struct {
	List []*UnifiedNotice `json:"list"`
}
