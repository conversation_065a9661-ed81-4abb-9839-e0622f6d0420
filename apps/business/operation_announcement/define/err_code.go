package define

import "e.coding.net/g-dtay0385/common/go-util/response"

var (
	AH220001Err = response.NewError(220001, "已下架不可修改")
	AH220002Err = response.NewError(220002, "仅限定时中和已发布的状态的可操作下架")
	AH220003Err = response.NewError(220003, "有关联运营公告的栏目不可删除")
	AH220004Err = response.NewError(220004, "仅已发布状态的运营公告可同步媒体")
	AH220005Err = response.NewError(220005, "不可修改为该状态")
	AH220006Err = response.NewError(220006, "运营公告已下架")
	AH220007Err = response.NewError(220007, "仅限待发布的状态的可操作发布")
)
