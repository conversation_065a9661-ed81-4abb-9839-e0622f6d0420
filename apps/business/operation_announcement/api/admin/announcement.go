package admin

import (
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetOperationAnnouncementList
// @Summary 查询运营公告列表
// @Description 查询运营公告列表
// @Tags 管理端-运营方公告管理
// @Param data query define.GetOperationAnnouncementAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOperationAnnouncementAdminListResp}
// @Router /admin/v1/operation_announcement/list [get]
// @Security Bearer
func GetOperationAnnouncementList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnouncementAdminListReq{}, s.GetOperationAnnouncementList)
}

// GetOperationAnnouncementDetail
// @Summary 查询运营公告详情
// @Description 查询运营公告详情
// @Tags 管理端-运营方公告管理
// @Param data query define.GetOperationAnnouncementAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOperationAnnouncementAdminDetailResp}
// @Router /admin/v1/operation_announcement/detail [get]
// @Security Bearer
func GetOperationAnnouncementDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnouncementAdminDetailReq{}, s.GetOperationAnnouncementDetail)
}

// AddOperationAnnouncement
// @Summary 新增运营公告
// @Description 新增运营公告
// @Tags 管理端-运营方公告管理
// @Param data body define.AddOperationAnnouncementReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddOperationAnnouncementResp}
// @Router /admin/v1/operation_announcement/add [POST]
// @Security Bearer
func AddOperationAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddOperationAnnouncementReq{}, s.AddOperationAnnouncement)
}

// EditOperationAnnouncement
// @Summary 编辑运营公告
// @Description 编辑运营公告
// @Tags 管理端-运营方公告管理
// @Param data body define.EditOperationAnnouncementReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnouncementResp}
// @Router /admin/v1/operation_announcement/edit [POST]
// @Security Bearer
func EditOperationAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnouncementReq{}, s.EditOperationAnnouncement)
}

// EditOperationAnnouncementStatus
// @Summary 运营公告状态编辑
// @Description 运营公告状态编辑
// @Tags 管理端-运营方公告管理
// @Param data body define.EditOperationAnnouncementStatusReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnouncementStatusResp}
// @Router /admin/v1/operation_announcement/edit_status [POST]
// @Security Bearer
func EditOperationAnnouncementStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnouncementStatusReq{}, s.EditOperationAnnouncementStatus)
}

// EditOperationAnnouncementPriority
// @Summary 运营公告优先级编辑
// @Description 运营公告优先级编辑
// @Tags 管理端-运营方公告管理
// @Param data body define.EditOperationAnnouncementPriorityReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnouncementPriorityResp}
// @Router /admin/v1/operation_announcement/edit_priority [POST]
// @Security Bearer
func EditOperationAnnouncementPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnouncementPriorityReq{}, s.EditOperationAnnouncementPriority)
}

// EditOperationAnnouncementAdIds
// @Summary 运营公告关联广告ID编辑
// @Description 运营公告关联广告ID编辑
// @Tags 管理端-运营方公告管理
// @Param data body define.EditOperationAnnouncementAdIdsReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnouncementAdIdsResp}
// @Router /admin/v1/operation_announcement/edit_ad_ids [POST]
// @Security Bearer
func EditOperationAnnouncementAdIds(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnouncementAdIdsReq{}, s.EditOperationAnnouncementAdIds)
}

// DelOperationAnnouncement
// @Summary 运营公告删除
// @Description 运营公告删除
// @Tags 管理端-运营方公告管理
// @Param data body define.DelOperationAnnouncementReq true "删除参数"
// @Success 200 {object} response.Data{data=define.DelOperationAnnouncementResp}
// @Router /admin/v1/operation_announcement/del [POST]
// @Security Bearer
func DelOperationAnnouncement(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelOperationAnnouncementReq{}, s.DelOperationAnnouncement)
}
