package admin

import (
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetOperationAnnCategoryList
// @Summary 查询运营公告分类列表
// @Description 查询运营公告分类列表
// @Tags 管理端-运营方公告分类管理
// @Param data query define.GetOperationAnnCategoryAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOperationAnnCategoryAdminListResp}
// @Router /admin/v1/operation_announcement/category/list [get]
// @Security Bearer
func GetOperationAnnCategoryList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnCategoryAdminListReq{}, s.GetOperationAnnCategoryList)
}

// GetOperationAnnCategoryDetail
// @Summary 查询运营公告分类详情
// @Description 查询运营公告分类详情
// @Tags 管理端-运营方公告分类管理
// @Param data query define.GetOperationAnnCategoryAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOperationAnnCategoryAdminDetailResp}
// @Router /admin/v1/operation_announcement/category/detail [get]
// @Security Bearer
func GetOperationAnnCategoryDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnCategoryAdminDetailReq{}, s.GetOperationAnnCategoryDetail)
}

// AddOperationAnnCategory
// @Summary 新增运营公告分类
// @Description 新增运营公告分类
// @Tags 管理端-运营方公告分类管理
// @Param data body define.AddOperationAnnCategoryReq true "新增参数"
// @Success 200 {object} response.Data{data=define.AddOperationAnnCategoryResp}
// @Router /admin/v1/operation_announcement/category/add [POST]
// @Security Bearer
func AddOperationAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.AddOperationAnnCategoryReq{}, s.AddOperationAnnCategory)
}

// EditOperationAnnCategory
// @Summary 编辑运营公告分类
// @Description 编辑运营公告分类
// @Tags 管理端-运营方公告分类管理
// @Param data body define.EditOperationAnnCategoryReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnCategoryResp}
// @Router /admin/v1/operation_announcement/category/edit [POST]
// @Security Bearer
func EditOperationAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnCategoryReq{}, s.EditOperationAnnCategory)
}

// EditOperationAnnCategoryPriority
// @Summary 运营公告分类优先级编辑
// @Description 运营公告分类优先级编辑
// @Tags 管理端-运营方公告分类管理
// @Param data body define.EditOperationAnnCategoryPriorityReq true "编辑参数"
// @Success 200 {object} response.Data{data=define.EditOperationAnnCategoryPriorityResp}
// @Router /admin/v1/operation_announcement/category/edit_priority [POST]
// @Security Bearer
func EditOperationAnnCategoryPriority(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.EditOperationAnnCategoryPriorityReq{}, s.EditOperationAnnCategoryPriority)
}

// DelOperationAnnCategory
// @Summary 运营公告分类删除
// @Description 运营公告分类删除
// @Tags 管理端-运营方公告分类管理
// @Param data body define.DelOperationAnnCategoryReq true "删除参数"
// @Success 200 {object} response.Data{data=define.DelOperationAnnCategoryResp}
// @Router /admin/v1/operation_announcement/category/del [POST]
// @Security Bearer
func DelOperationAnnCategory(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.DelOperationAnnCategoryReq{}, s.DelOperationAnnCategory)
}
