package web

import (
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetOperationAnnouncementWebList
// @Summary 查询运营公告列表
// @Description 查询运营公告列表（Web端）
// @Tags 用户端-运营方公告
// @Param data query define.GetOperationAnnouncementWebListReq true "查询参数"
// @Success 200 {object} define.GetOperationAnnouncementWebListResp
// @Router /web/v1/operation_announcement/list [get]
func GetOperationAnnouncementWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnouncementWebListReq{}, s.GetOperationAnnouncementWebList)
}

// GetOperationAnnouncementWebDetail
// @Summary 查询运营公告详情
// @Description 查询运营公告详情（Web端）
// @Tags 用户端-运营方公告
// @Param data query define.GetOperationAnnouncementWebDetailReq true "查询参数"
// @Success 200 {object} define.GetOperationAnnouncementWebDetailResp
// @Router /web/v1/operation_announcement/detail [get]
func GetOperationAnnouncementWebDetail(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnouncementWebDetailReq{}, s.GetOperationAnnouncementWebDetail)
}
