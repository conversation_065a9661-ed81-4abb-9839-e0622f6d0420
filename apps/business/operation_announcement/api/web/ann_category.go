package web

import (
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/service"
	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetOperationAnnCategoryWebList
// @Summary 查询运营公告分类列表
// @Description 查询运营公告分类列表（Web端）
// @Tags 用户端-运营方公告分类
// @Param data query define.GetOperationAnnCategoryWebListReq true "查询参数"
// @Success 200 {object} define.GetOperationAnnCategoryWebListResp
// @Router /web/v1/operation_announcement/category/list [get]
func GetOperationAnnCategoryWebList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOperationAnnCategoryWebListReq{}, s.GetOperationAnnCategoryWebList)
}
