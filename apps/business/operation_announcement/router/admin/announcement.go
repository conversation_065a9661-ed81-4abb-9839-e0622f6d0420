package admin

import (
	"app_service/apps/business/operation_announcement/api/admin"
	"github.com/gin-gonic/gin"
)

// OperationAnnouncement 运营公告管理端相关
func OperationAnnouncement(router *gin.RouterGroup) {
	group := router.Group("/operation_announcement")
	{
		// 获取运营公告列表
		group.GET("/list", admin.GetOperationAnnouncementList)
		// 获取运营公告详情
		group.GET("/detail", admin.GetOperationAnnouncementDetail)
		// 新增运营公告
		group.POST("/add", admin.AddOperationAnnouncement)
		// 编辑运营公告
		group.POST("/edit", admin.EditOperationAnnouncement)
		// 运营公告分类状态编辑
		group.POST("/edit_status", admin.EditOperationAnnouncementStatus)
		// 运营公告优先级编辑
		group.POST("/edit_priority", admin.EditOperationAnnouncementPriority)
		// 运营公告媒体同步编辑
		group.POST("/edit_ad_ids", admin.EditOperationAnnouncementAdIds)
		// 运营公告删除
		//group.POST("/del", admin.DelOperationAnnouncement)
	}
}
