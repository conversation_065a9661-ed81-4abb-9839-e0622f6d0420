package admin

import (
	"app_service/apps/business/operation_announcement/api/admin"
	"github.com/gin-gonic/gin"
)

// OperationAnnCategory 运营公告分类管理端相关
func OperationAnnCategory(router *gin.RouterGroup) {
	group := router.Group("/operation_announcement/category")
	{
		// 获取运营公告分类列表
		group.GET("/list", admin.GetOperationAnnCategoryList)
		//// 获取运营公告分类详情
		group.GET("/detail", admin.GetOperationAnnCategoryDetail)
		// 新增运营公告分类
		group.POST("/add", admin.AddOperationAnnCategory)
		// 编辑运营公告分类
		group.POST("/edit", admin.EditOperationAnnCategory)
		// 运营公告分类优先级编辑
		group.POST("/edit_priority", admin.EditOperationAnnCategoryPriority)
		// 运营公告分类删除
		group.POST("/del", admin.DelOperationAnnCategory)
	}
}
