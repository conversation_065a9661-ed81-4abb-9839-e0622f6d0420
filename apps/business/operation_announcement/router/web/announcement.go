package web

import (
	"app_service/apps/business/operation_announcement/api/web"
	"github.com/gin-gonic/gin"
)

// OperationAnnouncement 运营公告管理端相关
func OperationAnnouncement(router *gin.RouterGroup) {
	group := router.Group("/operation_announcement")
	{
		// 运营公告列表
		group.GET("/list", web.GetOperationAnnouncementWebList)
		// 运营公告详情
		group.GET("/detail", web.GetOperationAnnouncementWebDetail)
		// 运营公告分类列表
		group.GET("/category/list", web.GetOperationAnnCategoryWebList)
	}
}
