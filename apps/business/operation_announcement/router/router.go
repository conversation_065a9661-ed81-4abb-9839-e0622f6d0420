package router

import (
	"app_service/apps/business/operation_announcement/router/open"
	"app_service/apps/business/operation_announcement/router/web"
	"fmt"

	"app_service/apps/business/operation_announcement/router/admin"
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/middlewares/g/auth"
	"e.coding.net/g-dtay0385/common/go-middleware/g"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/contrib/instrumentation/github.com/gin-gonic/gin/otelgin"
)

func init() {
	global.Routers = append(global.Routers, LoadRouter)
}
func LoadRouter() {
	var r *gin.Engine
	engine := global.GetEngine()
	switch engine.(type) {
	case *gin.Engine:
		r = engine.(*gin.Engine)
	default:
		panic(fmt.Sprintf("服务启动失败,不支持的engine"))
	}

	adminRote(r)
	// 客户端路由
	webRote(r)

	openRoute(r)
}

// 管理端路由
func adminRote(router *gin.Engine) {
	w := router.Group("/admin/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Admin{
		NoAuthUrl: []string{},
	}))

	admin.OperationAnnouncement(w)
	admin.OperationAnnCategory(w)
}

// 客户端路由
func webRote(router *gin.Engine) {
	w := router.Group("/web/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Web{
		NoAuthUrl: []string{
			"/web/v1/operation_announcement/list",
			"/web/v1/operation_announcement/detail",
			"/web/v1/operation_announcement/category/list",
		},
	}))
	web.OperationAnnouncement(w)
}

// 对外路由
func openRoute(router *gin.Engine) {
	w := router.Group("/open/v1", middlewares.ParseHead)
	w.Use(otelgin.Middleware(global.GlobalConfig.Service.Name), g.Log(), g.Recovery(), auth.Auth(&auth.Open{
		Token: global.GlobalConfig.Service.Token,
		NoAuthUrl: []string{
			"/open/v1/notice/aggregate",
		},
	}))

	open.OperationAnnouncement(w)
}
