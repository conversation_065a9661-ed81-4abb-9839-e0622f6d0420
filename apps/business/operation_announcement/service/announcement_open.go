package service

import (
	"app_service/apps/business/operation_announcement/dal/model"
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/define/enums"
	"app_service/apps/business/operation_announcement/repo"
	"app_service/apps/business/operation_announcement/service/logic"
	"app_service/pkg/search"
	"app_service/pkg/util"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"time"
)

// SchedulePublish 定时发布运营公告
func (s *Service) SchedulePublish(req *define.SchedulePublishReq) (*define.SchedulePublishResp, error) {
	var annList []*model.OperationAnnouncement
	err := repo.GetDB().WithContext(s.ctx).
		Where("status = ?", enums.OperationAnnouncementStatusScheduled.Val()).
		Where("publish_time < ?", util.Now()).
		Find(&annList).Error
	if err != nil {
		log.Ctx(s.ctx).Errorf("定时发布运营方公告失败: %v", err)
		return nil, err
	}
	annIDs := make([]int64, len(annList))
	for idx, ann := range annList {
		annIDs[idx] = ann.OperationAnnouncementID
	}
	if len(annIDs) > 0 {
		// 更新状态为【已发布】
		updateParams := map[string]interface{}{
			"status": enums.OperationAnnouncementStatusPublished.Val(),
		}
		annSchema := repo.GetQuery().OperationAnnouncement
		updateQb := search.NewQueryBuilder().In(annSchema.OperationAnnouncementID, annIDs).Build()
		err = repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).UpdateField(updateParams, updateQb)
		if err != nil {
			log.Ctx(s.ctx).Errorf("定时发布运营方公告失败: %v", err)
			return nil, err
		}

		// 消息推送
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			for _, ann := range annList {
				if ann.MessagePush == enums.OperationAnnouncementMessagePushYes.Val() {
					pushErr := logic.PushOperationAnnouncementMessage(spanCtx, ann)
					if pushErr != nil {
						log.Ctx(spanCtx).Errorf("运营方公告消息推送失败: %v", pushErr)
					}

					time.Sleep(time.Second * 3)
				}
			}
		}()
	}

	return &define.SchedulePublishResp{}, nil
}
