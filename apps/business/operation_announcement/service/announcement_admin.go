package service

import (
	"app_service/apps/business/operation_announcement/dal/model"
	"app_service/apps/business/operation_announcement/define"
	"app_service/apps/business/operation_announcement/define/enums"
	"app_service/apps/business/operation_announcement/repo"
	"app_service/apps/business/operation_announcement/service/logic"
	commonDefine "app_service/apps/platform/common/define"
	commonEnum "app_service/apps/platform/common/define/enum"
	commonLogic "app_service/apps/platform/common/service/logic"
	issueFacade "app_service/apps/platform/issue/facade"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"
	"context"
	"encoding/json"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/datatypes"
)

// GetOperationAnnouncementList 查询运营公告列表
func (s *Service) GetOperationAnnouncementList(req *define.GetOperationAnnouncementAdminListReq) (*define.GetOperationAnnouncementAdminListResp, error) {
	db := repo.GetDB().WithContext(s.ctx)

	if !req.StartTime.IsZero() && !req.EndTime.IsZero() {
		switch req.TimeType {
		case enums.OperationAnnouncementTimeTypePublish:
			db = db.Where("publish_time >= ? AND publish_time <= ?", req.StartTime, req.EndTime)
		case enums.OperationAnnouncementTimeTypeCreate:
			db = db.Where("created_at >= ? AND created_at <= ?", req.StartTime, req.EndTime)
		}
	}
	if req.ID != 0 {
		db = db.Where("operation_announcement_id = ?", req.ID)
	}
	if req.Title != "" {
		db = db.Where("title LIKE ?", "%"+req.Title+"%")
	}
	if req.ItemId != "" {
		db = db.Where("JSON_CONTAINS(item_ids, ?)", "\""+req.ItemId+"\"")
	}
	if req.CategoryID != 0 {
		db = db.Where("category_id = ?", req.CategoryID)
	}
	if req.ChannelId != "" {
		db = db.Where("JSON_CONTAINS(channel_ids, ?)", "\""+req.ChannelId+"\"")
	}
	if req.Status != 0 {
		db = db.Where("status = ?", req.Status)
	}
	if req.CreatedBy != "" {
		db = db.Where("created_by = ?", req.CreatedBy)
	}

	// 排序、分页
	db = db.Order("created_at DESC")
	offset := (req.GetPage() - 1) * req.GetPageSize()
	if offset < 0 {
		offset = 0
	}
	var list []*model.OperationAnnouncement
	var count int64
	db.Model(&model.OperationAnnouncement{}).Count(&count)
	err := db.Offset(offset).Limit(req.GetPageSize()).Find(&list).Error
	if err != nil {
		return nil, err
	}

	resp := &define.GetOperationAnnouncementAdminListResp{
		List:  make([]*define.GetOperationAnnouncementAdminListData, 0),
		Total: count,
	}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有需要查询的ID
	var categoryIds []int64
	var allItemIds []string
	for _, v := range list {
		categoryIds = append(categoryIds, v.CategoryID)
		if v.ItemIds != nil {
			itemIds, err := logic.UnmarshalIds(*v.ItemIds)
			if err != nil {
				return nil, err
			}
			allItemIds = append(allItemIds, itemIds...)
		}
	}

	// 批量查询分类信息
	categorySchema := repo.GetQuery().OperationAnnCategory
	categories, _, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(
		search.NewQueryBuilder().In(categorySchema.OperationAnnCategoryID, categoryIds).Build(),
		1, len(categoryIds),
	)
	if err != nil {
		return nil, err
	}
	categoryMap := make(map[int64]*model.OperationAnnCategory)
	for _, category := range categories {
		categoryMap[category.OperationAnnCategoryID] = category
	}

	// 批量查询商品信息
	itemMap, err := issueFacade.GetIssueItemMap(s.ctx, allItemIds)
	if err != nil {
		log.Ctx(s.ctx).Errorf("GetIssueItemMap err:%v", err)
		return nil, err
	}

	dataList := make([]*define.GetOperationAnnouncementAdminListData, 0)
	for _, v := range list {
		// 解析ItemIds
		var itemIds []string
		var itemIdInfoList []*define.GetItemIdInfoList
		if v.ItemIds != nil {
			itemIds, err = logic.UnmarshalIds(*v.ItemIds)
			if err != nil {
				return nil, err
			}
			for _, itemId := range itemIds {
				if item, ok := itemMap[itemId]; ok {
					itemIdInfoList = append(itemIdInfoList, &define.GetItemIdInfoList{
						ID:       item.ID.Hex(),
						ItemId:   item.ItemID.Hex(),
						ItemName: item.ItemName,
						ImageUrl: item.ImageURL,
					})
				}
			}
		}

		// 解析ChannelIds
		var channelIds []string
		if len(v.ChannelIds) > 0 {
			channelIds, err = logic.UnmarshalIds(v.ChannelIds)
			if err != nil {
				return nil, err
			}
		}

		// 解析AdIds
		var adIds []string
		if v.AdIds != nil {
			adIds, err = logic.UnmarshalIds(*v.AdIds)
			if err != nil {
				return nil, err
			}
		}

		// 获取分类信息
		var categoryInfo *define.GetOperationAnnCategoryAdminLessDetailResp
		if category, ok := categoryMap[v.CategoryID]; ok {
			categoryInfo = &define.GetOperationAnnCategoryAdminLessDetailResp{
				ID:   category.OperationAnnCategoryID,
				Name: category.Name,
			}
		}

		dataList = append(dataList, &define.GetOperationAnnouncementAdminListData{
			ID:             v.OperationAnnouncementID,
			Title:          v.Title,
			Content:        v.Content,
			CategoryID:     v.CategoryID,
			CategoryInfo:   categoryInfo,
			Priority:       v.Priority,
			Status:         enums.OperationAnnouncementStatus(v.Status),
			PublishTime:    v.PublishTime,
			PublishType:    enums.OperationAnnouncementPublishType(v.PublishType),
			ItemIds:        itemIds,
			ItemIdInfoList: itemIdInfoList,
			ChannelIds:     channelIds,
			CreatedBy:      v.CreatedBy,
			AdIds:          adIds,
			CreatedAt:      v.CreatedAt,
			UpdatedAt:      v.UpdatedAt,
		})
	}
	resp.List = dataList
	return resp, nil
}

// GetOperationAnnouncementDetail 查询运营公告详情
func (s *Service) GetOperationAnnouncementDetail(req *define.GetOperationAnnouncementAdminDetailReq) (*define.GetOperationAnnouncementAdminDetailResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	builder := search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID)
	announcement, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}

	// Get item info list
	var itemInfoList []*define.GetItemIdInfoList
	var itemIds []string
	if announcement.ItemIds != nil {
		itemIds, err = logic.UnmarshalIds(*announcement.ItemIds)
		if err != nil {
			return nil, err
		}
		if len(itemIds) > 0 {
			issueItems, err := issueFacade.GetIssueItems(s.ctx, itemIds)
			if err != nil {
				log.Ctx(s.ctx).Errorf("GetIssueItems err:%v", err)
				return nil, err
			}

			for _, item := range issueItems {
				itemInfoList = append(itemInfoList, &define.GetItemIdInfoList{
					ID:       item.ID.Hex(),
					ItemId:   item.ItemID.Hex(),
					ItemName: item.ItemName,
					ImageUrl: item.ImageURL,
				})
			}
		}
	}

	// 解析ChannelIds
	var channelIds []string
	if len(announcement.ChannelIds) > 0 {
		channelIds, err = logic.UnmarshalIds(announcement.ChannelIds)
		if err != nil {
			return nil, err
		}
	}

	// CategoryID 查CategoryInfo
	categorySchema := repo.GetQuery().OperationAnnCategory
	category, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, announcement.CategoryID).Build(),
	)
	if err != nil {
		return nil, err
	}

	categoryInfo := &define.GetOperationAnnCategoryAdminLessDetailResp{
		ID:   category.OperationAnnCategoryID,
		Name: category.Name,
	}

	messagePush := enums.OperationAnnouncementMessagePushNo // 历史数据处理成 不推送
	if announcement.MessagePush != 0 {
		messagePush = enums.OperationAnnouncementMessagePushEnum(announcement.MessagePush)
	}
	resp := &define.GetOperationAnnouncementAdminDetailResp{
		ID:             announcement.OperationAnnouncementID,
		Title:          announcement.Title,
		Content:        announcement.Content,
		CategoryID:     announcement.CategoryID,
		CategoryInfo:   categoryInfo,
		ItemIds:        itemIds,
		ItemIdInfoList: itemInfoList,
		ChannelIds:     channelIds,
		Priority:       announcement.Priority,
		Status:         enums.OperationAnnouncementStatus(announcement.Status),
		PublishTime:    announcement.PublishTime,
		PublishType:    enums.OperationAnnouncementPublishType(announcement.PublishType),
		MessagePush:    messagePush,
		CreatedBy:      announcement.CreatedBy,
		CreatedAt:      announcement.CreatedAt,
		UpdatedAt:      announcement.UpdatedAt,
	}

	return resp, nil
}

// AddOperationAnnouncement 新增运营公告
func (s *Service) AddOperationAnnouncement(req *define.AddOperationAnnouncementReq) (*define.AddOperationAnnouncementResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement

	var itemIdsJsonData datatypes.JSON
	itemIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ItemIds))
	var channelIdsJsonData datatypes.JSON
	channelIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ChannelIds))

	announcement := &model.OperationAnnouncement{
		OperationAnnouncementID: snowflakeutl.GenerateID(),
		Title:                   req.Title,
		Content:                 req.Content,
		CategoryID:              req.CategoryID,
		Status:                  enums.OperationAnnouncementStatusDraft.Val(),
		PublishTime:             req.PublishTime,
		PublishType:             req.PublishType.Val(),
		MessagePush:             req.MessagePush.Val(),
		CreatedBy:               s.userService.GetAdminId(),
		ItemIds:                 &itemIdsJsonData,
		ChannelIds:              channelIdsJsonData,
		Priority:                req.Priority,
	}

	err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).Save(announcement)
	if err != nil {
		log.Ctx(s.ctx).Errorf("AddOperationAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     announcement.OperationAnnouncementID,
			RelateType:   commonEnum.OperationLogRelateTypeOperAnn,
			Action:       commonEnum.OperationLogActionCreate,
			AfterContent: announcement.Content,
			OperatedBy:   announcement.CreatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.AddOperationAnnouncementResp{
		ID: announcement.OperationAnnouncementID,
	}, nil
}

// EditOperationAnnouncement 编辑运营公告
func (s *Service) EditOperationAnnouncement(req *define.EditOperationAnnouncementReq) (*define.EditOperationAnnouncementResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	getAnn, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID).Build(),
	)
	if err != nil {
		return nil, err
	}

	if getAnn.Status == enums.OperationAnnouncementStatusOffline.Val() {
		return nil, define.AH220001Err
	}

	var status = enums.OperationAnnouncementStatus(getAnn.Status)
	if status == enums.OperationAnnouncementStatusDraft || status == enums.OperationAnnouncementStatusScheduled {
		if req.PublishType.Val() == enums.OperationAnnouncementPublishTypeImmediate.Val() {
			status = enums.OperationAnnouncementStatusDraft
		} else if req.PublishType.Val() == enums.OperationAnnouncementPublishTypeTiming.Val() {
			status = enums.OperationAnnouncementStatusScheduled
		}
	}

	var itemIdsJsonData datatypes.JSON
	itemIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ItemIds))
	var channelIdsJsonData datatypes.JSON
	channelIdsJsonData = datatypes.JSON(util.Obj2JsonStr(req.ChannelIds))

	announcement := &model.OperationAnnouncement{
		OperationAnnouncementID: req.ID,
		Title:                   req.Title,
		Content:                 req.Content,
		CategoryID:              req.CategoryID,
		Priority:                req.Priority,
		Status:                  status.Val(),
		PublishTime:             req.PublishTime,
		PublishType:             req.PublishType.Val(),
		MessagePush:             req.MessagePush.Val(),
		ItemIds:                 &itemIdsJsonData,
		ChannelIds:              channelIdsJsonData,
	}

	err = repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).UpdateById(announcement)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(getAnn)
	afterContent, _ := json.Marshal(announcement)
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      announcement.OperationAnnouncementID,
			RelateType:    commonEnum.OperationLogRelateTypeOperAnn,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditOperationAnnouncementResp{
		ID: req.ID,
	}, nil
}

// EditOperationAnnouncementStatus 编辑运营公告状态
func (s *Service) EditOperationAnnouncementStatus(req *define.EditOperationAnnouncementStatusReq) (*define.EditOperationAnnouncementStatusResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	builder := search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID)

	m := &model.OperationAnnouncement{
		Status: req.Status.Val(),
	}

	getAnn, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	isImmediatePublish := false
	if req.Status.Val() == enums.OperationAnnouncementStatusOffline.Val() {
		// 仅限定时中和已发布的状态的可操作下架
		if !(getAnn.Status == enums.OperationAnnouncementStatusScheduled.Val() ||
			getAnn.Status == enums.OperationAnnouncementStatusPublished.Val()) {
			return nil, define.AH220002Err
		}
	} else if req.Status.Val() == enums.OperationAnnouncementStatusPublished.Val() {
		// 仅限待发布的状态的可操作发布
		if getAnn.Status != enums.OperationAnnouncementStatusDraft.Val() {
			return nil, define.AH220007Err
		}
		if getAnn.PublishType == enums.OperationAnnouncementPublishTypeImmediate.Val() {
			isImmediatePublish = true
			m.Status = int32(enums.OperationAnnouncementStatusPublished)
			// 更新发布时间
			now := util.Now()
			m.PublishTime = &now
		} else if getAnn.PublishType == enums.OperationAnnouncementPublishTypeTiming.Val() {
			m.Status = int32(enums.OperationAnnouncementStatusScheduled)
		}
	} else {
		return nil, define.AH220005Err
	}

	err = repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m, builder.Build())
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnouncementStatus err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(map[string]any{"status": getAnn.Status})
	afterContent, _ := json.Marshal(map[string]any{"status": req.Status})
	go func() {
		var action commonEnum.OperationLogActionEnum
		if req.Status.Val() == enums.OperationAnnouncementStatusOffline.Val() {
			action = commonEnum.OperationLogActionRemove
		} else if req.Status.Val() == enums.OperationAnnouncementStatusPublished.Val() {
			action = commonEnum.OperationLogActionPublish
		}
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeOperAnn,
			Action:        action,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	// 消息推送
	if getAnn.MessagePush == enums.OperationAnnouncementMessagePushYes.Val() && isImmediatePublish {
		spanCtx := s.NewContextWithSpanContext(s.ctx)
		go func() {
			pushErr := logic.PushOperationAnnouncementMessage(spanCtx, getAnn)
			if pushErr != nil {
				log.Ctx(spanCtx).Errorf("运营方公告消息推送失败: %v", pushErr)
			}
		}()
	}

	return &define.EditOperationAnnouncementStatusResp{
		ID: req.ID,
	}, nil
}

// EditOperationAnnouncementPriority 编辑运营公告优先级
func (s *Service) EditOperationAnnouncementPriority(req *define.EditOperationAnnouncementPriorityReq) (*define.EditOperationAnnouncementPriorityResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	builder := search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID)

	getAnn, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	m := &model.OperationAnnouncement{
		Priority: req.Priority,
	}

	err = repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		builder.Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnouncementPriority err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(map[string]any{"priority": getAnn.Status})
	afterContent, _ := json.Marshal(map[string]any{"priority": req.Priority})
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeOperAnn,
			Action:        commonEnum.OperationLogActionUpdate,
			BeforeContent: string(beforeContent),
			AfterContent:  string(afterContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.EditOperationAnnouncementPriorityResp{
		ID: req.ID,
	}, nil
}

// EditOperationAnnouncementAdIds 编辑运营公告关联广告ID
func (s *Service) EditOperationAnnouncementAdIds(req *define.EditOperationAnnouncementAdIdsReq) (*define.EditOperationAnnouncementAdIdsResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement

	//// 仅已发布状态的运营公告可同步媒体
	//if len(req.AdIds) > 0 {
	//	getAnn, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
	//		search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID).Build(),
	//	)
	//	if err != nil {
	//		return nil, err
	//	}
	//	if getAnn.Status != enums.OperationAnnouncementStatusPublished.Val() {
	//		return nil, define.AH220004Err
	//	}
	//}

	var adIdsJson datatypes.JSON = datatypes.JSON(util.Obj2JsonStr(req.AdIds))
	m := &model.OperationAnnouncement{
		AdIds: &adIdsJson,
	}

	err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnouncementAdIds err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	afterContent, _ := json.Marshal(req)
	go func() {
		var action commonEnum.OperationLogActionEnum
		if len(req.AdIds) > 0 {
			action = commonEnum.OperationLogActionSyncMediaON
		} else {
			action = commonEnum.OperationLogActionSyncMediaOFF
		}
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:     req.ID,
			RelateType:   commonEnum.OperationLogRelateTypeOperAnn,
			Action:       action,
			AfterContent: string(afterContent),
			OperatedBy:   operatedBy,
			OperatedAt:   util.Now(),
		})
	}()

	return &define.EditOperationAnnouncementAdIdsResp{
		ID: req.ID,
	}, nil
}

// DelOperationAnnouncement 删除运营公告
func (s *Service) DelOperationAnnouncement(req *define.DelOperationAnnouncementReq) (*define.DelOperationAnnouncementResp, error) {
	annSchema := repo.GetQuery().OperationAnnouncement
	builder := search.NewQueryBuilder().Eq(annSchema.OperationAnnouncementID, req.ID)

	getAnn, err := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).SelectOne(
		builder.Build(),
	)
	if err != nil {
		return nil, err
	}

	m := &model.OperationAnnouncement{
		//Status: enums.OperationAnnouncementStatusOffline.Val(),
		//IsDel:  1,
	}

	err = repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx)).Update(m,
		builder.Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("DelOperationAnnouncement err:%v", err)
		return nil, err
	}

	// 保存操作日志
	operatedBy := s.userService.GetAdminId()
	beforeContent, _ := json.Marshal(getAnn)
	go func() {
		ctx := context.Background()
		_ = commonLogic.AddOperationLog(ctx, commonDefine.AddOperationLogParams{
			RelateID:      req.ID,
			RelateType:    commonEnum.OperationLogRelateTypeOperAnn,
			Action:        commonEnum.OperationLogActionDelete,
			BeforeContent: string(beforeContent),
			OperatedBy:    operatedBy,
			OperatedAt:    util.Now(),
		})
	}()

	return &define.DelOperationAnnouncementResp{
		ID: req.ID,
	}, nil
}

// GetOperationAnnCategoryList 查询运营公告分类列表
func (s *Service) GetOperationAnnCategoryList(req *define.GetOperationAnnCategoryAdminListReq) (*define.GetOperationAnnCategoryAdminListResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	builder := search.NewQueryBuilder().OrderByDesc(categorySchema.Priority, categorySchema.CreatedAt)

	// 获取分类列表
	//builder = builder.Eq(categorySchema.IsDel, 0)
	list, count, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectPage(builder.Build(), req.GetPage(), req.GetPageSize())
	if err != nil {
		return nil, err
	}

	resp := &define.GetOperationAnnCategoryAdminListResp{}
	if len(list) == 0 {
		return resp, nil
	}

	// 收集所有分类ID
	categoryIds := make([]int64, 0, len(list))
	for _, v := range list {
		categoryIds = append(categoryIds, v.OperationAnnCategoryID)
	}

	// 获取每个分类的运营公告数量
	categoryCountMap, err := logic.GetCategoryAnnCounts(s.ctx, categoryIds)
	if err != nil {
		return nil, err
	}

	// 构建响应
	dataList := make([]*define.GetOperationAnnCategoryAdminListData, 0)
	for _, v := range list {
		dataList = append(dataList, &define.GetOperationAnnCategoryAdminListData{
			ID:        v.OperationAnnCategoryID,
			Name:      v.Name,
			AnnNum:    categoryCountMap[v.OperationAnnCategoryID], // 使用映射获取数量，如果不存在则为0
			Priority:  v.Priority,
			Status:    enums.OperationAnnCategoryStatus(v.Status),
			CreatedAt: v.CreatedAt,
			UpdatedAt: v.UpdatedAt,
		})
	}
	resp.List = dataList
	resp.Total = count
	return resp, nil
}

// GetOperationAnnCategoryDetail 获取运营公告分类详情
func (s *Service) GetOperationAnnCategoryDetail(req *define.GetOperationAnnCategoryAdminDetailReq) (*define.GetOperationAnnCategoryAdminDetailResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	builder := search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, req.ID)

	category, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(builder.Build())
	if err != nil {
		return nil, err
	}

	return &define.GetOperationAnnCategoryAdminDetailResp{
		ID:              category.OperationAnnCategoryID,
		Name:            category.Name,
		TextColor:       category.TextColor,
		BackgroundColor: category.BackgroundColor,
		Priority:        category.Priority,
		Status:          enums.OperationAnnCategoryStatus(category.Status),
		CreatedAt:       category.CreatedAt,
		UpdatedAt:       category.UpdatedAt,
	}, nil
}

// AddOperationAnnCategory 新增运营公告分类
func (s *Service) AddOperationAnnCategory(req *define.AddOperationAnnCategoryReq) (*define.AddOperationAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	//getCategory, _ := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
	//	search.NewQueryBuilder().Eq(categorySchema.Name, req.Name).Build(),
	//)
	//if getCategory != nil {
	//	return nil, define.AH220003Err
	//}

	category := &model.OperationAnnCategory{
		OperationAnnCategoryID: snowflakeutl.GenerateID(),
		Name:                   req.Name,
		TextColor:              req.TextColor,
		BackgroundColor:        req.BackgroundColor,
		Priority:               req.Priority,
		Status:                 enums.OperationAnnCategoryStatusEnable.Val(),
	}

	err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Save(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("AddOperationAnnCategory err:%v", err)
		return nil, err
	}

	return &define.AddOperationAnnCategoryResp{
		ID: category.OperationAnnCategoryID,
	}, nil
}

// EditOperationAnnCategory 编辑运营公告分类
func (s *Service) EditOperationAnnCategory(req *define.EditOperationAnnCategoryReq) (*define.EditOperationAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	//getCategory, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).SelectOne(
	//	search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, req.ID).Build(),
	//)
	//if err != nil {
	//	return nil, err
	//}
	//
	//if getCategory != nil && getCategory.Name != req.Name {
	//	count, err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Count(
	//		search.NewQueryBuilder().
	//			Eq(categorySchema.Name, req.Name).
	//			Ne(categorySchema.OperationAnnCategoryID, req.ID).
	//			Build(),
	//	)
	//	if err != nil {
	//		return nil, err
	//	}
	//	if count > 0 {
	//		return nil, define.AH220001Err
	//	}
	//}

	category := &model.OperationAnnCategory{
		OperationAnnCategoryID: req.ID,
		Name:                   req.Name,
		TextColor:              req.TextColor,
		BackgroundColor:        req.BackgroundColor,
		Priority:               req.Priority,
	}

	err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).UpdateById(category)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnCategory err:%v", err)
		return nil, err
	}

	return &define.EditOperationAnnCategoryResp{
		ID: req.ID,
	}, nil
}

// EditOperationAnnCategoryPriority 编辑运营公告分类优先级
func (s *Service) EditOperationAnnCategoryPriority(req *define.EditOperationAnnCategoryPriorityReq) (*define.EditOperationAnnCategoryPriorityResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	m := &model.OperationAnnCategory{
		Priority: req.Priority,
	}

	err := repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("EditOperationAnnCategoryPriority err:%v", err)
		return nil, err
	}

	return &define.EditOperationAnnCategoryPriorityResp{
		ID: req.ID,
	}, nil
}

// DelOperationAnnCategory 删除运营公告分类
func (s *Service) DelOperationAnnCategory(req *define.DelOperationAnnCategoryReq) (*define.DelOperationAnnCategoryResp, error) {
	categorySchema := repo.GetQuery().OperationAnnCategory
	m := &model.OperationAnnCategory{
		Status: enums.OperationAnnCategoryStatusDeleted.Val(),
		IsDel:  1,
	}

	// 有关联运营公告的栏目不可删除
	annSchema := repo.GetQuery().OperationAnnouncement
	announcementRepo := repo.NewOperationAnnouncementRepo(annSchema.WithContext(s.ctx))
	count, err := announcementRepo.Count(
		search.NewQueryBuilder().Eq(annSchema.CategoryID, req.ID).Build(),
	)
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, define.AH220003Err
	}

	err = repo.NewOperationAnnCategoryRepo(categorySchema.WithContext(s.ctx)).Update(m,
		search.NewQueryBuilder().Eq(categorySchema.OperationAnnCategoryID, req.ID).Build(),
	)
	if err != nil {
		log.Ctx(s.ctx).Errorf("DelOperationAnnCategory err:%v", err)
		return nil, err
	}

	return &define.DelOperationAnnCategoryResp{
		ID: req.ID,
	}, nil
}
