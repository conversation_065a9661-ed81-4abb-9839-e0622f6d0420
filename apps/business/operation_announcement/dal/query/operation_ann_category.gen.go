// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/operation_announcement/dal/model"
)

func newOperationAnnCategory(db *gorm.DB, opts ...gen.DOOption) operationAnnCategory {
	_operationAnnCategory := operationAnnCategory{}

	_operationAnnCategory.operationAnnCategoryDo.UseDB(db, opts...)
	_operationAnnCategory.operationAnnCategoryDo.UseModel(&model.OperationAnnCategory{})

	tableName := _operationAnnCategory.operationAnnCategoryDo.TableName()
	_operationAnnCategory.ALL = field.NewAsterisk(tableName)
	_operationAnnCategory.OperationAnnCategoryID = field.NewInt64(tableName, "operation_ann_category_id")
	_operationAnnCategory.Name = field.NewString(tableName, "name")
	_operationAnnCategory.TextColor = field.NewString(tableName, "text_color")
	_operationAnnCategory.BackgroundColor = field.NewString(tableName, "background_color")
	_operationAnnCategory.Priority = field.NewInt32(tableName, "priority")
	_operationAnnCategory.Status = field.NewInt32(tableName, "status")
	_operationAnnCategory.CreatedAt = field.NewTime(tableName, "created_at")
	_operationAnnCategory.UpdatedAt = field.NewTime(tableName, "updated_at")
	_operationAnnCategory.IsDel = field.NewField(tableName, "is_del")

	_operationAnnCategory.fillFieldMap()

	return _operationAnnCategory
}

// operationAnnCategory 运营公告栏目表
type operationAnnCategory struct {
	operationAnnCategoryDo

	ALL                    field.Asterisk
	OperationAnnCategoryID field.Int64  // 主键ID
	Name                   field.String // 栏目名称
	TextColor              field.String // 文字颜色
	BackgroundColor        field.String // 背景颜色
	Priority               field.Int32  // 优先级，最大值9999
	Status                 field.Int32  // 状态：-1:已删除 1启用
	CreatedAt              field.Time   // 创建时间
	UpdatedAt              field.Time   // 更新时间
	IsDel                  field.Field  // 是否删除【0->未删除; 1->删除】

	fieldMap map[string]field.Expr
}

func (o operationAnnCategory) Table(newTableName string) *operationAnnCategory {
	o.operationAnnCategoryDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o operationAnnCategory) As(alias string) *operationAnnCategory {
	o.operationAnnCategoryDo.DO = *(o.operationAnnCategoryDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *operationAnnCategory) updateTableName(table string) *operationAnnCategory {
	o.ALL = field.NewAsterisk(table)
	o.OperationAnnCategoryID = field.NewInt64(table, "operation_ann_category_id")
	o.Name = field.NewString(table, "name")
	o.TextColor = field.NewString(table, "text_color")
	o.BackgroundColor = field.NewString(table, "background_color")
	o.Priority = field.NewInt32(table, "priority")
	o.Status = field.NewInt32(table, "status")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")
	o.IsDel = field.NewField(table, "is_del")

	o.fillFieldMap()

	return o
}

func (o *operationAnnCategory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *operationAnnCategory) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 9)
	o.fieldMap["operation_ann_category_id"] = o.OperationAnnCategoryID
	o.fieldMap["name"] = o.Name
	o.fieldMap["text_color"] = o.TextColor
	o.fieldMap["background_color"] = o.BackgroundColor
	o.fieldMap["priority"] = o.Priority
	o.fieldMap["status"] = o.Status
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt
	o.fieldMap["is_del"] = o.IsDel
}

func (o operationAnnCategory) clone(db *gorm.DB) operationAnnCategory {
	o.operationAnnCategoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o operationAnnCategory) replaceDB(db *gorm.DB) operationAnnCategory {
	o.operationAnnCategoryDo.ReplaceDB(db)
	return o
}

type operationAnnCategoryDo struct{ gen.DO }

type IOperationAnnCategoryDo interface {
	gen.SubQuery
	Debug() IOperationAnnCategoryDo
	WithContext(ctx context.Context) IOperationAnnCategoryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOperationAnnCategoryDo
	WriteDB() IOperationAnnCategoryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOperationAnnCategoryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOperationAnnCategoryDo
	Not(conds ...gen.Condition) IOperationAnnCategoryDo
	Or(conds ...gen.Condition) IOperationAnnCategoryDo
	Select(conds ...field.Expr) IOperationAnnCategoryDo
	Where(conds ...gen.Condition) IOperationAnnCategoryDo
	Order(conds ...field.Expr) IOperationAnnCategoryDo
	Distinct(cols ...field.Expr) IOperationAnnCategoryDo
	Omit(cols ...field.Expr) IOperationAnnCategoryDo
	Join(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo
	Group(cols ...field.Expr) IOperationAnnCategoryDo
	Having(conds ...gen.Condition) IOperationAnnCategoryDo
	Limit(limit int) IOperationAnnCategoryDo
	Offset(offset int) IOperationAnnCategoryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOperationAnnCategoryDo
	Unscoped() IOperationAnnCategoryDo
	Create(values ...*model.OperationAnnCategory) error
	CreateInBatches(values []*model.OperationAnnCategory, batchSize int) error
	Save(values ...*model.OperationAnnCategory) error
	First() (*model.OperationAnnCategory, error)
	Take() (*model.OperationAnnCategory, error)
	Last() (*model.OperationAnnCategory, error)
	Find() ([]*model.OperationAnnCategory, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperationAnnCategory, err error)
	FindInBatches(result *[]*model.OperationAnnCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.OperationAnnCategory) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOperationAnnCategoryDo
	Assign(attrs ...field.AssignExpr) IOperationAnnCategoryDo
	Joins(fields ...field.RelationField) IOperationAnnCategoryDo
	Preload(fields ...field.RelationField) IOperationAnnCategoryDo
	FirstOrInit() (*model.OperationAnnCategory, error)
	FirstOrCreate() (*model.OperationAnnCategory, error)
	FindByPage(offset int, limit int) (result []*model.OperationAnnCategory, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOperationAnnCategoryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o operationAnnCategoryDo) Debug() IOperationAnnCategoryDo {
	return o.withDO(o.DO.Debug())
}

func (o operationAnnCategoryDo) WithContext(ctx context.Context) IOperationAnnCategoryDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o operationAnnCategoryDo) ReadDB() IOperationAnnCategoryDo {
	return o.Clauses(dbresolver.Read)
}

func (o operationAnnCategoryDo) WriteDB() IOperationAnnCategoryDo {
	return o.Clauses(dbresolver.Write)
}

func (o operationAnnCategoryDo) Session(config *gorm.Session) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Session(config))
}

func (o operationAnnCategoryDo) Clauses(conds ...clause.Expression) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o operationAnnCategoryDo) Returning(value interface{}, columns ...string) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o operationAnnCategoryDo) Not(conds ...gen.Condition) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o operationAnnCategoryDo) Or(conds ...gen.Condition) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o operationAnnCategoryDo) Select(conds ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o operationAnnCategoryDo) Where(conds ...gen.Condition) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o operationAnnCategoryDo) Order(conds ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o operationAnnCategoryDo) Distinct(cols ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o operationAnnCategoryDo) Omit(cols ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o operationAnnCategoryDo) Join(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o operationAnnCategoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o operationAnnCategoryDo) RightJoin(table schema.Tabler, on ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o operationAnnCategoryDo) Group(cols ...field.Expr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o operationAnnCategoryDo) Having(conds ...gen.Condition) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o operationAnnCategoryDo) Limit(limit int) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o operationAnnCategoryDo) Offset(offset int) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o operationAnnCategoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o operationAnnCategoryDo) Unscoped() IOperationAnnCategoryDo {
	return o.withDO(o.DO.Unscoped())
}

func (o operationAnnCategoryDo) Create(values ...*model.OperationAnnCategory) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o operationAnnCategoryDo) CreateInBatches(values []*model.OperationAnnCategory, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o operationAnnCategoryDo) Save(values ...*model.OperationAnnCategory) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o operationAnnCategoryDo) First() (*model.OperationAnnCategory, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnCategory), nil
	}
}

func (o operationAnnCategoryDo) Take() (*model.OperationAnnCategory, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnCategory), nil
	}
}

func (o operationAnnCategoryDo) Last() (*model.OperationAnnCategory, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnCategory), nil
	}
}

func (o operationAnnCategoryDo) Find() ([]*model.OperationAnnCategory, error) {
	result, err := o.DO.Find()
	return result.([]*model.OperationAnnCategory), err
}

func (o operationAnnCategoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperationAnnCategory, err error) {
	buf := make([]*model.OperationAnnCategory, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o operationAnnCategoryDo) FindInBatches(result *[]*model.OperationAnnCategory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o operationAnnCategoryDo) Attrs(attrs ...field.AssignExpr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o operationAnnCategoryDo) Assign(attrs ...field.AssignExpr) IOperationAnnCategoryDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o operationAnnCategoryDo) Joins(fields ...field.RelationField) IOperationAnnCategoryDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o operationAnnCategoryDo) Preload(fields ...field.RelationField) IOperationAnnCategoryDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o operationAnnCategoryDo) FirstOrInit() (*model.OperationAnnCategory, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnCategory), nil
	}
}

func (o operationAnnCategoryDo) FirstOrCreate() (*model.OperationAnnCategory, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnCategory), nil
	}
}

func (o operationAnnCategoryDo) FindByPage(offset int, limit int) (result []*model.OperationAnnCategory, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o operationAnnCategoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o operationAnnCategoryDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o operationAnnCategoryDo) Delete(models ...*model.OperationAnnCategory) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *operationAnnCategoryDo) withDO(do gen.Dao) *operationAnnCategoryDo {
	o.DO = *do.(*gen.DO)
	return o
}
