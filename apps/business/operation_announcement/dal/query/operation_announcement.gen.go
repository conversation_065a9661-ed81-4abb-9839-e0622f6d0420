// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/operation_announcement/dal/model"
)

func newOperationAnnouncement(db *gorm.DB, opts ...gen.DOOption) operationAnnouncement {
	_operationAnnouncement := operationAnnouncement{}

	_operationAnnouncement.operationAnnouncementDo.UseDB(db, opts...)
	_operationAnnouncement.operationAnnouncementDo.UseModel(&model.OperationAnnouncement{})

	tableName := _operationAnnouncement.operationAnnouncementDo.TableName()
	_operationAnnouncement.ALL = field.NewAsterisk(tableName)
	_operationAnnouncement.OperationAnnouncementID = field.NewInt64(tableName, "operation_announcement_id")
	_operationAnnouncement.Title = field.NewString(tableName, "title")
	_operationAnnouncement.Content = field.NewString(tableName, "content")
	_operationAnnouncement.CategoryID = field.NewInt64(tableName, "category_id")
	_operationAnnouncement.Priority = field.NewInt32(tableName, "priority")
	_operationAnnouncement.Status = field.NewInt32(tableName, "status")
	_operationAnnouncement.PublishTime = field.NewTime(tableName, "publish_time")
	_operationAnnouncement.ItemIds = field.NewField(tableName, "item_ids")
	_operationAnnouncement.ChannelIds = field.NewField(tableName, "channel_ids")
	_operationAnnouncement.PublishType = field.NewInt32(tableName, "publish_type")
	_operationAnnouncement.MessagePush = field.NewInt32(tableName, "message_push")
	_operationAnnouncement.CreatedBy = field.NewString(tableName, "created_by")
	_operationAnnouncement.AdIds = field.NewField(tableName, "ad_ids")
	_operationAnnouncement.CreatedAt = field.NewTime(tableName, "created_at")
	_operationAnnouncement.UpdatedAt = field.NewTime(tableName, "updated_at")
	_operationAnnouncement.CategoryInfo = operationAnnouncementBelongsToCategoryInfo{
		db: db.Session(&gorm.Session{}),

		RelationField: field.NewRelation("CategoryInfo", "model.OperationAnnCategory"),
	}

	_operationAnnouncement.fillFieldMap()

	return _operationAnnouncement
}

// operationAnnouncement 运营公告表
type operationAnnouncement struct {
	operationAnnouncementDo

	ALL                     field.Asterisk
	OperationAnnouncementID field.Int64  // 主键ID
	Title                   field.String // 公告标题
	Content                 field.String // 公告内容
	CategoryID              field.Int64  // 栏目ID
	Priority                field.Int32  // 优先级
	Status                  field.Int32  // 状态：1待发布 2定时中 3已发布 4已下架
	PublishTime             field.Time   // 发布时间
	ItemIds                 field.Field  // 关联商品挂牌编码
	ChannelIds              field.Field  // 发布终端列表
	PublishType             field.Int32  // 发布方式：1立即发布 2定时发布
	MessagePush             field.Int32  // 消息推送【1:推送, 2:不推送】
	CreatedBy               field.String // 创建人
	AdIds                   field.Field  // 广告商ids，关联tmt-ad_id
	CreatedAt               field.Time   // 创建时间
	UpdatedAt               field.Time   // 更新时间
	CategoryInfo            operationAnnouncementBelongsToCategoryInfo

	fieldMap map[string]field.Expr
}

func (o operationAnnouncement) Table(newTableName string) *operationAnnouncement {
	o.operationAnnouncementDo.UseTable(newTableName)
	return o.updateTableName(newTableName)
}

func (o operationAnnouncement) As(alias string) *operationAnnouncement {
	o.operationAnnouncementDo.DO = *(o.operationAnnouncementDo.As(alias).(*gen.DO))
	return o.updateTableName(alias)
}

func (o *operationAnnouncement) updateTableName(table string) *operationAnnouncement {
	o.ALL = field.NewAsterisk(table)
	o.OperationAnnouncementID = field.NewInt64(table, "operation_announcement_id")
	o.Title = field.NewString(table, "title")
	o.Content = field.NewString(table, "content")
	o.CategoryID = field.NewInt64(table, "category_id")
	o.Priority = field.NewInt32(table, "priority")
	o.Status = field.NewInt32(table, "status")
	o.PublishTime = field.NewTime(table, "publish_time")
	o.ItemIds = field.NewField(table, "item_ids")
	o.ChannelIds = field.NewField(table, "channel_ids")
	o.PublishType = field.NewInt32(table, "publish_type")
	o.MessagePush = field.NewInt32(table, "message_push")
	o.CreatedBy = field.NewString(table, "created_by")
	o.AdIds = field.NewField(table, "ad_ids")
	o.CreatedAt = field.NewTime(table, "created_at")
	o.UpdatedAt = field.NewTime(table, "updated_at")

	o.fillFieldMap()

	return o
}

func (o *operationAnnouncement) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := o.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (o *operationAnnouncement) fillFieldMap() {
	o.fieldMap = make(map[string]field.Expr, 16)
	o.fieldMap["operation_announcement_id"] = o.OperationAnnouncementID
	o.fieldMap["title"] = o.Title
	o.fieldMap["content"] = o.Content
	o.fieldMap["category_id"] = o.CategoryID
	o.fieldMap["priority"] = o.Priority
	o.fieldMap["status"] = o.Status
	o.fieldMap["publish_time"] = o.PublishTime
	o.fieldMap["item_ids"] = o.ItemIds
	o.fieldMap["channel_ids"] = o.ChannelIds
	o.fieldMap["publish_type"] = o.PublishType
	o.fieldMap["message_push"] = o.MessagePush
	o.fieldMap["created_by"] = o.CreatedBy
	o.fieldMap["ad_ids"] = o.AdIds
	o.fieldMap["created_at"] = o.CreatedAt
	o.fieldMap["updated_at"] = o.UpdatedAt

}

func (o operationAnnouncement) clone(db *gorm.DB) operationAnnouncement {
	o.operationAnnouncementDo.ReplaceConnPool(db.Statement.ConnPool)
	return o
}

func (o operationAnnouncement) replaceDB(db *gorm.DB) operationAnnouncement {
	o.operationAnnouncementDo.ReplaceDB(db)
	return o
}

type operationAnnouncementBelongsToCategoryInfo struct {
	db *gorm.DB

	field.RelationField
}

func (a operationAnnouncementBelongsToCategoryInfo) Where(conds ...field.Expr) *operationAnnouncementBelongsToCategoryInfo {
	if len(conds) == 0 {
		return &a
	}

	exprs := make([]clause.Expression, 0, len(conds))
	for _, cond := range conds {
		exprs = append(exprs, cond.BeCond().(clause.Expression))
	}
	a.db = a.db.Clauses(clause.Where{Exprs: exprs})
	return &a
}

func (a operationAnnouncementBelongsToCategoryInfo) WithContext(ctx context.Context) *operationAnnouncementBelongsToCategoryInfo {
	a.db = a.db.WithContext(ctx)
	return &a
}

func (a operationAnnouncementBelongsToCategoryInfo) Session(session *gorm.Session) *operationAnnouncementBelongsToCategoryInfo {
	a.db = a.db.Session(session)
	return &a
}

func (a operationAnnouncementBelongsToCategoryInfo) Model(m *model.OperationAnnouncement) *operationAnnouncementBelongsToCategoryInfoTx {
	return &operationAnnouncementBelongsToCategoryInfoTx{a.db.Model(m).Association(a.Name())}
}

type operationAnnouncementBelongsToCategoryInfoTx struct{ tx *gorm.Association }

func (a operationAnnouncementBelongsToCategoryInfoTx) Find() (result *model.OperationAnnCategory, err error) {
	return result, a.tx.Find(&result)
}

func (a operationAnnouncementBelongsToCategoryInfoTx) Append(values ...*model.OperationAnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Append(targetValues...)
}

func (a operationAnnouncementBelongsToCategoryInfoTx) Replace(values ...*model.OperationAnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Replace(targetValues...)
}

func (a operationAnnouncementBelongsToCategoryInfoTx) Delete(values ...*model.OperationAnnCategory) (err error) {
	targetValues := make([]interface{}, len(values))
	for i, v := range values {
		targetValues[i] = v
	}
	return a.tx.Delete(targetValues...)
}

func (a operationAnnouncementBelongsToCategoryInfoTx) Clear() error {
	return a.tx.Clear()
}

func (a operationAnnouncementBelongsToCategoryInfoTx) Count() int64 {
	return a.tx.Count()
}

type operationAnnouncementDo struct{ gen.DO }

type IOperationAnnouncementDo interface {
	gen.SubQuery
	Debug() IOperationAnnouncementDo
	WithContext(ctx context.Context) IOperationAnnouncementDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IOperationAnnouncementDo
	WriteDB() IOperationAnnouncementDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IOperationAnnouncementDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IOperationAnnouncementDo
	Not(conds ...gen.Condition) IOperationAnnouncementDo
	Or(conds ...gen.Condition) IOperationAnnouncementDo
	Select(conds ...field.Expr) IOperationAnnouncementDo
	Where(conds ...gen.Condition) IOperationAnnouncementDo
	Order(conds ...field.Expr) IOperationAnnouncementDo
	Distinct(cols ...field.Expr) IOperationAnnouncementDo
	Omit(cols ...field.Expr) IOperationAnnouncementDo
	Join(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo
	RightJoin(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo
	Group(cols ...field.Expr) IOperationAnnouncementDo
	Having(conds ...gen.Condition) IOperationAnnouncementDo
	Limit(limit int) IOperationAnnouncementDo
	Offset(offset int) IOperationAnnouncementDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IOperationAnnouncementDo
	Unscoped() IOperationAnnouncementDo
	Create(values ...*model.OperationAnnouncement) error
	CreateInBatches(values []*model.OperationAnnouncement, batchSize int) error
	Save(values ...*model.OperationAnnouncement) error
	First() (*model.OperationAnnouncement, error)
	Take() (*model.OperationAnnouncement, error)
	Last() (*model.OperationAnnouncement, error)
	Find() ([]*model.OperationAnnouncement, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperationAnnouncement, err error)
	FindInBatches(result *[]*model.OperationAnnouncement, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.OperationAnnouncement) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IOperationAnnouncementDo
	Assign(attrs ...field.AssignExpr) IOperationAnnouncementDo
	Joins(fields ...field.RelationField) IOperationAnnouncementDo
	Preload(fields ...field.RelationField) IOperationAnnouncementDo
	FirstOrInit() (*model.OperationAnnouncement, error)
	FirstOrCreate() (*model.OperationAnnouncement, error)
	FindByPage(offset int, limit int) (result []*model.OperationAnnouncement, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IOperationAnnouncementDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (o operationAnnouncementDo) Debug() IOperationAnnouncementDo {
	return o.withDO(o.DO.Debug())
}

func (o operationAnnouncementDo) WithContext(ctx context.Context) IOperationAnnouncementDo {
	return o.withDO(o.DO.WithContext(ctx))
}

func (o operationAnnouncementDo) ReadDB() IOperationAnnouncementDo {
	return o.Clauses(dbresolver.Read)
}

func (o operationAnnouncementDo) WriteDB() IOperationAnnouncementDo {
	return o.Clauses(dbresolver.Write)
}

func (o operationAnnouncementDo) Session(config *gorm.Session) IOperationAnnouncementDo {
	return o.withDO(o.DO.Session(config))
}

func (o operationAnnouncementDo) Clauses(conds ...clause.Expression) IOperationAnnouncementDo {
	return o.withDO(o.DO.Clauses(conds...))
}

func (o operationAnnouncementDo) Returning(value interface{}, columns ...string) IOperationAnnouncementDo {
	return o.withDO(o.DO.Returning(value, columns...))
}

func (o operationAnnouncementDo) Not(conds ...gen.Condition) IOperationAnnouncementDo {
	return o.withDO(o.DO.Not(conds...))
}

func (o operationAnnouncementDo) Or(conds ...gen.Condition) IOperationAnnouncementDo {
	return o.withDO(o.DO.Or(conds...))
}

func (o operationAnnouncementDo) Select(conds ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Select(conds...))
}

func (o operationAnnouncementDo) Where(conds ...gen.Condition) IOperationAnnouncementDo {
	return o.withDO(o.DO.Where(conds...))
}

func (o operationAnnouncementDo) Order(conds ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Order(conds...))
}

func (o operationAnnouncementDo) Distinct(cols ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Distinct(cols...))
}

func (o operationAnnouncementDo) Omit(cols ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Omit(cols...))
}

func (o operationAnnouncementDo) Join(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Join(table, on...))
}

func (o operationAnnouncementDo) LeftJoin(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.LeftJoin(table, on...))
}

func (o operationAnnouncementDo) RightJoin(table schema.Tabler, on ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.RightJoin(table, on...))
}

func (o operationAnnouncementDo) Group(cols ...field.Expr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Group(cols...))
}

func (o operationAnnouncementDo) Having(conds ...gen.Condition) IOperationAnnouncementDo {
	return o.withDO(o.DO.Having(conds...))
}

func (o operationAnnouncementDo) Limit(limit int) IOperationAnnouncementDo {
	return o.withDO(o.DO.Limit(limit))
}

func (o operationAnnouncementDo) Offset(offset int) IOperationAnnouncementDo {
	return o.withDO(o.DO.Offset(offset))
}

func (o operationAnnouncementDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IOperationAnnouncementDo {
	return o.withDO(o.DO.Scopes(funcs...))
}

func (o operationAnnouncementDo) Unscoped() IOperationAnnouncementDo {
	return o.withDO(o.DO.Unscoped())
}

func (o operationAnnouncementDo) Create(values ...*model.OperationAnnouncement) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Create(values)
}

func (o operationAnnouncementDo) CreateInBatches(values []*model.OperationAnnouncement, batchSize int) error {
	return o.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (o operationAnnouncementDo) Save(values ...*model.OperationAnnouncement) error {
	if len(values) == 0 {
		return nil
	}
	return o.DO.Save(values)
}

func (o operationAnnouncementDo) First() (*model.OperationAnnouncement, error) {
	if result, err := o.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnouncement), nil
	}
}

func (o operationAnnouncementDo) Take() (*model.OperationAnnouncement, error) {
	if result, err := o.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnouncement), nil
	}
}

func (o operationAnnouncementDo) Last() (*model.OperationAnnouncement, error) {
	if result, err := o.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnouncement), nil
	}
}

func (o operationAnnouncementDo) Find() ([]*model.OperationAnnouncement, error) {
	result, err := o.DO.Find()
	return result.([]*model.OperationAnnouncement), err
}

func (o operationAnnouncementDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.OperationAnnouncement, err error) {
	buf := make([]*model.OperationAnnouncement, 0, batchSize)
	err = o.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (o operationAnnouncementDo) FindInBatches(result *[]*model.OperationAnnouncement, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return o.DO.FindInBatches(result, batchSize, fc)
}

func (o operationAnnouncementDo) Attrs(attrs ...field.AssignExpr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Attrs(attrs...))
}

func (o operationAnnouncementDo) Assign(attrs ...field.AssignExpr) IOperationAnnouncementDo {
	return o.withDO(o.DO.Assign(attrs...))
}

func (o operationAnnouncementDo) Joins(fields ...field.RelationField) IOperationAnnouncementDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Joins(_f))
	}
	return &o
}

func (o operationAnnouncementDo) Preload(fields ...field.RelationField) IOperationAnnouncementDo {
	for _, _f := range fields {
		o = *o.withDO(o.DO.Preload(_f))
	}
	return &o
}

func (o operationAnnouncementDo) FirstOrInit() (*model.OperationAnnouncement, error) {
	if result, err := o.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnouncement), nil
	}
}

func (o operationAnnouncementDo) FirstOrCreate() (*model.OperationAnnouncement, error) {
	if result, err := o.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.OperationAnnouncement), nil
	}
}

func (o operationAnnouncementDo) FindByPage(offset int, limit int) (result []*model.OperationAnnouncement, count int64, err error) {
	result, err = o.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = o.Offset(-1).Limit(-1).Count()
	return
}

func (o operationAnnouncementDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = o.Count()
	if err != nil {
		return
	}

	err = o.Offset(offset).Limit(limit).Scan(result)
	return
}

func (o operationAnnouncementDo) Scan(result interface{}) (err error) {
	return o.DO.Scan(result)
}

func (o operationAnnouncementDo) Delete(models ...*model.OperationAnnouncement) (result gen.ResultInfo, err error) {
	return o.DO.Delete(models)
}

func (o *operationAnnouncementDo) withDO(do gen.Dao) *operationAnnouncementDo {
	o.DO = *do.(*gen.DO)
	return o
}
