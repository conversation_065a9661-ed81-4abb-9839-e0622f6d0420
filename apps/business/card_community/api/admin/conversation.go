package admin

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetConversationListForAdmin
// @Summary 获取会话列表（管理端）
// @Description 管理员查看会话列表，支持多条件筛选
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/会话管理"
// @Param data query define.GetConversationAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConversationAdminListResp}
// @Router /admin/v1/conversations/list [get]
func GetConversationListForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetConversationAdminListReq{}, s.GetConversationListForAdmin)
}

// GetConversationDetailForAdmin
// @Summary 获取会话详情（管理端）
// @Description 管理员查看会话详情
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/会话管理"
// @Param data query define.GetConversationAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetConversationAdminDetailResp}
// @Router /admin/v1/conversations/detail [get]
func GetConversationDetailForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetConversationAdminDetailReq{}, s.GetConversationDetailForAdmin)
}
