package admin

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetPostListForAdmin
// @Summary 获取求购帖子列表（管理端）
// @Description 管理员查看求购帖子列表，支持多条件筛选
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购管理"
// @Param data query define.GetPostAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetPostAdminListResp}
// @Router /admin/v1/posts/list [get]
func GetPostListForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPostAdminListReq{}, s.GetPostListForAdmin)
}

// GetPostDetailForAdmin
// @Summary 获取求购帖子详情（管理端）
// @Description 管理员查看求购帖子详细信息
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购管理"
// @Param data query define.GetPostAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetPostAdminDetailResp}
// @Router /admin/v1/posts/detail [get]
func GetPostDetailForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetPostAdminDetailReq{}, s.GetPostDetailForAdmin)
}

// UpdatePostStatusForAdmin
// @Summary 更新帖子状态（管理端）
// @Description 管理员更新帖子状态，如违规下架
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购管理"
// @Param data body define.UpdatePostStatusAdminReq true "更新参数"
// @Success 200 {object} response.Data{data=define.UpdatePostStatusAdminResp}
// @Router /admin/v1/posts/edit [post]
func UpdatePostStatusForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdatePostStatusAdminReq{}, s.UpdatePostStatusForAdmin)
}
