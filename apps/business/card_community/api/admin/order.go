package admin

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"github.com/gin-gonic/gin"
)

// GetOrderListForAdmin
// @Summary 获取订单列表（管理端）
// @Description 管理员查看求购订单列表，支持多条件筛选和分页
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购订单"
// @Param data query define.GetOrderAdminListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOrderAdminListResp}
// @Router /admin/v1/card_community/orders/list [get]
func GetOrderListForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderAdminListReq{}, s.GetOrderListForAdmin)
}

// GetOrderDetailForAdmin
// @Summary 获取订单详情（管理端）
// @Description 管理员查看求购订单详情，包含卡片信息、订单信息、收货信息
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购订单"
// @Param data query define.GetOrderAdminDetailReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOrderAdminDetailResp}
// @Router /admin/v1/card_community/orders/detail [get]
func GetOrderDetailForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderAdminDetailReq{}, s.GetOrderDetailForAdmin)
}

// ExportOrdersForAdmin
// @Summary 导出订单数据（管理端）
// @Description 管理员导出求购订单数据到Excel文件，支持筛选条件
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购订单"
// @Param data query define.ExportOrderAdminReq true "导出参数"
// @Success 200 {object} response.Data
// @Router /admin/v1/card_community/orders/export [get]
func ExportOrdersForAdmin(ctx *gin.Context) {
	s := service.New(ctx)
	req := &define.ExportOrderAdminReq{}
	if err := ctx.ShouldBind(req); err != nil {
		g.Fail(ctx, response.ParamErr.SetMsg(err.Error()))
		return
	}
	err := s.ExportOrdersForAdmin(ctx, req)
	if err != nil {
		g.Fail(ctx, err)
		return
	}
}

// GetOrderAddressForAdminOpen
// @Summary 获取订单收货地址（不脱敏）
// @Description 管理员查看求购订单收货地址，返回完整地址信息
// @Tags 管理端-卡牌集社
// @x-apifox-folder "管理端/卡牌集市/求购订单"
// @Param data query define.GetOrderAdminAddressOpenReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetOrderAdminAddressOpenResp}
// @Router /admin/v1/card_community/orders/address [get]
func GetOrderAddressForAdminOpen(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetOrderAdminAddressOpenReq{}, s.GetOrderAddressForAdminOpen)
}
