package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetSmartReplyTemplate
// @Summary 获取智能回复模板
// @Description 获取商家的智能回复模板和开关状态
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/智能回复"
// @Success 200 {object} response.Data{data=define.GetSmartReplyTemplateResp}
// @Router /web/v1/smart_reply_template/detail [get]
func GetSmartReplyTemplate(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetSmartReplyTemplateReq{}, s.GetSmartReplyTemplate)
}

// UpdateSmartReplyTemplate
// @Summary 更新智能回复模板
// @Description 更新商家的智能回复模板内容
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/智能回复"
// @Param data body define.UpdateSmartReplyTemplateReq true "更新模板参数"
// @Success 200 {object} response.Data{data=define.UpdateSmartReplyTemplateResp}
// @Router /web/v1/smart_reply_template/edit [post]
func UpdateSmartReplyTemplate(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.UpdateSmartReplyTemplateReq{}, s.UpdateSmartReplyTemplate)
}

// ToggleSmartReply
// @Summary 切换智能回复开关
// @Description 开启或关闭商家的智能回复功能
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/智能回复"
// @Param data body define.ToggleSmartReplyReq true "切换开关参数"
// @Success 200 {object} response.Data{data=define.ToggleSmartReplyResp}
// @Router /web/v1/smart_reply_template/toggle [post]
func ToggleSmartReply(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.ToggleSmartReplyReq{}, s.ToggleSmartReply)
}
