package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetApplicationStatus
// @Summary 获取商家申请状态
// @Description 获取当前用户的商家申请状态
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/商家申请"
// @Success 200 {object} response.Data{data=define.GetMerchantApplicationStatusResp}
// @Router /web/v1/merchant_application/status [get]
func GetApplicationStatus(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMerchantApplicationStatusReq{}, s.GetApplicationStatus)
}

// CreateApplication
// @Summary 提交商家申请
// @Description 用户提交成为商家的申请
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/商家申请"
// @Param data body define.SubmitMerchantApplicationReq true "申请参数"
// @Success 200 {object} response.Data{data=define.SubmitMerchantApplicationResp}
// @Router /web/v1/merchant_application/add [post]
func CreateApplication(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SubmitMerchantApplicationReq{}, s.CreateApplication)
}
