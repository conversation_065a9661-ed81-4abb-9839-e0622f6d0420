package web

import (
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/service"

	"e.coding.net/g-dtay0385/common/go-util/gin_request_process"
	"github.com/gin-gonic/gin"
)

// GetMessageList
// @Summary 获取消息列表
// @Description 获取指定会话的消息列表
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/消息管理"
// @Param data query define.GetMessageListReq true "查询参数"
// @Success 200 {object} response.Data{data=define.GetMessageListResp}
// @Router /web/v1/conversations/messages/list [get]
func GetMessageList(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.GetMessageListReq{}, s.GetMessageList)
}

// SendMessage
// @Summary 发送消息
// @Description 在指定会话中发送消息（文本、图片或帖子快照）
// @Tags 用户端-卡牌集社
// @x-apifox-folder "用户端/卡牌集市/消息管理"
// @Param data body define.SendMessageReq true "发送消息参数"
// @Success 200 {object} response.Data{data=define.SendMessageResp}
// @Router /web/v1/conversations/messages/add [post]
func SendMessage(ctx *gin.Context) {
	s := service.New(ctx)
	gin_request_process.MainHandle(ctx, &define.SendMessageReq{}, s.SendMessage)
}
