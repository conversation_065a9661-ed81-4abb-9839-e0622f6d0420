package web

import (
	"app_service/apps/business/card_community/api/web"

	"github.com/gin-gonic/gin"
)

// MerchantApplication 注册商家申请相关路由
func MerchantApplication(router *gin.RouterGroup) {
	// 商家申请管理相关路由
	merchantApplicationGroup := router.Group("/merchant_application")
	{
		// 获取申请状态
		merchantApplicationGroup.GET("/status", web.GetApplicationStatus)
		// 提交商家申请
		merchantApplicationGroup.POST("/add", web.CreateApplication)
	}
}
