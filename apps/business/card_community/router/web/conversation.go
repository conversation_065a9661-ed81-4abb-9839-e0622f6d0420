package web

import (
	"app_service/apps/business/card_community/api/web"

	"github.com/gin-gonic/gin"
)

// Conversation 注册会话相关路由
func Conversation(router *gin.RouterGroup) {
	// 会话管理相关路由
	conversationGroup := router.Group("/conversations")
	{
		// 创建会话
		conversationGroup.POST("/add", web.CreateConversation)
		// 获取会话列表
		conversationGroup.GET("/list", web.GetConversationList)
		// 获取会话详情
		conversationGroup.GET("/detail", web.GetConversationDetail)
	}
}
