package web

import (
	"app_service/apps/business/card_community/api/web"

	"github.com/gin-gonic/gin"
)

// Post 注册帖子相关路由
func Post(router *gin.RouterGroup) {
	// 帖子管理相关路由
	postGroup := router.Group("/posts")
	{
		// 创建帖子
		postGroup.POST("/add", web.CreatePost)
		// 获取帖子列表
		postGroup.GET("/list", web.GetPostList)
		// 获取帖子详情
		postGroup.GET("/detail", web.GetPostDetail)
		// 获取我的帖子
		postGroup.GET("/my", web.GetMyPosts)
		// 编辑帖子内容
		postGroup.POST("/edit", web.EditPost)
		// 更新帖子状态
		postGroup.POST("/edit_status", web.UpdatePostStatus)
		// 删除帖子
		postGroup.POST("/delete", web.DeletePost)
	}
}
