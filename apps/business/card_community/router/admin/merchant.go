package admin

import (
	"app_service/apps/business/card_community/api/admin"

	"github.com/gin-gonic/gin"
)

// Merchant 注册管理端商家相关路由
func Merchant(router *gin.RouterGroup) {
	// 商家申请管理相关路由
	merchantGroup := router.Group("/merchant_applications")
	{
		// 获取商家申请列表
		merchantGroup.GET("/list", admin.GetMerchantApplicationListForAdmin)
		// 审核商家申请
		merchantGroup.POST("/review", admin.ReviewMerchantApplication)
	}
}
