package admin

import (
	"app_service/apps/business/card_community/api/admin"

	"github.com/gin-gonic/gin"
)

// Order 注册管理端订单相关路由
func Order(router *gin.RouterGroup) {
	// 订单管理相关路由
	orderGroup := router.Group("card_community/orders")
	{
		// 获取订单列表
		orderGroup.GET("/list", admin.GetOrderListForAdmin)
		// 获取订单详情
		orderGroup.GET("/detail", admin.GetOrderDetailForAdmin)
		// 获取订单收货地址（不脱敏）
		orderGroup.GET("/address", admin.GetOrderAddressForAdminOpen)
		// 导出订单数据
		orderGroup.GET("/export", admin.ExportOrdersForAdmin)
	}
}
