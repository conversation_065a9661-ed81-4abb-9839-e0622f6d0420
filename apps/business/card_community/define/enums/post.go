package enums

// PostStatus 帖子状态
type PostStatus int

const (
	PostStatusViolation PostStatus = -3 // 违规下架
	PostStatusInactive  PostStatus = -2 // 已下架
	PostStatusDeleted   PostStatus = -1 // 已删除
	PostStatusActive    PostStatus = 1  // 已上架
)

// String 返回帖子状态的字符串表示
func (s PostStatus) String() string {
	switch s {
	case PostStatusViolation:
		return "违规下架"
	case PostStatusInactive:
		return "已下架"
	case PostStatusDeleted:
		return "已删除"
	case PostStatusActive:
		return "已上架"
	default:
		return "未知状态"
	}
}

// IsValid 检查帖子状态是否有效
func (s PostStatus) IsValid() bool {
	switch s {
	case PostStatusViolation, PostStatusInactive, PostStatusDeleted, PostStatusActive:
		return true
	default:
		return false
	}
}
