package enums

// ApplicationStatus 申请状态
type ApplicationStatus int

const (
	ApplicationStatusRejected ApplicationStatus = -1 // 审核不通过
	ApplicationStatusPending  ApplicationStatus = 1  // 待审核
	ApplicationStatusApproved ApplicationStatus = 2  // 审核通过
)

// String 返回申请状态的字符串表示
func (s ApplicationStatus) String() string {
	switch s {
	case ApplicationStatusRejected:
		return "审核不通过"
	case ApplicationStatusPending:
		return "待审核"
	case ApplicationStatusApproved:
		return "审核通过"
	default:
		return "未知状态"
	}
}

// IsValid 检查申请状态是否有效
func (s ApplicationStatus) IsValid() bool {
	switch s {
	case ApplicationStatusRejected, ApplicationStatusPending, ApplicationStatusApproved:
		return true
	default:
		return false
	}
}
