package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 管理端商家申请列表相关结构体
type (
	// GetMerchantApplicationAdminListReq 获取管理端商家申请列表请求
	GetMerchantApplicationAdminListReq struct {
		pagination.Pagination
		StartTime time.Time               `form:"start_time" json:"start_time"` // 申请时间开始
		EndTime   time.Time               `form:"end_time" json:"end_time"`     // 申请时间结束
		UserID    string                  `form:"user_id" json:"user_id"`       // 用户ID
		Status    enums.ApplicationStatus `form:"status" json:"status"`         // 申请状态：-1=审核不通过 1=待审核 2=审核通过
	}

	// GetMerchantApplicationAdminListData 管理端商家申请列表数据
	GetMerchantApplicationAdminListData struct {
		ID         string                  `json:"id"`          // 申请ID
		UserID     string                  `json:"user_id"`     // 用户ID
		UserName   string                  `json:"user_name"`   // 用户名称
		Status     enums.ApplicationStatus `json:"status"`      // 申请状态：-1=审核不通过 1=待审核 2=审核通过
		AppliedAt  time.Time               `json:"applied_at"`  // 申请时间
		ReviewedAt *time.Time              `json:"reviewed_at"` // 审核时间
	}

	// GetMerchantApplicationAdminListResp 获取管理端商家申请列表响应
	GetMerchantApplicationAdminListResp struct {
		List  []*GetMerchantApplicationAdminListData `json:"list"`  // 商家申请列表
		Total int64                                  `json:"total"` // 总数
	}
)

// 管理端审核商家申请相关结构体
type (
	// ReviewMerchantApplicationReq 管理端审核商家申请请求
	ReviewMerchantApplicationReq struct {
		ID       string `json:"id" binding:"required"`       // 申请ID
		Approved *bool  `json:"approved" binding:"required"` // 是否通过
	}

	// ReviewMerchantApplicationResp 管理端审核商家申请响应
	ReviewMerchantApplicationResp struct {
		ID string `json:"id"` // 申请ID
	}
)
