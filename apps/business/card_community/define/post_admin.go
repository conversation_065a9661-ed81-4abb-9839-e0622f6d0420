package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 管理端帖子列表相关结构体
type (
	// GetPostAdminListReq 获取管理端帖子列表请求
	GetPostAdminListReq struct {
		pagination.Pagination
		StartTime time.Time        `form:"start_time" json:"start_time"` // 创建时间开始
		EndTime   time.Time        `form:"end_time" json:"end_time"`     // 创建时间结束
		PostID    string           `form:"post_id" json:"post_id"`       // 帖子ID
		Keyword   string           `form:"keyword" json:"keyword"`       // 关键字(搜索描述)
		UserID    string           `form:"user_id" json:"user_id"`       // 用户ID
		Status    enums.PostStatus `form:"status" json:"status"`         // 帖子状态筛选：-3=违规下架 -2=已下架 -1=已删除 1=已上架
	}

	// GetPostAdminListData 管理端帖子列表数据
	GetPostAdminListData struct {
		ID           string           `json:"id"`            // 帖子ID
		MerchantID   string           `json:"merchant_id"`   // 商家ID
		MerchantName string           `json:"merchant_name"` // 商家名称
		Description  string           `json:"description"`   // 帖子描述
		Price        int64            `json:"price"`         // 收购价格
		MediaFiles   []MediaFile      `json:"media_files"`   // 媒体文件
		Status       enums.PostStatus `json:"status"`        // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
		CreatedAt    time.Time        `json:"created_at"`    // 创建时间
	}

	// GetPostAdminListResp 获取管理端帖子列表响应
	GetPostAdminListResp struct {
		List  []*GetPostAdminListData `json:"list"`  // 帖子列表
		Total int64                   `json:"total"` // 总数
	}
)

// 管理端帖子详情相关结构体
type (
	// GetPostAdminDetailReq 获取管理端帖子详情请求
	GetPostAdminDetailReq struct {
		ID string `form:"id" json:"id" binding:"required"` // 帖子ID
	}

	// GetPostAdminDetailResp 获取管理端帖子详情响应
	GetPostAdminDetailResp struct {
		ID           string           `json:"id"`            // 帖子ID
		MerchantID   string           `json:"merchant_id"`   // 商家ID
		MerchantName string           `json:"merchant_name"` // 商家名称
		Description  string           `json:"description"`   // 帖子描述
		Price        int64            `json:"price"`         // 收购价格
		MediaFiles   []MediaFile      `json:"media_files"`   // 媒体文件
		Status       enums.PostStatus `json:"status"`        // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
		CreatedAt    time.Time        `json:"created_at"`    // 创建时间
		UpdatedAt    time.Time        `json:"updated_at"`    // 更新时间
	}
)

// 管理端更新帖子状态相关结构体
type (
	// UpdatePostStatusAdminReq 管理端更新帖子状态请求
	UpdatePostStatusAdminReq struct {
		ID     string           `json:"id" binding:"required"`     // 帖子ID
		Status enums.PostStatus `json:"status" binding:"required"` // 帖子状态：-3=违规下架 -2=已下架 -1=已删除 1=已上架
	}

	// UpdatePostStatusAdminResp 管理端更新帖子状态响应
	UpdatePostStatusAdminResp struct {
		ID string `json:"id"` // 帖子ID
	}
)
