package define

import (
	"app_service/apps/business/card_community/define/enums"
)

// 提交商家申请相关结构体
type (
	// SubmitMerchantApplicationReq 提交商家申请请求
	SubmitMerchantApplicationReq struct {
		// 简化后的商家申请请求，不再需要额外字段
	}

	// SubmitMerchantApplicationResp 提交商家申请响应
	SubmitMerchantApplicationResp struct {
		ID string `json:"id"` // 申请ID
	}
)

// 获取申请状态相关结构体
type (
	// GetMerchantApplicationStatusReq 获取申请状态请求
	GetMerchantApplicationStatusReq struct {
		// 无需参数，从当前登录用户获取用户ID
	}

	// GetMerchantApplicationStatusResp 获取申请状态响应
	GetMerchantApplicationStatusResp struct {
		ID     string                  `json:"id"`      // 申请ID
		UserID string                  `json:"user_id"` // 用户ID
		Status enums.ApplicationStatus `json:"status"`  // 申请状态：-1=审核不通过 1=待审核 2=审核通过
	}
)
