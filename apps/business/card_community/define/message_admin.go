package define

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
	"app_service/pkg/pagination"
)

// 管理端消息列表相关结构体
type (
	// GetMessageAdminListReq 获取管理端消息列表请求
	GetMessageAdminListReq struct {
		pagination.Pagination
		ConversationID string `form:"conversation_id" json:"conversation_id" binding:"required"` // 会话ID
	}

	// GetMessageAdminListData 管理端消息列表数据
	GetMessageAdminListData struct {
		ID            string            `json:"id"`                       // 消息ID
		ClientMsgID   string            `json:"client_msg_id"`            // 客户端生成的消息ID，用于幂等性控制
		BigUserID     string            `json:"big_user_id"`              // 较大的用户ID
		SmallUserID   string            `json:"small_user_id"`            // 较小的用户ID
		Direction     int               `json:"direction"`                // 消息方向：-1=较大uid向较小uid发送消息，1=较小uid向较大uid发送消息
		MessageType   enums.MessageType `json:"message_type"`             // 消息类型：1=文本 2=图片 3=帖子快照 4=订单消息
		Content       string            `json:"content"`                  // 消息内容
		Media         *MediaFile        `json:"media,omitempty"`          // 媒体文件，可能为空
		PostID        string            `json:"post_id,omitempty"`        // 帖子ID（帖子消息类型时有值）
		PostSnapshot  *PostSnapshot     `json:"post_snapshot,omitempty"`  // 帖子快照信息（帖子消息类型时有值）
		OrderID       string            `json:"order_id,omitempty"`       // 订单ID（订单消息类型时有值）
		OrderSnapshot *OrderSnapshot    `json:"order_snapshot,omitempty"` // 订单快照信息（订单消息类型时有值）
		IsSmartReply  bool              `json:"is_smart_reply"`           // 是否为智能回复
		CreatedAt     time.Time         `json:"created_at"`               // 创建时间
	}

	// GetMessageAdminListResp 获取管理端消息列表响应
	GetMessageAdminListResp struct {
		List    []*GetMessageAdminListData `json:"list"`     // 消息列表
		HasMore bool                       `json:"has_more"` // 是否有更多数据
	}
)
