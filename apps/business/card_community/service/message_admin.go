package service

import (
	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	commondefine "app_service/apps/platform/common/define"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetMessageListForAdmin 获取管理端消息列表
func (s *Service) GetMessageListForAdmin(req *define.GetMessageAdminListReq) (*define.GetMessageAdminListResp, error) {
	// 验证会话是否存在
	conversationSchema := repo.GetQuery().Conversation
	conversation, err := repo.NewConversationRepo(conversationSchema.WithContext(s.ctx)).SelectOne(
		search.NewQueryBuilder().
			Eq(conversationSchema.ID, req.ConversationID).
			Eq(conversationSchema.IsDeleted, int32(0)).
			Build(),
	)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500101Err // 会话不存在
		}
		log.Ctx(s.ctx).Errorf("查询会话失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 计算用户对的大小用户ID（用于消息查询）
	bigUserID, smallUserID, _ := model.CalculateUserPair(conversation.ParticipantID, conversation.OtherParticipantID)

	// 查询消息列表
	messageSchema := repo.GetQuery().Message
	queryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.BigUserID, bigUserID).
		Eq(messageSchema.SmallUserID, smallUserID).
		OrderByDesc(messageSchema.ClientMsgNumber). // 按客户端消息序号倒序
		OrderByDesc(messageSchema.CreatedAt)        // 如果客户端消息序号相同，按创建时间倒序

	// 查询消息列表
	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(s.ctx)).SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询管理端消息列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 如果没有消息，直接返回空列表
	if len(messages) == 0 {
		return &define.GetMessageAdminListResp{
			List:    []*define.GetMessageAdminListData{},
			HasMore: false,
		}, nil
	}

	// 转换为响应格式
	list := make([]*define.GetMessageAdminListData, 0, len(messages))

	for _, msg := range messages {
		// 获取媒体文件信息
		mediaFile, err := msg.GetMediaFile()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
			mediaFile = nil
		}

		// 获取帖子快照信息
		postSnapshot, err := msg.GetPostSnapshot()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取帖子快照失败: %v", err)
			postSnapshot = nil
		}

		// 获取订单快照信息
		orderSnapshot, err := msg.GetOrderSnapshot()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取订单快照失败: %v", err)
			orderSnapshot = nil
		}

		list = append(list, &define.GetMessageAdminListData{
			ID:            msg.ID,
			ClientMsgID:   msg.ClientMsgID,
			BigUserID:     msg.BigUserID,
			SmallUserID:   msg.SmallUserID,
			Direction:     int(msg.Direction),
			MessageType:   msg.GetMessageType(),
			Content:       msg.Content,
			Media:         mediaFile,
			PostID:        msg.PostID,
			PostSnapshot:  postSnapshot,
			OrderID:       msg.OrderID,
			OrderSnapshot: orderSnapshot,
			IsSmartReply:  msg.IsSmartReply,
			CreatedAt:     msg.CreatedAt,
		})
	}

	// 计算是否有更多数据（管理端消息列表特例使用HasMore分页格式）
	hasMore := len(list) == req.GetPageSize()

	return &define.GetMessageAdminListResp{
		List:    list,
		HasMore: hasMore,
	}, nil
}
