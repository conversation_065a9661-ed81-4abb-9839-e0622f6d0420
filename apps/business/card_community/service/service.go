package service

import (
	"app_service/pkg/middlewares/g/auth"
	"context"

	"go.opentelemetry.io/otel/trace"
)

// Service 业务服务
// 传递上下文
type Service struct {
	ctx         context.Context
	userService *auth.UserService
}

func New(ctx context.Context) *Service {
	return &Service{
		ctx:         ctx,
		userService: auth.NewUserService(ctx),
	}
}

func (s *Service) NewContextWithSpanContext(parent context.Context) context.Context {
	spanContext := trace.SpanContextFromContext(parent)
	return trace.ContextWithSpanContext(context.Background(), spanContext)
}
