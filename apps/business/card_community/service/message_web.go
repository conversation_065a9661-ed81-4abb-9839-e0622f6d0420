package service

import (
	"strconv"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	"app_service/apps/platform/common/constant"
	commondefine "app_service/apps/platform/common/define"
	"app_service/pkg/search"
	"app_service/pkg/util"
	"app_service/pkg/util/snowflakeutl"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetMessageList 获取消息列表
func (s *Service) GetMessageList(req *define.GetMessageListReq) (*define.GetMessageListResp, error) {
	userID := s.userService.GetUserId()

	// 参数验证
	if req.ConversationID == "" {
		return nil, define.CC500102Err
	}

	// 验证会话是否属于当前用户
	conversation, err := logic.ValidateConversationAccess(s.ctx, req.ConversationID, userID)
	if err != nil {
		return nil, err
	}

	// 计算用户对的大小用户ID（用于消息查询）
	bigUserID, smallUserID, _ := model.CalculateUserPair(userID, conversation.OtherParticipantID)

	// 查询消息列表
	messageSchema := repo.GetQuery().Message
	queryBuilder := search.NewQueryBuilder().
		Eq(messageSchema.BigUserID, bigUserID).
		Eq(messageSchema.SmallUserID, smallUserID).
		OrderByDesc(messageSchema.ClientMsgNumber). // 按客户端消息序号倒序
		OrderByDesc(messageSchema.CreatedAt)        // 如果客户端消息序号相同，按创建时间倒序

	// 查询消息列表
	queryWrapper := queryBuilder.Build()

	// 添加分页条件
	queryWrapper.ScopeOpts = append(queryWrapper.ScopeOpts, search.MakePaginate(req.GetPageSize(), req.GetPage()))

	messages, err := repo.NewMessageRepo(messageSchema.WithContext(s.ctx)).SelectList(queryWrapper)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询消息列表失败: %v", err)
		return nil, commondefine.CommonErr
	}

	// 如果没有消息，直接返回空列表
	if len(messages) == 0 {
		return &define.GetMessageListResp{
			List:    []*define.GetMessageListData{},
			HasMore: false,
		}, nil
	}

	// 转换为响应格式
	list := make([]*define.GetMessageListData, 0, len(messages))

	for _, msg := range messages {
		// 获取媒体文件信息
		mediaFile, err := msg.GetMediaFile()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取媒体文件失败: %v", err)
			mediaFile = nil
		}

		// 获取帖子快照信息
		postSnapshot, err := msg.GetPostSnapshot()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取帖子快照失败: %v", err)
			postSnapshot = nil
		}

		// 获取订单快照信息（订单消息类型使用）
		orderSnapshot, err := msg.GetOrderSnapshot()
		if err != nil {
			log.Ctx(s.ctx).Errorf("获取订单快照失败: %v", err)
			orderSnapshot = nil
		}

		list = append(list, &define.GetMessageListData{
			ID:            msg.ID,
			ClientMsgID:   msg.ClientMsgID,
			MessageType:   msg.GetMessageType(),
			Content:       msg.Content,
			Media:         mediaFile,
			PostID:        msg.PostID,
			PostSnapshot:  postSnapshot,
			OrderID:       msg.OrderID,
			OrderSnapshot: orderSnapshot,
			IsSmartReply:  msg.IsSmartReply,
			IsSent:        msg.IsSentByUser(userID),
			CreatedAt:     msg.CreatedAt,
		})
	}

	// 标记会话为已读
	err = logic.MarkConversationAsRead(s.ctx, req.ConversationID)
	if err != nil {
		// 这里不返回错误，因为消息列表已经成功获取，只是更新已读状态失败
		log.Ctx(s.ctx).Errorf("标记会话已读失败: %v", err)
	}

	// 计算是否有更多数据（用户端分页使用HasMore格式）
	hasMore := len(list) == req.GetPageSize()

	return &define.GetMessageListResp{
		List:    list,
		HasMore: hasMore,
	}, nil
}

// SendMessage 发送消息
func (s *Service) SendMessage(req *define.SendMessageReq) (*define.SendMessageResp, error) {
	userID := s.userService.GetUserId()

	// 生成客户端消息ID（如果未提供）
	if req.ClientMsgID == "" {
		req.ClientMsgID = strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	}
	if req.ConversationID == "" {
		return nil, define.CC500102Err
	}
	if req.ReceiverID == "" {
		return nil, define.CC500205Err
	}
	if !req.MessageType.IsValid() {
		return nil, define.CC500206Err
	}

	// 验证消息内容
	if err := logic.ValidateMessageContent(req); err != nil {
		return nil, err
	}

	// 验证会话是否属于当前用户
	conversation, err := logic.ValidateConversationAccess(s.ctx, req.ConversationID, userID)
	if err != nil {
		return nil, err
	}

	// 验证接收者ID是否与会话中的对方一致
	if req.ReceiverID != conversation.OtherParticipantID {
		return nil, define.CC500205Err.SetMsg("接收者ID与会话不匹配")
	}

	// 检查客户端消息ID是否已存在（幂等性控制，仅使用缓存）
	if existingResp, err := logic.CheckMessageIdempotent(s.ctx, req.ClientMsgID); err != nil {
		log.Ctx(s.ctx).Errorf("检查消息幂等性失败: %v", err)
		return nil, commondefine.CommonErr
	} else if existingResp != nil {
		// 缓存命中，返回已有消息的信息（幂等性）
		log.Ctx(s.ctx).Debugf("消息幂等性缓存命中: clientMsgID=%s", req.ClientMsgID)
		return existingResp, nil
	}

	// 检查消息发送频率限制
	if err := logic.CheckMessageFrequencyLimit(s.ctx, conversation, userID, req.ReceiverID); err != nil {
		return nil, err
	}

	// 计算用户对的大小用户ID和消息方向
	bigUserID, smallUserID, direction := model.CalculateUserPair(userID, req.ReceiverID)

	// 生成消息ID
	messageID := strconv.FormatInt(snowflakeutl.GenerateID(), 10)
	now := time.Now()

	// 创建消息模型
	message := &model.Message{
		ID:           messageID,
		ClientMsgID:  req.ClientMsgID,
		SmallUserID:  smallUserID,
		BigUserID:    bigUserID,
		Direction:    direction,
		MessageType:  int32(req.MessageType),
		Content:      req.Content,
		PostID:       req.PostID,
		IsSmartReply: false, // 用户发送的消息不是智能回复
		AppChannel:   constant.GetAppChannel(s.ctx),
		AppVersion:   constant.GetAppVersion(s.ctx),
		ClientType:   constant.GetClientType(s.ctx),
		IP:           constant.GetClientIP(s.ctx),
	}

	// 设置媒体文件
	if req.Media != nil {
		if err := message.SetMediaFile(req.Media); err != nil {
			log.Ctx(s.ctx).Errorf("设置媒体文件失败: %v", err)
			return nil, commondefine.CommonErr.SetMsg("媒体文件格式错误")
		}
	}

	// 设置帖子快照
	if req.PostSnapshot != nil {
		if err := message.SetPostSnapshot(req.PostSnapshot); err != nil {
			log.Ctx(s.ctx).Errorf("设置帖子快照失败: %v", err)
			return nil, commondefine.CommonErr.SetMsg("帖子快照格式错误")
		}
	}

	// 设置订单信息（订单消息类型使用）
	//if req.OrderSnapshot != nil {
	//	if err := message.SetOrderSnapshot(req.OrderSnapshot); err != nil {
	//		log.Ctx(s.ctx).Errorf("设置订单快照失败: %v", err)
	//		return nil, commondefine.CommonErr.SetMsg("订单快照格式错误")
	//	}
	//}

	// 使用事务保存消息并更新会话
	err = repo.GetDB().WithContext(s.ctx).Transaction(func(tx *gorm.DB) error {
		// 保存消息
		if err := tx.Create(message).Error; err != nil {
			// 检查是否是重复键错误（幂等性处理）
			if util.IsMySQLDuplicateError(err) {
				log.Ctx(s.ctx).Debugf("消息幂等性数据库检查命中: clientMsgID=%s", req.ClientMsgID)
				return define.CC500204Err // 返回消息重复错误
			}
			log.Ctx(s.ctx).Errorf("保存消息失败: %v", err)
			return commondefine.CommonErr
		}

		// 更新会话的最后消息信息
		// 普通消息传入false，会设置接收者状态
		if err := logic.UpdateConversationLastMessage(s.ctx, tx, userID, req.ReceiverID, messageID, req.Content, req.MessageType, false, &req.PostID, nil); err != nil {
			return err // 直接返回 logic 层的错误，已经是标准错误码
		}

		return nil
	})

	if err != nil {
		log.Ctx(s.ctx).Errorf("发送消息失败: %v", err)
		return nil, err // 直接返回错误，保持错误码
	}

	// 构造响应（新消息）
	resp := &define.SendMessageResp{
		ID:        messageID,
		CreatedAt: now,
	}

	// 同步设置幂等性缓存，避免并发问题
	if err := logic.SetMessageIdempotentCache(s.ctx, req.ClientMsgID, resp); err != nil {
		// 缓存设置失败不影响主流程，只记录日志
		log.Ctx(s.ctx).Warnf("设置消息幂等性缓存失败: %v", err)
	}

	// 异步推送消息通知
	spanCtx := s.NewContextWithSpanContext(s.ctx)
	go func() {
		if err := logic.PushMessageNotification(spanCtx, message, req.ReceiverID); err != nil {
			log.Ctx(spanCtx).Errorf("推送消息通知失败: %v", err)
		}
	}()

	return resp, nil
}
