package service

import (
	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/repo"
	"app_service/apps/business/card_community/service/logic"
	commondefine "app_service/apps/platform/common/define"
	"app_service/pkg/search"
	"app_service/pkg/util/snowflakeutl"
	"strconv"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// GetSmartReplyTemplate 获取智能回复模板
func (s *Service) GetSmartReplyTemplate(req *define.GetSmartReplyTemplateReq) (*define.GetSmartReplyTemplateResp, error) {
	// 获取当前登录用户ID（商家ID）
	userID := s.userService.GetUserId()

	// 查询智能回复模板
	template, exists, err := logic.GetSmartReplyTemplateWithErrorHandling(s.ctx, userID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询智能回复模板失败: %v", err)
		return nil, commondefine.CommonErr
	}

	if !exists {
		// 模板不存在，返回空数据
		return &define.GetSmartReplyTemplateResp{
			ID:              "",
			MerchantID:      userID,
			TemplateContent: "",
			IsEnabled:       false,
		}, nil
	}

	return &define.GetSmartReplyTemplateResp{
		ID:              template.ID,
		MerchantID:      template.MerchantID,
		TemplateContent: template.TemplateContent,
		IsEnabled:       template.IsEnabled,
		CreatedAt:       template.CreatedAt,
		UpdatedAt:       template.UpdatedAt,
	}, nil
}

// UpdateSmartReplyTemplate 更新智能回复模板
func (s *Service) UpdateSmartReplyTemplate(req *define.UpdateSmartReplyTemplateReq) (*define.UpdateSmartReplyTemplateResp, error) {
	// 获取当前登录用户ID（商家ID）
	userID := s.userService.GetUserId()

	// 验证模板内容
	if req.TemplateContent == "" {
		return nil, define.CC500302Err
	}

	// 构造模板数据
	template := &model.SmartReplyTemplate{
		MerchantID:      userID,
		TemplateContent: req.TemplateContent,
		IsEnabled:       req.Enabled,
	}

	// 使用GORM的Save方法，基于merchant_id进行upsert
	smartReplySchema := repo.GetQuery().SmartReplyTemplate
	db := repo.GetDB().WithContext(s.ctx)

	// 先尝试查找现有记录
	var existingTemplate model.SmartReplyTemplate
	err := db.Where("merchant_id = ?", userID).First(&existingTemplate).Error

	if err != nil && err != gorm.ErrRecordNotFound {
		log.Ctx(s.ctx).Errorf("查询智能回复模板失败: %v", err)
		return nil, commondefine.CommonErr
	}

	if err == gorm.ErrRecordNotFound {
		// 记录不存在，创建新记录
		template.ID = strconv.FormatInt(snowflakeutl.GenerateID(), 10)

		err = repo.NewSmartReplyTemplateRepo(smartReplySchema.WithContext(s.ctx)).Save(template)
		if err != nil {
			log.Ctx(s.ctx).Errorf("创建智能回复模板失败: %v", err)
			return nil, commondefine.CommonErr
		}
	} else {
		// 记录存在，更新现有记录 - 使用 map 确保零值字段也能更新
		updateData := map[string]interface{}{
			"template_content": req.TemplateContent,
			"is_enabled":       req.Enabled, // 即使为 false 也会被更新
		}

		queryWrapper := search.NewQueryBuilder().
			Eq(smartReplySchema.ID, existingTemplate.ID).
			Build()

		err = repo.NewSmartReplyTemplateRepo(smartReplySchema.WithContext(s.ctx)).UpdateField(updateData, queryWrapper)
		if err != nil {
			log.Ctx(s.ctx).Errorf("更新智能回复模板失败: %v", err)
			return nil, commondefine.CommonErr
		}

		// 更新返回数据中的 ID 和时间
		template.ID = existingTemplate.ID
	}

	return &define.UpdateSmartReplyTemplateResp{
		ID:        template.ID,
		UpdatedAt: template.UpdatedAt,
	}, nil
}

// ToggleSmartReply 切换智能回复开关
func (s *Service) ToggleSmartReply(req *define.ToggleSmartReplyReq) (*define.ToggleSmartReplyResp, error) {
	// 获取当前登录用户ID（商家ID）
	userID := s.userService.GetUserId()

	// 查询现有模板
	template, exists, err := logic.GetSmartReplyTemplateWithErrorHandling(s.ctx, userID)
	if err != nil {
		log.Ctx(s.ctx).Errorf("查询智能回复模板失败: %v", err)
		return nil, commondefine.CommonErr
	}

	if !exists {
		// 模板不存在，返回空数据
		return &define.ToggleSmartReplyResp{
			Enabled: req.Enabled,
		}, nil
	}

	// 更新开关状态
	log.Ctx(s.ctx).Infof("准备更新智能回复开关: ID=%s, 原状态=%v, 新状态=%v", template.ID, template.IsEnabled, req.Enabled)

	db := repo.GetDB().WithContext(s.ctx)

	// 只更新 IsEnabled 字段
	result := db.Model(&model.SmartReplyTemplate{}).Where("id = ?", template.ID).Update("is_enabled", req.Enabled)
	if result.Error != nil {
		log.Ctx(s.ctx).Errorf("更新智能回复开关失败: %v", result.Error)
		return nil, commondefine.CommonErr
	}

	log.Ctx(s.ctx).Infof("更新智能回复开关成功: 影响行数=%d", result.RowsAffected)

	if result.RowsAffected == 0 {
		log.Ctx(s.ctx).Errorf("更新智能回复开关失败: 没有找到对应记录, ID=%s", template.ID)
		return nil, commondefine.CommonErr
	}

	return &define.ToggleSmartReplyResp{
		Enabled: req.Enabled,
	}, nil
}
