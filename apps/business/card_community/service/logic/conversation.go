package logic

import (
	"context"
	"errors"
	"time"

	"app_service/apps/business/card_community/dal/model"
	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"
	"app_service/apps/business/card_community/repo"
	commondefine "app_service/apps/platform/common/define"
	"app_service/pkg/search"

	log "e.coding.net/g-dtay0385/common/go-logger"
	"gorm.io/gorm"
)

// ValidateConversationAccess 验证会话是否属于当前用户
// 返回会话信息和错误
func ValidateConversationAccess(ctx context.Context, conversationID, userID string) (*model.Conversation, error) {
	conversationSchema := repo.GetQuery().Conversation
	conversation, err := repo.NewConversationRepo(conversationSchema.WithContext(ctx)).SelectOne(
		search.NewQueryBuilder().
			Eq(conversationSchema.ID, conversationID).
			Eq(conversationSchema.ParticipantID, userID).
			Eq(conversationSchema.IsDeleted, int32(0)).
			Build(),
	)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, define.CC500101Err
		}
		log.Ctx(ctx).Errorf("查询会话失败: %v", err)
		return nil, commondefine.CommonErr
	}
	return conversation, nil
}

// MarkConversationAsRead 标记会话为已读
// 更新指定会话的 LastReadTime 为当前时间，UnreadCount 重置为 0
// 注意：调用前需要先通过 ValidateConversationAccess 验证权限
func MarkConversationAsRead(ctx context.Context, conversationID string) error {
	// 获取会话表schema
	conversationSchema := repo.GetQuery().Conversation

	now := time.Now()
	updateData := map[string]interface{}{
		"last_read_time": &now,
		"unread_count":   0,
		"updated_at":     now,
	}

	err := repo.NewConversationRepo(conversationSchema.WithContext(ctx)).UpdateField(
		updateData,
		search.NewQueryBuilder().
			Eq(conversationSchema.ID, conversationID).
			Build(),
	)
	if err != nil {
		log.Ctx(ctx).Errorf("标记会话已读失败: conversationID=%s, error=%v", conversationID, err)
		return err
	}

	log.Ctx(ctx).Infof("会话已标记为已读: conversationID=%s", conversationID)
	return nil
}

// FindExistingConversation 查找两个用户之间的现有会话，如果没有找到返回nil
func FindExistingConversation(ctx context.Context, sellerID, buyerID string) *model.Conversation {
	query := repo.GetQuery()
	conversationSchema := query.Conversation
	conversationRepo := repo.NewConversationRepo(conversationSchema.WithContext(ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(conversationSchema.ParticipantID, sellerID).
		Eq(conversationSchema.OtherParticipantID, buyerID).
		Eq(conversationSchema.IsDeleted, int32(0)).
		Build()

	conversation, err := conversationRepo.SelectOne(queryWrapper)
	if err == nil && conversation != nil {
		return conversation
	}

	// 如果没找到，记录日志并返回nil
	if errors.Is(err, gorm.ErrRecordNotFound) {
		log.Ctx(ctx).Infof("未找到用户 %s 和 %s 之间的会话", sellerID, buyerID)
	} else if err != nil {
		log.Ctx(ctx).Errorf("查询会话失败: %v", err)
	}

	return nil
}

// CheckSellerUnpaidOrderLimit 校验卖家待支付订单数量限制（不能超过5单）
func CheckSellerUnpaidOrderLimit(ctx context.Context, sellerID string) error {
	query := repo.GetQuery()
	cardOrderSchema := query.CardOrder
	cardOrderRepo := repo.NewCardOrderRepo(cardOrderSchema.WithContext(ctx))

	queryWrapper := search.NewQueryBuilder().
		Eq(cardOrderSchema.SellerID, sellerID).
		Eq(cardOrderSchema.Status, enums.OrderStatusUnPaid.Int32()).
		Eq(cardOrderSchema.SellerDeleted, false).
		Build()

	count, err := cardOrderRepo.Count(queryWrapper)
	if err != nil {
		log.Ctx(ctx).Errorf("查询卖家待支付订单数量失败: %v", err)
		return commondefine.CommonErr.SetMsg("查询订单失败")
	}

	// 如果待支付订单数量 >= 5，则不能创建新订单
	if count >= 5 {
		log.Ctx(ctx).Infof("卖家 %s 待支付订单数量已达上限: %d", sellerID, count)
		return define.CC500406Err.SetMsg("你有5笔订单买家还未支付，不能再创建了哦")
	}

	log.Ctx(ctx).Infof("卖家 %s 当前待支付订单数量: %d", sellerID, count)
	return nil
}
