// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNameCardOrder = "card_orders"

// CardOrder 卡牌交易订单表
type CardOrder struct {
	ID                  string          `gorm:"column:id;type:varchar(64);primaryKey;comment:订单ID（同时作为订单号显示给用户）" json:"id"`                                                                // 订单ID（同时作为订单号显示给用户）
	ConversationGroupID string          `gorm:"column:conversation_group_id;type:varchar(64);not null;comment:会话组ID（关联conversations表的conversation_group_id）" json:"conversation_group_id"` // 会话组ID（关联conversations表的conversation_group_id）
	BuyerID             string          `gorm:"column:buyer_id;type:varchar(64);not null;comment:买家ID" json:"buyer_id"`                                                                    // 买家ID
	SellerID            string          `gorm:"column:seller_id;type:varchar(64);not null;comment:卖家ID" json:"seller_id"`                                                                  // 卖家ID
	CardItems           datatypes.JSON  `gorm:"column:card_items;type:json;not null;comment:卡片信息，类型:[]CardItem" json:"card_items"`                                                         // 卡片信息，类型:[]CardItem
	FirstCardImageURL   string          `gorm:"column:first_card_image_url;type:varchar(500);not null;comment:第一张卡牌图片URL（用于订单展示）" json:"first_card_image_url"`                             // 第一张卡牌图片URL（用于订单展示）
	CardGroupsCount     int32           `gorm:"column:card_groups_count;type:int;not null;comment:卡片组数" json:"card_groups_count"`                                                          // 卡片组数
	TotalQuantity       int32           `gorm:"column:total_quantity;type:int;not null;comment:卡片总数量" json:"total_quantity"`                                                               // 卡片总数量
	TotalAmount         int64           `gorm:"column:total_amount;type:bigint;not null;comment:订单总金额(分)" json:"total_amount"`                                                             // 订单总金额(分)
	PayAmount           int64           `gorm:"column:pay_amount;type:bigint;not null;comment:支付金额(分)" json:"pay_amount"`                                                                  // 支付金额(分)
	PaymentMethod       int32           `gorm:"column:payment_method;type:tinyint;comment:支付方式（如：卡牌钱包等）" json:"payment_method"`                                                            // 支付方式（如：卡牌钱包等）
	TransactionNo       string          `gorm:"column:transaction_no;type:varchar(64);comment:支付流水单号" json:"transaction_no"`                                                               // 支付流水单号
	Status              int32           `gorm:"column:status;type:tinyint;not null;comment:订单状态：0=待支付 10=待发货 20=已发货 30=已完成 40=已取消" json:"status"`                                          // 订单状态：0=待支付 10=待发货 20=已发货 30=已完成 40=已取消
	CancelType          int32           `gorm:"column:cancel_type;type:tinyint;comment:取消类型：1=买家取消 2=卖家取消 3=超时取消" json:"cancel_type"`                                                      // 取消类型：1=买家取消 2=卖家取消 3=超时取消
	ShippingAddress     *datatypes.JSON `gorm:"column:shipping_address;type:json;comment:收货地址信息，类型:Address" json:"shipping_address"`                                                       // 收货地址信息，类型:Address
	PaymentAt           *time.Time      `gorm:"column:payment_at;type:datetime(3);comment:支付时间" json:"payment_at"`                                                                         // 支付时间
	DeliveredAt         *time.Time      `gorm:"column:delivered_at;type:datetime(3);comment:发货时间" json:"delivered_at"`                                                                     // 发货时间
	ReceivedAt          *time.Time      `gorm:"column:received_at;type:datetime(3);comment:收货时间" json:"received_at"`                                                                       // 收货时间
	ExpiredAt           time.Time       `gorm:"column:expired_at;type:datetime(3);not null;comment:订单过期时间" json:"expired_at"`                                                              // 订单过期时间
	FinishedAt          *time.Time      `gorm:"column:finished_at;type:datetime(3);comment:订单完成时间" json:"finished_at"`                                                                     // 订单完成时间
	CancelAt            *time.Time      `gorm:"column:cancel_at;type:datetime(3);comment:订单取消时间" json:"cancel_at"`                                                                         // 订单取消时间
	BuyerDeleted        bool            `gorm:"column:buyer_deleted;type:tinyint(1);not null;comment:买家是否删除：0=否 1=是" json:"buyer_deleted"`                                                 // 买家是否删除：0=否 1=是
	SellerDeleted       bool            `gorm:"column:seller_deleted;type:tinyint(1);not null;comment:卖家是否删除：0=否 1=是" json:"seller_deleted"`                                               // 卖家是否删除：0=否 1=是
	CreatedAt           time.Time       `gorm:"column:created_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:创建时间" json:"created_at"`                                   // 创建时间
	UpdatedAt           time.Time       `gorm:"column:updated_at;type:datetime(3);not null;default:CURRENT_TIMESTAMP(3);comment:更新时间" json:"updated_at"`                                   // 更新时间
}

// TableName CardOrder's table name
func (*CardOrder) TableName() string {
	return TableNameCardOrder
}
