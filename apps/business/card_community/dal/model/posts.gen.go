// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	"gorm.io/datatypes"
)

const TableNamePost = "posts"

// Post 求购帖子表
type Post struct {
	ID          string          `gorm:"column:id;type:varchar(64);primaryKey;comment:帖子ID" json:"id"`                                                                                                           // 帖子ID
	MerchantID  string          `gorm:"column:merchant_id;type:varchar(64);not null;comment:商家ID" json:"merchant_id"`                                                                                           // 商家ID
	Description string          `gorm:"column:description;type:text;comment:帖子描述" json:"description"`                                                                                                           // 帖子描述
	MediaFiles  *datatypes.JSON `gorm:"column:media_files;type:json;comment:媒体文件数组，格式:[{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}]" json:"media_files"` // 媒体文件数组，格式:[{"url":"URL","type":1,"size":1024,"width":800,"height":600,"duration":30,"thumbnail_url":"缩略图URL"}]
	Status      int32           `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1=已上架 -1=已删除 -2=已下架 -3=违规下架" json:"status"`                                                                     // 状态：1=已上架 -1=已删除 -2=已下架 -3=违规下架
	CreatedAt   time.Time       `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                                                                      // 创建时间
	UpdatedAt   time.Time       `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                                                                      // 更新时间
	Price       int64           `gorm:"column:price;type:bigint;not null;comment:收购价格(分)" json:"price"`                                                                                                         // 收购价格(分)
}

// TableName Post's table name
func (*Post) TableName() string {
	return TableNamePost
}
