package model

import (
	"encoding/json"
	"time"

	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"

	"gorm.io/datatypes"
)

// GetCardItems 获取卡片信息列表
func (o *CardOrder) GetCardItems() ([]define.CardItem, error) {
	if o.CardItems == nil {
		return nil, nil
	}

	var cardItems []define.CardItem
	err := json.Unmarshal([]byte(o.CardItems), &cardItems)
	if err != nil {
		return nil, err
	}
	return cardItems, nil
}

// SetCardItems 设置卡片信息列表
func (o *CardOrder) SetCardItems(cardItems []define.CardItem) error {
	if cardItems == nil {
		o.CardItems = nil
		return nil
	}

	data, err := json.Marshal(cardItems)
	if err != nil {
		return err
	}

	o.CardItems = datatypes.JSON(data)
	return nil
}

// GetShippingAddress 获取收货地址信息
func (o *CardOrder) GetShippingAddress() (*define.Address, error) {
	if o.ShippingAddress == nil {
		return nil, nil
	}

	var address define.Address
	err := json.Unmarshal([]byte(*o.ShippingAddress), &address)
	if err != nil {
		return nil, err
	}
	return &address, nil
}

// SetShippingAddress 设置收货地址信息
func (o *CardOrder) SetShippingAddress(address *define.Address) error {
	if address == nil {
		o.ShippingAddress = nil
		return nil
	}

	data, err := json.Marshal(address)
	if err != nil {
		return err
	}

	jsonData := datatypes.JSON(data)
	o.ShippingAddress = &jsonData
	return nil
}

// GetStatus 获取订单状态枚举
func (o *CardOrder) GetStatus() enums.OrderStatus {
	return enums.OrderStatus(o.Status)
}

// SetStatus 设置订单状态
func (o *CardOrder) SetStatus(status enums.OrderStatus) {
	o.Status = status.Int32()
}

// GetCancelType 获取取消类型枚举
func (o *CardOrder) GetCancelType() enums.CancelType {
	return enums.CancelType(o.CancelType)
}

// SetCancelType 设置取消类型
func (o *CardOrder) SetCancelType(cancelType enums.CancelType) {
	o.CancelType = cancelType.Int32()
}

// IsUnPaid 判断订单是否待支付
func (o *CardOrder) IsUnPaid() bool {
	return o.GetStatus() == enums.OrderStatusUnPaid
}

// IsUnDelivered 判断订单是否待发货
func (o *CardOrder) IsUnDelivered() bool {
	return o.GetStatus() == enums.OrderStatusUnDelivered
}

// IsUnReceive 判断订单是否待收货
func (o *CardOrder) IsUnReceive() bool {
	return o.GetStatus() == enums.OrderStatusUnReceive
}

// IsCompleted 判断订单是否已完成
func (o *CardOrder) IsCompleted() bool {
	return o.GetStatus() == enums.OrderStatusCompleted
}

// IsCanceled 判断订单是否已取消
func (o *CardOrder) IsCanceled() bool {
	return o.GetStatus() == enums.OrderStatusCanceled
}

// CanCancel 判断订单是否可以取消
func (o *CardOrder) CanCancel() bool {
	return o.GetStatus().CanCancel()
}

// CanDelete 判断订单是否可以删除
func (o *CardOrder) CanDelete() bool {
	return o.GetStatus().CanDelete()
}

// CanPay 判断订单是否可以支付
func (o *CardOrder) CanPay() bool {
	return o.IsUnPaid() && !o.IsExpired()
}

// CanShip 判断订单是否可以发货
func (o *CardOrder) CanShip() bool {
	return o.IsUnDelivered()
}

// CanConfirmReceived 判断订单是否可以确认收货
func (o *CardOrder) CanConfirmReceived() bool {
	return o.IsUnReceive()
}

// IsExpired 判断订单是否已过期
func (o *CardOrder) IsExpired() bool {
	return time.Now().After(o.ExpiredAt)
}

// IsPaid 判断订单是否已支付
func (o *CardOrder) IsPaid() bool {
	return o.PaymentAt != nil && !o.IsUnPaid()
}

// IsDelivered 判断订单是否已发货
func (o *CardOrder) IsDelivered() bool {
	return o.DeliveredAt != nil && (o.IsUnReceive() || o.IsCompleted())
}

// IsReceived 判断订单是否已收货
func (o *CardOrder) IsReceived() bool {
	return o.ReceivedAt != nil && o.IsCompleted()
}

// IsDeletedByBuyer 判断订单是否被买家删除
func (o *CardOrder) IsDeletedByBuyer() bool {
	return o.BuyerDeleted
}

// IsDeletedBySeller 判断订单是否被卖家删除
func (o *CardOrder) IsDeletedBySeller() bool {
	return o.SellerDeleted
}

// IsDeletedByUser 判断订单是否被指定用户删除
func (o *CardOrder) IsDeletedByUser(userID string) bool {
	if userID == o.BuyerID {
		return o.IsDeletedByBuyer()
	}
	if userID == o.SellerID {
		return o.IsDeletedBySeller()
	}
	return false
}

// GetUserType 获取指定用户在订单中的角色类型
func (o *CardOrder) GetUserType(userID string) enums.UserType {
	if userID == o.BuyerID {
		return enums.UserTypeBuyer
	}
	if userID == o.SellerID {
		return enums.UserTypeSeller
	}
	return 0 // 无效类型
}

// IsBuyer 判断指定用户是否为买家
func (o *CardOrder) IsBuyer(userID string) bool {
	return userID == o.BuyerID
}

// IsSeller 判断指定用户是否为卖家
func (o *CardOrder) IsSeller(userID string) bool {
	return userID == o.SellerID
}

// GetCounterpartID 获取对方用户ID
func (o *CardOrder) GetCounterpartID(userID string) string {
	if userID == o.BuyerID {
		return o.SellerID
	}
	if userID == o.SellerID {
		return o.BuyerID
	}
	return ""
}

// SetPaymentInfo 设置支付信息
func (o *CardOrder) SetPaymentInfo(paymentMethod enums.PaymentMethod, transactionNo string, payAmount int64) {
	o.PaymentMethod = paymentMethod.Int32()
	o.TransactionNo = transactionNo
	o.PayAmount = payAmount
	now := time.Now()
	o.PaymentAt = &now
}

// GetPaymentMethod 获取支付方式枚举
func (o *CardOrder) GetPaymentMethod() enums.PaymentMethod {
	return enums.PaymentMethod(o.PaymentMethod)
}

// SetDeliveryInfo 设置发货信息
func (o *CardOrder) SetDeliveryInfo() {
	now := time.Now()
	o.DeliveredAt = &now
}

// SetReceiveInfo 确认收货并完成订单（买家确认收货时调用，同时设置收货时间和订单完成时间）
func (o *CardOrder) SetReceiveInfo() {
	now := time.Now()
	o.ReceivedAt = &now // 设置收货时间
	o.FinishedAt = &now // 设置订单完成时间
}

// SetCancelInfo 设置取消信息
func (o *CardOrder) SetCancelInfo(cancelType enums.CancelType) {
	o.SetCancelType(cancelType)
	now := time.Now()
	o.CancelAt = &now
	o.FinishedAt = &now
}

// MarkDeletedByUser 标记订单被指定用户删除
func (o *CardOrder) MarkDeletedByUser(userID string) {
	if userID == o.BuyerID {
		o.BuyerDeleted = true
	} else if userID == o.SellerID {
		o.SellerDeleted = true
	}
}

// GetStatusText 获取订单状态文本
func (o *CardOrder) GetStatusText() string {
	return o.GetStatus().String()
}

// GetCancelTypeText 获取取消类型文本
func (o *CardOrder) GetCancelTypeText() string {
	if o.CancelType == 0 {
		return ""
	}
	return o.GetCancelType().String()
}

// GetRemainingTime 获取订单剩余时间（用于倒计时显示）
func (o *CardOrder) GetRemainingTime() time.Duration {
	if o.IsExpired() {
		return 0
	}
	return time.Until(o.ExpiredAt)
}

// GetRemainingHours 获取订单剩余小时数（用于倒计时显示）
func (o *CardOrder) GetRemainingHours() int {
	remaining := o.GetRemainingTime()
	if remaining <= 0 {
		return 0
	}
	return int(remaining.Hours())
}

// GetRemainingMinutes 获取订单剩余分钟数（用于倒计时显示）
func (o *CardOrder) GetRemainingMinutes() int {
	remaining := o.GetRemainingTime()
	if remaining <= 0 {
		return 0
	}
	return int(remaining.Minutes()) % 60
}
