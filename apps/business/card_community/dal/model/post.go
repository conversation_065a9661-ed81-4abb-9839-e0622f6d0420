package model

import (
	"encoding/json"

	"app_service/apps/business/card_community/define"
	"app_service/apps/business/card_community/define/enums"

	"gorm.io/datatypes"
)

// GetMediaFiles 获取媒体文件列表
func (p *Post) GetMediaFiles() ([]define.MediaFile, error) {
	if p.MediaFiles == nil {
		return []define.MediaFile{}, nil
	}

	var mediaFiles []define.MediaFile
	err := json.Unmarshal([]byte(*p.MediaFiles), &mediaFiles)
	if err != nil {
		return nil, err
	}
	return mediaFiles, nil
}

// SetMediaFiles 设置媒体文件列表
func (p *Post) SetMediaFiles(mediaFiles []define.MediaFile) error {
	if len(mediaFiles) == 0 {
		p.MediaFiles = nil
		return nil
	}

	data, err := json.Marshal(mediaFiles)
	if err != nil {
		return err
	}

	jsonData := datatypes.JSON(data)
	p.MediaFiles = &jsonData
	return nil
}

// GetStatus 获取帖子状态
func (p *Post) GetStatus() enums.PostStatus {
	return enums.PostStatus(p.Status)
}

// SetStatus 设置帖子状态
func (p *Post) SetStatus(status enums.PostStatus) {
	p.Status = int32(status)
}
