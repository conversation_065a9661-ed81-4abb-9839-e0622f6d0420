package model

import (
	"time"

	"app_service/apps/business/card_community/define/enums"
)

// GetStatus 获取申请状态
func (m *MerchantApplication) GetStatus() enums.ApplicationStatus {
	return enums.ApplicationStatus(m.Status)
}

// SetStatus 设置申请状态
func (m *MerchantApplication) SetStatus(status enums.ApplicationStatus) {
	m.Status = int32(status)
}

// Approve 批准申请
func (m *MerchantApplication) Approve(reviewerID string) {
	m.SetStatus(enums.ApplicationStatusApproved)
	m.ReviewerID = reviewerID
	now := time.Now()
	m.ReviewedAt = &now
}

// Reject 拒绝申请
func (m *MerchantApplication) Reject(reviewerID string) {
	m.SetStatus(enums.ApplicationStatusRejected)
	m.ReviewerID = reviewerID
	now := time.Now()
	m.ReviewedAt = &now
}

// IsPending 是否待审核
func (m *MerchantApplication) IsPending() bool {
	return m.GetStatus() == enums.ApplicationStatusPending
}

// IsApproved 是否已批准
func (m *MerchantApplication) IsApproved() bool {
	return m.GetStatus() == enums.ApplicationStatusApproved
}

// IsRejected 是否已拒绝
func (m *MerchantApplication) IsRejected() bool {
	return m.GetStatus() == enums.ApplicationStatusRejected
}
