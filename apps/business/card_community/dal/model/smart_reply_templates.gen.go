// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameSmartReplyTemplate = "smart_reply_templates"

// SmartReplyTemplate 智能回复模板表
type SmartReplyTemplate struct {
	ID              string    `gorm:"column:id;type:varchar(64);primaryKey;comment:模板ID" json:"id"`                                      // 模板ID
	MerchantID      string    `gorm:"column:merchant_id;type:varchar(64);not null;comment:商家ID" json:"merchant_id"`                      // 商家ID
	TemplateContent string    `gorm:"column:template_content;type:text;not null;comment:模板内容" json:"template_content"`                   // 模板内容
	IsEnabled       bool      `gorm:"column:is_enabled;type:tinyint(1);not null;default:1;comment:是否启用" json:"is_enabled"`               // 是否启用
	CreatedAt       time.Time `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"` // 创建时间
	UpdatedAt       time.Time `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"` // 更新时间
}

// TableName SmartReplyTemplate's table name
func (*SmartReplyTemplate) TableName() string {
	return TableNameSmartReplyTemplate
}
