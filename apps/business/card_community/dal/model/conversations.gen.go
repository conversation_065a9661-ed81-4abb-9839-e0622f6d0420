// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"
)

const TableNameConversation = "conversations"

// Conversation 会话表（双向记录）
type Conversation struct {
	ID                  string     `gorm:"column:id;type:varchar(64);primaryKey;comment:会话记录ID" json:"id"`                                                       // 会话记录ID
	ConversationGroupID string     `gorm:"column:conversation_group_id;type:varchar(64);not null;comment:会话组ID（同一对话的买家和卖家记录共享此ID）" json:"conversation_group_id"` // 会话组ID（同一对话的买家和卖家记录共享此ID）
	ParticipantID       string     `gorm:"column:participant_id;type:varchar(64);not null;comment:参与者ID（用户ID或商家ID）" json:"participant_id"`                       // 参与者ID（用户ID或商家ID）
	ParticipantType     int32      `gorm:"column:participant_type;type:tinyint;not null;comment:参与者类型：1=用户 2=商家" json:"participant_type"`                        // 参与者类型：1=用户 2=商家
	OtherParticipantID  string     `gorm:"column:other_participant_id;type:varchar(64);not null;comment:对方ID" json:"other_participant_id"`                       // 对方ID
	PostID              string     `gorm:"column:post_id;type:varchar(64);comment:关联的帖子ID" json:"post_id"`                                                       // 关联的帖子ID
	LastMessageID       string     `gorm:"column:last_message_id;type:varchar(64);comment:最后消息ID" json:"last_message_id"`                                        // 最后消息ID
	LastMessageContent  string     `gorm:"column:last_message_content;type:varchar(500);comment:最后消息内容" json:"last_message_content"`                             // 最后消息内容
	LastMessageTime     *time.Time `gorm:"column:last_message_time;type:datetime;comment:最后消息时间" json:"last_message_time"`                                       // 最后消息时间
	LastMessageType     int32      `gorm:"column:last_message_type;type:tinyint;comment:最后消息类型：1=文本 2=图片 3=帖子快照" json:"last_message_type"`                       // 最后消息类型：1=文本 2=图片 3=帖子快照
	LastReadTime        *time.Time `gorm:"column:last_read_time;type:datetime;comment:最后阅读时间" json:"last_read_time"`                                             // 最后阅读时间
	UnreadCount         int32      `gorm:"column:unread_count;type:int;not null;comment:未读消息数" json:"unread_count"`                                              // 未读消息数
	IsDeleted           int32      `gorm:"column:is_deleted;type:tinyint;not null;comment:是否删除：0=正常 1=已删除" json:"is_deleted"`                                    // 是否删除：0=正常 1=已删除
	Status              int32      `gorm:"column:status;type:tinyint;not null;default:1;comment:状态：1=正常 -1=限制中" json:"status"`                                   // 状态：1=正常 -1=限制中
	CreatedAt           time.Time  `gorm:"column:created_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"created_at"`                    // 创建时间
	UpdatedAt           time.Time  `gorm:"column:updated_at;type:datetime;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"updated_at"`                    // 更新时间
}

// TableName Conversation's table name
func (*Conversation) TableName() string {
	return TableNameConversation
}
