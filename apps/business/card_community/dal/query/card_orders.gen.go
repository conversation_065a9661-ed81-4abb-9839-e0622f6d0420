// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"app_service/apps/business/card_community/dal/model"
)

func newCardOrder(db *gorm.DB, opts ...gen.DOOption) cardOrder {
	_cardOrder := cardOrder{}

	_cardOrder.cardOrderDo.UseDB(db, opts...)
	_cardOrder.cardOrderDo.UseModel(&model.CardOrder{})

	tableName := _cardOrder.cardOrderDo.TableName()
	_cardOrder.ALL = field.NewAsterisk(tableName)
	_cardOrder.ID = field.NewString(tableName, "id")
	_cardOrder.ConversationGroupID = field.NewString(tableName, "conversation_group_id")
	_cardOrder.BuyerID = field.NewString(tableName, "buyer_id")
	_cardOrder.SellerID = field.NewString(tableName, "seller_id")
	_cardOrder.CardItems = field.NewField(tableName, "card_items")
	_cardOrder.FirstCardImageURL = field.NewString(tableName, "first_card_image_url")
	_cardOrder.CardGroupsCount = field.NewInt32(tableName, "card_groups_count")
	_cardOrder.TotalQuantity = field.NewInt32(tableName, "total_quantity")
	_cardOrder.TotalAmount = field.NewInt64(tableName, "total_amount")
	_cardOrder.PayAmount = field.NewInt64(tableName, "pay_amount")
	_cardOrder.PaymentMethod = field.NewInt32(tableName, "payment_method")
	_cardOrder.TransactionNo = field.NewString(tableName, "transaction_no")
	_cardOrder.Status = field.NewInt32(tableName, "status")
	_cardOrder.CancelType = field.NewInt32(tableName, "cancel_type")
	_cardOrder.ShippingAddress = field.NewField(tableName, "shipping_address")
	_cardOrder.PaymentAt = field.NewTime(tableName, "payment_at")
	_cardOrder.DeliveredAt = field.NewTime(tableName, "delivered_at")
	_cardOrder.ReceivedAt = field.NewTime(tableName, "received_at")
	_cardOrder.ExpiredAt = field.NewTime(tableName, "expired_at")
	_cardOrder.FinishedAt = field.NewTime(tableName, "finished_at")
	_cardOrder.CancelAt = field.NewTime(tableName, "cancel_at")
	_cardOrder.BuyerDeleted = field.NewBool(tableName, "buyer_deleted")
	_cardOrder.SellerDeleted = field.NewBool(tableName, "seller_deleted")
	_cardOrder.CreatedAt = field.NewTime(tableName, "created_at")
	_cardOrder.UpdatedAt = field.NewTime(tableName, "updated_at")

	_cardOrder.fillFieldMap()

	return _cardOrder
}

// cardOrder 卡牌交易订单表
type cardOrder struct {
	cardOrderDo

	ALL                 field.Asterisk
	ID                  field.String // 订单ID（同时作为订单号显示给用户）
	ConversationGroupID field.String // 会话组ID（关联conversations表的conversation_group_id）
	BuyerID             field.String // 买家ID
	SellerID            field.String // 卖家ID
	CardItems           field.Field  // 卡片信息，类型:[]CardItem
	FirstCardImageURL   field.String // 第一张卡牌图片URL（用于订单展示）
	CardGroupsCount     field.Int32  // 卡片组数
	TotalQuantity       field.Int32  // 卡片总数量
	TotalAmount         field.Int64  // 订单总金额(分)
	PayAmount           field.Int64  // 支付金额(分)
	PaymentMethod       field.Int32  // 支付方式（如：卡牌钱包等）
	TransactionNo       field.String // 支付流水单号
	Status              field.Int32  // 订单状态：0=待支付 10=待发货 20=已发货 30=已完成 40=已取消
	CancelType          field.Int32  // 取消类型：1=买家取消 2=卖家取消 3=超时取消
	ShippingAddress     field.Field  // 收货地址信息，类型:Address
	PaymentAt           field.Time   // 支付时间
	DeliveredAt         field.Time   // 发货时间
	ReceivedAt          field.Time   // 收货时间
	ExpiredAt           field.Time   // 订单过期时间
	FinishedAt          field.Time   // 订单完成时间
	CancelAt            field.Time   // 订单取消时间
	BuyerDeleted        field.Bool   // 买家是否删除：0=否 1=是
	SellerDeleted       field.Bool   // 卖家是否删除：0=否 1=是
	CreatedAt           field.Time   // 创建时间
	UpdatedAt           field.Time   // 更新时间

	fieldMap map[string]field.Expr
}

func (c cardOrder) Table(newTableName string) *cardOrder {
	c.cardOrderDo.UseTable(newTableName)
	return c.updateTableName(newTableName)
}

func (c cardOrder) As(alias string) *cardOrder {
	c.cardOrderDo.DO = *(c.cardOrderDo.As(alias).(*gen.DO))
	return c.updateTableName(alias)
}

func (c *cardOrder) updateTableName(table string) *cardOrder {
	c.ALL = field.NewAsterisk(table)
	c.ID = field.NewString(table, "id")
	c.ConversationGroupID = field.NewString(table, "conversation_group_id")
	c.BuyerID = field.NewString(table, "buyer_id")
	c.SellerID = field.NewString(table, "seller_id")
	c.CardItems = field.NewField(table, "card_items")
	c.FirstCardImageURL = field.NewString(table, "first_card_image_url")
	c.CardGroupsCount = field.NewInt32(table, "card_groups_count")
	c.TotalQuantity = field.NewInt32(table, "total_quantity")
	c.TotalAmount = field.NewInt64(table, "total_amount")
	c.PayAmount = field.NewInt64(table, "pay_amount")
	c.PaymentMethod = field.NewInt32(table, "payment_method")
	c.TransactionNo = field.NewString(table, "transaction_no")
	c.Status = field.NewInt32(table, "status")
	c.CancelType = field.NewInt32(table, "cancel_type")
	c.ShippingAddress = field.NewField(table, "shipping_address")
	c.PaymentAt = field.NewTime(table, "payment_at")
	c.DeliveredAt = field.NewTime(table, "delivered_at")
	c.ReceivedAt = field.NewTime(table, "received_at")
	c.ExpiredAt = field.NewTime(table, "expired_at")
	c.FinishedAt = field.NewTime(table, "finished_at")
	c.CancelAt = field.NewTime(table, "cancel_at")
	c.BuyerDeleted = field.NewBool(table, "buyer_deleted")
	c.SellerDeleted = field.NewBool(table, "seller_deleted")
	c.CreatedAt = field.NewTime(table, "created_at")
	c.UpdatedAt = field.NewTime(table, "updated_at")

	c.fillFieldMap()

	return c
}

func (c *cardOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := c.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (c *cardOrder) fillFieldMap() {
	c.fieldMap = make(map[string]field.Expr, 25)
	c.fieldMap["id"] = c.ID
	c.fieldMap["conversation_group_id"] = c.ConversationGroupID
	c.fieldMap["buyer_id"] = c.BuyerID
	c.fieldMap["seller_id"] = c.SellerID
	c.fieldMap["card_items"] = c.CardItems
	c.fieldMap["first_card_image_url"] = c.FirstCardImageURL
	c.fieldMap["card_groups_count"] = c.CardGroupsCount
	c.fieldMap["total_quantity"] = c.TotalQuantity
	c.fieldMap["total_amount"] = c.TotalAmount
	c.fieldMap["pay_amount"] = c.PayAmount
	c.fieldMap["payment_method"] = c.PaymentMethod
	c.fieldMap["transaction_no"] = c.TransactionNo
	c.fieldMap["status"] = c.Status
	c.fieldMap["cancel_type"] = c.CancelType
	c.fieldMap["shipping_address"] = c.ShippingAddress
	c.fieldMap["payment_at"] = c.PaymentAt
	c.fieldMap["delivered_at"] = c.DeliveredAt
	c.fieldMap["received_at"] = c.ReceivedAt
	c.fieldMap["expired_at"] = c.ExpiredAt
	c.fieldMap["finished_at"] = c.FinishedAt
	c.fieldMap["cancel_at"] = c.CancelAt
	c.fieldMap["buyer_deleted"] = c.BuyerDeleted
	c.fieldMap["seller_deleted"] = c.SellerDeleted
	c.fieldMap["created_at"] = c.CreatedAt
	c.fieldMap["updated_at"] = c.UpdatedAt
}

func (c cardOrder) clone(db *gorm.DB) cardOrder {
	c.cardOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return c
}

func (c cardOrder) replaceDB(db *gorm.DB) cardOrder {
	c.cardOrderDo.ReplaceDB(db)
	return c
}

type cardOrderDo struct{ gen.DO }

type ICardOrderDo interface {
	gen.SubQuery
	Debug() ICardOrderDo
	WithContext(ctx context.Context) ICardOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ICardOrderDo
	WriteDB() ICardOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ICardOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ICardOrderDo
	Not(conds ...gen.Condition) ICardOrderDo
	Or(conds ...gen.Condition) ICardOrderDo
	Select(conds ...field.Expr) ICardOrderDo
	Where(conds ...gen.Condition) ICardOrderDo
	Order(conds ...field.Expr) ICardOrderDo
	Distinct(cols ...field.Expr) ICardOrderDo
	Omit(cols ...field.Expr) ICardOrderDo
	Join(table schema.Tabler, on ...field.Expr) ICardOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ICardOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) ICardOrderDo
	Group(cols ...field.Expr) ICardOrderDo
	Having(conds ...gen.Condition) ICardOrderDo
	Limit(limit int) ICardOrderDo
	Offset(offset int) ICardOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ICardOrderDo
	Unscoped() ICardOrderDo
	Create(values ...*model.CardOrder) error
	CreateInBatches(values []*model.CardOrder, batchSize int) error
	Save(values ...*model.CardOrder) error
	First() (*model.CardOrder, error)
	Take() (*model.CardOrder, error)
	Last() (*model.CardOrder, error)
	Find() ([]*model.CardOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CardOrder, err error)
	FindInBatches(result *[]*model.CardOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.CardOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ICardOrderDo
	Assign(attrs ...field.AssignExpr) ICardOrderDo
	Joins(fields ...field.RelationField) ICardOrderDo
	Preload(fields ...field.RelationField) ICardOrderDo
	FirstOrInit() (*model.CardOrder, error)
	FirstOrCreate() (*model.CardOrder, error)
	FindByPage(offset int, limit int) (result []*model.CardOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ICardOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (c cardOrderDo) Debug() ICardOrderDo {
	return c.withDO(c.DO.Debug())
}

func (c cardOrderDo) WithContext(ctx context.Context) ICardOrderDo {
	return c.withDO(c.DO.WithContext(ctx))
}

func (c cardOrderDo) ReadDB() ICardOrderDo {
	return c.Clauses(dbresolver.Read)
}

func (c cardOrderDo) WriteDB() ICardOrderDo {
	return c.Clauses(dbresolver.Write)
}

func (c cardOrderDo) Session(config *gorm.Session) ICardOrderDo {
	return c.withDO(c.DO.Session(config))
}

func (c cardOrderDo) Clauses(conds ...clause.Expression) ICardOrderDo {
	return c.withDO(c.DO.Clauses(conds...))
}

func (c cardOrderDo) Returning(value interface{}, columns ...string) ICardOrderDo {
	return c.withDO(c.DO.Returning(value, columns...))
}

func (c cardOrderDo) Not(conds ...gen.Condition) ICardOrderDo {
	return c.withDO(c.DO.Not(conds...))
}

func (c cardOrderDo) Or(conds ...gen.Condition) ICardOrderDo {
	return c.withDO(c.DO.Or(conds...))
}

func (c cardOrderDo) Select(conds ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Select(conds...))
}

func (c cardOrderDo) Where(conds ...gen.Condition) ICardOrderDo {
	return c.withDO(c.DO.Where(conds...))
}

func (c cardOrderDo) Order(conds ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Order(conds...))
}

func (c cardOrderDo) Distinct(cols ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Distinct(cols...))
}

func (c cardOrderDo) Omit(cols ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Omit(cols...))
}

func (c cardOrderDo) Join(table schema.Tabler, on ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Join(table, on...))
}

func (c cardOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.LeftJoin(table, on...))
}

func (c cardOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.RightJoin(table, on...))
}

func (c cardOrderDo) Group(cols ...field.Expr) ICardOrderDo {
	return c.withDO(c.DO.Group(cols...))
}

func (c cardOrderDo) Having(conds ...gen.Condition) ICardOrderDo {
	return c.withDO(c.DO.Having(conds...))
}

func (c cardOrderDo) Limit(limit int) ICardOrderDo {
	return c.withDO(c.DO.Limit(limit))
}

func (c cardOrderDo) Offset(offset int) ICardOrderDo {
	return c.withDO(c.DO.Offset(offset))
}

func (c cardOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ICardOrderDo {
	return c.withDO(c.DO.Scopes(funcs...))
}

func (c cardOrderDo) Unscoped() ICardOrderDo {
	return c.withDO(c.DO.Unscoped())
}

func (c cardOrderDo) Create(values ...*model.CardOrder) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Create(values)
}

func (c cardOrderDo) CreateInBatches(values []*model.CardOrder, batchSize int) error {
	return c.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (c cardOrderDo) Save(values ...*model.CardOrder) error {
	if len(values) == 0 {
		return nil
	}
	return c.DO.Save(values)
}

func (c cardOrderDo) First() (*model.CardOrder, error) {
	if result, err := c.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.CardOrder), nil
	}
}

func (c cardOrderDo) Take() (*model.CardOrder, error) {
	if result, err := c.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.CardOrder), nil
	}
}

func (c cardOrderDo) Last() (*model.CardOrder, error) {
	if result, err := c.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.CardOrder), nil
	}
}

func (c cardOrderDo) Find() ([]*model.CardOrder, error) {
	result, err := c.DO.Find()
	return result.([]*model.CardOrder), err
}

func (c cardOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.CardOrder, err error) {
	buf := make([]*model.CardOrder, 0, batchSize)
	err = c.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (c cardOrderDo) FindInBatches(result *[]*model.CardOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return c.DO.FindInBatches(result, batchSize, fc)
}

func (c cardOrderDo) Attrs(attrs ...field.AssignExpr) ICardOrderDo {
	return c.withDO(c.DO.Attrs(attrs...))
}

func (c cardOrderDo) Assign(attrs ...field.AssignExpr) ICardOrderDo {
	return c.withDO(c.DO.Assign(attrs...))
}

func (c cardOrderDo) Joins(fields ...field.RelationField) ICardOrderDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Joins(_f))
	}
	return &c
}

func (c cardOrderDo) Preload(fields ...field.RelationField) ICardOrderDo {
	for _, _f := range fields {
		c = *c.withDO(c.DO.Preload(_f))
	}
	return &c
}

func (c cardOrderDo) FirstOrInit() (*model.CardOrder, error) {
	if result, err := c.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.CardOrder), nil
	}
}

func (c cardOrderDo) FirstOrCreate() (*model.CardOrder, error) {
	if result, err := c.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.CardOrder), nil
	}
}

func (c cardOrderDo) FindByPage(offset int, limit int) (result []*model.CardOrder, count int64, err error) {
	result, err = c.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = c.Offset(-1).Limit(-1).Count()
	return
}

func (c cardOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = c.Count()
	if err != nil {
		return
	}

	err = c.Offset(offset).Limit(limit).Scan(result)
	return
}

func (c cardOrderDo) Scan(result interface{}) (err error) {
	return c.DO.Scan(result)
}

func (c cardOrderDo) Delete(models ...*model.CardOrder) (result gen.ResultInfo, err error) {
	return c.DO.Delete(models)
}

func (c *cardOrderDo) withDO(do gen.Dao) *cardOrderDo {
	c.DO = *do.(*gen.DO)
	return c
}
