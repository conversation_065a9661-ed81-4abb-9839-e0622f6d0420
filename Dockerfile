# 使用官方的Go语言基础镜像
FROM golang:1.19 AS builder

ENV PROJECT_NAME app_service

# 设置工作目录
WORKDIR /app
# 复制项目的所有文件
COPY . .

# git ssh
COPY id_rsa /root/.ssh/id_rsa
RUN chmod 600 /root/.ssh/id_rsa

RUN mkdir -p -m 0600 ~/.ssh && \
    ssh-keyscan e.coding.net >> ~/.ssh/known_hosts \
# git ssh

# 设置Go环境变量
RUN go env -w GO111MODULE=on \
    && go env -w GOPRIVATE=e.coding.net \
    && go env -w GONOPROXY=e.coding.net \
    && go env -w GONOSUMDB=e.coding.net

# 配置git
RUN git config --global url."ssh://****************:".insteadOf "https://e.coding.net"

# 复制项目的go.mod和go.sum文件
COPY go.mod go.sum ./

# 复制项目的配置文件
COPY conf ./

# 下载依赖
RUN go mod tidy

# 安装必要的依赖
RUN go get github.com/micro/cli/v2@v2.1.2

# 构建项目
RUN CGO_ENABLED=0 GOOS=linux go build -o /app/builds/$PROJECT_NAME /app/cmd/http_server/main.go

# 使用一个较小的基础镜像来运行构建的二进制文件
FROM alpine:latest

# 设置工作目录
WORKDIR /app

ENV ENV=dev

# RUN 设置 Asia/Shanghai 时区
RUN apk --no-cache add tzdata  && \
    ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    echo "Asia/Shanghai" > /etc/timezone

# 复制构建的二进制文件
COPY --from=builder /app/builds/app_service /app
COPY --from=builder /app/conf /app/conf

# 暴露服务端口（根据你的服务配置调整）
EXPOSE 8001

# 运行服务
CMD ["./app_service"]
