package global

const (
	DateTimeFormat = "2006-01-02 15:04:05"
	DateFormat     = "2006-01-02"
)

// 逻辑删除【0->未删除；1->已删除】
const (
	NORMAL  = 0 // 未删除
	DELETED = 1 //已删除
)

const (
	IntFalse = int32(0)
	IntTrue  = int32(1)
)

// 公共配置
const ()

// 请求头参数
const (
	AppChannel    = "app_channel"
	AppVersion    = "app_version"
	ClientType    = "client_type"
	Ip            = "ip"
	Authorization = "Authorization"
)

const (
	EnvDev  = "dev"
	EnvProd = "prod"
)

const (
	GameNameInnovationService = "app_service"
)

const (
	TimeLayout = "2006-01-02T15:04:05.000Z07:00"
)

const (
	TmtDb = "wc-tmtbg"
	WatDb = "wc-watbg"
	PatDb = "wc-patbg"
	CogDb = "wc-cogbg"
)

const (
	MaxStoryUserItemCount = 3000
)
