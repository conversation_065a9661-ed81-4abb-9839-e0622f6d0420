package global

import (
	"github.com/chenmingyong0423/go-mongox/v2"
)

var (
	MongoDBList map[string]*mongox.Database
)

func Pat() *mongox.Database {
	db, ok := MongoDBList[PatDb]
	if !ok || db == nil {
		panic("pat db no init")
	}
	return db
}

func Tmt() *mongox.Database {
	db, ok := MongoDBList[TmtDb]
	if !ok || db == nil {
		panic("tmt db no init")
	}
	return db
}

func Wat() *mongox.Database {
	db, ok := MongoDBList[WatDb]
	if !ok || db == nil {
		panic("wat db no init")
	}
	return db
}

func Cog() *mongox.Database {
	db, ok := MongoDBList[CogDb]
	if !ok || db == nil {
		panic("cog db no init")
	}
	return db
}
