package excelize_lib

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"github.com/xuri/excelize/v2"
	"go.uber.org/atomic"
	"math/rand"
	"net/http"
	"net/url"
	"strconv"
	"sync"
	"time"
)

var (
	defaultSheetName = "Sheet1" //默认Sheet名称
	defaultHeight    = 25.0     //默认行高度
)

const (
	batchSize = 2000
	goroutine = 20 // 并发goroutine数
)

type LkExcelExport struct {
	file      *excelize.File
	sheetName string //可定义默认sheet名称
}

func NewExcel() *LkExcelExport {
	return &LkExcelExport{file: createFile(), sheetName: defaultSheetName}
}

// ExportToPath 导出基本的表格
func (l *LkExcelExport) ExportToPath(params []map[string]string, data []map[string]interface{}, path string) (string, error) {
	l.export(params, data)
	name := createFileName()
	filePath := path + "/" + name
	err := l.file.SaveAs(filePath)
	return filePath, err
}

// ExportToStream 导出到浏览器。此处使用的gin框架 其他框架可自行修改ctx
func (l *LkExcelExport) ExportToStream(params []map[string]string, data []map[string]interface{}, ctx *gin.Context) (err error) {
	l.export(params, data)
	buffer, _ := l.file.WriteToBuffer()

	ctx.Header("Content-Description", "File Transfer")
	ctx.Header("Content-Disposition", `attachment; filename=`+url.QueryEscape(createFileName()))
	ctx.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	ctx.Header("Content-Length", string(len(buffer.Bytes())))
	ctx.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buffer.Bytes())
	return err
}

// 设置首行
func (l *LkExcelExport) writeTop(params []map[string]string) {
	topStyle, _ := l.file.NewStyle(`{"font":{"bold":true},"alignment":{"horizontal":"center","vertical":"center"}}`)
	var word = 'A'
	//首行写入
	for _, conf := range params {
		title := conf["title"]
		width, _ := strconv.ParseFloat(conf["width"], 64)
		line := fmt.Sprintf("%c1", word)
		//设置标题
		_ = l.file.SetCellValue(l.sheetName, line, title)
		//列宽
		_ = l.file.SetColWidth(l.sheetName, fmt.Sprintf("%c", word), fmt.Sprintf("%c", word), width)
		//设置样式
		_ = l.file.SetCellStyle(l.sheetName, line, line, topStyle)
		word++
	}
}

// 写入数据
func (l *LkExcelExport) writeData(params []map[string]string, data []map[string]interface{}) {
	lineStyle, _ := l.file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	//数据写入
	var j = 2 //数据开始行数
	for i, val := range data {
		//设置行高
		_ = l.file.SetRowHeight(l.sheetName, i+1, defaultHeight)
		//逐列写入
		var word = 'A'
		for _, conf := range params {
			valKey := conf["key"]
			line := fmt.Sprintf("%c%v", word, j)
			isNum := conf["is_num"]

			//设置值
			if isNum != "0" {
				valNum := fmt.Sprintf("'%v", val[valKey])
				_ = l.file.SetCellValue(l.sheetName, line, valNum)
			} else {
				_ = l.file.SetCellValue(l.sheetName, line, val[valKey])
			}

			//设置样式
			_ = l.file.SetCellStyle(l.sheetName, line, line, lineStyle)
			word++
		}
		j++
	}
	//设置行高 尾行
	_ = l.file.SetRowHeight(l.sheetName, len(data)+1, defaultHeight)
}

// 协程写入数据
func (l *LkExcelExport) concurrencyWriteData(params []map[string]string, data []map[string]interface{}) {
	var wg sync.WaitGroup
	ch := make(chan int, goroutine) // 使用制并发goroutine数

	// 创建并发的协程数量
	go func() {
		for i := 0; i < goroutine; i++ {
			ch <- i
		}
	}()
	// 计算切分的段数
	N := len(data)/batchSize + 1
	segments := make([][]map[string]interface{}, N)
	for i := 0; i < N; i++ {
		start := i * batchSize
		end := start + batchSize
		if i == N-1 {
			end = len(data) // 最后一段包含剩余的所有元素
		}
		segments[i] = data[start:end]
	}
	lineStyle, _ := l.file.NewStyle(&excelize.Style{
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	//数据写入
	var j = atomic.NewInt32(2) //数据开始行数
	for n, segment := range segments {
		fmt.Printf("Segment %d:\n", n+1)
		idx := <-ch // 获取一个goroutine
		wg.Add(1)
		go func(data []map[string]interface{}) {
			defer func() {
				wg.Done()
				ch <- idx // 归还goroutine
			}()
			for i, val := range data {
				//设置行高
				_ = l.file.SetRowHeight(l.sheetName, n*batchSize+i+1, defaultHeight)
				//逐列写入
				var word = 'A'
				for _, conf := range params {
					valKey := conf["key"]
					line := fmt.Sprintf("%c%v", word, j)
					isNum := conf["is_num"]

					//设置值
					if isNum != "0" {
						valNum := fmt.Sprintf("'%v", val[valKey])
						_ = l.file.SetCellValue(l.sheetName, line, valNum)
					} else {
						_ = l.file.SetCellValue(l.sheetName, line, val[valKey])
					}

					//设置样式
					_ = l.file.SetCellStyle(l.sheetName, line, line, lineStyle)
					word++
				}
				j.Add(1)
			}
		}(segment)
		wg.Wait()
	}
	//设置行高 尾行
	_ = l.file.SetRowHeight(l.sheetName, len(data)+1, defaultHeight)
}

func (l *LkExcelExport) export(params []map[string]string, data []map[string]interface{}) {
	l.writeTop(params)
	l.writeData(params, data)
}

func (l *LkExcelExport) concurrencyExport(params []map[string]string, data []map[string]interface{}) {
	l.writeTop(params)
	l.concurrencyWriteData(params, data)
}

func createFile() *excelize.File {
	f := excelize.NewFile()
	// 创建一个默认工作表
	sheetName := defaultSheetName
	index := f.NewSheet(sheetName)
	// 设置工作簿的默认工作表
	f.SetActiveSheet(index)
	return f
}

func createFileName() string {
	name := time.Now().Format("2006-01-02-15-04-05")
	rand.Seed(time.Now().UnixNano())
	return fmt.Sprintf("excel-%v-%v.xlsx", name, rand.Int63n(time.Now().Unix()))
}
