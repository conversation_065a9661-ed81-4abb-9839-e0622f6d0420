package snowflakeutl

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"github.com/bwmarrin/snowflake"
	"github.com/go-redis/redis/v8"
	"time"
)

var newNode *snowflake.Node

func Init(c *redis.Client) {
	// 获取唯一的 NodeID
	nodeID, err := getUniqueNodeID(c)
	if err != nil {
		panic("snowflake node get fail")
	}

	// 创建 Snowflake 节点
	node, err := snowflake.NewNode(nodeID)
	if node == nil || err != nil {
		panic("snowflake not created")
	}
	newNode = node
}

func getUniqueNodeID(c *redis.Client) (int64, error) {
	var ctx = context.Background()
	// 使用 Redis 的分布式锁机制
	lockKey := "snowflake_node_id_lock"
	lockTTL := 10 * time.Second

	// 尝试获取锁
	for {
		ok, err := c.SetNX(ctx, lockKey, 1, lockTTL).Result()
		if err != nil {
			return 0, err
		}
		if ok {
			// 成功获取锁
			break
		}
		// 等待一段时间后重试
		time.Sleep(100 * time.Millisecond)
	}
	defer c.Del(ctx, lockKey) // 释放锁

	// 获取当前最大 NodeID
	nodeIdKey := "snowflake_node"
	nodeId, err := c.Incr(ctx, nodeIdKey).Result()
	if err != nil {
		return 0, err
	}
	if nodeId > 1023 {
		_, err = c.Set(ctx, nodeIdKey, 1, 0).Result()
		if err != nil {
			log.Fatal("snowflake reset node id fail", err)
		}
		nodeId = 1
	}

	return nodeId, nil
}

func GenerateID() int64 {
	return newNode.Generate().Int64()
}

func GenID() snowflake.ID {
	return newNode.Generate()
}
