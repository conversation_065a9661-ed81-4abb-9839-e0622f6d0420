package util

// UniqueStringSlice 对切片进行去重
func UniqueStringSlice(list []string) (res []string) {
	dataMap := make(map[string]string)
	for _, v := range list {
		dataMap[v] = v
	}
	for _, v := range dataMap {
		res = append(res, v)
	}
	return
}

// UniqueInt64Slice 对切片进行去重
func UniqueInt64Slice(list []int64) (res []int64) {
	dataMap := make(map[int64]int64)
	for _, v := range list {
		dataMap[v] = v
	}
	for _, v := range dataMap {
		res = append(res, v)
	}
	return
}

func ConvertPointerArrayToStringArray(ptrArr []*string) []string {
	var strArr []string
	for _, ptr := range ptrArr {
		if ptr != nil { // 检查指针是否为nil
			strArr = append(strArr, *ptr) // 解引用指针并添加到字符串数组中
		}
	}
	return strArr
}
