package util

import (
	"crypto/rand"
	"encoding/binary"
	"strconv"
)

// GenerateRandomSeeds 生成随机种子数组
func GenerateRandomSeeds(count int) ([]int64, error) {
	seeds := make([]int64, count)
	for i := 0; i < count; i++ {
		seed, err := GenerateRandomSeed()
		if err != nil {
			return seeds, err
		}
		seeds[i] = seed
	}
	return seeds, nil
}

// GenerateStringRandomSeeds 生成随机种子数组
func GenerateStringRandomSeeds(count int) ([]string, error) {
	seeds := make([]string, count)
	for i := 0; i < count; i++ {
		seed, err := GenerateRandomSeed()
		if err != nil {
			return seeds, err
		}
		seeds[i] = strconv.FormatInt(seed, 10)
	}
	return seeds, nil
}

// GenerateAnyRandomSeeds 生成随机种子数组
func GenerateAnyRandomSeeds(count int) ([]interface{}, error) {
	seeds := make([]interface{}, count)
	for i := 0; i < count; i++ {
		seed, err := GenerateRandomSeed()
		if err != nil {
			return seeds, err
		}
		seeds[i] = strconv.FormatInt(seed, 10)
	}
	return seeds, nil
}

// GenerateRandomSeed 生成随机种子
func GenerateRandomSeed() (int64, error) {
	var b [8]byte
	_, err := rand.Read(b[:])
	if err != nil {
		return 0, err
	}
	return int64(binary.LittleEndian.Uint64(b[:])), nil
}
