package kafka_util

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"go-micro.dev/v4/broker"
)

func SendMsg(ctx context.Context, topic string, data any) error {
	msg := &broker.Message{
		Body: []byte(util.Obj2JsonStr(data)),
	}
	err := global.BROKER.Publish(topic, msg)
	if err != nil {
		log.Ctx(ctx).Errorf("Kafka Publish topic[%s], data[%s] err: %v", topic, util.Obj2JsonStr(data), err)
		return err
	}
	return nil
}
