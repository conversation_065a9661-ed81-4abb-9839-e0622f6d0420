package util

import (
	"bytes"
	"encoding/gob"
	"encoding/json"
	"github.com/mitchellh/mapstructure"
	"reflect"
	"strconv"
	"strings"
)

// JsonStr2Map json字符串转map
func JsonStr2Map(str string) (mapObj map[string]interface{}, err error) {
	var result map[string]interface{}
	if err := json.Unmarshal([]byte(str), &result); err != nil {
		return nil, err
	}
	return result, nil
}

// Obj2Map obj转map
func Obj2Map(obj interface{}) (mapObj map[string]interface{}, err error) {
	if reflect.TypeOf(obj).Kind() == reflect.String {
		return JsonStr2Map(StrVal(obj))
	}
	// 结构体转json
	b, err := json.Marshal(obj)
	if err != nil {
		return nil, err
	}

	var result map[string]interface{}
	if err := json.Unmarshal(b, &result); err != nil {
		return nil, err
	}
	return result, nil
}

func Obj2JsonStr(obj interface{}) (result string) {
	if obj != nil {
		data, err := Marshal(obj)
		if err != nil {
			return ""
		}
		return string(data)
	}
	return ""
}

func Marshal(data interface{}) ([]byte, error) {
	bf := bytes.NewBuffer([]byte{})
	jsonEncoder := json.NewEncoder(bf)
	jsonEncoder.SetEscapeHTML(false)
	if err := jsonEncoder.Encode(data); err != nil {
		return nil, err
	}
	return bf.Bytes(), nil
}

// Map2Struct map转结构体
func Map2Struct(mapObj map[string]interface{}, data interface{}) error {
	marshal, err := json.Marshal(mapObj)
	if err != nil {
		return nil
	}
	err = json.Unmarshal(marshal, data)
	if err != nil {
		return nil
	}
	return mapstructure.Decode(mapObj, data)
}

// JsonStr2Struct json字符串转结构体
func JsonStr2Struct(jsonStr string, data interface{}) error {
	obj, err := Obj2Map(jsonStr)
	if err != nil {
		return err
	}
	return Map2Struct(obj, data)
}

// JsonArrayObj2Struct json数组对象转结构体
func JsonArrayObj2Struct(jsonArrayObj []interface{}, data any) error {
	return JsonStr2Struct(Obj2JsonStr(jsonArrayObj), data)
}

// JsonArrayStr2Struct json数组转结构体
func JsonArrayStr2Struct(jsonStr string, data any) error {
	err := json.Unmarshal([]byte(jsonStr), data)
	if err != nil {
		return err
	}
	return nil
}

// Number2Int64 obj转换int64
func Number2Int64(value interface{}) int64 {
	intVal, _ := strconv.ParseInt(StrVal(value), 10, 64)
	return intVal
}

// Number2Int32 obj转换int32
func Number2Int32(value interface{}) int32 {
	intVal, _ := strconv.ParseInt(StrVal(value), 10, 64)
	return int32(intVal)
}

// NumberStr2Int32Slice 字符串转成[]int32 1,2,3 -> [1,2,3]
func NumberStr2Int32Slice(value string) ([]int32, error) {
	strValues := strings.Split(value, ",")
	nums := make([]int32, len(strValues))
	for i, str := range strValues {
		num, err := strconv.ParseInt(str, 10, 32)
		if err != nil {
			return nil, err
		}
		nums[i] = int32(num)
	}

	return nums, nil
}

// NumberStr2Int64Slice 字符串转成[]int64 1,2,3 -> [1,2,3]
func NumberStr2Int64Slice(value string) ([]int64, error) {
	strValues := strings.Split(value, ",")
	nums := make([]int64, len(strValues))
	for i, str := range strValues {
		num, err := strconv.ParseInt(str, 10, 32)
		if err != nil {
			return nil, err
		}
		nums[i] = num
	}

	return nums, nil
}

// Bool2Int32 bool转换int32
func Bool2Int32(isTrue bool) *int32 {
	var result int32
	if isTrue {
		result = int32(1)
		return &result
	}
	result = int32(0)
	return &result
}

// GetBytes interface转换byte数组
func GetBytes(key interface{}) ([]byte, error) {
	var buf bytes.Buffer
	enc := gob.NewEncoder(&buf)
	err := enc.Encode(key)
	if err != nil {
		return nil, err
	}
	return buf.Bytes(), nil
}
