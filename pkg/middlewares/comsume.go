package middlewares

import (
	commondefine "app_service/apps/platform/common/define"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"fmt"
	"go-micro.dev/v4/broker"
	"runtime/debug"
)

// ConsumerInterface 定义消费者接口
type ConsumerInterface interface {
	GetTopic() string
	GetGroup() string
	HandleFun() broker.Handler
}

// SafeHandler 装饰器函数，为handler添加panic恢复机制
func SafeHandler(handler broker.Handler) broker.Handler {
	return func(event broker.Event) (err error) {
		defer func() {
			if r := recover(); r != nil {
				stackTrace := string(debug.Stack())
				errMsg := fmt.Sprintf("Panic recovered in consumer: %v\nStack trace:\n%s", r, stackTrace)
				log.Errorf(errMsg)
				err = commondefine.CommonWarnErr.Err(fmt.Errorf("panic recovered: %v", r))
			}
		}()
		return handler(event)
	}
}

// BaseConsumer 基础消费者结构体
type BaseConsumer struct {
	topic string
	group string
}

// NewBaseConsumer 创建基础消费者
func NewBaseConsumer(topic, group string) BaseConsumer {
	return BaseConsumer{
		topic: topic,
		group: group,
	}
}

func (b BaseConsumer) GetTopic() string {
	return b.topic
}

func (b BaseConsumer) GetGroup() string {
	return b.group
}
