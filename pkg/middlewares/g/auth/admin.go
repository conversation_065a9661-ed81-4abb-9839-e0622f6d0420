package auth

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type Admin struct {
	// 不需要鉴权的地址
	NoAuthUrl []string
}

func (c *Admin) CheckToken(token string, ctx context.Context) (any, error) {
	// 远程校验token 获取用户信息
	info, err := pat.CheckAdminJwt(ctx, token)
	log.Ctx(ctx).Infof("获取用户信息 info:%v", info)
	if err != nil {
		log.Ctx(ctx).Errorf("获取用户信息 err:%+v", err)
		return nil, err
	}

	if info == nil {
		return nil, response.TokenErr.SetMsg("鉴权失败")
	}

	return info, nil
}

func (c *Admin) GetNoAuthUrl() []string {
	return c.NoAuthUrl
}

// GetAdminFromCtx 获取用户信息
func GetAdminFromCtx(ctx context.Context) (*pat.CheckAdmJwtUserInfo, bool) {
	acc, ok := ctx.Value(userInfo).(*pat.CheckAdmJwtUserInfo)
	return acc, ok
}
