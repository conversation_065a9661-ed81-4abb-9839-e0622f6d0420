package auth

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"e.coding.net/g-dtay0385/common/go-util/response/g"
	"errors"
	"fmt"
	"github.com/gin-gonic/gin"
	"net/http"
	"strings"
)

type TokenChecker interface {
	CheckToken(token string, ctx context.Context) (any, error)
	GetNoAuthUrl() []string
}

func Auth(tokenChecker TokenChecker) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		// 不需要鉴权的地址
		if isNoAuthUrl(tokenChecker.GetNoAuthUrl(), ctx.Request.URL.Path) {
			auth := ctx.GetHeader("Authorization")
			if auth == "" {
				ctx.Next()
				return
			}
		}

		// 从上下文获取token
		token, err := getTokenFromCtx(ctx)
		if err != nil {
			g.Fail(ctx, response.TokenErr.Err(err))
			ctx.Abort()
			return
		}

		// 校验令牌
		info, err := tokenChecker.CheckToken(token, ctx)
		if err != nil {
			if e, ok := err.(*response.Codes); ok && e.Code == http.StatusForbidden {
				g.Fail(ctx, response.PermissionErr.Err(err))
			} else {
				g.Fail(ctx, response.TokenErr.Err(err))
			}
			ctx.Abort()
			return
		}

		// 保存用户信息
		ctx.Set(userInfo, info)

		ctx.Next()
	}
}

// 是否为不需要鉴权的地址
func isNoAuthUrl(noAuthUrl []string, nowUrl string) bool {
	for _, url := range noAuthUrl {
		if url == nowUrl {
			return true
		}
	}
	return false
}

// 从上下文获取token
func getTokenFromCtx(ctx *gin.Context) (string, error) {
	auth := ctx.GetHeader("Authorization")
	tokens := strings.Split(auth, " ")
	if auth == "" || len(tokens) != 2 || strings.ToLower(tokens[0]) != "bearer" {
		return "", errors.New(fmt.Sprintf("Authorization为空或格式不对:%s", auth))
	}
	token := tokens[1]
	return token, nil
}
