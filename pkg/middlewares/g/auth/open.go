package auth

import (
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"errors"
)

type Open struct {
	Token string
	// 不需要鉴权的地址
	NoAuthUrl []string
}

func (o *Open) CheckToken(token string, ctx context.Context) (any, error) {
	if token != o.Token {
		log.Ctx(ctx).Errorf("open鉴权失败 %s", token)
		return nil, errors.New("鉴权失败")
	}
	return nil, nil
}

func (o *Open) GetNoAuthUrl() []string {
	return o.NoAuthUrl
}
