package search

import (
	"gorm.io/gen"
	"gorm.io/gen/field"
)

func MakeCondition(q interface{}) func(dao gen.Dao) gen.Dao {
	return func(dao gen.Dao) gen.Dao {
		condition := &GormCondition{
			GormWhere: GormWhere{},
			GormOrder: GormOrder{},
			Join:      make([]*GormJoin, 0),
		}
		BuildSearchQuery(q, condition)
		for _, join := range condition.Join {
			if join == nil {
				continue
			}
			dao.Join(join.Tabler, join.Expr...)
		}
		if len(condition.Where) > 0 {
			dao.Where(condition.Where...)
		}
		if len(condition.Order) > 0 {
			dao.Order(condition.Order...)
		}
		return dao
	}
}

func MakePaginate(pageSize, pageIndex int) func(dao gen.Dao) gen.Dao {
	return func(dao gen.Dao) gen.Dao {
		offset := (pageIndex - 1) * pageSize
		if offset < 0 {
			offset = 0
		}
		return dao.Offset(offset).Limit(pageSize)
	}
}

func Paginate(pageSize, pageIndex int) (int, int) {
	offset := (pageIndex - 1) * pageSize
	if offset < 0 {
		offset = 0
	}
	return offset, pageSize
}

type ScopeOpt func(gen.Dao) gen.Dao

func MakeOpt(opts ...ScopeOpt) func(dao gen.Dao) gen.Dao {
	return func(dao gen.Dao) gen.Dao {
		if len(opts) != 0 {
			for _, o := range opts {
				if o != nil {
					dao = o(dao)
				}
			}
		}
		return dao
	}
}

func MakeScope(scopeIds []int64, field field.Int64) func(dao gen.Dao) gen.Dao {
	return func(dao gen.Dao) gen.Dao {
		if len(scopeIds) != 0 {
			return dao.Where(field.In(scopeIds...))
		}
		return dao
	}
}
