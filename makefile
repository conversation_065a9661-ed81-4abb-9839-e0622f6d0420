install-lint-requirements=sh -c '\
if command -v ${GOPATH}/bin/golangci-lint >/dev/null 2>&1;then \
		echo "golangci-lint installed already"; \
		${GOPATH}/bin/golangci-lint --version; \
	else \
		echo "golangci-lint not installed, proceed to install"; \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b ${GOPATH}/bin v1.59.1; \
	fi' install-lint-requirements

lint-install:
	@${install-lint-requirements}

lint:
	golangci-lint run -v --timeout 10m --skip-dirs="vendor" --skip-files ".*(_test|\.gen)\.go" --exclude SA4006,unused,sa1012,asmdecl,S1004,SA5008,SA1029  ./...

check: lint-install lint

swag:
	swag init  -g ./main.go -o ../../docs --instanceName app_service --parseDependency --parseDepth 6  --parseInternal