package third_party

import (
	"app_service/pkg/util"
	"context"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"fmt"
	"github.com/pkg/errors"
)

const (
	SubmitTaskUrl    = "%s/open/v1/ai_base/submit_task"
	GetTaskResultUrl = "%s/open/v1/ai_base/get_task_result"
)

type SubmitTaskReq struct {
	ClientId    string `json:"client_id"`    //客户端ID
	PromptParam string `json:"prompt_param"` //prompt参数，json格式
}

type SubmitTaskResp struct {
	TaskId     int64  `json:"task_id"`      //任务Id
	QueueIndex uint64 `json:"queue_index"`  //当前任务在SD队列中的位置，如果队列还没进入 SD，则为 0
	Status     int32  `json:"status"`       //1-waiting 5-executing 10-executed 99-stopped
	EstTimeSec int64  `json:"est_time_sec"` //预计执行时间，单位秒
}

// SubmitTask 提交
func SubmitTask(prompt string) (*SubmitTaskResp, error) {
	req := SubmitTaskReq{
		ClientId:    "aa",
		PromptParam: prompt,
	}
	reqMap, err := util.Obj2Map(req)
	if err != nil {
		return nil, errors.Wrap(err, "obj2map error")
	}

	rsp := &SubmitTaskResp{}
	res := make(map[string]interface{}, 0)
	url := fmt.Sprintf(SubmitTaskUrl, "")
	err = utilRequest.New(context.Background(),
		utilRequest.WithMethodPost(),
		utilRequest.WithUrl(url),
		utilRequest.WithParams(reqMap)).Call(&res)
	if err != nil {
		return nil, errors.Wrap(err, "request aibase err")
	}

	if err = util.Map2Struct(res, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}

type GetTaskReq struct {
	TaskId int64 `json:"task_id"` //任务Id
}

type GetTaskResp struct {
	TaskId      int64  `json:"task_id"`      //任务Id
	QueueIndex  int64  `json:"queue_index"`  //当前任务在SD队列中的位置，如果队列还没进入 SD，则为 0
	Status      int32  `json:"status"`       //1-waiting 5-executing 10-executed 99-stopped
	EstTimeSec  int64  `json:"est_time_sec"` //预计执行时间，单位秒
	FinalOutput string `json:"final_output"` //结果，JSON格式
}

// GetTaskResult 获取任务结果
func GetTaskResult(taskId int64) (*GetTaskResp, error) {
	req := GetTaskReq{
		TaskId: taskId,
	}
	reqMap, err := util.Obj2Map(req)
	if err != nil {
		return nil, errors.Wrap(err, "obj2map error")
	}

	rsp := &GetTaskResp{}
	res := make(map[string]interface{}, 0)
	url := fmt.Sprintf(GetTaskResultUrl, "")
	err = utilRequest.New(context.Background(),
		utilRequest.WithMethodPost(),
		utilRequest.WithUrl(url),
		utilRequest.WithParams(reqMap)).Call(&res)
	if err != nil {
		return nil, errors.Wrap(err, "request aibase err")
	}

	if err = util.Map2Struct(res, rsp); err != nil {
		return nil, err
	}

	return rsp, nil
}
