package coupon

import (
	"app_service/third_party/base"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

// ReclaimByCodeReq 回收优惠券请求
type ReclaimByCodeReq struct {
	CouponId string `json:"coupon_id" form:"coupon_id"`
}

// ReclaimByCode 回收优惠券
func ReclaimByCode(ctx context.Context, form *ReclaimByCodeReq) (*base.Rsp, error) {
	rsp := &base.Rsp{}
	req, err := request.Coupon()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"coupon_id": form.CouponId,
	}

	err = req.Call(
		ctx,
		"open/v1/user_coupon/reclaim",
		&rsp,
		utilRequest.WithMethodPost(),
		utilRequest.WithParams(params),
	)
	if err != nil {
		log.Ctx(ctx).Errorf("回收优惠券异常，返回数据：%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("回收优惠券异常，返回数据：%v", rsp)
		return rsp, response.Fail.SetMsg(rsp.Desc)
	}

	return rsp, err
}
