package coupon

import (
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/mor"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"encoding/json"
	"errors"
	"fmt"
)

// ReceiveReq 领券请求
type ReceiveReq struct {
	Data         []*ReceiveData `json:"data" form:"data"`
	MarketId     int64          `json:"market_id" form:"market_id"`
	ActivityId   string         `json:"activity_id" form:"activity_id"`
	ActivityName string         `json:"activity_name" form:"activity_name"`
	SceneId      int32          `json:"scene_id" form:"scene_id"`
}

type ReceiveData struct {
	UserId       string `json:"user_id" form:"user_id"`
	Quantity     int32  `json:"quantity" form:"quantity"`
	CouponId     int64  `json:"coupon_id" form:"coupon_id"`
	CouponCode   string `json:"coupon_code" form:"coupon_code"`
	CouponPackId int64  `json:"coupon_pack_id" form:"coupon_pack_id"`
}

// ReceiveRspRowData 领取优惠券响应信息条目
type ReceiveRspRowData struct {
	UserId               string `json:"user_id" form:"user_id"`
	Quantity             int32  `json:"quantity" form:"quantity"`
	CouponId             int64  `json:"coupon_id" form:"coupon_id"`
	CouponCode           string `json:"coupon_code" form:"coupon_code"`
	CouponPackId         int64  `json:"coupon_pack_id" form:"coupon_pack_id"`
	Success              int32  `json:"success" form:"success"`
	Desc                 string `json:"desc" form:"desc"`
	CouponName           string `json:"coupon_name" form:"coupon_name"`
	StartTime            string `json:"start_time,omitempty" form:"start_time"`                           // 开始时间
	ExpireTime           string `json:"expire_time,omitempty" form:"expire_time"`                         // 过期时间
	OrderMinPrice        int32  `json:"order_min_price,omitempty" form:"order_min_price"`                 // 门槛金额
	Reduction            int32  `json:"reduction,omitempty" form:"reduction"`                             // 减免金额
	CouponCollectLogCode string `json:"coupon_collect_log_code,omitempty" form:"coupon_collect_log_code"` // 核销吗
}

// ReceiveRsp 响应
type ReceiveRsp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data []*ReceiveRspRowData `json:"data" form:"data"`
}

// Receive 领券
func Receive(ctx context.Context, f *ReceiveReq) (*ReceiveRsp, error) {
	rsp := &ReceiveRsp{}

	// 初始化请求参数
	reqParams := map[string]interface{}{}
	// 结构体转json
	fJson, _ := json.Marshal(f)
	if err := json.Unmarshal(fJson, &reqParams); err != nil {
		return nil, err
	}

	req, err := request.Coupon()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/v1/coupon/receive",
		&rsp,
		utilRequest.WithMethodPost(),
		utilRequest.WithParams(reqParams),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			mor.SendWarnMsg(context.Background(), global.GlobalConfig.WarnId, fmt.Sprintf("领券异常,请求数据：%+v,返回数据：%+v", util.Obj2JsonStr(reqParams), err))
		}
		log.Ctx(ctx).Errorf("领券异常，返回数据：%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("领券异常，返回数据：%v", rsp)
		return rsp, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp, err
}
