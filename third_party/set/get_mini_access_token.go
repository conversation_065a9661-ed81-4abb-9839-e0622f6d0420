package set

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type WechatMiniAccessTokenResp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data string `json:"data" form:"data"`
}

func GetWechatMiniAccessToken(ctx context.Context, appId string) (string, error) {
	rsp := &WechatMiniAccessTokenResp{}
	req, err := request.Set()
	if err != nil {
		return "", err
	}
	err = req.Call(
		ctx,
		"open/wechat_mini/v1/access_token",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"app_id": appId}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return "", err
	}

	if rsp.Code != 0 {
		return "", response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
