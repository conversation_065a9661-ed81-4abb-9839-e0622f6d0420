package set

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type SendValidCodeReq struct {
	SceneId     int32  `json:"scene_id"`     // 短信模板Id
	MobilePhone string `json:"mobile_phone"` // 手机号
	Code        string `json:"code"`         // 验证码
	ExpireTime  int32  `json:"expire_time"`  // 过期时间
}

type SendValidCodeResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

func SendValidCode(ctx context.Context, params *SendValidCodeReq) (*SendValidCodeResp, error) {
	rsp := &SendValidCodeResp{}
	req, err := request.Set()
	if err != nil {
		return nil, err
	}
	// params转成map[string]interface{}

	err = req.Call(
		ctx,
		"open/sms/v1/send_valid_code",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{
			"scene_id":     params.SceneId,
			"mobile_phone": params.MobilePhone,
			"code":         params.Code,
			"expire_time":  params.ExpireTime,
		}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp, err
}
