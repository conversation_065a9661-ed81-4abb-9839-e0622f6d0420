package set

import (
	"app_service/apps/platform/system/define"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type Resp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data *define.GetJsSDKResp `json:"data" form:"data"`
}

func GetJsSDK(ctx context.Context, appId string, url string) (*define.GetJsSDKResp, error) {
	rsp := &Resp{}
	req, err := request.Set()
	if err != nil {
		return nil, err
	}
	// params转成map[string]interface{}

	err = req.Call(
		ctx,
		"open/wechat_office/v1/jssdk",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{
			"app_id": appId,
			"url":    url,
		}),
		utilRequest.WithMethodGet(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data, err
}
