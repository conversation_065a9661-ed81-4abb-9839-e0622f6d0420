package define

type WithdrawOrderAddressInfo struct {
	Name        string `json:"name"`
	MobilePhone string `json:"mobile_phone"`
	Code        string `json:"code"`
	Area        string `json:"area"`
	Place       string `json:"place"`
}

type (
	BonusExchangeWithdrawOrderReq struct {
		OpenUserId    string                    `json:"open_user_id"`
		ExchangeLogId string                    `json:"exchange_log_id"`
		ExchangeQty   int32                     `json:"exchange_qty"`
		AddressId     string                    `json:"address_id"`
		AddressInfo   *WithdrawOrderAddressInfo `json:"address_info"`
		SteamItemId   string                    `json:"steam_item_id"`
	}
	BonusExchangeWithdrawOrderInfo struct {
		ItemWithdrawOrderID string `json:"item_withdraw_order_id"`
	}
	BonusExchangeWithdrawOrderResp struct {
		Code int32                          `json:"code"`
		Desc string                         `json:"desc"`
		Data BonusExchangeWithdrawOrderInfo `json:"data"`
	}
)

// ItemWithdrawOrderStatus 订单状态
type ItemWithdrawOrderStatus int32

func (s ItemWithdrawOrderStatus) Val() int32 {
	return int32(s)
}

const (
	// WithdrawOrderStatusAbandoned 废弃订单
	WithdrawOrderStatusAbandoned ItemWithdrawOrderStatus = -2
	// WithdrawOrderStatusDeleted 已删除订单
	WithdrawOrderStatusDeleted ItemWithdrawOrderStatus = -1
	// WithdrawOrderStatusExpired 无效订单
	WithdrawOrderStatusExpired ItemWithdrawOrderStatus = 0
	// WithdrawOrderStatusDone 订单已完成
	WithdrawOrderStatusDone ItemWithdrawOrderStatus = 1
	// WithdrawOrderStatusAftersaleing 售后中
	WithdrawOrderStatusAftersaleing ItemWithdrawOrderStatus = 2
	// WithdrawOrderStatusAfterSaleRefunding 退款中
	WithdrawOrderStatusAfterSaleRefunding ItemWithdrawOrderStatus = 3
	// WithdrawOrderStatusAfterSaleDone 已退款(售后完成)
	WithdrawOrderStatusAfterSaleDone ItemWithdrawOrderStatus = 4
	// WithdrawOrderStatusCreated 已下单，next 待物品锁定
	WithdrawOrderStatusCreated ItemWithdrawOrderStatus = 10
	// WithdrawOrderStatusLocked 物品已锁定，next 待物品检查
	WithdrawOrderStatusLocked ItemWithdrawOrderStatus = 20
	// WithdrawOrderStatusChecked 物品已检查，next 待发货 | 人工审核
	WithdrawOrderStatusChecked ItemWithdrawOrderStatus = 30
	// WithdrawOrderStatusWaitForPay 等待付款
	WithdrawOrderStatusWaitForPay ItemWithdrawOrderStatus = 35
	// WithdrawOrderStatusWaitForShip 等待发货
	WithdrawOrderStatusWaitForShip ItemWithdrawOrderStatus = 36
	// WithdrawOrderStatusPartShiped 部分发货，next 待收货确定
	WithdrawOrderStatusPartShiped ItemWithdrawOrderStatus = 39
	// WithdrawOrderStatusShiped 已发货，next 待收货确定
	WithdrawOrderStatusShiped ItemWithdrawOrderStatus = 40
	// WithdrawOrderStatusReceived 已收货，next 待完成
	WithdrawOrderStatusReceived ItemWithdrawOrderStatus = 50
	// WithdrawOrderStatusWaitForMake 等待商品制作完成
	WithdrawOrderStatusWaitForMake ItemWithdrawOrderStatus = 60
	// WithdrawOrderStatusWaitForDue 等待支付尾款
	WithdrawOrderStatusWaitForDue ItemWithdrawOrderStatus = 61
)

// ShipType 发货类型
type ShipType int32

func (s ShipType) Val() int32 {
	return int32(s)
}

const (
	// ShipTypeRequired 需要发货
	ShipTypeRequired ShipType = 1
	// ShipTypeNotRequired 无需发货
	ShipTypeNotRequired ShipType = 2
)

// GoodsType 商品类型
type GoodsType int32

func (g GoodsType) Val() int32 {
	return int32(g)
}

const (
	// GoodsTypeReal 真实商品
	GoodsTypeReal GoodsType = 100
	// GoodsTypeCSGO CSGO饰品
	GoodsTypeCSGO GoodsType = 730
)

// OrderType 订单类型
type OrderType int32

func (t OrderType) Val() int32 {
	return int32(t)
}

const (
	// OrderTypeNormal 普通商品订单
	OrderTypeNormal OrderType = 1
	// OrderTypePresale 预售
	OrderTypePresale OrderType = 2
	// OrderTypeSeckill 秒杀商品订单
	OrderTypeSeckill OrderType = 3
	// OrderTypeNft nft
	OrderTypeNft OrderType = 4
	// OrderTypeDraw 抽选
	OrderTypeDraw OrderType = 5
	// OrderTypeNftOfCoinStore 元气金币商城兑换NFT订单
	OrderTypeNftOfCoinStore OrderType = 6
	// OrderTypeSteamItemOfCoinStore 元气金币商城兑换真实商品
	OrderTypeSteamItemOfCoinStore OrderType = 7
	// OrderTypeDelivery 气仓提货订单
	OrderTypeDelivery OrderType = 8
	// OrderTypeWcBonusOrder 文潮金币商城订单
	OrderTypeWcBonusOrder OrderType = 9
)
