package define

const (
	YcOpenErrorCodeLowStocks = 84102853 // 库存不足
)

// CommonResp 通用响应结构
type CommonResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

type (
	Record struct {
		// 状态
		Status string `json:"status"`
		// 时间
		Time string `json:"time"`
		// 格式化后的时间
		Ftime string `json:"ftime"`
		// 内容
		Context string `json:"context"`
		// 行政区域名称
		AreaName string `json:"area_name"`
		// 行政区域编码
		AreaCode string `json:"area_code"`
	}

	Freight struct {
		// 状态
		// Status FreightStatusEnum,
		// 用户ID
		UserId string `json:"user_id"`
		// 订单ID
		WithdrawOrderId string `json:"withdraw_order_id"`
		// 运单号
		FreightNo string `json:"freight_no"`
		// 运单号
		IsCheck bool `json:"is_check"`
		// 快递公司
		Com string `json:"com"`
		// 快递公司
		Records []Record `json:"records"`
	}
)
