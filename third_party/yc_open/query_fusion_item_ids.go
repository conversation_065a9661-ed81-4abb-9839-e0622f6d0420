package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type QueryFusionItemIdsResp struct {
	Code int32                    `json:"code" form:"code"`
	Desc string                   `json:"desc" form:"desc"`
	Data []*QueryFusionItemIdsRes `json:"data" form:"data"`
}

type QueryFusionItemIdsRes struct {
	ItemId      string   `json:"item_id"`
	Count       int32    `json:"count"`
	UserItemIds []string `json:"user_item_ids"`
}

// QueryFusionItemIds 查询融合物品数量ID
func QueryFusionItemIds(ctx context.Context, openUserId string, itemIds []string) ([]*QueryFusionItemIdsRes, error) {
	rsp := &QueryFusionItemIdsResp{}

	params := map[string]interface{}{
		"open_user_id": openUserId,
		"item_ids":     itemIds,
		"sign":         "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryFusionItemIds sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/query_fusion_item_ids"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询融合物品数量ID异常，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询融合物品数量ID异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("查询融合物品数量ID失败")
	}
	return rsp.Data, err
}

// QueryFusionItemIdsSortCostPrice 查询融合物品数量ID(根据成本价排序)
func QueryFusionItemIdsSortCostPrice(ctx context.Context, openUserId string, itemIds []string, buyPriceSort int32) ([]*QueryFusionItemIdsRes, error) {
	rsp := &QueryFusionItemIdsResp{}

	params := map[string]interface{}{
		"open_user_id": openUserId,
		"item_ids":     itemIds,
		"sign":         "",
	}
	if buyPriceSort != 0 {
		params["buy_price_sort"] = buyPriceSort
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryFusionItemIds sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/query_fusion_item_ids"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询融合物品数量ID异常，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询融合物品数量ID异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("查询融合物品数量ID失败")
	}
	return rsp.Data, err
}
