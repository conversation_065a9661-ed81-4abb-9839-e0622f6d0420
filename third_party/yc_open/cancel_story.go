package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type CancelStoryResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

const (
	CancelStorySuccess = 1
	CancelStoryUnknown = 0
	CancelStoryFail    = -1
)

// CancelStory 取消取消物品探索
func CancelStory(ctx context.Context, openUserId string, orderId string, userItemIds []string) (int32, error) {
	log.Ctx(ctx).Infof("CancelStory openUserId:%+v, orderId:%+v, userItemIds:%+v", openUserId, orderId, userItemIds)
	rsp := &CancelStoryResp{}

	params := map[string]interface{}{
		"user_item_ids": userItemIds,
		"sign":          "",
	}
	if openUserId != "" {
		params["open_user_id"] = openUserId
	}
	if orderId != "" {
		params["story_order_id"] = orderId
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("CancelStory sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("CancelStory GetAppAccess err，返回数据：%v", err)
		return CancelStoryFail, err
	}

	url := "/open/user_item/cancel_story"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("取消物品探索超时异常，返回数据：%v", err)
			return CancelStoryUnknown, response.Fail.SetMsg("取消物品探索未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("取消物品探索超时异常，返回数据：%v", err)
			return CancelStoryUnknown, response.Fail.SetMsg("取消物品探索请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("取消物品探索异常，返回数据：%v", err)
		return CancelStoryFail, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("取消物品探索异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return CancelStoryFail, response.Fail.SetMsg("取消物品探索失败")
	}
	log.Ctx(ctx).Infof("取消物品探索 params:%+v,rsp:%+v", params, rsp)
	return CancelStorySuccess, err
}
