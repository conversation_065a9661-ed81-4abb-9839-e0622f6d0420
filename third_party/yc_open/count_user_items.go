package yc_open

import (
	"app_service/global"
	"app_service/third_party/yc_open/define"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type CountUserItemsResp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data []*CountUserItemsRes `json:"data" form:"data"`
}

type CountUserItemsRes struct {
	ItemID string `json:"item_id"`
	Count  int32  `json:"count"`
	Cost   int64  `json:"cost"`
}

// CountUserItems 查询用户商品列表统计数据
func CountUserItems(ctx context.Context, openUserId string, itemIds []string) ([]*CountUserItemsRes, error) {
	rsp := &CountUserItemsResp{}

	params := map[string]interface{}{
		"open_user_id":         openUserId,
		"item_ids":             itemIds,
		"status_list":          []any{define.UserItemStatusOwned, define.UserItemStatusForSale},
		"ignore_receive_types": []any{define.UserItemReceiveTypeWcReSell},
		"sign":                 "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("CountUserItems sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/countUserItemsByItemIds"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询气仓物品计数，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询气仓物品计数，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询气仓物品计数失败")
	}
	return rsp.Data, err
}
