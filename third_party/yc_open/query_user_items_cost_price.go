package yc_open

import (
	"app_service/global"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type QueryUserItemsCostPriceResp struct {
	Code int32                       `json:"code" form:"code"`
	Desc string                      `json:"desc" form:"desc"`
	Data *QueryUserItemsCostPriceRes `json:"data" form:"data"`
}

type QueryUserItemsCostPriceRes struct {
	List []*QueryUserItemsCostPriceData `json:"list"`
}

type QueryUserItemsCostPriceData struct {
	ID   string `json:"_id"`
	Cost int64  `json:"cost"`
}

// QueryUserItemsCostPrice 查询物品成本价
func QueryUserItemsCostPrice(ctx context.Context, userItemId []string) ([]*QueryUserItemsCostPriceData, error) {
	rsp := &QueryUserItemsCostPriceResp{}

	params := map[string]interface{}{
		"user_item_ids": userItemId,
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryUserItemsCostPrice sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/query_user_items_cost_price"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询物品成本价，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询物品成本价，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询物品成本价失败")
	}
	return rsp.Data.List, err
}
