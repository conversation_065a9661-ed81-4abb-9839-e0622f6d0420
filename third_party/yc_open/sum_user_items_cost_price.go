package yc_open

import (
	"app_service/global"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type SumUserItemsCostPriceResp struct {
	Code int32                     `json:"code" form:"code"`
	Desc string                    `json:"desc" form:"desc"`
	Data *SumUserItemsCostPriceRes `json:"data" form:"data"`
}

type SumUserItemsCostPriceRes struct {
	TotalCost int64 `json:"total_cost"`
}

// SumUserItemsCostPrice 统计物品成本价
func SumUserItemsCostPrice(ctx context.Context, openUserId string, userItemId []string) (int64, error) {
	rsp := &SumUserItemsCostPriceResp{}

	params := map[string]interface{}{
		"open_user_id":  openUserId,
		"user_item_ids": userItemId,
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("SumUserItemsCostPrice sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return 0, err
	}

	url := "/open/user_item/sum_user_items_cost_price"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("统计物品成本价，返回数据：%v", err)
		return 0, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("统计物品成本价，返回数据：%v", rsp)
		return 0, response.Fail.SetMsg("统计物品成本价失败")
	}
	return rsp.Data.TotalCost, err
}
