package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type StoryFusionResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

const (
	StoryFusionSuccess = 1
	StoryFusionUnknown = 0
	StoryFusionFail    = -1
)

// StoryFusion 物品探索
func StoryFusion(ctx context.Context, openUserId string, orderId string, userItemIds []string, storyDestroyUserItemIds []string) (int32, error) {
	log.Ctx(ctx).Infof("StoryFusionFusion openUserId:%+v, orderId:%+v, userItemIds:%+v, storyDestroyUserItemIds:%+v", openUserId, orderId, userItemIds, storyDestroyUserItemIds)
	rsp := &StoryFusionResp{}

	params := map[string]interface{}{
		"open_user_id":                openUserId,
		"user_item_ids":               userItemIds,
		"story_destroy_user_item_ids": storyDestroyUserItemIds,
		"story_order_id":              orderId,
		"sign":                        "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("StoryFusionFusion sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("StoryFusionFusion GetAppAccess err，返回数据：%v", err)
		return StoryFusionFail, err
	}

	url := "/open/user_item/story"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)

	log.Ctx(ctx).Infof("StoryFusion SynthesisStoryFusion params:%+v", params)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("物品探索超时异常，返回数据：%v", err)
			return StoryFusionUnknown, response.Fail.SetMsg("物品探索未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("物品探索超时异常，返回数据：%v", err)
			return StoryFusionUnknown, response.Fail.SetMsg("物品探索请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("物品探索异常，返回数据：%v", err)
		return StoryFusionFail, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("物品探索异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return StoryFusionFail, response.Fail.SetMsg("物品探索失败")
	}
	log.Ctx(ctx).Infof("物品探索成功 params:%+v,rsp:%+v", params, rsp)
	return StoryFusionSuccess, err
}
