package yc_open

import (
	"app_service/global"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type CountValidUserItemsResp struct {
	Code int32                     `json:"code" form:"code"`
	Desc string                    `json:"desc" form:"desc"`
	Data []*CountValidUserItemsRes `json:"data" form:"data"`
}

type CountValidUserItemsRes struct {
	ItemID string `json:"item_id"`
	Count  int64  `json:"count"`
}

// CountValidUserItems 从云仓获取指定商品的持仓总数
func CountValidUserItems(ctx context.Context, itemIds []string) ([]*CountValidUserItemsRes, error) {
	rsp := &CountValidUserItemsResp{}

	params := map[string]interface{}{
		"item_ids": itemIds,
		"sign":     "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("CountValidUserItems sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/countValidUserItemsByItemIds"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询指定商品的持仓总数，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询指定商品的持仓总数，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询指定商品的持仓总数失败")
	}
	return rsp.Data, err
}
