package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type FusionResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data interface{} `json:"data" form:"data"`
}

const (
	FusionSuccess = 1
	FusionUnknown = 0
	FusionFail    = -1
)

// Fusion 物品融合
func Fusion(ctx context.Context, openUserId string, orderId string, userItemIds []string, synthesisDestroyUserItemIds []string) (int32, error) {
	log.Ctx(ctx).Infof("Fusion openUserId:%+v, orderId:%+v, userItemIds:%+v", openUserId, orderId, userItemIds)
	rsp := &FusionResp{}

	params := map[string]interface{}{
		"open_user_id":                 openUserId,
		"user_item_ids":                userItemIds,
		"fusion_destroy_user_item_ids": synthesisDestroyUserItemIds,
		"fusion_order_id":              orderId,
		"sign":                         "",
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("Fusion sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("Fusion GetAppAccess err，返回数据：%v", err)
		return FusionFail, err
	}

	url := "/open/user_item/fusion"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)

	log.Ctx(ctx).Infof("Fusion SynthesisFusion params:%+v", params)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("物品融合超时异常，返回数据：%v", err)
			return FusionUnknown, response.Fail.SetMsg("物品融合未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("物品融合超时异常，返回数据：%v", err)
			return FusionUnknown, response.Fail.SetMsg("物品融合请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("物品融合异常，返回数据：%v", err)
		return FusionFail, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("物品融合异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return FusionFail, response.Fail.SetMsg("物品融合失败")
	}
	log.Ctx(ctx).Infof("物品融合成功 params:%+v,rsp:%+v", params, rsp)
	return FusionSuccess, err
}
