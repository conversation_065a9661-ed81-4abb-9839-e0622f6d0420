package yc_open

import (
	"app_service/global"
	"app_service/third_party/yc_open/define"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request/pat"

	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

// 订单相关结构体
type (
	// OrderExtends 订单扩展信息
	OrderExtends struct {
		IsBuyOrder bool `json:"is_buy_order"` // 是否为直购商品
		//Remark     string `json:"remark"`       // 备注
		AddressID string `json:"address_id"` // 买家地址ID
		// AddressInfo            string              `json:"address_info"`              // 地址信息(JSON字符串)
		Address                OrderAddress        `json:"address"`                   // 地址信息
		CouponID               string              `json:"coupon_id"`                 // 使用的优惠券ID
		CouponAmount           int64               `json:"coupon_amount"`             // 优惠金额
		FreightAmount          int64               `json:"freight_amount"`            // 运费
		AdminSaleFreightAmount int64               `json:"admin_sale_freight_amount"` // 管理员调整运费
		Items                  []OrderExtendsItems `json:"items"`                     // 商品列表
		Appid                  int64               `json:"appid"`                     // 商品类型
		OrderType              define.OrderType    `json:"order_type"`                // 订单类型
		RechargeOrderID        string              `json:"recharge_order_id"`         // 充值订单ID【尾款充值订单】
		RechargeTime           *time.Time          `json:"recharge_time"`             // 充值时间【尾款支付时间】
		DepositAmount          int64               `json:"deposit_amount"`            // 定金
		BalanceDueAmount       int64               `json:"balance_due_amount"`        // 尾款
		RemindMobilePhone      string              `json:"remind_mobile_phone"`       // 订阅提醒手机号码
		DepositRechargeTime    *time.Time          `json:"deposit_recharge_time"`     // 定金支付时间
		DepositRechargeOrderID string              `json:"deposit_recharge_order_id"` // 定金支付订单ID
	}

	// OrderExtendsItems 订单扩展中的商品信息
	OrderExtendsItems struct {
		ItemID     string `json:"item_id"`      // 商品ID
		MallItemID string `json:"mall_item_id"` // 商品ID（从直购商城取回）
		// UserItemID string `json:"user_item_id"` // 用户背包ID（从用户背包取回）
		UserItemIDs      []string                       `json:"user_item_ids"`      // 用户背包ID（从用户背包取回）
		Quantity         int64                          `json:"quantity"`           // 商品数
		PayAmount        int64                          `json:"pay_amount"`         // 实付金额
		FreightCheckTime *time.Time                     `json:"freight_check_time"` // 签收时间
		Status           define.ItemWithdrawOrderStatus `json:"status"`             // 子订单状态
		FreightTime      *time.Time                     `json:"freight_time"`       // 发货时间
		FreightNo        string                         `json:"freight_no"`         // 物流单号
		ReceiveType      define.UserItemReceiveTypeEnum `json:"receive_type"`
	}

	// OrderItem 订单商品信息
	OrderItem struct {
		ID           string          `json:"_id"`            // 商品ID
		IconURL      string          `json:"icon_url"`       // 商品主图
		ItemName     string          `json:"item_name"`      // 商品名称
		ItemHashName string          `json:"item_hash_name"` // 商品英文名
		Description  string          `json:"description"`    // 商品描述
		ClassifyID   []string        `json:"classify_id"`    // 分类ID
		ShipType     define.ShipType `json:"ship_type"`      // 发货类型
		Coin         int64           `json:"coin"`           // 元气金币（单位：个，不用乘除100）
	}

	// OrderAddress 订单地址信息
	OrderAddress struct {
		Name        string `json:"name"`             // 收件人姓名
		MobilePhone string `json:"mobile_phone"`     // 收件人手机号
		Code        string `json:"code"`             // 地区编码
		Area        string `json:"area"`             // 所在地区
		Place       string `json:"place"`            // 详细地址
		Remark      string `json:"remark,omitempty"` // 备注
	}
)

// 订单详情相关结构体
type (
	// GetWithdrawOrderResp 查询取回订单响应
	GetWithdrawOrderResp struct {
		Code int32                `json:"code" form:"code"` // 状态码，0表示成功
		Desc string               `json:"desc" form:"desc"` // 状态描述
		Data *WithdrawOrderDetail `json:"data" form:"data"` // 响应数据
	}

	// WithdrawOrderDetail 取回订单详情
	WithdrawOrderDetail struct {
		ID             string                         `json:"_id"`              // 订单ID
		Extends        OrderExtends                   `json:"extends"`          // 订单扩展信息
		Status         define.ItemWithdrawOrderStatus `json:"status"`           // 订单状态
		UserItemAmount int64                          `json:"user_item_amount"` // 用户商品数量
		CreatedAt      time.Time                      `json:"created_at"`       // 创建时间
		UpdatedAt      time.Time                      `json:"updated_at"`       // 更新时间
		Expire         int64                          `json:"expire"`           // 过期时间(秒)
		AdminRemark    string                         `json:"admin_remark"`     // 管理员备注
		//User           OrderUser                      `json:"user"`             // 用户信息
		//UserID     string           `json:"user_id"` // 用户ID
		OpenUserID string `json:"open_user_id"`
		//Address    OrderAddress     `json:"address"`     // 地址信息
		AppChannel string           `json:"app_channel"` // 应用渠道
		AppVersion string           `json:"app_version"` // 应用版本
		ClientType string           `json:"client_type"` // 客户端类型
		Freights   []define.Freight `json:"freights"`    // 物流信息
	}
)

// 订单列表相关结构体
type (
	// WithdrawOrderListResp 查询订单列表响应
	WithdrawOrderListResp struct {
		Code int32                  `json:"code" form:"code"` // 状态码，0表示成功
		Desc string                 `json:"desc" form:"desc"` // 状态描述
		Data *WithdrawOrderListData `json:"data" form:"data"` // 响应数据
	}

	// WithdrawOrderListData 订单列表数据
	WithdrawOrderListData struct {
		List  []WithdrawOrderListItem `json:"list"`  // 订单列表
		Total int64                   `json:"total"` // 总数量
	}

	// WithdrawOrderListItem 订单列表项
	WithdrawOrderListItem struct {
		ID        string                         `json:"_id"`        // 订单ID
		Extends   OrderExtends                   `json:"extends"`    // 订单扩展信息
		Status    define.ItemWithdrawOrderStatus `json:"status"`     // 订单状态
		CreatedAt time.Time                      `json:"created_at"` // 创建时间
		UpdatedAt time.Time                      `json:"updated_at"` // 更新时间
		//Items     []OrderItem                    `json:"items"`      // 商品信息列表：[非空]表示该订单真实商品
		//User      OrderUser                      `json:"user"`       // 用户信息：[1]玩家, [2]主播
		//UserID     string `json:"user_id"` // 云仓用户ID
		OpenUserID string `json:"open_user_id"`
	}
)

// 订单日志相关结构体
type (
	// OrderLogsResp 订单操作日志响应
	OrderLogsResp struct {
		Code int32          `json:"code" form:"code"` // 状态码，0表示成功
		Desc string         `json:"desc" form:"desc"` // 状态描述
		Data *OrderLogsData `json:"data" form:"data"` // 响应数据
	}

	// OrderLogsData 订单操作日志数据
	OrderLogsData struct {
		List  []interface{} `json:"list"`  // 日志列表
		Total int64         `json:"total"` // 日志总数
	}
)

// 订单更新相关结构体
type (
	// UpdateOrderReq 取回订单更新请求
	UpdateOrderReq struct {
		Status      *define.ItemWithdrawOrderStatus `json:"status" form:"status"`             // 订单状态
		AdminRemark string                          `json:"admin_remark" form:"admin_remark"` // 管理员备注
		Extends     UpdateExtends                   `json:"extends" form:"extends"`           // 扩展信息
		Operator    *pat.CheckAdmJwtUserInfo        `json:"operator" form:"operator"`         // 操作员信息
	}

	// UpdateExtends 更新订单扩展信息
	UpdateExtends struct {
		FreightAmount int64 `json:"freight_amount"` // 邮费
		// AddressInfo   string       `json:"address_info"`   // 地址信息(JSON字符串)：例："{\"name\":\"airmart\",\"mobile_phone\":\"15818446166\",\"code\":\"230404\",\"area\":\"广东省|深圳市|南山区\",\"place\":\"北邮科技大厦11楼\"}"
		Address *OrderAddress `json:"address"` // 地址信息
	}
)

// 同步物流
type (
	// FreightSyncReq 同步物流请求
	FreightSyncReq struct {
		FreightNos []string `json:"freight_nos"` // 快递单号列表
	}
)

// GetWithdrawOrderV2 查询直购订单V2
func GetWithdrawOrderV2(ctx context.Context, withdrawOrderID string) (*WithdrawOrderDetail, error) {
	rsp := &GetWithdrawOrderResp{}

	params := map[string]interface{}{
		"withdraw_order_id": withdrawOrderID,
		"sign":              "",
	}

	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("GetWithdrawOrderV2 sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/withdraw_order/detail"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询直购订单V2，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询直购订单V2，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询直购订单V2失败")
	}
	return rsp.Data, err
}

// GetWithdrawOrderList 查询订单列表
func GetWithdrawOrderList(ctx context.Context, page, limit int, params map[string]interface{}) (*WithdrawOrderListData, error) {
	rsp := &WithdrawOrderListResp{}

	// 合并请求参数
	requestParams := map[string]interface{}{
		"page":        page - 1, // 云仓那边从 0 开始，本项目从 1 开始
		"limit":       limit,
		"data_source": 2, // 文潮
		"sign":        "",
	}

	// 合并其他可选参数
	for k, v := range params {
		requestParams[k] = v
	}

	sign := MD5Sign(requestParams)
	requestParams["sign"] = sign
	log.Ctx(ctx).Infof("GetWithdrawOrderList sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/withdraw_order/list"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(requestParams),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询订单列表，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询订单列表，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询订单列表失败")
	}
	return rsp.Data, err
}

// UpdateOrder 取回订单更新
func UpdateOrder(ctx context.Context, orderID string, req *UpdateOrderReq) error {
	rsp := &define.CommonResp{}

	// 构建请求参数，所有字段都是可选的
	params := map[string]interface{}{
		"sign": "",
	}

	if req.Status != nil {
		params["status"] = *req.Status
	}

	if req.AdminRemark != "" {
		params["admin_remark"] = req.AdminRemark
	}

	if req.Extends.FreightAmount != 0 || req.Extends.Address != nil {
		extends := make(map[string]interface{})
		if req.Extends.FreightAmount != 0 {
			extends["freight_amount"] = req.Extends.FreightAmount
		}
		if req.Extends.Address != nil {
			addressInfo, _ := json.Marshal(req.Extends.Address)
			extends["address_info"] = string(addressInfo)
		}
		params["extends"] = extends
	}

	if req.Operator != nil {
		params["operator"] = req.Operator
	}

	objKeysMap := map[string]interface{}{
		"extends":  struct{}{},
		"operator": struct{}{},
	}
	sign := MD5SignConvertObj(params, objKeysMap)
	params["sign"] = sign
	log.Ctx(ctx).Infof("UpdateOrder sign:%+v", sign)

	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return err
	}

	url := fmt.Sprintf("/open/withdraw_order/edit/%s", orderID)
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params), // 使用 WithParams 传递参数，对于 POST 请求会自动放入请求体
		utilRequest.WithMethodPost(),   // 使用 POST 方法
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	if err != nil {
		log.Ctx(ctx).Errorf("取回订单更新，返回数据：%v", err)
		return err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("取回订单更新，返回数据：%v", rsp)
		return response.Fail.SetMsg("取回订单更新失败")
	}
	return nil
}

// GetOrderLogs 订单操作日志
func GetOrderLogs(ctx context.Context, orderID string) (*OrderLogsData, error) {
	rsp := &OrderLogsResp{}

	params := map[string]interface{}{
		"id":   orderID,
		"sign": "",
	}

	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("GetOrderLogs sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/withdraw_order/orderLogs/" + orderID
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("订单操作日志，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("订单操作日志，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("获取订单操作日志失败")
	}
	return rsp.Data, nil
}

// FreightSync 同步物流
func FreightSync(ctx context.Context, req *FreightSyncReq) error {
	rsp := &define.CommonResp{}

	// 构建请求参数
	params := map[string]interface{}{
		"freight_nos": req.FreightNos,
		"sign":        "",
	}

	// 生成签名
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("FreightSync sign:%+v", sign)

	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return err
	}

	url := "/open/freight/freightSync"
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params), // 使用 WithParams 传递参数，对于 POST 请求会自动放入请求体
		utilRequest.WithMethodPost(),   // 使用 POST 方法
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	if err != nil {
		log.Ctx(ctx).Errorf("同步物流，返回数据：%v", err)
		return err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("同步物流，返回数据：%v", rsp)
		return response.Fail.SetMsg("同步物流失败")
	}
	return nil
}

const (
	BatchWithdrawOrderSuccess   = 1  // 明确成功
	BatchWithdrawOrderUnknown   = 0  // 未知状态
	BatchWithdrawOrderFailed    = -1 // 明确失败
	BatchWithdrawOrderLowStocks = 2  // 库存不足
)

// BatchWithdrawOrder 批量发货
func BatchWithdrawOrder(ctx context.Context, req *BatchWithdrawOrderReq) (*BatchWithdrawOrderResp, int32, error) {
	logPrefix := "[BatchWithdrawOrder]"

	rsp := &GetBatchWithdrawOrderResp{}

	// 构建请求参数
	params := map[string]interface{}{
		"user_item_ids": req.UserItemIDs,
		// "charge_account": req.ChargeAccount,
		"address_type": 2, //文潮地址
		"address_info": req.AddressInfo,
		// "coupon_id":      req.CouponID,
		// "remark":         req.Remark,
		"open_user_id": req.OpenUserID,
		"sign":         "",
	}
	if req.Operator != nil {
		params["operator"] = req.Operator
	}

	objKeysMap := map[string]interface{}{
		"address_info": struct{}{},
		"operator":     struct{}{},
	}
	sign := MD5SignConvertObj(params, objKeysMap)
	params["sign"] = sign
	log.Ctx(ctx).Infof("BatchWithdrawOrder sign:%+v", sign)

	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, BatchWithdrawOrderFailed, err
	}

	url := "/open/user_item/withdraw"
	err = utilRequest.New(ctx,
		utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url),
		utilRequest.WithParams(params), // 使用 WithParams 传递参数，对于 POST 请求会自动放入请求体
		utilRequest.WithMethodPost(),   // 使用 POST 方法
		utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken)),
	).Call(&rsp)

	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf(logPrefix+" 批量发货未知异常，返回数据：%v", err)
			return nil, BatchWithdrawOrderUnknown, response.Fail.SetMsg("批量发货未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf(logPrefix+" 批量发货超时，返回数据：%v", err)
			return nil, BatchWithdrawOrderUnknown, response.Fail.SetMsg("批量发货请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf(logPrefix+" 批量发货异常，返回数据：%v", err)
		return nil, BatchWithdrawOrderFailed, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("批量发货，返回数据：%v", rsp)
		// 识别库存不足情况
		if rsp.Code == define.YcOpenErrorCodeLowStocks {
			return nil, BatchWithdrawOrderLowStocks, response.Fail.SetMsg("批量发货失败，库存不足")
		}
		return nil, BatchWithdrawOrderFailed, response.Fail.SetMsg("批量发货失败")
	}

	if len(rsp.Data.OrderIds) == 0 {
		log.Ctx(ctx).Errorf("批量发货，返回数据：%v", rsp)
		return nil, BatchWithdrawOrderFailed, response.Fail.SetMsg("批量发货失败")
	}

	return rsp.Data, BatchWithdrawOrderSuccess, nil
}
