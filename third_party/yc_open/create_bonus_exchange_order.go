package yc_open

import (
	"app_service/global"
	"app_service/pkg/middlewares"
	"app_service/pkg/util"
	"app_service/third_party/yc_open/define"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"github.com/gin-gonic/gin"
	"net"
	"time"
)

const (
	BonusExchangeSuccess   = 1  // 明确成功
	BonusExchangeUnknown   = 0  // 未知状态（可以重试）
	BonusExchangeFailed    = -1 // 明确失败
	BonusExchangeLowStocks = 2  // 库存不足
)

type CreateBonusExchangeOrderResp struct{}

func CreateBonusExchangeOrder(ctx context.Context, req *define.BonusExchangeWithdrawOrderReq) (*define.BonusExchangeWithdrawOrderInfo, int32, error) {
	logPrefix := "[CreateBonusExchangeOrder]"
	params := map[string]interface{}{
		"open_user_id":    req.OpenUserId,
		"exchange_log_id": req.ExchangeLogId,
		"exchange_qty":    req.ExchangeQty,
		"address_id":      req.AddressId,
		"address_info":    req.AddressInfo,
		"steam_item_id":   req.SteamItemId,
		"sign":            "",
	}
	objKeysMap := map[string]interface{}{
		"address_info": struct{}{},
	}
	sign := MD5SignConvertObj(params, objKeysMap)
	params["sign"] = sign
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf(logPrefix+" GetAppAccess err，返回数据：%v", err)
		return nil, BonusExchangeFailed, err
	}

	url := "/open/withdraw_order/create_wc_bonus_exchange_order"
	reqHeaders := make([]*utilRequest.Header, 0)
	reqHeaders = append(reqHeaders, utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))
	if ginCtx, ok := ctx.(*gin.Context); ok {
		reqHeaders = append(reqHeaders, utilRequest.BuildHeader(middlewares.AppChannel, ginCtx.GetHeader(middlewares.AppChannel)))
		reqHeaders = append(reqHeaders, utilRequest.BuildHeader(middlewares.ClientType, ginCtx.GetHeader(middlewares.ClientType)))
		reqHeaders = append(reqHeaders, utilRequest.BuildHeader(middlewares.AppVersion, ginCtx.GetHeader(middlewares.AppVersion)))
	}
	rsp := &define.BonusExchangeWithdrawOrderResp{}
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(reqHeaders...)).Call(&rsp)

	log.Ctx(ctx).Infof(logPrefix+" params:%+v", params)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf(logPrefix+" 积分兑换未知异常，返回数据：%v", err)
			return nil, BonusExchangeUnknown, response.Fail.SetMsg("积分兑换未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf(logPrefix+" 积分兑换超时，返回数据：%v", err)
			return nil, BonusExchangeUnknown, response.Fail.SetMsg("积分兑换请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf(logPrefix+" 积分兑换异常，返回数据：%v", err)
		return nil, BonusExchangeFailed, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf(logPrefix+" 积分兑换异常，返回数据：%v", util.Obj2JsonStr(rsp))
		// 识别库存不足情况
		if rsp.Code == define.YcOpenErrorCodeLowStocks {
			return nil, BonusExchangeLowStocks, response.Fail.SetMsg("积分兑换失败，库存不足")
		}
		return nil, BonusExchangeFailed, response.Fail.SetMsg("积分兑换失败")
	}

	return &rsp.Data, BonusExchangeSuccess, nil
}
