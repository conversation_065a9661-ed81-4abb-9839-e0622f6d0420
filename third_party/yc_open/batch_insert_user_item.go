package yc_open

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type BatchInsertUserItemReq struct {
	OpenUserId    string             `json:"open_user_id"`    // 用户openUserId
	ReceiveType   int32              `json:"receive_type"`    // 发放来源
	Items         []*BatchInsertItem `json:"items"`           // 物品信息
	SourceOrderId string             `json:"source_order_id"` // 来源订单ID
}

type BatchInsertItem struct {
	ItemId       string     `json:"item_id"`       // 物品id
	ItemNum      int32      `json:"item_num"`      // 数量
	SaleMode     int32      `json:"sale_mode"`     // 销售模式
	DeliveryTime *time.Time `json:"delivery_time"` // 提货时间
	BuyPrice     int64      `json:"buy_price"`     // 买入价格
	StoryTags    int32      `json:"story_tags"`    // 故事玩法标签
	FusionTags   int32      `json:"fusion_tags"`   // 融合玩法标签
}

type BatchInsertUserItemData struct {
}

type BatchInsertUserItemRsp struct {
	Code int32                   `json:"code" form:"code"`
	Desc string                  `json:"desc" form:"desc"`
	Data BatchInsertUserItemData `json:"data" form:"data"`
}

const (
	BatchInsertUserItemSuccess = 1
	BatchInsertUserItemUnknown = 0
	BatchInsertUserItemFail    = -1
)

// BatchInsertUserItem 批量发放物品
func BatchInsertUserItem(ctx context.Context, form *BatchInsertUserItemReq) (int32, error) {
	log.Ctx(ctx).Infof("BatchInsertUserItem form:%+v", util.Obj2JsonStr(form))
	rsp := &BatchInsertUserItemRsp{}

	params := map[string]interface{}{
		"user_id":         form.OpenUserId,
		"receive_type":    form.ReceiveType,
		"items":           form.Items,
		"source_order_id": form.SourceOrderId,
	}
	var itemSize []int32
	for i := 0; i < len(form.Items); i++ {
		itemSize = append(itemSize, int32(i))
	}
	convertObjKeyMap := map[string]interface{}{
		"items": itemSize,
	}
	sign := MD5SignConvertObj(params, convertObjKeyMap)
	params["sign"] = sign
	log.Ctx(ctx).Infof("BatchInsertUserItem sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		log.Ctx(ctx).Errorf("BatchInsertUserItem GetAppAccess err，返回数据：%v", err)
		return BatchInsertUserItemFail, err
	}

	url := "/open/user_item/batch_insert_user_item"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(), utilRequest.WithTimeOut(time.Second*60),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			// 未知异常
			log.Ctx(ctx).Errorf("批量发放物品超时异常，返回数据：%v", err)
			return BatchInsertUserItemUnknown, response.Fail.SetMsg("批量发放物品未知异常")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("批量发放物品超时异常，返回数据：%v", err)
			return BatchInsertUserItemUnknown, response.Fail.SetMsg("批量发放物品请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("批量发放物品异常，返回数据：%v", err)
		return BatchInsertUserItemFail, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("批量发放物品异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return BatchInsertUserItemFail, response.Fail.SetMsg("批量发放物品失败")
	}
	log.Ctx(ctx).Infof("批量发放物品 params:%+v,rsp:%+v", params, rsp)
	return BatchInsertUserItemSuccess, err
}
