package yc_open

import (
	"app_service/global"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type QueryUserItemsResp struct {
	Code int32              `json:"code" form:"code"`
	Desc string             `json:"desc" form:"desc"`
	Data *QueryUserItemsRes `json:"data" form:"data"`
}

type QueryUserItemsRes struct {
	List  []*QueryUserItemsData `json:"list" form:"list"`
	Total int64                 `json:"total"`
}

type QueryUserItemsData struct {
	ID         string `json:"_id"`
	ItemID     string `json:"item_id"`
	CostPrice  int64  `json:"cost"`
	FusionTags int32  `json:"fusion_tags"`
	StoryTags  int32  `json:"story_tags"`
}

// QueryUserItems 查询用户商品列表
func QueryUserItems(ctx context.Context, openUserId string, itemId string, buyPriceSort *int32, page, pageSize int) (*QueryUserItemsRes, error) {
	rsp := &QueryUserItemsResp{}

	params := map[string]interface{}{
		"page":         page - 1, // 云仓那边从 0 开始，本项目从 1 开始
		"limit":        pageSize,
		"open_user_id": openUserId,
		"item_id":      itemId,
		"sign":         "",
	}
	if buyPriceSort != nil && *buyPriceSort != 0 {
		params["buy_price_sort"] = *buyPriceSort
	}
	sign := MD5Sign(params)
	params["sign"] = sign
	log.Ctx(ctx).Infof("QueryUserItems sign:%+v", sign)
	appAccessRes, err := GetAppAccess(ctx)
	if err != nil {
		return nil, err
	}

	url := "/open/user_item/query_user_items"
	err = utilRequest.New(ctx, utilRequest.WithUrl(global.GlobalConfig.Yc.Host+url), utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithHeaders(utilRequest.BuildHeader("Authorization", "Bearer "+appAccessRes.AccessToken))).Call(&rsp)
	if err != nil {
		log.Ctx(ctx).Errorf("查询用户商品列表，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询用户商品列表，返回数据：%v", rsp)
		return nil, response.Fail.SetMsg("查询用户商品列表失败")
	}
	return rsp.Data, err
}
