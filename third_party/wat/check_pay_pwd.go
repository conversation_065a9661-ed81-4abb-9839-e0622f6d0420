package wat

import (
	"context"
	"errors"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
)

// CheckPayPwdReq 检查支付密码请求参数
type CheckPayPwdReq struct {
	UserId string `json:"user_id"` // 用户ID
	Pwd    string `json:"pwd"`     // 支付密码
}

// CheckPayPwdResp 检查支付密码响应
type CheckPayPwdResp struct {
	Code int32  `json:"code"`
	Desc string `json:"desc"`
	Data string `json:"data"`
}

// CheckPayPwd 检查用户支付密码是否正确
func CheckPayPwd(ctx context.Context, req *CheckPayPwdReq) error {
	rsp := &CheckPayPwdResp{}

	watReq, err := request.Wat()
	if err != nil {
		return err
	}

	params := map[string]interface{}{
		"user_id": req.UserId,
		"pwd":     req.Pwd,
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(params),
		utilRequest.WithMethodGet(),
	}

	err = watReq.Call(
		ctx,
		"open/wallet/v1/check_user_wallet_pay_pwd",
		&rsp,
		opts...,
	)
	if err != nil {
		return err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("校验用户支付密码失败, params:%+v, res:%+v", params, rsp)
		return errors.New(rsp.Desc)
	}

	return nil
}
