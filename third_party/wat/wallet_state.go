package wat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
)

type GetWalletStateResp struct {
	Code int32        `json:"code" form:"code"`
	Desc string       `json:"desc" form:"desc"`
	Data *WalletState `json:"data" form:"data"`
}

type WalletState struct {
	Id       string             `json:"_id,omitempty" form:"_id"`
	WalletId string             `json:"wallet_id,omitempty" form:"wallet_id"`
	Summary  WalletStateSummary `json:"summary,omitempty" form:"summary"`
}

type WalletStateSummary struct {
	SpendAmounts    int32 `json:"spend_amounts,omitempty" form:"spend_amounts"`
	SpendTimes      int32 `json:"spend_times,omitempty" form:"spend_times"`
	RechargeAmounts int32 `json:"recharge_amounts,omitempty" form:"recharge_amounts"`
	RechargeTimes   int32 `json:"recharge_times,omitempty" form:"recharge_times"`
}

// GetWalletState 获取用户累计充值金额
func GetWalletState(ctx context.Context, userId string) (*GetWalletStateResp, error) {
	rsp := &GetWalletStateResp{}

	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_id": userId,
		}),
		utilRequest.WithMethodGet(),
	}
	err = req.Call(
		ctx,
		"open/wallet/v1/wallet_state",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return rsp, err
}
