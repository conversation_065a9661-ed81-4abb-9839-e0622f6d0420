package wat

import (
	"app_service/pkg/util"
	"app_service/third_party/base"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"
)

type OpUserGiftReq struct {
	UserId     string `json:"user_id"`     // 必传
	FiId       string `json:"fi_id"`       // fffffffffffffffffff00041
	Amount     int32  `json:"amount"`      // 金额
	Currency   string `json:"currency"`    // CNY
	GiftType   int32  `json:"gift_type"`   // 1:礼金;2:补偿;11:活动礼金;21:代理奖励;
	GiftReason string `json:"gift_reason"` // 备注
}

// OpUserGift 赠送米粒
func OpUserGift(ctx context.Context, form *OpUserGiftReq) (*base.Rsp, error) {
	rsp := &base.Rsp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":     form.UserId,
		"fi_id":       form.FiId,
		"amount":      form.Amount,
		"currency":    form.Currency,
		"gift_type":   form.GiftType,
		"gift_reason": form.GiftReason,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/op_user_gift",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("赠送米粒异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return rsp, response.Fail.SetMsg("赠送米粒失败")
	}
	return rsp, nil
}
