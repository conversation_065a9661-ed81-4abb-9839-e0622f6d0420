package wat

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"
)

type SumSpendAmountReq struct {
	UserIds   []string `json:"user_ids"`       // 必传
	StartTime string   `json:"start_time_str"` // 开始时间 // 2024071912
	EndTime   string   `json:"end_time_str"`   // 结束时间 // 2024071912
}

type SumSpendAmountData struct {
	UserId      string `json:"userId"`
	TotalAmount int32  `json:"totalAmount"`
}

type SumSpendAmountRsp struct {
	Code int32                 `json:"code" form:"code"`
	Desc string                `json:"desc" form:"desc"`
	Data []*SumSpendAmountData `json:"data" form:"data"`
}

// SumSpendAmount 消费金额统计查询
func SumSpendAmount(ctx context.Context, form *SumSpendAmountReq) (*SumSpendAmountRsp, error) {
	rsp := &SumSpendAmountRsp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_ids":       form.UserIds,
		"start_time_str": form.StartTime,
		"end_time_str":   form.EndTime,
	}

	err = req.Call(
		ctx,
		"open/wallet/order/v1/recharge/sum_spend_amount",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("消费金额统计查询异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("消费金额统计查询失败")
	}
	return rsp, nil
}
