package wat

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"
)

type TransferReq struct {
	UserId        string `json:"user_id"`         // 必传
	TokenAmount   int32  `json:"token_amount"`    // 米粒数量
	CoinAmount    int32  `json:"coin_amount"`     // 元气金币数量
	BalanceAmount int32  `json:"balance_amount"`  // 余额(分)
	SourceOrderId string `json:"source_order_id"` // 关联的订单id
	Transsn       int32  `json:"transsn"`
}

type TransferInfo struct {
	TokenAmount   int32 `json:"token_amount"`   // 米粒数量
	CoinAmount    int32 `json:"coin_amount"`    // 元气金币数量
	BalanceAmount int32 `json:"balance_amount"` // 余额(分)
}

type GetTransferRsp struct {
	Code int32        `json:"code" form:"code"`
	Desc string       `json:"desc" form:"desc"`
	Data TransferInfo `json:"data" form:"data"`
}

// Transfer 转账
func Transfer(ctx context.Context, f TransferReq) (*GetTransferRsp, error) {
	rsp := &GetTransferRsp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":         f.UserId,
		"token_amount":    f.TokenAmount,
		"balance_amount":  f.BalanceAmount,
		"coin_amount":     f.CoinAmount,
		"source_order_id": f.SourceOrderId,
		"transsn":         f.Transsn,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/transfer",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("转账异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("转账失败")
	}
	return rsp, nil
}
