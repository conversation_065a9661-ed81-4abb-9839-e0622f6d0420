package wat

import (
	"app_service/pkg/util"
	"app_service/third_party/base"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"
)

type OpUserTokenDeductionReq struct {
	UserId          string `json:"user_id"`          // 必传
	FiId            string `json:"fi_id"`            // fffffffffffffffffff00042
	Amount          int32  `json:"amount"`           // 金额
	DeductionType   int32  `json:"deduction_type"`   // 1:扣款
	DeductionReason string `json:"deduction_reason"` // 备注
}

// OpUserTokenDeduction 扣除米粒
func OpUserTokenDeduction(ctx context.Context, form *OpUserTokenDeductionReq) (*base.Rsp, error) {
	rsp := &base.Rsp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":          form.UserId,
		"fi_id":            form.FiId,
		"amount":           form.Amount,
		"deduction_type":   form.DeductionType,
		"deduction_reason": form.DeductionReason,
	}

	err = req.Call(
		ctx,
		"open/wallet/v1/op_user_token_deduction",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("扣除米粒异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return rsp, response.Fail.SetMsg("扣除米粒失败")
	}
	return rsp, nil
}
