package wat

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"time"
)

type SumAmountReq struct {
	UserIds   []string `json:"user_ids"`   // 必传
	StartTime string   `json:"start_time"` // 开始时间
	EndTime   string   `json:"end_time"`   // 结束时间
}

type SumAmountData struct {
	UserId      string `json:"userId"`
	TotalAmount int32  `json:"totalAmount"`
}

type SumAmountRsp struct {
	Code int32            `json:"code" form:"code"`
	Desc string           `json:"desc" form:"desc"`
	Data []*SumAmountData `json:"data" form:"data"`
}

// SumAmount 充值金额统计查询
func SumAmount(ctx context.Context, form *SumAmountReq) (*SumAmountRsp, error) {
	rsp := &SumAmountRsp{}
	req, err := request.Wat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_ids":   form.UserIds,
		"start_time": form.StartTime,
		"end_time":   form.EndTime,
	}

	err = req.Call(
		ctx,
		"open/wallet/order/v1/recharge/sum_amount",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("充值金额统计查询异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("充值金额统计查询失败")
	}
	return rsp, nil
}
