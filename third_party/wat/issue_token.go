package wat

import (
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/mor"
	"context"
	log "e.coding.net/g-dtay0385/common/go-logger"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
)

type IssueTokenForm struct {
	UserId        string     `json:"user_id" form:"user_id"`                 // 必传
	TokenAmount   int32      `json:"token_amount" form:"token_amount"`       // 米粒数量
	SourceOrderId string     `json:"source_order_id" form:"source_order_id"` // 关联的订单id
	Transsn       TransferSN `json:"transsn" form:"transsn"`
}

// IssueToken 发放米粒
func IssueToken(ctx context.Context, f *IssueTokenForm) (*GetTransferRsp, error) {
	// 转账
	rsp, err := Transfer(ctx, TransferReq{
		UserId:        f.UserId,
		TokenAmount:   f.<PERSON>,
		SourceOrderId: f.SourceOrderId,
		Transsn:       f.Transsn.Val(),
	})
	if err != nil {
		log.Ctx(ctx).Errorf("发放米粒异常，返回数据：%v", rsp)
		if errors.Is(err, context.Canceled) {
			mor.SendWarnMsg(context.Background(), global.GlobalConfig.WarnId, fmt.Sprintf("发放米粒异常,请求数据：%+v,返回数据：%+v", util.Obj2JsonStr(f), err))
		}
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg("发放米粒失败")
	}

	return rsp, nil
}
