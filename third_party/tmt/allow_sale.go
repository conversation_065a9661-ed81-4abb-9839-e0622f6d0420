package tmt

import (
	"app_service/global"
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
	"net"
	"time"
)

type AllowSaleRsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"`
}

// AllowSale 是否在交易时间
func AllowSale(ctx context.Context) (bool, error) {
	rsp := &AllowSaleRsp{}
	req, err := request.Tmt()
	if err != nil {
		return false, err
	}

	err = req.Call(
		ctx,
		"open/sale_order/v1/allow_sale",
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return false, response.Fail.SetMsg("查询是否在交易时间请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("查询是否在交易时间超时异常，返回数据：%v", err)
			return false, response.Fail.SetMsg("查询是否在交易时间请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("查询是否在交易时间异常，返回数据：%v", err)
		return false, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询是否在交易时间异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return false, response.Fail.SetMsg("查询是否在交易时间失败")
	}
	return rsp.Data, err
}

type IsHolidayRsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data bool   `json:"data" form:"data"`
}

// IsHoliday 是否假日
func IsHoliday(ctx context.Context) (bool, error) {
	// 优先从缓存取
	now := util.Now()
	cacheKey := fmt.Sprintf("app_service:is_holiday:%s", now.Format("2006-01-02"))
	exists, err := global.REDIS.Exists(ctx, cacheKey).Result()
	if err != nil {
		return false, err
	}
	if exists == 1 {
		isHoliday, err := global.REDIS.Get(ctx, cacheKey).Bool()
		if err != nil {
			return false, err
		}
		return isHoliday, nil
	}

	rsp := &IsHolidayRsp{}
	req, err := request.Tmt()
	if err != nil {
		return false, err
	}

	err = req.Call(
		ctx,
		"open/sale_order/v1/is_holiday",
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return false, response.Fail.SetMsg("查询是否在交易时间请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("查询是否假日超时异常，返回数据：%v", err)
			return false, response.Fail.SetMsg("查询是否假日请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("查询是否假日异常，返回数据：%v", err)
		return false, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询是否假日异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return false, response.Fail.SetMsg("查询是否假日失败")
	}

	// 缓存数据
	todayEnd := util.GetEndOfDay(now)
	_, err = global.REDIS.Set(ctx, cacheKey, rsp.Data, todayEnd.Sub(now)).Result()
	if err != nil {
		log.Ctx(ctx).Errorf("cache is_holiday to redis error: %v", err)
	}

	return rsp.Data, err
}
