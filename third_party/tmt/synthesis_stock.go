package tmt

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type SynthesisStockReq struct {
	ItemId   string `json:"item_id"`
	Quantity int32  `json:"quantity"`
}

type SynthesisStockRsp struct {
	Code int32               `json:"code" form:"code"`
	Desc string              `json:"desc" form:"desc"`
	Data *SynthesisStockData `json:"data" form:"data"`
}

type SynthesisStockData struct {
	Status int32 `json:"status"`
}

const (
	SynthesisStockSuccess = 1
	SynthesisStockUnknown = 0
	SynthesisStockFail    = -1
)

// SynthesisStock 融合玩法库存控制
func SynthesisStock(ctx context.Context, form *SynthesisStockReq) (*SynthesisStockData, error) {
	rsp := &SynthesisStockRsp{}
	req, err := request.Tmt()
	data := &SynthesisStockData{}
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"item_id":  form.ItemId,
		"quantity": form.Quantity,
	}

	log.Ctx(ctx).Infof("SynthesisStock params：%v", util.Obj2JsonStr(params))
	err = req.Call(
		ctx,
		"open/issue_item/synthesis_stock",
		&rsp,
		utilRequest.WithMethodPost(),
		utilRequest.WithParams(params),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			data.Status = SynthesisStockUnknown
			return data, response.Fail.SetMsg("融合玩法库存控制请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("融合玩法库存控制超时异常，返回数据：%v", err)
			data.Status = SynthesisStockUnknown
			return data, response.Fail.SetMsg("融合玩法库存控制请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("融合玩法库存控制异常，返回数据：%v", err)
		data.Status = SynthesisStockFail
		return data, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("融合玩法库存控制异常，返回数据：%v", util.Obj2JsonStr(rsp))
		data.Status = SynthesisStockFail
		return data, response.Fail.SetMsg("融合玩法库存控制失败")
	}
	data = rsp.Data
	data.Status = SynthesisStockSuccess
	log.Ctx(ctx).Infof("融合玩法库存控制params：%v,返回数据：%v", util.Obj2JsonStr(params), util.Obj2JsonStr(data))
	return data, err
}
