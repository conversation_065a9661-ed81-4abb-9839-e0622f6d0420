package tmt

import (
	"app_service/pkg/util"
	"context"
	"errors"
	"net"
	"time"

	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type Address struct {
	ID          string    `json:"id" form:"id"`
	Name        string    `json:"name" form:"name"`
	MobilePhone string    `json:"mobile_phone" form:"mobile_phone"`
	UserID      string    `json:"user_id" form:"user_id"`
	Status      int32     `json:"status" form:"status"`
	CreatedAt   time.Time `json:"created_at" form:"created_at"`
	UpdatedAt   time.Time `json:"updated_at" form:"updated_at"`
	IsDefault   bool      `json:"is_default" form:"is_default"`
	Code        string    `json:"code" form:"code"`
	Area        string    `json:"area" form:"area"`
	Place       string    `json:"place" form:"place"`
	Remark      string    `json:"remark" form:"remark"`
}

type AddressDetailRsp struct {
	Code int32    `json:"code" form:"code"`
	Desc string   `json:"desc" form:"desc"`
	Data *Address `json:"data" form:"data"`
}

type AddressListRsp struct {
	Code int32      `json:"code" form:"code"`
	Desc string     `json:"desc" form:"desc"`
	Data []*Address `json:"data" form:"data"`
}

// GetDefaultAddressDetail 获取默认地址
func GetDefaultAddressDetail(ctx context.Context, userId string) (*Address, error) {
	rsp := &AddressDetailRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}

	err = req.Call(
		ctx,
		"open/user/v1/default_address/"+userId,
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return nil, response.Fail.SetMsg("查询默认地址请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("查询默认地址超时异常，返回数据：%v", err)
			return nil, response.Fail.SetMsg("查询默认地址请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("查询默认地址异常，返回数据：%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询默认地址异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("查询默认地址失败")
	}

	return rsp.Data, err
}

// GetUserAddressList 获取用户地址列表
func GetUserAddressList(ctx context.Context, userId string) ([]*Address, error) {
	rsp := &AddressListRsp{}
	req, err := request.Tmt()
	if err != nil {
		return nil, err
	}

	// 构建查询参数，获取启用状态的地址，按创建时间排序
	err = req.Call(
		ctx,
		"open/user/v1/address_list/"+userId,
		&rsp,
		utilRequest.WithMethodGet(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			return nil, response.Fail.SetMsg("查询用户地址列表请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			log.Ctx(ctx).Errorf("查询用户地址列表超时异常，返回数据：%v", err)
			return nil, response.Fail.SetMsg("查询用户地址列表请求超时")
		}
		log.Ctx(ctx).Errorf("查询用户地址列表异常，返回数据：%v", err)
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("查询用户地址列表异常，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("查询用户地址列表失败")
	}

	return rsp.Data, nil
}
