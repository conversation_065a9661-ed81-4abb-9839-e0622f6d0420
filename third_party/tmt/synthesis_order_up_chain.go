package tmt

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type SynthesisOrderUpChainReq struct {
	OrderId        string `json:"order_id"`
	OrderType      int32  `json:"order_type"`
	ItemName       string `json:"item_name"`
	ItemId         string `json:"item_id"`
	SkuNo          string `json:"sku_no"`
	ItemChainHash  string `json:"item_chain_hash"`
	BenefitName    string `json:"BenefitName"`
	BenefitID      string `json:"BenefitID"`
	Price          int32  `json:"price"`
	Quantity       int32  `json:"quantity"`
	TotalAmount    int32  `json:"total_amount"`
	PayAmount      int32  `json:"pay_amount"`
	TradeType      int32  `json:"trade_type"`
	Fee            int32  `json:"fee"`
	FromUserName   string `json:"from_user_name"`
	FromUserId     string `json:"from_user_id"`
	FromItemHash   string `json:"from_item_hash"`
	ToUserName     string `json:"to_user_name"`
	ToUserId       string `json:"to_user_id"`
	CreatedAt      string `json:"created_at"`
	PaidAt         string `json:"paid_at"`
	FinishedAt     string `json:"finished_at"`
	CancelApplyAt  string `json:"cancel_apply_at"`
	CancelDoneAt   string `json:"cancel_done_at"`
	FusionMaterial string `json:"FusionMaterial"`
	ChainDataId    string `json:"chain_data_id"`
}

type SynthesisOrderUpChainRsp struct {
	Code int32                      `json:"code" form:"code"`
	Desc string                     `json:"desc" form:"desc"`
	Data *SynthesisOrderUpChainData `json:"data" form:"data"`
}

type SynthesisOrderUpChainData struct {
	ChainHash   string `json:"chain_hash" form:"chain_hash"`
	ChainDataId string `json:"chain_data_id" form:"chain_data_id"`
	Status      int32  `json:"status" form:"status"`
}

const (
	SynthesisOrderUpChainSuccess = 1
	SynthesisOrderUpChainUnknown = 0
	SynthesisOrderUpChainFail    = -1
)

// SynthesisOrderUpChain 合成订单上链
func SynthesisOrderUpChain(ctx context.Context, form *SynthesisOrderUpChainReq) (*SynthesisOrderUpChainData, error) {
	rsp := &SynthesisOrderUpChainRsp{}
	req, err := request.Tmt()
	data := &SynthesisOrderUpChainData{}
	if err != nil {
		return nil, err
	}

	synthesisData := map[string]interface{}{
		"order_id":        form.OrderId,
		"order_type":      form.OrderType,
		"item_name":       form.ItemName,
		"item_id":         form.ItemId,
		"sku_no":          form.SkuNo,
		"item_chain_hash": form.ItemChainHash,
		"BenefitName":     form.BenefitName,
		"BenefitID":       form.BenefitID,
		"price":           form.Price,
		"quantity":        form.Quantity,
		"total_amount":    form.TotalAmount,
		"pay_amount":      form.PayAmount,
		"trade_type":      form.TradeType,
		"fee":             form.Fee,
		"from_user_name":  form.FromUserName,
		"from_user_id":    form.FromUserId,
		"from_item_hash":  form.FromItemHash,
		"to_user_name":    form.ToUserName,
		"to_user_id":      form.ToUserId,
		"created_at":      form.CreatedAt,
		"paid_at":         form.PaidAt,
		"finished_at":     form.FinishedAt,
		"cancel_apply_at": form.CancelApplyAt,
		"cancel_done_at":  form.CancelDoneAt,
		"FusionMaterial":  form.FusionMaterial,
	}

	params := map[string]interface{}{
		"synthesis_data": synthesisData,
		"data_id":        form.ChainDataId,
	}

	log.Ctx(ctx).Infof("SynthesisOrderUpChain params：%v", util.Obj2JsonStr(params))
	err = req.Call(
		ctx,
		"open/issue_item/synthesis_up_chain",
		&rsp,
		utilRequest.WithMethodPost(),
		utilRequest.WithParams(params),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			data.Status = SynthesisOrderUpChainUnknown
			return data, response.Fail.SetMsg("合成订单上链请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("合成订单上链超时异常，返回数据：%v", err)
			data.Status = SynthesisOrderUpChainUnknown
			return data, response.Fail.SetMsg("合成订单上链请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("合成订单上链异常，返回数据：%v", err)
		data.Status = SynthesisOrderUpChainFail
		return data, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("合成订单上链异常，返回数据：%v", util.Obj2JsonStr(rsp))
		data.Status = SynthesisOrderUpChainFail
		return data, response.Fail.SetMsg("合成订单上链失败")
	}
	data = rsp.Data
	data.Status = SynthesisOrderUpChainSuccess
	log.Ctx(ctx).Infof("合成订单上链params：%v,返回数据：%v", util.Obj2JsonStr(params), util.Obj2JsonStr(data))
	return data, err
}
