package tmt

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type StoryStockReq struct {
	ItemId   string `json:"item_id"`
	Quantity int32  `json:"quantity"`
}

type StoryStockRsp struct {
	Code int32           `json:"code" form:"code"`
	Desc string          `json:"desc" form:"desc"`
	Data *StoryStockData `json:"data" form:"data"`
}

type StoryStockData struct {
	Status int32 `json:"status"`
}

const (
	StoryStockSuccess = 1
	StoryStockUnknown = 0
	StoryStockFail    = -1
)

// StoryStock 故事玩法库存控制
func StoryStock(ctx context.Context, form *StoryStockReq) (*StoryStockData, error) {
	rsp := &StoryStockRsp{}
	req, err := request.Tmt()
	data := &StoryStockData{}
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"item_id":  form.ItemId,
		"quantity": form.Quantity,
	}

	log.Ctx(ctx).Infof("StoryStock params：%v", util.Obj2JsonStr(params))
	err = req.Call(
		ctx,
		"open/issue_item/story_stock",
		&rsp,
		utilRequest.WithMethodPost(),
		utilRequest.WithParams(params),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			data.Status = StoryStockUnknown
			return data, response.Fail.SetMsg("故事玩法库存控制请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("故事玩法库存控制超时异常，返回数据：%v", err)
			data.Status = StoryStockUnknown
			return data, response.Fail.SetMsg("故事玩法库存控制请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("故事玩法库存控制异常，返回数据：%v", err)
		data.Status = StoryStockFail
		return data, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("故事玩法库存控制异常，返回数据：%v", util.Obj2JsonStr(rsp))
		data.Status = StoryStockFail
		return data, response.Fail.SetMsg("故事玩法库存控制失败")
	}
	data = rsp.Data
	data.Status = StoryStockSuccess
	log.Ctx(ctx).Infof("故事玩法库存控制params：%v,返回数据：%v", util.Obj2JsonStr(params), util.Obj2JsonStr(data))
	return data, err
}
