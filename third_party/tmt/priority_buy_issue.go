package tmt

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"net"
	"time"
)

type PriorityBuyIssueReq struct {
	PriorityBuyId string `json:"priority_buy_id"` // 优先购权益ID
	UserId        string `json:"user_id"`         // 优先购权益ID
	Quantity      int32  `json:"quantity"`        // 发放数量
	IssueType     int32  `json:"issue_type"`      // 发放类型 2-合成
	SourceOrderId string `json:"source_order_id"` // 优先购权益ID
}

type PriorityBuyIssueData struct {
	IssueRecordId string `json:"issue_record_id"` // 发放记录ID
}

type PriorityBuyIssueRsp struct {
	Code int32                `json:"code" form:"code"`
	Desc string               `json:"desc" form:"desc"`
	Data PriorityBuyIssueData `json:"data" form:"data"`
}

type PriorityBuyIssueRes struct {
	Status        int32  `json:"status"`
	IssueRecordId string `json:"issue_record_id"` // 发放记录ID
}

const (
	PriorityBuyIssueSuccess = 1
	PriorityBuyIssueUnknown = 0
	PriorityBuyIssueFail    = -1
)

// PriorityBuyIssue 优先购权益发放
func PriorityBuyIssue(ctx context.Context, form *PriorityBuyIssueReq) (*PriorityBuyIssueRes, error) {
	rsp := &PriorityBuyIssueRsp{}
	res := &PriorityBuyIssueRes{}
	req, err := request.Tmt()
	if err != nil {
		res.Status = PriorityBuyIssueFail
		return res, err
	}

	params := map[string]interface{}{
		"priority_buy_id": form.PriorityBuyId,
		"user_id":         form.UserId,
		"quantity":        form.Quantity,
		"issue_type":      form.IssueType,
		"source_order_id": form.SourceOrderId,
	}

	err = req.Call(
		ctx,
		"open/v1/priority_buy/issue",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			res.Status = PriorityBuyIssueUnknown
			return res, response.Fail.SetMsg("优先购权益发放请求超时")
		}
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			// 未知异常
			log.Ctx(ctx).Errorf("优先购权益发放超时异常，返回数据：%v", err)
			res.Status = PriorityBuyIssueUnknown
			return res, response.Fail.SetMsg("优先购权益发放请求超时")
		}
		// 明确异常
		log.Ctx(ctx).Errorf("优先购权益发放异常，返回数据：%v", err)
		res.Status = PriorityBuyIssueFail
		return res, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("优先购权益发放异常，返回数据：%v", util.Obj2JsonStr(rsp))
		res.Status = PriorityBuyIssueFail
		return res, response.Fail.SetMsg("优先购权益发放失败")
	}
	res.Status = PriorityBuyIssueSuccess
	res.IssueRecordId = rsp.Data.IssueRecordId
	return res, err
}
