package pat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
)

type GetRobotReq struct {
	Count   int32    `json:"count" form:"count"`
	UserIds []string `json:"user_ids" form:"user_ids"`
}
type GetRobotResp struct {
	Code int32               `json:"code" form:"code"`
	Desc string              `json:"desc" form:"desc"`
	Data *GetRobotDetailData `json:"data" form:"data"`
}

type GetRobotDetailData struct {
	GetRobotData []*GetRobotDetail `json:"list" form:"list"`
}

type GetRobotDetail struct {
	Id          string `json:"_id,omitempty" form:"_id"`
	MobilePhone string `json:"mobile_phone,omitempty" form:"mobile_phone"`
	Status      int32  `json:"status,omitempty" form:"status"`
	Nickname    string `json:"nickname,omitempty" form:"nickname"`
	Avatar      string `json:"avatar,omitempty" form:"avatar"`
	Type        int32  `json:"type,omitempty" form:"type"`
}

// GetRobot 随机获取机器人
func GetRobot(ctx context.Context, form *GetRobotReq) (*GetRobotResp, error) {
	rsp := &GetRobotResp{}

	req, err := request.Pat()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"count": form.Count,
	}
	if len(form.UserIds) > 0 {
		params["user_ids"] = form.UserIds
	}
	opts := []utilRequest.Option{
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
	}
	err = req.Call(
		ctx,
		"open/user/v1/get_robot",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return rsp, err
}
