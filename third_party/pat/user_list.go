package pat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"math/rand"
	"time"
)

type GetUserListReq struct {
	Page        int32  `json:"page" form:"page"`
	Limit       int32  `json:"limit" form:"limit"`
	QueryGroups string `json:"queryGroups" form:"queryGroups"`
}
type GetUserListResp struct {
	Code int32            `json:"code" form:"code"`
	Desc string           `json:"desc" form:"desc"`
	Data *GetUserListData `json:"data" form:"data"`
}

type GetUserListData struct {
	GetUserListData []*GetUserListDetail `json:"list" form:"list"`
}

type GetUserListDetail struct {
	Id          string `json:"_id,omitempty" form:"_id"`
	MobilePhone string `json:"mobile_phone,omitempty" form:"mobile_phone"`
	Status      int32  `json:"status,omitempty" form:"status"`
	Nickname    string `json:"nickname,omitempty" form:"nickname"`
	Avatar      string `json:"avatar,omitempty" form:"avatar"`
	Type        int32  `json:"type,omitempty" form:"type"`
}

// GetUserList 获取用户信息列表
func GetUserList(ctx context.Context, form *GetUserListReq) (*GetUserListResp, error) {
	rsp := &GetUserListResp{}

	req, err := request.Pat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"page":        form.Page,
			"limit":       form.Limit,
			"queryGroups": form.QueryGroups,
		}),
		utilRequest.WithMethodGet(),
	}
	err = req.Call(
		ctx,
		"open/user/v1/list",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return rsp, err
}

// SelectRandomUsers 从用户列表中随机选择指定数量的数据
func SelectRandomUsers(data []*GetUserListDetail, addCount int32) []*GetUserListDetail {
	rand.Seed(time.Now().UnixNano()) // 初始化随机数生成器
	// 确保 addCount 不超过数据长度
	if int32(len(data)) < addCount {
		addCount = int32(len(data))
	}

	selectedIndices := make(map[int]bool)                    // 存储已选择的索引
	selectedUsers := make([]*GetUserListDetail, 0, addCount) // 存储随机选择的用户

	// 随机选择 addCount 个不同的索引
	for int32(len(selectedIndices)) < addCount {
		index := rand.Intn(len(data)) // 生成一个随机索引
		if !selectedIndices[index] {  // 检查索引是否已经被选择过
			selectedIndices[index] = true
		}
	}

	// 根据选择的索引收集用户数据
	for index := range selectedIndices {
		selectedUsers = append(selectedUsers, data[index])
	}
	return selectedUsers
}
