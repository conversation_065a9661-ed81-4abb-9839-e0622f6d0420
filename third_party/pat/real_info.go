package pat

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
)

type RealInfoResp struct {
	Code int32       `json:"code" form:"code"`
	Desc string      `json:"desc" form:"desc"`
	Data []*RealInfo `json:"data" form:"data"`
}

type RealInfo struct {
	Id       string `json:"_id,omitempty" form:"_id"`
	Nickname string `json:"nickname,omitempty" form:"nickname"`
	Avatar   string `json:"avatar,omitempty" form:"avatar"`
	RealName string `json:"real_name,omitempty" form:"real_name"`
}

// GetRealInfo 获取用户实名信息
func GetRealInfo(ctx context.Context, userIds []string) ([]*RealInfo, error) {
	rsp := &RealInfoResp{}

	req, err := request.Pat()
	if err != nil {
		return nil, err
	}

	opts := []utilRequest.Option{
		utilRequest.WithParams(map[string]interface{}{
			"user_ids": userIds,
		}),
		utilRequest.WithMethodPost(),
	}
	err = req.Call(
		ctx,
		"open/user/v1/get_users_real_info/",
		&rsp,
		opts...,
	)
	if err != nil {
		return nil, err
	}

	return rsp.Data, err
}
