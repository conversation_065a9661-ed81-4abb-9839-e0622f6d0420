package unx

import (
	"app_service/global"
	"app_service/pkg/util"
	"app_service/third_party/mor"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"errors"
	"fmt"
	"time"
)

type BatchIssueUserItemItem struct {
	ItemId      string `json:"item_id"`      // 真实商品id
	QualityType int32  `json:"quality_type"` // 物品品质类型(1超稀有2隐藏款3普通款4欧皇款)
	ItemNum     int    `json:"item_num"`     // 数量
	//ExchangePrice   *int   `json:"exchange_price"`   //寄售总的
	//ExchangeBalance *int   `json:"exchange_balance"` //寄售得到的balance
	//ExchangeToken   *int   `json:"exchange_token"`   //寄售得到的token
	// 新增，用户物品来源追溯
	//OrderBalanceAmount  *int32     `json:"order_balance_amount"`  // 余额支付
	//OrderDiscountAmount *int32     `json:"order_discount_amount"` // 优惠金额
	//OrderCoinAmount     *int32     `json:"order_coin_amount"`     // 金币支付金额
	//OrderTokenAmount    *int32     `json:"order_token_amount"`    // 米粒支付金额
	//PurchaseAmount      *int32     `json:"purchase_amount"`       // 单价
	//OrderCreateTime     *time.Time `json:"order_create_time"`     // 订单创建时间
}

type BatchIssueUserItemReq struct {
	UserId   string                    `json:"user_id" form:"user_id" json:"user_id,omitempty"` // 必传
	GameName string                    `json:"game_name" form:"game_name"`                      // 必传
	OrderId  string                    `json:"order_id" form:"order_id"`                        // 关联的订单id
	Items    []*BatchIssueUserItemItem `json:"items" form:"items" json:"items"`                 // 必传
}

type BatchIssueUserItemData struct {
	List []*BatchIssueUserItemDataIssueList `json:"issue_list" form:"issue_list"`
}

type BatchIssueUserItemDataIssueList struct {
	TrId       string `json:"tr_id"`
	ItemId     string `json:"item_id"`
	UserItemId string `json:"user_item_id"`
	Amount     int    `json:"amount"`
}

type BatchIssueUserItemRsp struct {
	Code int32                  `json:"code" form:"code"`
	Desc string                 `json:"desc" form:"desc"`
	Data BatchIssueUserItemData `json:"data" form:"data"`
}

// BatchIssueUserItem 实物奖励批量下发
func BatchIssueUserItem(ctx context.Context, form *BatchIssueUserItemReq) (*BatchIssueUserItemRsp, error) {
	rsp := &BatchIssueUserItemRsp{}
	req, err := request.Unx()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":         form.UserId,
		"game_name":       form.GameName,
		"source_order_id": form.OrderId,
		"items":           form.Items,
	}

	err = req.Call(
		ctx,
		"open/item/v1/batch_issue_user_item",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(60*time.Second),
	)
	if err != nil {
		if errors.Is(err, context.Canceled) {
			mor.SendWarnMsg(context.Background(), global.GlobalConfig.WarnId, fmt.Sprintf("实物奖励批量下发异常,请求数据：%+v,返回数据：%+v", util.Obj2JsonStr(params), err))
		}
		log.Ctx(ctx).Errorf("实物奖励批量下发异常，返回数据：%v", err)
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("实物奖励批量下发异常，返回数据：%v", rsp)
		return rsp, response.Fail.SetMsg("实物奖励批量下发失败")
	}
	return rsp, err
}
