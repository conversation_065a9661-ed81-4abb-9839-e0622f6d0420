package unx

import (
	"app_service/third_party/base"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
	"golang.org/x/net/context"
	"time"
)

type TakeBackUserItemReq struct {
	UserId     string `json:"user_id" form:"user_id" json:"user_id,omitempty"`                // 必传
	UserItemId string `json:"user_item_id" form:"user_item_id" json:"user_item_id,omitempty"` // 必传
	Remark     string `json:"remark" form:"remark"`                                           // 非必传
}

// TakeBackUserItem 用户物品回收
func TakeBackUserItem(ctx context.Context, form *TakeBackUserItemReq) (*base.Rsp, error) {
	rsp := &base.Rsp{}
	req, err := request.Unx()
	if err != nil {
		return nil, err
	}

	params := map[string]interface{}{
		"user_id":      form.UserId,
		"user_item_id": form.UserItemId,
		"remark":       form.Remark,
	}

	err = req.Call(
		ctx,
		"open/item/v1/takeback_user_item",
		&rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
		utilRequest.WithTimeOut(30*time.Second),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("用户物品回收异常，返回数据：%v", rsp)
		return rsp, response.SystemErr.SetMsg("用户物品回收失败")
	}

	return rsp, err
}
