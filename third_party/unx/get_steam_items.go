package unx

import (
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type SteamItemInfo struct {
	Id              string   `json:"_id" form:"id"`
	SkuId           string   `json:"sku_no" form:"sku_no"`
	ItemName        string   `json:"item_name" form:"item_name"`                 // 商品名称
	IconUrl         string   `json:"icon_url" form:"icon_url"`                   // 商品图片
	PurchasePrice   int32    `json:"purchase_price" form:"purchase_price"`       // 商品进价
	SellPrice       int32    `json:"sell_price" form:"sell_price"`               // 商品售价
	Spec            string   `json:"spec" form:"spec"`                           // 规格
	ItemClassifyIds []string `json:"item_classify_ids" form:"item_classify_ids"` //item_classify_ids
	IpClassifyIds   []string `json:"ip_classify_ids" form:"ip_classify_ids"`     //ip_classify_ids
}

type GetSteamItemInfoByIdsData struct {
	Total int32            `json:"total" form:"total"`
	List  []*SteamItemInfo `json:"list" form:"list"`
}

type GetSteamItemInfoByIdsRsp struct {
	Code int32                     `json:"code" form:"code"`
	Desc string                    `json:"desc" form:"desc"`
	Data GetSteamItemInfoByIdsData `json:"data" form:"data"`
}

func GetSteamItemInfoByIds(ctx context.Context, id []string) ([]*SteamItemInfo, error) {
	rsp := &GetSteamItemInfoByIdsRsp{}
	req, err := request.Unx()
	if err != nil {
		return nil, err
	}
	err = req.Call(
		ctx,
		"open/steam_item/v1/get_steam_items",
		&rsp,
		utilRequest.WithParams(map[string]interface{}{"item_ids": id}),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}

	if rsp.Code != 0 {
		return nil, response.Fail.SetMsg(rsp.Desc)
	}
	return rsp.Data.List, err
}
