package base

import (
	"encoding/json"
	"fmt"
	"strings"
)

type Rsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data any    `json:"data" form:"data"`
}

// BuildQueryGroups 构建查询结构
func BuildQueryGroups(params map[string]interface{}) string {
	var queryGroups []string
	for k, v := range params {
		keys := strings.Split(k, "||")
		if len(keys) != 2 {
			continue
		}

		// 将值转换为 JSON 字符串
		valueJSON, _ := json.Marshal(v)
		valueStr := string(valueJSON)

		// 构造单个查询条件
		query := fmt.Sprintf(`["%s","%s",%s]`, keys[0], keys[1], valueStr)
		queryGroups = append(queryGroups, query)
	}

	// 将所有查询条件组合成一个数组
	return "[" + strings.Join(queryGroups, ",") + "]"
}
