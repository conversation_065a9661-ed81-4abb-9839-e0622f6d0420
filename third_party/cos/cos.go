package cos

import (
	"app_service/global"
	"context"
	"fmt"
	"github.com/tencentyun/cos-go-sdk-v5"
	"io"
	"net/http"
	"net/url"
)

var client *cos.Client

// InitCOSClient 初始化COS客户端
func InitCOSClient(conf global.Cos) *cos.Client {
	u, _ := url.Parse(fmt.Sprintf("https://%s.cos.%s.myqcloud.com", conf.Bucket, conf.Region))
	b := &cos.BaseURL{BucketURL: u}
	client = cos.NewClient(b, &http.Client{
		Transport: &cos.AuthorizationTransport{
			SecretID:  conf.SecretId,
			SecretKey: conf.<PERSON>Key,
		},
	})
	return client
}

// Upload 上传文件
func Upload(ctx context.Context, filePath string, reader io.Reader) (*cos.Response, error) {
	res, err := client.Object.Put(ctx, filePath, reader, nil)
	if err != nil {
		return nil, err
	}
	return res, nil
}
