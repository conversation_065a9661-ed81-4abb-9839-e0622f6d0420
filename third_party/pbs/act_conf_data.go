package pbs

import (
	"app_service/global"
	"context"
	"fmt"
	"time"

	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"github.com/pkg/errors"
)

type ActAdQueryConfFromResp struct {
	ID        int64     `json:"id"`
	Title     string    `json:"title"`
	Link      string    `json:"link"`
	BeginTime time.Time `json:"begin_time"` //活动开始时间
}

type actQueryResp struct {
	Code int                       `json:"code"`
	Msg  string                    `json:"msg"`
	Data []*ActAdQueryConfFromResp `json:"data"`
}

func GetPbsHost() string {
	if global.GlobalConfig.Pbs != nil {
		return global.GlobalConfig.Pbs.AdminHost
	}
	return ""
}

// QueryActWithAdData 通过 PBS服务查询活动数据
func QueryActWithAdData(ctx context.Context, adID string) ([]*ActAdQueryConfFromResp, error) {
	pbsHost := GetPbsHost()
	url := fmt.Sprintf("%s/openAdvertiser/v1/ad/query", pbsHost)
	params := map[string]interface{}{
		"ad_id": adID,
	}
	rsp := &actQueryResp{}
	err := utilRequest.New(ctx,
		utilRequest.WithMethodGet(),
		utilRequest.WithUrl(url),
		utilRequest.WithParams(params),
		//utilRequest.WithTimeOut(5),
	).Call(rsp)
	if err != nil {
		return nil, errors.Wrap(err, "pbs request error")
	}

	if rsp.Code != 0 {
		return nil, errors.New(rsp.Msg)
	}
	return rsp.Data, nil
}
