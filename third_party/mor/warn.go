package mor

import (
	"app_service/pkg/util"
	"context"
	"e.coding.net/g-dtay0385/common/go-airmart-client/request"
	log "e.coding.net/g-dtay0385/common/go-logger"
	utilRequest "e.coding.net/g-dtay0385/common/go-util/request"
	"e.coding.net/g-dtay0385/common/go-util/response"
)

type WarnReq struct {
	Id                  string   `json:"id" form:"id"`
	Msg                 string   `json:"msg" form:"msg"`
	MentionedList       []string `json:"mentioned_list,omitempty" form:"mentioned_list,omitempty"`
	MentionedMobileList []string `json:"mentioned_mobile_list,omitempty" form:"mentioned_mobile_list,omitempty"`
}

type WarnRsp struct {
	Code int32  `json:"code" form:"code"`
	Desc string `json:"desc" form:"desc"`
	Data any    `json:"data" form:"data"`
}

// SendWarnMsg 告警
func SendWarnMsg(ctx context.Context, id string, msg string) {
	go func() {
		req := &WarnReq{
			Id:  id,
			Msg: msg,
		}
		res, err := Warn(ctx, req)
		if err != nil {
			log.Ctx(ctx).Errorf("[warn.SendWarnMsg] 调用告警接口失败,id: %v, msg:%v, err:%v", req.Id, req.Msg, err)
		} else {
			log.Ctx(ctx).Infof("[warn.SendWarnMsg] 调用告警接口成功,id: %v, msg:%v, res:%v", req.Id, req.Msg, util.Obj2JsonStr(res))
		}
	}()
}

// Warn 告警
func Warn(ctx context.Context, f *WarnReq) (*WarnRsp, error) {
	rsp := &WarnRsp{}
	req, err := request.Mor()
	if err != nil {
		return nil, err
	}

	textData := map[string]interface{}{
		"content": f.Msg,
	}
	if len(f.MentionedList) > 0 {
		textData["mentioned_list"] = f.MentionedList
	}
	if len(f.MentionedMobileList) > 0 {
		textData["mentioned_mobile_list"] = f.MentionedMobileList
	}
	params := map[string]interface{}{
		"id": f.Id,
		"message": map[string]interface{}{
			"msgtype": "text",
			"text":    textData,
		},
	}

	err = req.Call(
		ctx,
		"open/warn/v1/dingding",
		rsp,
		utilRequest.WithParams(params),
		utilRequest.WithMethodPost(),
	)
	if err != nil {
		return nil, err
	}
	if rsp.Code != 0 {
		log.Ctx(ctx).Errorf("调用告警接口，返回数据：%v", util.Obj2JsonStr(rsp))
		return nil, response.Fail.SetMsg("告警失败")
	}
	return rsp, nil
}
