DROP TABLE IF EXISTS `ship_manage`;

CREATE TABLE `ship_manage` (
   `ship_manage_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
   `yc_id` varchar(255) NOT NULL COMMENT '云仓订单 id',
   `withdraw_type` tinyint NOT NULL COMMENT '提货方式：1：手动提货 2：强制发货',
   `order_type` tinyint NOT NULL COMMENT '订单来源：1：云仓提货 2：文潮提货',
   `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
   `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
   PRIMARY KEY (`ship_manage_id`),
   UNIQUE KEY `uniq_yc_id` (`yc_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='云仓发货管理表';
