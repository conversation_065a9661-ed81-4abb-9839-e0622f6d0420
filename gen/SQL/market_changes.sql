
CREATE TABLE `market_changes` (
  `market_changes_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) NOT NULL COMMENT '标题',
  `content` text DEFAULT NULL COMMENT '内容',
  `category_id` bigint unsigned NOT NULL COMMENT '栏目ID',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：1待发布 2定时中 3已发布 4已下架',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `publish_type` tinyint NOT NULL DEFAULT 1 COMMENT '发布方式：1立即发布 2定时发布',
  `message_push` tinyint NOT NULL DEFAULT '0' COMMENT '消息推送【0->不推送; 1->推送】',
  `created_by` char(24) NOT NULL COMMENT '创建人',
  `updated_by` char(24) NOT NULL COMMENT '更新人',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`market_changes_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_publish_time` (`publish_time`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_updated_by` (`updated_by`),
  KEY `idx_market_changes_status_publish_time` (`status`,`publish_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行情异动表';


CREATE TABLE `category` (
  `category_id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `relate_type` tinyint NOT NULL COMMENT '关联类型，1行情异动',
  `name` varchar(50) NOT NULL COMMENT '名称',
  `text_color` char(7) NOT NULL COMMENT '文字颜色',
  `background_color` char(7) NOT NULL COMMENT '背景颜色',
  `priority` int unsigned NOT NULL COMMENT '优先级，最大值9999',
  `status` tinyint NOT NULL DEFAULT 1 COMMENT '状态：-1:已删除 0禁用 1启用 ',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除【0->未删除; 1->删除】',
  PRIMARY KEY (`category_id`),
  KEY `idx_priority` (`priority`),
  KEY `idx_relate_type` (`relate_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='栏目表';


CREATE TABLE `market_changes_item` (
  `market_changes_id` bigint unsigned NOT NULL COMMENT '行情异动ID',
  `item_id` varchar(255) NOT NULL COMMENT '商品ID (商品挂牌编码)',
  `item_name` varchar(255) NOT NULL COMMENT '商品名称',
  `image_url` varchar(255) DEFAULT NULL COMMENT '商品图片',
  `price_changes` decimal(10,2) NOT NULL COMMENT '价格变动',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`market_changes_id`, `item_id`),
  KEY `idx_mci_item_id` (`item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行情异动与商品关联表';


CREATE TABLE `market_changes_channel` (
  `market_changes_id` bigint unsigned NOT NULL COMMENT '行情异动ID',
  `channel_id` varchar(255) NOT NULL COMMENT '发布终端ID',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`market_changes_id`, `channel_id`),
  KEY `idx_mcc_channel_id` (`channel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='行情异动与发布终端关联表';


