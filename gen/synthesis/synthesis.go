package synthesis

import (
	"app_service/gen/tool"
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@tcp(localhost:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/synthesis/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	s := g.GenerateModelAs("synthesis", "Synthesis")
	sm := g.GenerateModelAs("synthesis_materials", "SynthesisMaterials")
	so := g.GenerateModelAs(
		"synthesis_order",
		"SynthesisOrder",
		gen.FieldRelate(
			field.HasMany,
			"SynthesisOrderDetail",
			g.GenerateModelAs("synthesis_order_detail", "SynthesisOrderDetail"),
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"SynthesisOrderID"}},
			},
		),
		gen.FieldRelate(
			field.BelongsTo,
			"Synthesis",
			s,
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"SynthesisID"}},
			},
		),
	)
	sod := g.GenerateModelAs(
		"synthesis_order_detail",
		"SynthesisOrderDetail",
		gen.FieldRelate(
			field.BelongsTo,
			"SynthesisOrder",
			so,
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"SynthesisOrderID"}},
			}),
	)
	slog := g.GenerateModelAs("synthesis_log", "SynthesisLog")
	smr := g.GenerateModelAs("synthesis_materials_release", "SynthesisMaterialsRelease")
	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, s, sm, so, sod, slog, smr,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "synthesis", "Synthesis", "synthesis", "ISynthesisDo", "Synthesis")
	tool.GenRepo("business", "synthesis", "SynthesisMaterials", "synthesis_materials", "ISynthesisMaterialsDo", "SynthesisMaterials")
	tool.GenRepo("business", "synthesis", "SynthesisOrder", "synthesis_order", "ISynthesisOrderDo", "SynthesisOrder")
	tool.GenRepo("business", "synthesis", "SynthesisOrderDetail", "synthesis_order_detail", "ISynthesisOrderDetailDo", "SynthesisOrderDetail")
	tool.GenRepo("business", "synthesis", "SynthesisLog", "synthesis_log", "ISynthesisLogDo", "SynthesisLog")
	tool.GenRepo("business", "synthesis", "SynthesisMaterialsRelease", "synthesis_materials_release", "ISynthesisMaterialsReleaseDo", "SynthesisMaterialsRelease")
}
