package common

import (
	"app_service/gen/tool"

	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/platform/common/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	// 生成公共配置模型
	commonConfig := g.GenerateModelAs("common_config", "CommonConfig")
	// 生成操作日志模型
	operationLog := g.GenerateModelAs("operation_log", "OperationLog")
	// 生成栏目模型
	category := g.GenerateModelAs("category", "Category")

	applyBasic := make([]interface{}, 0)
	applyBasic = append(applyBasic, commonConfig, operationLog, category)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, commonConfig, operationLog, category)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("platform", "common", "CommonConfig", "common_config", "ICommonConfigDo", "CommonConfig")
	tool.GenRepo("platform", "common", "OperationLog", "operation_log", "IOperationLogDo", "OperationLog")
	tool.GenRepo("platform", "common", "Category", "category", "ICategoryDo", "Category")
}
