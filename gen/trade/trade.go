package trade

import (
	"app_service/gen/tool"
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@tcp(localhost:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/trade/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	mo := g.GenerateModelAs("market_overview", "MarketOverview")
	ci := g.GenerateModelAs("circulation_item", "CirculationItem")
	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, mo, ci,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "trade", "MarketOverview", "market_overview", "IMarketOverviewDo", "MarketOverview")
	tool.GenRepo("business", "trade", "CirculationItem", "circulation_item", "ICirculationItemDo", "CirculationItem")
}
