package story

import (
	"app_service/gen/tool"
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@tcp(localhost:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/story/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	s := g.GenerateModelAs("story", "Story")
	sm := g.GenerateModelAs("story_materials", "StoryMaterials")
	so := g.GenerateModelAs(
		"story_order",
		"StoryOrder",
		gen.FieldRelate(
			field.HasMany,
			"StoryOrderDetail",
			g.GenerateModelAs("story_order_detail", "StoryOrderDetail"),
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"StoryOrderID"}},
			},
		),
		gen.FieldRelate(
			field.BelongsTo,
			"Story",
			s,
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"StoryID"}},
			},
		),
	)
	sod := g.GenerateModelAs(
		"story_order_detail",
		"StoryOrderDetail",
		gen.FieldRelate(
			field.BelongsTo,
			"StoryOrder",
			so,
			&field.RelateConfig{
				GORMTag: map[string][]string{"foreignKey": {"StoryOrderID"}},
			}),
	)
	slog := g.GenerateModelAs("story_log", "StoryLog")
	smr := g.GenerateModelAs("story_materials_release", "StoryMaterialsRelease")
	ss := g.GenerateModelAs("story_scene", "StoryScene")
	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, s, sm, so, sod, slog, smr, ss,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "story", "Story", "story", "IStoryDo", "Story")
	tool.GenRepo("business", "story", "StoryMaterials", "story_materials", "IStoryMaterialsDo", "StoryMaterials")
	tool.GenRepo("business", "story", "StoryOrder", "story_order", "IStoryOrderDo", "StoryOrder")
	tool.GenRepo("business", "story", "StoryOrderDetail", "story_order_detail", "IStoryOrderDetailDo", "StoryOrderDetail")
	tool.GenRepo("business", "story", "StoryLog", "story_log", "IStoryLogDo", "StoryLog")
	tool.GenRepo("business", "story", "StoryMaterialsRelease", "story_materials_release", "IStoryMaterialsReleaseDo", "StoryMaterialsRelease")
	tool.GenRepo("business", "story", "StoryScene", "story_scene", "IStorySceneDo", "StoryScene")
}
