package operation_announcement

import (
	"app_service/gen/tool"
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/operation_announcement/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	// 生成公告栏目模型
	annCategory := g.GenerateModelAs("operation_ann_category", "OperationAnnCategory")

	// 生成公告模型，并配置与公告栏目的关联关系
	ann := g.GenerateModelAs(
		"operation_announcement",
		"OperationAnnouncement",
		gen.FieldRelate(
			field.BelongsTo,
			"CategoryInfo",
			annCategory,
			&field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"category_id"},
					"references": {"operation_ann_category_id"},
				},
			},
		),
	)

	applyBasic := make([]interface{}, 0)
	applyBasic = append(applyBasic, ann, annCategory)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "operation_announcement", "OperationAnnouncement", "operation_announcement", "IOperationAnnouncementDo", "OperationAnnouncement")
	tool.GenRepo("business", "operation_announcement", "OperationAnnCategory", "operation_ann_category", "IOperationAnnCategoryDo", "OperationAnnCategory")
}
