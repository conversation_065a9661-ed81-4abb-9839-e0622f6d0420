package ship_manage

import (
	"app_service/gen/tool"

	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

// genModel 生成 GORM 模型和查询代码
func genModel() {
	// 数据库连接配置
	dsn := "root:123456@/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}

	// 初始化 GORM 生成器
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/yc/dal/query", // 输出目录
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true, // 为字段生成 GORM 标签
	})
	g.UseDB(db)

	// 配置数据库类型到Go类型的映射
	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			// 处理可空的时间类型
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	// 为 ship_manage 表生成模型
	shipManageModel := g.GenerateModelAs("ship_manage", "ShipManage")

	// 应用模型到生成器
	applyModels := []interface{}{shipManageModel}
	g.ApplyBasic(applyModels...)
	g.ApplyInterface(func(method model.Method) {}, applyModels...)

	// 执行代码生成
	g.Execute()
}

// Gen 是生成 ship_manage 表相关代码的主函数
// 包含模型定义和仓库层代码
func Gen() {
	// 1. 生成基础模型和查询代码
	genModel()

	// 2. 生成仓库层代码
	tool.GenRepo(
		"business",      // 模块名称
		"yc",            // 子目录
		"ShipManage",    // 模型名称
		"ship_manage",   // 数据库表名
		"IShipManageDo", // 查询接口名称
		"ShipManage",    // 结构体名称
	)
}
