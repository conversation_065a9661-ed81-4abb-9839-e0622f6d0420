package marketchanges

import (
	"app_service/gen/tool"

	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:123456@/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/business/market_changes/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	// 生成栏目模型
	category := g.GenerateModelAs("category", "Category")

	// 生成行情异动商品关联模型
	marketChangesItem := g.GenerateModelAs("market_changes_item", "MarketChangesItem")

	// 生成行情异动渠道关联模型
	marketChangesChannel := g.GenerateModelAs("market_changes_channel", "MarketChangesChannel")

	// 生成行情异动模型，并配置与行情异动栏目、商品、渠道的关联关系
	marketChanges := g.GenerateModelAs(
		"market_changes",
		"MarketChanges",
		gen.FieldRelate(
			field.BelongsTo,
			"CategoryInfo",
			category,
			&field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"CategoryID"},
					"references": {"CategoryID"},
				},
			},
		),
		gen.FieldRelate(
			field.HasMany,
			"Items",
			marketChangesItem,
			&field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"MarketChangesID"},
					"references": {"MarketChangesID"},
				},
			},
		),
		gen.FieldRelate(
			field.HasMany,
			"Channels",
			marketChangesChannel,
			&field.RelateConfig{
				GORMTag: map[string][]string{
					"foreignKey": {"MarketChangesID"},
					"references": {"MarketChangesID"},
				},
			},
		),
	)

	applyBasic := make([]interface{}, 0)
	applyBasic = append(applyBasic, marketChanges, category, marketChangesItem, marketChangesChannel)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("business", "market_changes", "MarketChanges", "market_changes", "IMarketChangesDo", "MarketChanges")
	tool.GenRepo("business", "market_changes", "Category", "category", "ICategoryDo", "Category")
	tool.GenRepo("business", "market_changes", "MarketChangesItem", "market_changes_item", "IMarketChangesItemDo", "MarketChangesItem")
	tool.GenRepo("business", "market_changes", "MarketChangesChannel", "market_changes_channel", "IMarketChangesChannelDo", "MarketChangesChannel")
}
