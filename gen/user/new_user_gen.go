package user

import (
	"app_service/gen/tool"
	"github.com/golang/mock/mockgen/model"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gen/field"
	"gorm.io/gorm"
)

func genModel() {
	dsn := "root:nnA6MXpPQhqkxxEBLo@tcp(192.168.0.193:3306)/app_service?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		panic(err)
	}
	g := gen.NewGenerator(gen.Config{
		OutPath:          "./apps/platform/user/dal/query",
		Mode:             gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldWithTypeTag: true,
	})
	g.UseDB(db)

	isDelType := gen.FieldType("is_del", "soft_delete.DeletedAt")
	isDelFlag := gen.FieldGORMTag("is_del", func(tag field.GormTag) field.GormTag {
		return tag.Append("softDelete", "flag")
	})
	g.WithOpts(isDelType, isDelFlag)

	dataTypeMap := map[string]func(gorm.ColumnType) (dataType string){
		"datetime": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*time.Time"
			}
			return "time.Time"
		},
		"json": func(columnType gorm.ColumnType) (dataType string) {
			if n, ok := columnType.Nullable(); ok && n {
				return "*datatypes.JSON"
			}
			return "datatypes.JSON"
		},
	}
	g.WithDataTypeMap(dataTypeMap)

	newUser := g.GenerateModelAs("new_user", "NewUser")
	userBind := g.GenerateModelAs("user_bind", "UserBind")
	userPurchaseLog := g.GenerateModelAs("user_purchase_log", "UserPurchaseLog")
	userLoginLog := g.GenerateModelAs("user_login_log", "UserLoginLog")
	applyBasic := make([]interface{}, 0)
	applyBasic = append(
		applyBasic, newUser, userBind, userPurchaseLog, userLoginLog,
	)
	g.ApplyBasic(applyBasic...)
	g.ApplyInterface(func(method model.Method) {}, applyBasic...)
	g.Execute()
}

func Gen() {
	genModel()
	tool.GenRepo("platform", "user", "NewUser", "new_user", "INewUserDo", "NewUser")
	tool.GenRepo("platform", "user", "UserBind", "user_bind", "IUserBindDo", "UserBind")
	tool.GenRepo("platform", "user", "UserPurchaseLog", "user_purchase_log", "IUserPurchaseLogDo", "UserPurchaseLog")
	tool.GenRepo("platform", "user", "UserLoginLog", "user_login_log", "IUserLoginLogDo", "UserLoginLog")
}
