# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 开发命令

### 构建和测试
- `make lint-install`: 安装 golangci-lint 代码检查工具
- `make lint`: 运行 Go 代码检查，使用项目特定规则
- `make check`: 安装检查工具并运行完整代码检查
- `make check-error-codes`: 检查重复或无效的错误码
- `make check-all`: 运行代码检查和错误码验证
- `make swag`: 生成 Swagger API 文档

### 构建命令
- `go build -o http_server cmd/http_server/main.go`: 构建 HTTP 服务器
- `deploy/build.sh`: 生产环境构建脚本 (Linux ARM64)

### 开发服务器
- `go run cmd/http_server/main.go`: 本地启动 HTTP 服务器
- `go run cmd/task_server/main.go`: 启动异步任务服务器

## 架构概览

这是一个 Go 微服务应用，采用分层架构支持创新业务功能。系统使用：

- **框架**: Go + Gin (HTTP)，go-micro (微服务)
- **数据库**: MySQL (主库)，MongoDB (文档)，Redis (缓存)，Elasticsearch (搜索)
- **消息队列**: Kafka 异步处理
- **监控**: OpenTelemetry 链路追踪，Jaeger

### 核心架构分层

1. **接入层** (`cmd/`): 服务入口点
   - `http_server`: REST API 服务器 (Gin)
   - `task_server`: 异步/定时任务
   - `grpc_server`: RPC 通信

2. **业务层** (`apps/`):
   - `business/`: 核心业务模块 (card_community, story, synthesis 等)
   - `platform/`: 共享平台服务 (user, asset, system)
   - 每个模块遵循模式: `api/`, `define/`, `router/`, `service/`, `dal/`, `repo/`

3. **通用层** (`pkg/`, `third_party/`):
   - `pkg/`: 共享工具，中间件，分页功能
   - `third_party/`: 外部服务集成

4. **数据层**: MySQL/MongoDB/Redis/Elasticsearch

### 模块结构模式

`apps/business/` 中每个业务模块遵循此结构:
```
模块名称/
├── api/          # API 处理器 (admin/, web/, open/)
├── define/       # 请求/响应 DTO 和枚举
├── router/       # 路由定义
├── service/      # 业务逻辑
├── dal/          # 数据访问层 (模型，查询)
├── repo/         # 仓储模式 (如果使用)
└── consume/      # Kafka 消息消费者
```

### 重要的开发约定
- 所有业务模块都有三种路由类型：admin (管理端), web (用户端), open (开放接口)
- dal/model/ 目录包含数据库模型，支持 MySQL 和 MongoDB
- define/enums/ 目录包含业务枚举定义
- service/logic/ 目录包含具体业务逻辑实现

## 配置管理

- 环境配置在 `conf/http_server/`:
  - `config-dev.yaml`: 开发环境
  - `config-sit.yaml`: SIT 环境  
  - `config-prod.yaml`: 生产环境
- 全局配置在 `global/config.go`

## 数据库

- 使用 GORM v2 操作 MySQL 和 MongoDB
- 模型在 `dal/model/` 目录生成
- 查询构建器在 `dal/query/` 使用 GORM Gen
- MongoDB 文档在 `dal/model/mongdb/`

## 核心开发实践

- 错误码集中管理 (使用 `make check-error-codes` 检查)
- 从代码注释自动生成 Swagger 文档
- 全应用结构化日志
- OpenTelemetry 链路追踪可观测性
- Kafka 消费者异步处理
- Redis 缓存和分布式锁

## 第三方集成

`third_party/` 目录包含以下集成:
- 云服务 (腾讯云 COS 等)
- 支付系统  
- 推送通知 (极光推送)
- 用户管理服务
- 内容审核服务

## 当前分支上下文

正在 `feat/1761/card_community` 分支工作，最近更改包括:
- 卡片社区路由配置
- HTTP 服务器主配置  
- API 文档更新
- 卡片社区功能的数据库迁移